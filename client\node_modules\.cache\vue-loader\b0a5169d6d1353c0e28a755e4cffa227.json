{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=template&id=25dba680&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748528250348}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}