{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=template&id=25dba680&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748533042874}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9InBxZS1vd25lci1kYXNoYm9hcmQtY29udGFpbmVyIj4KICA8ZGl2IGNsYXNzPSJjb250ZW50LXdyYXBwZXIiPgogICAgPCEtLSBIZWFkZXIgU2VjdGlvbiB3aXRoIENyaXRpY2FsIElzc3VlcyBTdW1tYXJ5IC0tPgogICAgPGRpdiBjbGFzcz0iZGFzaGJvYXJkLWhlYWRlciI+CiAgICAgIDxoMz57eyBwcWVPd25lciB9fSdzIERhc2hib2FyZDwvaDM+CiAgICAgIDxkaXYgY2xhc3M9Imxhc3QtdXBkYXRlZC10ZXh0Ij4KICAgICAgICBMYXN0IFVwZGF0ZWQ6IHt7IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKSB9fQogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0gS2V5IE1ldHJpY3MgU2VjdGlvbiAtLT4KICAgIDxkaXYgY2xhc3M9ImtleS1tZXRyaWNzLXNlY3Rpb24iPgogICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtY2FyZCI+CiAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWljb24gbmV3LWlzc3VlcyI+CiAgICAgICAgICA8c3ZnIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJjdXJyZW50Q29sb3IiPgogICAgICAgICAgICA8cGF0aCBkPSJNMTYsMkExNCwxNCwwLDEsMCwzMCwxNiwxNCwxNCwwLDAsMCwxNiwyWm0wLDI2QTEyLDEyLDAsMSwxLDI4LDE2LDEyLDEyLDAsMCwxLDE2LDI4WiI+PC9wYXRoPgogICAgICAgICAgICA8cGF0aCBkPSJNMjAuNTksMjIsMTUsMTYuNDFWN2gydjguNThsNSw1LjAxTDIwLjU5LDIyeiI+PC9wYXRoPgogICAgICAgICAgPC9zdmc+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWNvbnRlbnQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWxhYmVsIj5OZXcgQ3JpdGljYWwgSXNzdWVzPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtdmFsdWUiPnt7IG5ld0NyaXRpY2FsSXNzdWVzLmxlbmd0aCB9fTwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWRlc2NyaXB0aW9uIj5UaGlzIE1vbnRoPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgoKICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWNhcmQiPgogICAgICAgIDxkaXYgY2xhc3M9Im1ldHJpYy1pY29uIGNyaXRpY2FsLWlzc3VlcyI+CiAgICAgICAgICA8c3ZnIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJjdXJyZW50Q29sb3IiPgogICAgICAgICAgICA8cGF0aCBkPSJNMTYsMkExNCwxNCwwLDEsMCwzMCwxNiwxNCwxNCwwLDAsMCwxNiwyWm0wLDI2QTEyLDEyLDAsMSwxLDI4LDE2LDEyLDEyLDAsMCwxLDE2LDI4WiI+PC9wYXRoPgogICAgICAgICAgICA8cGF0aCBkPSJNMTYsMTBhNiw2LDAsMSwwLDYsNkE2LDYsMCwwLDAsMTYsMTBaIj48L3BhdGg+CiAgICAgICAgICA8L3N2Zz4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtY29udGVudCI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtbGFiZWwiPkNyaXRpY2FsIElzc3VlczwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLXZhbHVlIj57eyB1bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMubGVuZ3RoIH19PC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtZGVzY3JpcHRpb24iPk5lZWQgUmVzb2x1dGlvbjwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KCiAgICAgIDxkaXYgY2xhc3M9Im1ldHJpYy1jYXJkIj4KICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtaWNvbiB2YWxpZGF0ZWQiPgogICAgICAgICAgPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0iY3VycmVudENvbG9yIj4KICAgICAgICAgICAgPHBhdGggZD0iTTI2LDRINkEyLDIsMCwwLDAsNCw2VjI2YTIsMiwwLDAsMCwyLDJIMjZhMiwyLDAsMCwwLDItMlY2QTIsMiwwLDAsMCwyNiw0Wk0xNCwyMS41bC01LTUsMS41OS0xLjVMMTQsMTguMzUsMjEuNDEsMTEsMjMsMTIuNThaIj48L3BhdGg+CiAgICAgICAgICA8L3N2Zz4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtY29udGVudCI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtbGFiZWwiPlZhbGlkYXRlZCBGYWlsczwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLXZhbHVlIj57eyB2YWxpZGF0ZWRDb3VudCB9fTwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWRlc2NyaXB0aW9uIj5UaGlzIE1vbnRoPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgoKICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWNhcmQiPgogICAgICAgIDxkaXYgY2xhc3M9Im1ldHJpYy1pY29uIHVudmFsaWRhdGVkIj4KICAgICAgICAgIDxzdmcgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiB2aWV3Qm94PSIwIDAgMzIgMzIiIGZpbGw9ImN1cnJlbnRDb2xvciI+CiAgICAgICAgICAgIDxwYXRoIGQ9Ik0yNiw0SDZBMiwyLDAsMCwwLDQsNlYyNmEyLDIsMCwwLDAsMiwySDI2YTIsMiwwLDAsMCwyLTJWNkEyLDIsMCwwLDAsMjYsNFpNNiwyNlY2SDI2VjI2WiI+PC9wYXRoPgogICAgICAgICAgICA8cGF0aCBkPSJNMTQsMjEuNWwtNS01LDEuNTktMS41TDE0LDE4LjM1LDIxLjQxLDExLDIzLDEyLjU4WiI+PC9wYXRoPgogICAgICAgICAgPC9zdmc+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWNvbnRlbnQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLWxhYmVsIj5VbnZhbGlkYXRlZCBGYWlsczwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWV0cmljLXZhbHVlIj57eyB1bnZhbGlkYXRlZENvdW50IH19PC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRyaWMtZGVzY3JpcHRpb24iPk5lZWQgVmFsaWRhdGlvbjwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0gUm9vdCBDYXVzZSBBbmFseXNpcyBTZWN0aW9uIC0tPgogICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1jYXJkIGNoYXJ0LXNlY3Rpb24iPgogICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWhlYWRlciI+CiAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZS1jb250YWluZXIiPgogICAgICAgICAgPGg0IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj5Sb290IENhdXNlIEFuYWx5c2lzPC9oND4KICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tc3VidGl0bGUiPkN1cnJlbnQgTW9udGggQW5hbHlzaXM8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIFJvb3QgQ2F1c2UgQ2hhcnQgQ29udHJvbHMgLS0+CiAgICAgIDxkaXYgY2xhc3M9ImNoYXJ0LWNvbnRyb2xzIj4KICAgICAgICA8ZGl2IGNsYXNzPSJjb250cm9sLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbCBmb3I9InJvb3QtY2F1c2Utdmlldy1kcm9wZG93biIgY2xhc3M9ImNvbnRyb2wtbGFiZWwiPlZpZXcgQnk6PC9sYWJlbD4KICAgICAgICAgIDxjdi1kcm9wZG93bgogICAgICAgICAgICBpZD0icm9vdC1jYXVzZS12aWV3LWRyb3Bkb3duIgogICAgICAgICAgICB2LW1vZGVsPSJyb290Q2F1c2VWaWV3QnkiCiAgICAgICAgICAgIGNsYXNzPSJjb250cm9sLWRyb3Bkb3duIgogICAgICAgICAgPgogICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0icm9vdENhdXNlIj5Sb290IENhdXNlPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgPC9jdi1kcm9wZG93bj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJjb250cm9sLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbCBmb3I9InJvb3QtY2F1c2UtdGltZS1kcm9wZG93biIgY2xhc3M9ImNvbnRyb2wtbGFiZWwiPlRpbWUgUmFuZ2U6PC9sYWJlbD4KICAgICAgICAgIDxjdi1kcm9wZG93bgogICAgICAgICAgICBpZD0icm9vdC1jYXVzZS10aW1lLWRyb3Bkb3duIgogICAgICAgICAgICB2LW1vZGVsPSJyb290Q2F1c2VUaW1lUmFuZ2UiCiAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZVJvb3RDYXVzZVRpbWVSYW5nZUNoYW5nZSIKICAgICAgICAgICAgY2xhc3M9ImNvbnRyb2wtZHJvcGRvd24iCiAgICAgICAgICA+CiAgICAgICAgICAgIDxjdi1kcm9wZG93bi1pdGVtIHZhbHVlPSI2bW9udGgiPjYgTW9udGhzPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iM21vbnRoIj4zIE1vbnRoczwvY3YtZHJvcGRvd24taXRlbT4KICAgICAgICAgIDwvY3YtZHJvcGRvd24+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0iY29udHJvbC1ncm91cCIgdi1pZj0iYnJlYWtvdXRHcm91cHMgJiYgYnJlYWtvdXRHcm91cHMubGVuZ3RoID4gMCI+CiAgICAgICAgICA8bGFiZWwgZm9yPSJyb290LWNhdXNlLWdyb3VwLWRyb3Bkb3duIiBjbGFzcz0iY29udHJvbC1sYWJlbCI+R3JvdXA6PC9sYWJlbD4KICAgICAgICAgIDxjdi1kcm9wZG93bgogICAgICAgICAgICBpZD0icm9vdC1jYXVzZS1ncm91cC1kcm9wZG93biIKICAgICAgICAgICAgdi1tb2RlbD0icm9vdENhdXNlU2VsZWN0ZWRHcm91cCIKICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlUm9vdENhdXNlR3JvdXBDaGFuZ2UiCiAgICAgICAgICAgIGNsYXNzPSJjb250cm9sLWRyb3Bkb3duIgogICAgICAgICAgPgogICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iYWxsIj5BbGwgR3JvdXBzPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbQogICAgICAgICAgICAgIHYtZm9yPSJncm91cCBpbiBicmVha291dEdyb3VwcyIKICAgICAgICAgICAgICA6a2V5PSJncm91cC5uYW1lIgogICAgICAgICAgICAgIDp2YWx1ZT0iZ3JvdXAubmFtZSIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIHt7IGdyb3VwLm5hbWUgfX0KICAgICAgICAgICAgPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgPC9jdi1kcm9wZG93bj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIFJvb3QgQ2F1c2UgQ2hhcnQgLS0+CiAgICAgIDxkaXYgY2xhc3M9ImNoYXJ0LWNvbnRhaW5lciI+CiAgICAgICAgPFJvb3RDYXVzZUNoYXJ0CiAgICAgICAgICByZWY9InJvb3RDYXVzZUNoYXJ0IgogICAgICAgICAgOmRhdGE9InJvb3RDYXVzZUNoYXJ0RGF0YSIKICAgICAgICAgIDpsb2FkaW5nPSJpc1Jvb3RDYXVzZURhdGFMb2FkaW5nIgogICAgICAgICAgOmhlaWdodD0iJzQwMHB4JyIKICAgICAgICAgIHRpdGxlPSJSb290IENhdXNlIENhdGVnb3JpZXMgYnkgTW9udGgiCiAgICAgICAgICBAYmFyLWNsaWNrPSJoYW5kbGVSb290Q2F1c2VCYXJDbGljayIKICAgICAgICAvPgogICAgICA8L2Rpdj4KCiAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tZm9vdGVyIj4KICAgICAgICA8cCBjbGFzcz0ic2VjdGlvbi1kZXNjcmlwdGlvbiI+CiAgICAgICAgICBSb290IGNhdXNlIGFuYWx5c2lzIHNob3dpbmcgZGVmZWN0IGNhdGVnb3JpZXMgYW5kIHRoZWlyIGZhaWwgcmF0ZXMgb3ZlciB0aW1lLgogICAgICAgICAgQ2xpY2sgb24gYmFycyB0byBzZWUgZGV0YWlsZWQgaW5mb3JtYXRpb24uCiAgICAgICAgPC9wPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0gTWFpbiBDb250ZW50IExheW91dCAtLT4KICAgIDxkaXYgY2xhc3M9ImRhc2hib2FyZC1tYWluLWNvbnRlbnQiPgogICAgICA8IS0tIExlZnQgQ29sdW1uIC0tPgogICAgICA8ZGl2IGNsYXNzPSJkYXNoYm9hcmQtY29sdW1uIj4KICAgICAgICA8IS0tIENyaXRpY2FsIElzc3VlcyBTZWN0aW9uIC0tPgogICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tY2FyZCI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWhlYWRlciBjcml0aWNhbC1pc3N1ZXMtaGVhZGVyIiBAY2xpY2s9InRvZ2dsZUNyaXRpY2FsSXNzdWVzRXhwYW5kZWQiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgPGg0IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj5Dcml0aWNhbCBJc3N1ZXM8L2g0PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tc3VidGl0bGUiPklzc3VlcyByZXF1aXJpbmcgaW1tZWRpYXRlIGF0dGVudGlvbjwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1jb250cm9scyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdHVzLWluZGljYXRvciIgOmNsYXNzPSJ7ICdmbGFzaGluZyc6ICFpc0NyaXRpY2FsSXNzdWVzRXhwYW5kZWQgJiYgdW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLmxlbmd0aCA+IDAgfSI+CiAgICAgICAgICAgICAgICB7eyB1bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMubGVuZ3RoIH19CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZXhwYW5kLWluZGljYXRvciIgOmNsYXNzPSJ7ICdleHBhbmRlZCc6IGlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZCB9Ij4KICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgMzIgMzIiIGZpbGw9ImN1cnJlbnRDb2xvciI+CiAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xNiAyMkw2IDEyIDcuNCAxMC42IDE2IDE5LjIgMjQuNiAxMC42IDI2IDEyeiI+PC9wYXRoPgogICAgICAgICAgICAgICAgPC9zdmc+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CgogICAgICAgICAgPGRpdiB2LWlmPSJpc0NyaXRpY2FsSXNzdWVzRXhwYW5kZWQiIGNsYXNzPSJzZWN0aW9uLWNvbnRlbnQiPgogICAgICAgICAgICA8IS0tIEZpbHRlciBDb250cm9scyBpbiBhIFNpbmdsZSBSb3cgLS0+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1jb250YWluZXIiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1oZWFkZXIiPgogICAgICAgICAgICAgICAgPGg1IGNsYXNzPSJmaWx0ZXItdGl0bGUiPkZpbHRlciBJc3N1ZXM8L2g1PgogICAgICAgICAgICAgICAgPGN2LWJ1dHRvbgogICAgICAgICAgICAgICAgICBraW5kPSJnaG9zdCIKICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJjbGVhci1maWx0ZXJzLWJ1dHRvbiIKICAgICAgICAgICAgICAgICAgQGNsaWNrPSJjbGVhckZpbHRlcnMiCiAgICAgICAgICAgICAgICAgIHYtaWY9ImlzRmlsdGVyc0FjdGl2ZSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgQ2xlYXIgRmlsdGVycwogICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1jb250cm9scyI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItZ3JvdXAiPgogICAgICAgICAgICAgICAgICA8bGFiZWwgZm9yPSJzZXZlcml0eS1kcm9wZG93biIgY2xhc3M9ImZpbHRlci1sYWJlbCI+U2V2ZXJpdHk6PC9sYWJlbD4KICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duCiAgICAgICAgICAgICAgICAgICAgaWQ9InNldmVyaXR5LWRyb3Bkb3duIgogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNldmVyaXR5RmlsdGVyIgogICAgICAgICAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZVNldmVyaXR5RmlsdGVyQ2hhbmdlIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJmaWx0ZXItZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iYWxsIj5BbGwgU2V2ZXJpdGllczwvY3YtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iaGlnaCI+SGlnaDwvY3YtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0ibWVkaXVtIj5NZWRpdW08L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDwvY3YtZHJvcGRvd24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItZ3JvdXAiPgogICAgICAgICAgICAgICAgICA8bGFiZWwgZm9yPSJhbmFseXNpcy1kcm9wZG93biIgY2xhc3M9ImZpbHRlci1sYWJlbCI+QW5hbHlzaXMgVHlwZTo8L2xhYmVsPgogICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24KICAgICAgICAgICAgICAgICAgICBpZD0iYW5hbHlzaXMtZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iYW5hbHlzaXNUeXBlRmlsdGVyIgogICAgICAgICAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZUFuYWx5c2lzRmlsdGVyQ2hhbmdlIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJmaWx0ZXItZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iYWxsIj5BbGwgQW5hbHlzaXMgVHlwZXM8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duLWl0ZW0gdmFsdWU9IlJvb3QgQ2F1c2UiPlJvb3QgQ2F1c2U8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDwvY3YtZHJvcGRvd24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICA8IS0tIENyaXRpY2FsIElzc3VlcyBMaXN0IC0tPgogICAgICAgICAgICA8ZGl2IHYtaWY9ImZpbHRlcmVkQ3JpdGljYWxJc3N1ZXMubGVuZ3RoID4gMCIgY2xhc3M9Imlzc3Vlcy1saXN0Ij4KICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICB2LWZvcj0iaXNzdWUgaW4gZmlsdGVyZWRDcml0aWNhbElzc3VlcyIKICAgICAgICAgICAgICAgIDprZXk9Imlzc3VlLmlkIgogICAgICAgICAgICAgICAgY2xhc3M9Imlzc3VlLWNhcmQiCiAgICAgICAgICAgICAgICBAY2xpY2s9InRvZ2dsZUlzc3VlRXhwYW5kZWQoaXNzdWUpIgogICAgICAgICAgICAgICAgOmNsYXNzPSJ7ICdleHBhbmRlZCc6IGlzSXNzdWVFeHBhbmRlZChpc3N1ZS5pZCkgfSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS1oZWFkZXIiPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS10YWdzIj4KICAgICAgICAgICAgICAgICAgICA8Y3YtdGFnCiAgICAgICAgICAgICAgICAgICAgICA6a2luZD0iaXNzdWUuc2V2ZXJpdHkgPT09ICdoaWdoJyA/ICdyZWQnIDogJ21hZ2VudGEnIgogICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpc3N1ZS5zZXZlcml0eSA9PT0gJ2hpZ2gnID8gJ0hpZ2gnIDogJ01lZGl1bSciCiAgICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgICAgICA8Y3YtdGFnCiAgICAgICAgICAgICAgICAgICAgICBraW5kPSJwdXJwbGUiCiAgICAgICAgICAgICAgICAgICAgICBjbGFzcz0iYW5hbHlzaXMtdGFnIgogICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpc3N1ZS5hbmFseXNpc1R5cGUiCiAgICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpc3N1ZS10aXRsZSI+e3sgaXNzdWUuY2F0ZWdvcnkgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imlzc3VlLW1ldGFkYXRhIj4KICAgICAgICAgICAgICAgICAgICA8Y3YtdGFnCiAgICAgICAgICAgICAgICAgICAgICBraW5kPSJjb29sLWdyYXkiCiAgICAgICAgICAgICAgICAgICAgICBjbGFzcz0ibW9udGgtdGFnIgogICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpc3N1ZS5tb250aCIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpc3N1ZS1tdWx0aXBsaWVyIiA6Y2xhc3M9Imlzc3VlLnNldmVyaXR5ID09PSAnaGlnaCcgPyAnaGlnaC1zZXZlcml0eScgOiAnbWVkaXVtLXNldmVyaXR5JyI+CiAgICAgICAgICAgICAgICAgICAgICB7eyBpc3N1ZS5pbmNyZWFzZU11bHRpcGxpZXIgPT09ICcobmV3KScgPyAnKG5ldyknIDogYCR7aXNzdWUuaW5jcmVhc2VNdWx0aXBsaWVyfXhgIH19CiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZXhwYW5kLWluZGljYXRvciI+CiAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0iY3VycmVudENvbG9yIj4KICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xNiAyMkw2IDEyIDcuNCAxMC42IDE2IDE5LjIgMjQuNiAxMC42IDI2IDEyeiI+PC9wYXRoPgogICAgICAgICAgICAgICAgICAgIDwvc3ZnPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imlzc3VlLWNvbnRlbnQiIHYtaWY9ImlzSXNzdWVFeHBhbmRlZChpc3N1ZS5pZCkiPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhaS1kZXNjcmlwdGlvbiI+CiAgICAgICAgICAgICAgICAgICAgPHA+e3sgaXNzdWUuYWlEZXNjcmlwdGlvbiB8fCAnTG9hZGluZyBBSSBkZXNjcmlwdGlvbi4uLicgfX08L3A+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgPCEtLSBBY3Rpb24gQ29tbWVudCBUZXh0IEJveCAtLT4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWN0aW9uLWNvbW1lbnQiPgogICAgICAgICAgICAgICAgICAgIDxjdi10ZXh0LWFyZWEKICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9Imlzc3VlLmNvbW1lbnQiCiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD0iQWN0aW9uIENvbW1lbnRzIgogICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IkFkZCB5b3VyIGNvbW1lbnRzIG9yIGFjdGlvbiBwbGFuIGhlcmUuLi4iCiAgICAgICAgICAgICAgICAgICAgICA6aGVscGVyLXRleHQ9Imlzc3VlLmNvbW1lbnQgPyBgJHtpc3N1ZS5jb21tZW50Lmxlbmd0aH0gY2hhcmFjdGVyc2AgOiAnVGhpcyB3aWxsIGJlIGFkZGVkIHRvIHRoZSBBY3Rpb24gVHJhY2tlciciCiAgICAgICAgICAgICAgICAgICAgPjwvY3YtdGV4dC1hcmVhPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imlzc3VlLWFjdGlvbnMiPgogICAgICAgICAgICAgICAgICAgIDxjdi1idXR0b24KICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9InRlcnRpYXJ5IgogICAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcD0idmlld0lzc3VlRGV0YWlscyhpc3N1ZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgVmlldyBEYXRhCiAgICAgICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgPGN2LWJ1dHRvbgogICAgICAgICAgICAgICAgICAgICAga2luZD0idGVydGlhcnkiCiAgICAgICAgICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICAgICAgICAgIEBjbGljay5zdG9wPSJ1cGRhdGVJc3N1ZShpc3N1ZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgVXBkYXRlCiAgICAgICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgPGN2LWJ1dHRvbgogICAgICAgICAgICAgICAgICAgICAga2luZD0ic2Vjb25kYXJ5IgogICAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcD0idXBkYXRlSXNzdWUoaXNzdWUsIGZhbHNlLCB0cnVlKSIKICAgICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgICBNYXJrIE91dHN0YW5kaW5nCiAgICAgICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgPGN2LWJ1dHRvbgogICAgICAgICAgICAgICAgICAgICAga2luZD0icHJpbWFyeSIKICAgICAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgICAgICAgQGNsaWNrLnN0b3A9InVwZGF0ZUlzc3VlKGlzc3VlLCB0cnVlLCBmYWxzZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgTWFyayBSZXNvbHZlZAogICAgICAgICAgICAgICAgICAgIDwvY3YtYnV0dG9uPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJuby1kYXRhLW1lc3NhZ2UiPgogICAgICAgICAgICAgIE5vIGNyaXRpY2FsIGlzc3VlcyBmb3VuZCBtYXRjaGluZyB0aGUgc2VsZWN0ZWQgZmlsdGVycy4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPCEtLSBPdXRzdGFuZGluZyBJc3N1ZXMgU2VjdGlvbiAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWNhcmQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1oZWFkZXIgb3V0c3RhbmRpbmctaXNzdWVzLWhlYWRlciIgQGNsaWNrPSJ0b2dnbGVPdXRzdGFuZGluZ0lzc3Vlc0V4cGFuZGVkIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZS1jb250YWluZXIiPgogICAgICAgICAgICAgIDxoNCBjbGFzcz0ic2VjdGlvbi10aXRsZSI+T3V0c3RhbmRpbmcgSXNzdWVzPC9oND4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXN1YnRpdGxlIj5BY2NlcHRlZCBpc3N1ZXMgdGhhdCBhcmUgYmVpbmcgbW9uaXRvcmVkPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWNvbnRyb2xzIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0dXMtaW5kaWNhdG9yIG91dHN0YW5kaW5nLWluZGljYXRvciI+CiAgICAgICAgICAgICAgICB7eyBvdXRzdGFuZGluZ0lzc3Vlcy5sZW5ndGggfX0KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJleHBhbmQtaW5kaWNhdG9yIiA6Y2xhc3M9InsgJ2V4cGFuZGVkJzogaXNPdXRzdGFuZGluZ0lzc3Vlc0V4cGFuZGVkIH0iPgogICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0iY3VycmVudENvbG9yIj4KICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTE2IDIyTDYgMTIgNy40IDEwLjYgMTYgMTkuMiAyNC42IDEwLjYgMjYgMTJ6Ij48L3BhdGg+CiAgICAgICAgICAgICAgICA8L3N2Zz4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8ZGl2IHYtaWY9ImlzT3V0c3RhbmRpbmdJc3N1ZXNFeHBhbmRlZCIgY2xhc3M9InNlY3Rpb24tY29udGVudCI+CiAgICAgICAgICAgIDwhLS0gRmlsdGVyIENvbnRyb2xzIGluIGEgU2luZ2xlIFJvdyAtLT4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iZmlsdGVyLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZmlsdGVyLWhlYWRlciI+CiAgICAgICAgICAgICAgICA8aDUgY2xhc3M9ImZpbHRlci10aXRsZSI+RmlsdGVyIE91dHN0YW5kaW5nIElzc3VlczwvaDU+CiAgICAgICAgICAgICAgICA8Y3YtYnV0dG9uCiAgICAgICAgICAgICAgICAgIGtpbmQ9Imdob3N0IgogICAgICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICAgICAgY2xhc3M9ImNsZWFyLWZpbHRlcnMtYnV0dG9uIgogICAgICAgICAgICAgICAgICBAY2xpY2s9ImNsZWFyT3V0c3RhbmRpbmdGaWx0ZXJzIgogICAgICAgICAgICAgICAgICB2LWlmPSJpc091dHN0YW5kaW5nRmlsdGVyc0FjdGl2ZSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgQ2xlYXIgRmlsdGVycwogICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1jb250cm9scyI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItZ3JvdXAiPgogICAgICAgICAgICAgICAgICA8bGFiZWwgZm9yPSJvdXRzdGFuZGluZy1jYXRlZ29yeS1kcm9wZG93biIgY2xhc3M9ImZpbHRlci1sYWJlbCI+Q2F0ZWdvcnk6PC9sYWJlbD4KICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duCiAgICAgICAgICAgICAgICAgICAgaWQ9Im91dHN0YW5kaW5nLWNhdGVnb3J5LWRyb3Bkb3duIgogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9Im91dHN0YW5kaW5nQ2F0ZWdvcnlGaWx0ZXIiCiAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlT3V0c3RhbmRpbmdDYXRlZ29yeUZpbHRlckNoYW5nZSIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0iZmlsdGVyLWRyb3Bkb3duIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duLWl0ZW0gdmFsdWU9ImFsbCI+QWxsIENhdGVnb3JpZXM8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duLWl0ZW0KICAgICAgICAgICAgICAgICAgICAgIHYtZm9yPSJjYXRlZ29yeSBpbiBvdXRzdGFuZGluZ0NhdGVnb3JpZXMiCiAgICAgICAgICAgICAgICAgICAgICA6a2V5PSJjYXRlZ29yeSIKICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iY2F0ZWdvcnkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAge3sgY2F0ZWdvcnkgfX0KICAgICAgICAgICAgICAgICAgICA8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDwvY3YtZHJvcGRvd24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItZ3JvdXAiPgogICAgICAgICAgICAgICAgICA8bGFiZWwgZm9yPSJvdXRzdGFuZGluZy1hbmFseXNpcy1kcm9wZG93biIgY2xhc3M9ImZpbHRlci1sYWJlbCI+QW5hbHlzaXMgVHlwZTo8L2xhYmVsPgogICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24KICAgICAgICAgICAgICAgICAgICBpZD0ib3V0c3RhbmRpbmctYW5hbHlzaXMtZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ib3V0c3RhbmRpbmdBbmFseXNpc1R5cGVGaWx0ZXIiCiAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlT3V0c3RhbmRpbmdBbmFseXNpc0ZpbHRlckNoYW5nZSIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0iZmlsdGVyLWRyb3Bkb3duIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duLWl0ZW0gdmFsdWU9ImFsbCI+QWxsIEFuYWx5c2lzIFR5cGVzPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgICAgICAgICAgIDxjdi1kcm9wZG93bi1pdGVtIHZhbHVlPSJSb290IENhdXNlIj5Sb290IENhdXNlPC9jdi1kcm9wZG93bi1pdGVtPgogICAgICAgICAgICAgICAgICA8L2N2LWRyb3Bkb3duPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgPCEtLSBPdXRzdGFuZGluZyBJc3N1ZXMgTGlzdCAtLT4KICAgICAgICAgICAgPGRpdiB2LWlmPSJmaWx0ZXJlZE91dHN0YW5kaW5nSXNzdWVzLmxlbmd0aCA+IDAiIGNsYXNzPSJpc3N1ZXMtbGlzdCI+CiAgICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgICAgdi1mb3I9Imlzc3VlIGluIGZpbHRlcmVkT3V0c3RhbmRpbmdJc3N1ZXMiCiAgICAgICAgICAgICAgICA6a2V5PSJpc3N1ZS5pZCIKICAgICAgICAgICAgICAgIGNsYXNzPSJpc3N1ZS1jYXJkIG91dHN0YW5kaW5nLWlzc3VlLWNhcmQiCiAgICAgICAgICAgICAgICBAY2xpY2s9InRvZ2dsZU91dHN0YW5kaW5nSXNzdWVFeHBhbmRlZChpc3N1ZSkiCiAgICAgICAgICAgICAgICA6Y2xhc3M9InsgJ2V4cGFuZGVkJzogaXNPdXRzdGFuZGluZ0lzc3VlRXhwYW5kZWQoaXNzdWUuaWQpIH0iCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXNzdWUtaGVhZGVyIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXNzdWUtdGFncyI+CiAgICAgICAgICAgICAgICAgICAgPGN2LXRhZwogICAgICAgICAgICAgICAgICAgICAga2luZD0iYmx1ZSIKICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPSJPdXRzdGFuZGluZyIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgIDxjdi10YWcKICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9InB1cnBsZSIKICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJhbmFseXNpcy10YWciCiAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9Imlzc3VlLmFuYWx5c2lzVHlwZSIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9Imlzc3VlLXRpdGxlIj57eyBpc3N1ZS5jYXRlZ29yeSB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXNzdWUtbWV0YWRhdGEiPgogICAgICAgICAgICAgICAgICAgIDxjdi10YWcKICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9ImNvb2wtZ3JheSIKICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJtb250aC10YWciCiAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9Imlzc3VlLm1vbnRoIgogICAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9Imlzc3VlLW11bHRpcGxpZXIiIDpjbGFzcz0icGFyc2VGbG9hdChpc3N1ZS5jdXJyZW50UGVyZm9ybWFuY2UpID4gMS4zID8gJ21lZGl1bS1wZXJmb3JtYW5jZScgOiAnbG93LXBlcmZvcm1hbmNlJyI+CiAgICAgICAgICAgICAgICAgICAgICBDdXJyZW50OiB7eyBpc3N1ZS5jdXJyZW50UGVyZm9ybWFuY2UgfX0KICAgICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJleHBhbmQtaW5kaWNhdG9yIj4KICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJjdXJyZW50Q29sb3IiPgogICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTE2IDIyTDYgMTIgNy40IDEwLjYgMTYgMTkuMiAyNC42IDEwLjYgMjYgMTJ6Ij48L3BhdGg+CiAgICAgICAgICAgICAgICAgICAgPC9zdmc+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXNzdWUtY29udGVudCIgdi1pZj0iaXNPdXRzdGFuZGluZ0lzc3VlRXhwYW5kZWQoaXNzdWUuaWQpIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWNjZXB0YW5jZS1kZXRhaWxzIj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhY2NlcHRhbmNlLWRhdGUiPgogICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz5BY2NlcHRlZCBvbjo8L3N0cm9uZz4ge3sgaXNzdWUuYWNjZXB0YW5jZURhdGUgfX0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhY2NlcHRlZC1ieSI+CiAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkFjY2VwdGVkIGJ5Ojwvc3Ryb25nPiB7eyBpc3N1ZS5hY2NlcHRlZEJ5IH19CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWktZGVzY3JpcHRpb24iPgogICAgICAgICAgICAgICAgICAgIDxwPnt7IGlzc3VlLmFpRGVzY3JpcHRpb24gfHwgJ0xvYWRpbmcgQUkgZGVzY3JpcHRpb24uLi4nIH19PC9wPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDwhLS0gQWNjZXB0YW5jZSBDb21tZW50IC0tPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhY2NlcHRhbmNlLWNvbW1lbnQiPgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbW1lbnQtbGFiZWwiPkFjY2VwdGFuY2UgUmVhc29uOjwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbW1lbnQtdGV4dCI+e3sgaXNzdWUuY29tbWVudCB9fTwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imlzc3VlLWFjdGlvbnMiPgogICAgICAgICAgICAgICAgICAgIDxjdi1idXR0b24KICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9InRlcnRpYXJ5IgogICAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcD0idmlld1BlcmZvcm1hbmNlRGF0YShpc3N1ZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgVmlldyBQZXJmb3JtYW5jZQogICAgICAgICAgICAgICAgICAgIDwvY3YtYnV0dG9uPgogICAgICAgICAgICAgICAgICAgIDxjdi1idXR0b24KICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9InRlcnRpYXJ5IgogICAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcD0idmlld0lzc3VlRGV0YWlscyhpc3N1ZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgVmlldyBEYXRhCiAgICAgICAgICAgICAgICAgICAgPC9jdi1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgPGN2LWJ1dHRvbgogICAgICAgICAgICAgICAgICAgICAga2luZD0icHJpbWFyeSIKICAgICAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgICAgICAgQGNsaWNrLnN0b3A9InVwZGF0ZUlzc3VlKGlzc3VlLCB0cnVlLCBmYWxzZSkiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgTWFyayBSZXNvbHZlZAogICAgICAgICAgICAgICAgICAgIDwvY3YtYnV0dG9uPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJuby1kYXRhLW1lc3NhZ2UiPgogICAgICAgICAgICAgIE5vIG91dHN0YW5kaW5nIGlzc3VlcyBmb3VuZCBtYXRjaGluZyB0aGUgc2VsZWN0ZWQgZmlsdGVycy4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPCEtLSBSZXNvbHZlZCBJc3N1ZXMgU2VjdGlvbiAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWNhcmQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1oZWFkZXIgcmVzb2x2ZWQtaXNzdWVzLWhlYWRlciIgQGNsaWNrPSJ0b2dnbGVSZXNvbHZlZElzc3Vlc0V4cGFuZGVkIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZS1jb250YWluZXIiPgogICAgICAgICAgICAgIDxoNCBjbGFzcz0ic2VjdGlvbi10aXRsZSI+UmVzb2x2ZWQgSXNzdWVzPC9oND4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXN1YnRpdGxlIj5UcmFjayBwZXJmb3JtYW5jZSBvZiByZXNvbHZlZCBpc3N1ZXM8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tY29udHJvbHMiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXR1cy1pbmRpY2F0b3IgcmVzb2x2ZWQtaW5kaWNhdG9yIj4KICAgICAgICAgICAgICAgIHt7IHJlc29sdmVkSXNzdWVzLmxlbmd0aCB9fQogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImV4cGFuZC1pbmRpY2F0b3IiIDpjbGFzcz0ieyAnZXhwYW5kZWQnOiBpc1Jlc29sdmVkSXNzdWVzRXhwYW5kZWQgfSI+CiAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJjdXJyZW50Q29sb3IiPgogICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMTYgMjJMNiAxMiA3LjQgMTAuNiAxNiAxOS4yIDI0LjYgMTAuNiAyNiAxMnoiPjwvcGF0aD4KICAgICAgICAgICAgICAgIDwvc3ZnPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDxkaXYgdi1pZj0iaXNSZXNvbHZlZElzc3Vlc0V4cGFuZGVkIiBjbGFzcz0ic2VjdGlvbi1jb250ZW50Ij4KICAgICAgICAgICAgPCEtLSBGaWx0ZXIgQ29udHJvbHMgaW4gYSBTaW5nbGUgUm93IC0tPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItY29udGFpbmVyIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItaGVhZGVyIj4KICAgICAgICAgICAgICAgIDxoNSBjbGFzcz0iZmlsdGVyLXRpdGxlIj5GaWx0ZXIgUmVzb2x2ZWQgSXNzdWVzPC9oNT4KICAgICAgICAgICAgICAgIDxjdi1idXR0b24KICAgICAgICAgICAgICAgICAga2luZD0iZ2hvc3QiCiAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgICBjbGFzcz0iY2xlYXItZmlsdGVycy1idXR0b24iCiAgICAgICAgICAgICAgICAgIEBjbGljaz0iY2xlYXJSZXNvbHZlZEZpbHRlcnMiCiAgICAgICAgICAgICAgICAgIHYtaWY9ImlzUmVzb2x2ZWRGaWx0ZXJzQWN0aXZlIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICBDbGVhciBGaWx0ZXJzCiAgICAgICAgICAgICAgICA8L2N2LWJ1dHRvbj4KICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZmlsdGVyLWNvbnRyb2xzIj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1ncm91cCI+CiAgICAgICAgICAgICAgICAgIDxsYWJlbCBmb3I9ImNhdGVnb3J5LWRyb3Bkb3duIiBjbGFzcz0iZmlsdGVyLWxhYmVsIj5DYXRlZ29yeTo8L2xhYmVsPgogICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24KICAgICAgICAgICAgICAgICAgICBpZD0iY2F0ZWdvcnktZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0icmVzb2x2ZWRDYXRlZ29yeUZpbHRlciIKICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVSZXNvbHZlZENhdGVnb3J5RmlsdGVyQ2hhbmdlIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJmaWx0ZXItZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iYWxsIj5BbGwgQ2F0ZWdvcmllczwvY3YtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbQogICAgICAgICAgICAgICAgICAgICAgdi1mb3I9ImNhdGVnb3J5IGluIHJlc29sdmVkQ2F0ZWdvcmllcyIKICAgICAgICAgICAgICAgICAgICAgIDprZXk9ImNhdGVnb3J5IgogICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJjYXRlZ29yeSIKICAgICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgICB7eyBjYXRlZ29yeSB9fQogICAgICAgICAgICAgICAgICAgIDwvY3YtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICAgICAgPC9jdi1kcm9wZG93bj4KICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1ncm91cCI+CiAgICAgICAgICAgICAgICAgIDxsYWJlbCBmb3I9InJlc29sdmVkLWFuYWx5c2lzLWRyb3Bkb3duIiBjbGFzcz0iZmlsdGVyLWxhYmVsIj5BbmFseXNpcyBUeXBlOjwvbGFiZWw+CiAgICAgICAgICAgICAgICAgIDxjdi1kcm9wZG93bgogICAgICAgICAgICAgICAgICAgIGlkPSJyZXNvbHZlZC1hbmFseXNpcy1kcm9wZG93biIKICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJyZXNvbHZlZEFuYWx5c2lzVHlwZUZpbHRlciIKICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVSZXNvbHZlZEFuYWx5c2lzRmlsdGVyQ2hhbmdlIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJmaWx0ZXItZHJvcGRvd24iCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8Y3YtZHJvcGRvd24taXRlbSB2YWx1ZT0iYWxsIj5BbGwgQW5hbHlzaXMgVHlwZXM8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgPGN2LWRyb3Bkb3duLWl0ZW0gdmFsdWU9IlJvb3QgQ2F1c2UiPlJvb3QgQ2F1c2U8L2N2LWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDwvY3YtZHJvcGRvd24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICA8IS0tIFJlc29sdmVkIElzc3VlcyBMaXN0IC0tPgogICAgICAgICAgICA8ZGl2IHYtaWY9ImZpbHRlcmVkUmVzb2x2ZWRJc3N1ZXMubGVuZ3RoID4gMCIgY2xhc3M9Imlzc3Vlcy1saXN0Ij4KICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICB2LWZvcj0iaXNzdWUgaW4gZmlsdGVyZWRSZXNvbHZlZElzc3VlcyIKICAgICAgICAgICAgICAgIDprZXk9Imlzc3VlLmlkIgogICAgICAgICAgICAgICAgY2xhc3M9Imlzc3VlLWNhcmQgcmVzb2x2ZWQtaXNzdWUtY2FyZCIKICAgICAgICAgICAgICAgIEBjbGljaz0idG9nZ2xlUmVzb2x2ZWRJc3N1ZUV4cGFuZGVkKGlzc3VlKSIKICAgICAgICAgICAgICAgIDpjbGFzcz0ieyAnZXhwYW5kZWQnOiBpc1Jlc29sdmVkSXNzdWVFeHBhbmRlZChpc3N1ZS5pZCkgfSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS1oZWFkZXIiPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS10YWdzIj4KICAgICAgICAgICAgICAgICAgICA8Y3YtdGFnCiAgICAgICAgICAgICAgICAgICAgICBraW5kPSJncmVlbiIKICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPSJSZXNvbHZlZCIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgIDxjdi10YWcKICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9InB1cnBsZSIKICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJhbmFseXNpcy10YWciCiAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9Imlzc3VlLmFuYWx5c2lzVHlwZSIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9Imlzc3VlLXRpdGxlIj57eyBpc3N1ZS5jYXRlZ29yeSB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXNzdWUtbWV0YWRhdGEiPgogICAgICAgICAgICAgICAgICAgIDxjdi10YWcKICAgICAgICAgICAgICAgICAgICAgIGtpbmQ9ImNvb2wtZ3JheSIKICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJtb250aC10YWciCiAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9Imlzc3VlLm1vbnRoIgogICAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9Imlzc3VlLW11bHRpcGxpZXIiIDpjbGFzcz0iaXNzdWUuY3VycmVudFBlcmZvcm1hbmNlLnN0YXJ0c1dpdGgoJzAnKSA/ICdnb29kLXBlcmZvcm1hbmNlJyA6ICdtZWRpdW0tcGVyZm9ybWFuY2UnIj4KICAgICAgICAgICAgICAgICAgICAgIEN1cnJlbnQ6IHt7IGlzc3VlLmN1cnJlbnRQZXJmb3JtYW5jZSB9fQogICAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImV4cGFuZC1pbmRpY2F0b3IiPgogICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgMzIgMzIiIGZpbGw9ImN1cnJlbnRDb2xvciI+CiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMTYgMjJMNiAxMiA3LjQgMTAuNiAxNiAxOS4yIDI0LjYgMTAuNiAyNiAxMnoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICA8L3N2Zz4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS1jb250ZW50IiB2LWlmPSJpc1Jlc29sdmVkSXNzdWVFeHBhbmRlZChpc3N1ZS5pZCkiPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXNvbHV0aW9uLWRldGFpbHMiPgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InJlc29sdXRpb24tZGF0ZSI+CiAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPlJlc29sdmVkIG9uOjwvc3Ryb25nPiB7eyBpc3N1ZS5yZXNvbHV0aW9uRGF0ZSB9fQogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im9yaWdpbmFsLXNldmVyaXR5Ij4KICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+T3JpZ2luYWwgU2V2ZXJpdHk6PC9zdHJvbmc+IHt7IGlzc3VlLnNldmVyaXR5ID09PSAnaGlnaCcgPyAnSGlnaCcgOiAnTWVkaXVtJyB9fQogICAgICAgICAgICAgICAgICAgICAgKHt7IGlzc3VlLmluY3JlYXNlTXVsdGlwbGllciB9fXgpCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWktZGVzY3JpcHRpb24iPgogICAgICAgICAgICAgICAgICAgIDxwPnt7IGlzc3VlLmFpRGVzY3JpcHRpb24gfHwgJ0xvYWRpbmcgQUkgZGVzY3JpcHRpb24uLi4nIH19PC9wPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDwhLS0gUmVzb2x1dGlvbiBDb21tZW50IC0tPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXNvbHV0aW9uLWNvbW1lbnQiPgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbW1lbnQtbGFiZWwiPlJlc29sdXRpb24gQWN0aW9uczo8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb21tZW50LXRleHQiPnt7IGlzc3VlLmNvbW1lbnQgfX08L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpc3N1ZS1hY3Rpb25zIj4KICAgICAgICAgICAgICAgICAgICA8Y3YtYnV0dG9uCiAgICAgICAgICAgICAgICAgICAgICBraW5kPSJ0ZXJ0aWFyeSIKICAgICAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgICAgICAgQGNsaWNrLnN0b3A9InZpZXdQZXJmb3JtYW5jZURhdGEoaXNzdWUpIgogICAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICAgIFZpZXcgUGVyZm9ybWFuY2UKICAgICAgICAgICAgICAgICAgICA8L2N2LWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgICA8Y3YtYnV0dG9uCiAgICAgICAgICAgICAgICAgICAgICBraW5kPSJ0ZXJ0aWFyeSIKICAgICAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgICAgICAgQGNsaWNrLnN0b3A9InZpZXdJc3N1ZURldGFpbHMoaXNzdWUpIgogICAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICAgIFZpZXcgRGF0YQogICAgICAgICAgICAgICAgICAgIDwvY3YtYnV0dG9uPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJuby1kYXRhLW1lc3NhZ2UiPgogICAgICAgICAgICAgIE5vIHJlc29sdmVkIGlzc3VlcyBmb3VuZCBtYXRjaGluZyB0aGUgc2VsZWN0ZWQgZmlsdGVycy4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4KPC9kaXY+Cg=="}, null]}