{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=template&id=25dba680&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748534636733}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}