<template>
  <div ref='chartHolder'>
    <!-- <ccv-simple-bar-chart ref= "barChart" :data="data" :options="options"></ccv-simple-bar-chart>  -->
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { GaugeChart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'Gauge<PERSON><PERSON>',
  props: {
    data: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      options: {
        title: 'Percentage of Fails Validated',
        resizable: true,
        height: '250px',
        gauge: {
          type: 'semi',
          status: 'danger'
  }
      },
    };
  },
  mounted() {
    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    this.chart = new GaugeChart(chartHolder, {
      data: this.data,
      options: this.options,
    });

    // this.chart.services.events.addEventListener('bar-click', (e) => {
    //   this.eventType(e);
    // });
  },
};
</script>

<style>
</style>
