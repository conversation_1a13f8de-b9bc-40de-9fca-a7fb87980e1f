
// MongoDB removed
// const schedule = require('node-schedule');
// const runSchedule = true;
const logger = require('../utils/logger.js');

class InitManager {
    static async initCore() {
        // MongoDB removed - initialization disabled
        console.log("MongoDB removed - initialization disabled");
        await logger.logInfo("MongoDB removed - initialization disabled");
        // if (runSchedule) {
        //     //schedule.scheduleJob('*/10 * * * *', function () { //run every 10 minutes
        //     //schedule.scheduleJob('*/12 * * * * *', function () {
        //     schedule.scheduleJob('0 52 20 * * *', function () {//run at hh:mm daily
        //         console.log('Mail_Scheduled Job run at ' + new Date());

        //         var spawn = require('child_process').spawn;

        //         var ls = spawn('node', ['./app/scheduledJob/Mail_Scheduled.js']);

        //         ls.stdout.on('data', function (data) {
        //             console.log('data from child: ' + data);
        //         });

        //         ls.stderr.on('data', function (data) {
        //             console.log('error from child: ' + data);
        //         });

        //         ls.on('close', function (code) {
        //             console.log('child exists with code: ' + code);
        //         });
        //     });

        //     schedule.scheduleJob('*/60 * * * *', function () {//run every 60 minutes
        //         console.log('Clear lock of user Job run at ' + new Date());

        //         var spawn = require('child_process').spawn;

        //         var ls = spawn('node', ['./app/scheduledJob/unlock_users.js']);

        //         ls.stdout.on('data', function (data) {
        //             console.log('data from child: ' + data);
        //         });

        //         ls.stderr.on('data', function (data) {
        //             console.log('error from child: ' + data);
        //         });

        //         ls.on('close', function (code) {
        //             console.log('child exists with code: ' + code);
        //         });
        //     });

        //     let cbn_time = '0 9 15 2 *' //run 9 am on Feb 15th
        //     const cbn_result = await sysData.findOne({ description: "CBN" })
        //     if (cbn_result && cbn_result.next_run) {
        //         cbn_time = cbn_result.next_run
        //     }
        //     console.log('cbn_time: ', cbn_time)
        //     schedule.scheduleJob(cbn_time, function () {
        //         console.log('CBN Request Job run at ' + new Date());

        //         var spawn = require('child_process').spawn;

        //         var ls = spawn('node', ['./app/scheduledJob/CBN_Request.js']);

        //         ls.stdout.on('data', function (data) {
        //             console.log('data from child: ' + data);
        //         });

        //         ls.stderr.on('data', function (data) {
        //             console.log('error from child: ' + data);
        //         });

        //         ls.on('close', function (code) {
        //             console.log('child exists with code: ' + code);
        //         });
        //     });

        //     let user_refresh_time = '0 5 * * 6' //run at 5:00am Saturday 0 5 * * 6
        //     const user_refresh_result = await sysData.findOne({ description: "user_refresh" })
        //     if (user_refresh_result && user_refresh_result.next_run) {
        //         user_refresh_time = user_refresh_result.next_run
        //     }
        //     console.log('user_refresh_time: ', user_refresh_time)
        //     schedule.scheduleJob(user_refresh_time, function () {
        //         console.log('user_refresh Job run at ' + new Date());

        //         var spawn = require('child_process').spawn;

        //         var ls = spawn('node', ['./app/scheduledJob/user_refresh.js']);

        //         ls.stdout.on('data', function (data) {
        //             console.log('data from child: ' + data);
        //         });

        //         ls.stderr.on('data', function (data) {
        //             console.log('error from child: ' + data);
        //         });

        //         ls.on('close', function (code) {
        //             console.log('child exists with code: ' + code);
        //         });
        //     });

        //     schedule.scheduleJob('0 5 * * *', function () {//run at 5:00am every day 0 5 * * *
        //         console.log('check expiration Job run at ' + new Date());

        //         var spawn = require('child_process').spawn;

        //         var ls = spawn('node', ['./app/scheduledJob/check_exp.js']);

        //         ls.stdout.on('data', function (data) {
        //             console.log('data from child: ' + data);
        //         });

        //         ls.stderr.on('data', function (data) {
        //             console.log('error from child: ' + data);
        //         });

        //         ls.on('close', function (code) {
        //             console.log('child exists with code: ' + code);
        //         });
        //     });
        // }
    }
}

module.exports = InitManager;