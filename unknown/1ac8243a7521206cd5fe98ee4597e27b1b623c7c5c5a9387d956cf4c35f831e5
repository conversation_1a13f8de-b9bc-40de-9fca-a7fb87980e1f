<template>
  <div class="validation2-container">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <!-- Classification Modal -->
    <div v-if="showClassificationModal" class="modal-overlay">
      <div class="modal-content">
        <h3>Edit Classification</h3>
        <p>Defect ID: {{ selectedDefect ? selectedDefect.DEFECT_ID : '' }}</p>
        <p class="description-text">{{ selectedDefect ? selectedDefect.DESCRIPTION : '' }}</p>

        <div class="modal-form">
          <div class="form-group">
            <label>Primary Classification</label>
            <select v-model="editClassification.primary" class="modal-select">
              <option value="Mechanical">Mechanical</option>
              <option value="Functional">Functional</option>
              <option value="Need More Info">Need More Info</option>
            </select>
          </div>

          <div class="form-group">
            <label>Subcategory</label>
            <select v-model="editClassification.subcategory" class="modal-select">
              <option value="Scratches">Scratches</option>
              <option value="Bent">Bent</option>
              <option value="Plugging">Plugging</option>
              <option value="Discolor">Discolor</option>
              <option value="Misalignment">Misalignment</option>
              <option value="Need More Info">Need More Info</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <div class="modal-actions">
          <button class="modal-button cancel" @click="closeClassificationModal">Cancel</button>
          <button class="modal-button save" @click="saveClassification">Save</button>
        </div>
      </div>
    </div>

    <!-- Batch Results Modal -->
    <div v-if="showBatchResultsModal" class="modal-overlay">
      <div class="modal-content batch-results-modal">
        <h3>Batch Processing Results</h3>
        <p>Successfully processed {{ batchResults.length }} defects</p>

        <div class="batch-results-container">
          <table class="batch-results-table">
            <thead>
              <tr>
                <th>Defect ID</th>
                <th>Description</th>
                <th>Primary</th>
                <th>Subcategory</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="result in batchResults" :key="result.defectId">
                <td>{{ result.defectId }}</td>
                <td class="description-cell">{{ result.description }}</td>
                <td>
                  <div class="classification-tag" :class="result.classification.primary.toLowerCase()">
                    {{ result.classification.primary }}
                  </div>
                </td>
                <td>
                  <div class="classification-tag subcategory" :class="getSubcategoryClassFromString(result.classification.subcategory)">
                    {{ result.classification.subcategory }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="modal-actions">
          <button class="modal-button save" @click="closeBatchResultsModal">Close</button>
        </div>
      </div>
    </div>

    <main id="main-content" class="main-content">
      <div class="validation2-header">
        <h1>Defect Validation 2.0</h1>
        <p>Analyze and validate defects with AI assistance</p>
      </div>

      <div class="controls-row">
        <div class="time-period-dropdown">
          <label for="time-period" class="dropdown-label">Time Period</label>
          <select
            id="time-period"
            v-model="selectedTimeFilter"
            class="cv-dropdown"
            @change="handleTimeFilterChange"
          >
            <option v-for="option in timeFilterOptions" :key="option.value" :value="option.value">
              {{ option.text }}
            </option>
          </select>
        </div>

        <!-- Primary and subcategory dropdowns removed as requested -->

        <div class="button-group">
          <cv-button
            kind="primary"
            @click="processNewDefects"
            :disabled="processingDefects"
          >
            {{ processingDefects ? 'Processing...' : 'Process All New Defects' }}
          </cv-button>

          <div class="batch-process-controls">
            <div class="batch-size-input">
              <label for="batch-size" class="dropdown-label">Batch Size</label>
              <input
                id="batch-size"
                type="number"
                v-model.number="batchSize"
                min="1"
                max="20"
                class="cv-input"
                :disabled="processingDefects"
              />
            </div>
            <cv-button
              kind="secondary"
              @click="processLimitedBatch"
              :disabled="processingDefects"
            >
              Process Batch & View Results
            </cv-button>
          </div>
        </div>

        <div v-if="processingMessage" class="processing-message">
          {{ processingMessage }}
        </div>
      </div>

      <div class="pareto-section">
        <h2>Defect Classification Breakdown</h2>
        <div v-if="loadingCounts" class="loading-indicator">
          <cv-loading />
        </div>
        <div v-else-if="classificationCounts" class="chart-container">
          <div class="pareto-chart" data-carbon-theme="g90">
            <HBarChart
              :data="chartData"
              :loading="loadingCounts"
              title="Defect Classification Breakdown"
              height="400px"
            />
          </div>
        </div>
      </div>

      <div class="defects-table-section">
        <h2>Defect Details</h2>
        <div v-if="loadingData" class="loading-indicator">
          <cv-loading />
        </div>
        <div v-else class="table-container">
          <cv-data-table
            :columns="tableColumns"
            :data="tableData"
            :pagination="{ pageSize: 10 }"
            :sortable="true"
          >
            <template slot="data">
              <cv-data-table-row
                v-for="(row, rowIndex) in tableData"
                :key="rowIndex"
                :value="`${row.DEFECT_ID}`"
              >
                <cv-data-table-cell>{{ row.DEFECT_ID }}</cv-data-table-cell>
                <cv-data-table-cell>{{ formatDate(row.MFSDATE) }}</cv-data-table-cell>
                <cv-data-table-cell>{{ row.STAGE }}</cv-data-table-cell>
                <cv-data-table-cell>{{ row.DESCRIPTION }}</cv-data-table-cell>
                <cv-data-table-cell>
                  <div class="classification-cell">
                    <div v-if="getPrimaryClassification(row)" class="classification-tag" :class="getPrimaryClassification(row).toLowerCase()">
                      {{ getPrimaryClassification(row) }}
                    </div>
                    <div v-else class="classification-tag unclassified">
                      Unclassified
                    </div>
                  </div>
                </cv-data-table-cell>
                <cv-data-table-cell>
                  <div class="classification-cell">
                    <div v-if="getSubcategoryClassification(row)" class="classification-tag subcategory" :class="getSubcategoryClass(row)">
                      {{ getSubcategoryClassification(row) }}
                    </div>
                    <div v-else class="classification-tag unclassified">
                      Unclassified
                    </div>
                    <button
                      v-if="row.DEFECT_ID"
                      class="edit-button"
                      @click="openClassificationModal(row)"
                    >
                      Edit
                    </button>
                  </div>
                </cv-data-table-cell>
              </cv-data-table-row>
            </template>
          </cv-data-table>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import MainHeader from "@/components/MainHeader";
import HBarChart from "@/components/HBarChart/HBarChart";

export default {
  name: "ValidationAiPage",
  components: {
    MainHeader,
    HBarChart
  },
  data() {
    return {
      expandedSideNav: true,
      useFixed: false,
      selectedTimeFilter: "month",
      timeFilterOptions: [
        { value: "month", text: "Past Month" },
        { value: "week", text: "Past Week" },
        { value: "day", text: "Past Day" }
      ],
      classificationOptions: [
        { value: "Mechanical", text: "Mechanical" },
        { value: "Functional", text: "Functional" }
      ],
      loadingData: false,
      loadingCounts: false,
      processingDefects: false,
      processingMessage: '',
      validationData: [],
      filteredData: [],
      primaryFilter: 'all',
      subcategoryFilter: 'all',
      classificationCounts: null,
      showClassificationModal: false,
      showBatchResultsModal: false,
      selectedDefect: null,
      editClassification: {
        primary: 'Mechanical',
        subcategory: 'Other'
      },
      batchSize: 5,
      batchResults: [],
      tableColumns: [
        { label: "Defect ID", key: "defect_id" },
        { label: "Date", key: "date" },
        { label: "Stage", key: "stage" },
        { label: "Description", key: "description" },
        { label: "Primary", key: "primary" },
        { label: "Subcategory", key: "subcategory" }
      ]
    };
  },
  computed: {
    tableData() {
      return this.filteredData || [];
    },
    chartData() {
      if (!this.classificationCounts) return [];

      // Format the data for the horizontal bar chart
      return [
        { group: "Classification", key: "Mechanical", value: this.classificationCounts.Mechanical || 0 },
        { group: "Classification", key: "Functional", value: this.classificationCounts.Functional || 0 },
        { group: "Classification", key: "Unclassified", value: this.classificationCounts.Unclassified || 0 }
      ];
    }
  },
  methods: {
    async fetchValidationData() {
      this.loadingData = true;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_validation_data`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.validationData = data.data;

        // Apply filters to the data
        this.applyFilters();
      } catch (error) {
        console.error("Error fetching validation data:", error);
      } finally {
        this.loadingData = false;
      }
    },

    async applyFilters() {
      this.loadingData = true;
      // Set default values for filters since dropdowns were removed
      this.primaryFilter = 'all';
      this.subcategoryFilter = 'all';

      try {
        // Use the server-side filtering endpoint
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}query_by_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter,
            primaryFilter: this.primaryFilter,
            subcategoryFilter: this.subcategoryFilter
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.filteredData = data.data;

        // Update the validation data as well to keep it in sync
        this.validationData = data.data;
      } catch (error) {
        console.error("Error applying filters:", error);

        // Fallback to client-side filtering if server-side fails
        if (!this.validationData || this.validationData.length === 0) {
          this.filteredData = [];
          return;
        }

        // Filter the data based on primary and subcategory filters
        this.filteredData = this.validationData.filter(item => {
          // Handle missing classification
          if (!item.classification) {
            return (this.primaryFilter === 'all' || this.primaryFilter === 'Need More Info') &&
                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Need More Info');
          }

          // Handle string classification (backward compatibility)
          if (typeof item.classification === 'string') {
            const primary = item.classification;
            return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&
                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Other');
          }

          // Handle object classification
          const primary = item.classification.primary || 'Need More Info';
          const subcategory = item.classification.subcategory || 'Need More Info';

          return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&
                 (this.subcategoryFilter === 'all' || this.subcategoryFilter === subcategory);
        });
      } finally {
        this.loadingData = false;
      }
    },

    async fetchClassificationCounts() {
      this.loadingCounts = true;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_classification_counts`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.classificationCounts = data.data;
      } catch (error) {
        console.error("Error fetching classification counts:", error);
      } finally {
        this.loadingCounts = false;
      }
    },

    async processNewDefects() {
      this.processingDefects = true;
      this.processingMessage = 'Starting AI classification...';
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_new_defects`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',
            timeFilter: this.selectedTimeFilter // Pass the current time filter
          })
        });

        // Check for HTTP errors
        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage;
          try {
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;
          } catch (e) {
            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Processing result:", data);

        // Check for API errors
        if (data.status === 'error') {
          throw new Error(data.message || 'Unknown error occurred');
        }

        // Update processing message
        if (data.message) {
          this.processingMessage = data.message;
        }

        // Refresh data after processing
        setTimeout(() => {
          this.fetchValidationData();
          this.fetchClassificationCounts();
          this.processingDefects = false;

          // Keep the message visible for a bit longer
          setTimeout(() => {
            this.processingMessage = '';
          }, 5000);
        }, 2000);
      } catch (error) {
        console.error("Error processing defects:", error);
        this.processingDefects = false;
        this.processingMessage = `Error: ${error.message}`;

        // Clear error message after a delay
        setTimeout(() => {
          this.processingMessage = '';
        }, 10000); // Show error for longer (10 seconds)
      }
    },

    async updateClassification(defectId, classification) {
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            defectId,
            classification
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Update local data
        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);
        if (defect) {
          defect.classification = classification;
        }

        // Refresh classification counts
        this.fetchClassificationCounts();
      } catch (error) {
        console.error("Error updating classification:", error);
      }
    },

    handleTimeFilterChange() {
      this.fetchValidationData();
      this.fetchClassificationCounts();
    },

    resetFilters() {
      // Filters are already set to 'all' in applyFilters method
      this.applyFilters();
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return date.toLocaleDateString();
    },

    // Helper methods for classification
    getPrimaryClassification(row) {
      if (!row.classification) return null;

      // Handle string classification (backward compatibility)
      if (typeof row.classification === 'string') {
        return row.classification;
      }

      // Handle object classification
      return row.classification.primary || 'Need More Info';
    },

    getSubcategoryClassification(row) {
      if (!row.classification) return null;

      // Handle string classification (backward compatibility)
      if (typeof row.classification === 'string') {
        return 'Other';
      }

      // Handle object classification
      return row.classification.subcategory || 'Need More Info';
    },

    getSubcategoryClass(row) {
      const subcategory = this.getSubcategoryClassification(row);
      if (!subcategory) return '';

      // Convert to lowercase and remove spaces for CSS class
      return subcategory.toLowerCase().replace(/\s+/g, '-');
    },

    getSubcategoryClassFromString(subcategory) {
      if (!subcategory) return '';

      // Convert to lowercase and remove spaces for CSS class
      return subcategory.toLowerCase().replace(/\s+/g, '-');
    },

    async processLimitedBatch() {
      if (this.batchSize < 1 || this.batchSize > 20) {
        this.processingMessage = 'Batch size must be between 1 and 20';
        setTimeout(() => {
          this.processingMessage = '';
        }, 3000);
        return;
      }
      let token = this.$store.getters.getToken;
      this.processingDefects = true;
      this.processingMessage = `Processing batch of ${this.batchSize} defects...`;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_limited_batch`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token
          },
          body: JSON.stringify({
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',
            timeFilter: this.selectedTimeFilter,
            batchSize: this.batchSize
          })
        });

        // Check for HTTP errors
        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage;
          try {
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;
          } catch (e) {
            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Batch processing result:", data);

        // Check for API errors
        if (data.status === 'error') {
          throw new Error(data.message || 'Unknown error occurred');
        }

        // Update processing message
        if (data.message) {
          this.processingMessage = data.message;
        }

        // Store the batch results
        this.batchResults = data.results || [];

        // Show the batch results modal
        if (this.batchResults.length > 0) {
          this.showBatchResultsModal = true;
        }

        // Refresh data
        this.fetchValidationData();
        this.fetchClassificationCounts();
        this.processingDefects = false;

      } catch (error) {
        console.error("Error processing batch:", error);
        this.processingDefects = false;
        this.processingMessage = `Error: ${error.message}`;

        // Clear error message after a delay
        setTimeout(() => {
          this.processingMessage = '';
        }, 10000); // Show error for longer (10 seconds)
      }
    },

    closeBatchResultsModal() {
      this.showBatchResultsModal = false;
      this.batchResults = [];
    },

    // Modal methods
    openClassificationModal(row) {
      this.selectedDefect = row;

      // Initialize with current classification or defaults
      if (row.classification) {
        if (typeof row.classification === 'string') {
          // Handle string classification (backward compatibility)
          this.editClassification = {
            primary: row.classification,
            subcategory: 'Other'
          };
        } else {
          // Handle object classification
          this.editClassification = {
            primary: row.classification.primary || 'Need More Info',
            subcategory: row.classification.subcategory || 'Need More Info'
          };
        }
      } else {
        // Default values for new classification
        this.editClassification = {
          primary: 'Mechanical',
          subcategory: 'Other'
        };
      }

      this.showClassificationModal = true;
    },

    closeClassificationModal() {
      this.showClassificationModal = false;
      this.selectedDefect = null;
    },

    async saveClassification() {
      if (!this.selectedDefect) return;

      try {
        const defectId = this.selectedDefect.DEFECT_ID;
        const classification = { ...this.editClassification };

        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            defectId,
            classification
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Update local data
        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);
        if (defect) {
          defect.classification = classification;
        }

        // Refresh classification counts and apply filters
        this.fetchClassificationCounts();
        this.applyFilters();

        // Close the modal
        this.closeClassificationModal();
      } catch (error) {
        console.error("Error updating classification:", error);
      }
    }
  },
  mounted() {
    this.fetchValidationData();
    this.fetchClassificationCounts();
  }
};
</script>

<style scoped lang="scss">
.validation2-container {
  min-height: 100vh;
  background-color: #161616;
  color: #f4f4f4;
}

.main-content {
  padding: 2rem;
}

.validation2-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  p {
    color: #c6c6c6;
  }
}

.controls-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.time-period-dropdown {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.batch-process-controls {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
}

.batch-size-input {
  display: flex;
  flex-direction: column;
  width: 80px;
}

.cv-input {
  background-color: #262626;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  height: 40px;
  width: 100%;
}

.dropdown-label {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  color: #c6c6c6;
}

.cv-dropdown {
  background-color: #262626;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  height: 40px;
  width: 100%;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23f4f4f4' d='M8 11L3 6h10z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  cursor: pointer;
}

.cv-dropdown:focus {
  outline: 2px solid #0f62fe;
  outline-offset: -2px;
}

.pareto-section, .defects-table-section {
  margin-bottom: 2rem;

  h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
}

.chart-container {
  height: 400px;
  width: 100%;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.table-container {
  margin-top: 1rem;
}

.classification-cell {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.classification-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;

  &.mechanical {
    background-color: #8a3ffc;
    color: white;
  }

  &.functional {
    background-color: #33b1ff;
    color: white;
  }

  &.unclassified {
    background-color: #da1e28;
    color: white;
  }

  &.need-more-info {
    background-color: #ff832b;
    color: white;
  }

  &.subcategory {
    &.scratches {
      background-color: #6929c4;
      color: white;
    }

    &.bent {
      background-color: #1192e8;
      color: white;
    }

    &.plugging {
      background-color: #005d5d;
      color: white;
    }

    &.discolor {
      background-color: #9f1853;
      color: white;
    }

    &.misalignment {
      background-color: #fa4d56;
      color: white;
    }

    &.other {
      background-color: #4589ff;
      color: white;
    }
  }
}

.edit-button {
  background-color: #393939;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  margin-left: 0.5rem;

  &:hover {
    background-color: #4c4c4c;
  }
}

.filter-dropdown {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  margin-right: 1rem;
}

.processing-message {
  margin-left: 1rem;
  color: #33b1ff;
  font-size: 0.875rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #262626;
  border-radius: 4px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

  h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }

  .description-text {
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    background-color: #393939;
    border-radius: 4px;
    max-height: 100px;
    overflow-y: auto;
  }
}

.batch-results-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.batch-results-container {
  margin: 1rem 0;
  max-height: 60vh;
  overflow-y: auto;
  border: 1px solid #525252;
  border-radius: 4px;
}

.batch-results-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #525252;
  }

  th {
    background-color: #393939;
    font-weight: 600;
  }

  tr:hover {
    background-color: #333333;
  }

  .description-cell {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.modal-form {
  margin-bottom: 1.5rem;

  .form-group {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
  }

  .modal-select {
    width: 100%;
    padding: 0.5rem;
    background-color: #393939;
    color: #f4f4f4;
    border: 1px solid #525252;
    border-radius: 4px;
    font-size: 1rem;
  }
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .modal-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;

    &.cancel {
      background-color: #393939;
      color: #f4f4f4;
      border: 1px solid #525252;

      &:hover {
        background-color: #4c4c4c;
      }
    }

    &.save {
      background-color: #0f62fe;
      color: white;
      border: none;

      &:hover {
        background-color: #0353e9;
      }
    }
  }
}
</style>
