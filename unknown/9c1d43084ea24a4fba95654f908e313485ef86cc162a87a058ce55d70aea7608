<template>
  <div style="position: relative;">
    <ccv-line-chart ref="lineChart" :data="data" :options="mergedOptions"></ccv-line-chart>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { CvButton, CvTextInput } from '@carbon/vue'; // Import Carbon Components

Vue.use(chartsVue);
Vue.component('cv-button', CvButton);
Vue.component('cv-text-input', CvTextInput);

export default {
  name: 'LineChart',
  props: {
    data: Array,
    loading: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: "400px"
    },
    eventType: {
      type: Function,
      default: () => {}
    },
    title: {
      type: String,
      default: 'Commodity X Factor Chart'
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showActionBox: false,
      actionInput: '',
      actionBoxStyle: {
        position: 'absolute',
        left: '0px',
        top: '0px',
        display: 'none'
      },
      defaultOptions: {
        title: `${this.title}`,
        tooltip:{
          showTotal: false
        },
        axes: {
          bottom: {
            title: 'Month',
            mapsTo: 'date',
            scaleType: 'time',
            timeScale: {
              timeInterval: 'monthly'
            },
          },
          left: {
            mapsTo: 'value',
            title: 'XFactor',
            scaleType: 'linear',
            thresholds: [
              {
                value: 1,
                fillColor: 'blue',
                label: 'Target'
              },
              {
                value: 1.20,
                fillColor: 'red',
                label: 'Threshold'
              },
            ]
          }
        },
        grid: {
          x: {enabled: false},
          y: {enabled: true}
        },
        data: {
          loading: this.loading
        },
        height: this.height,
      }
    };
  },
  computed: {
    mergedOptions() {
      // Merge the default options with the props options
      const mergedOptions = { ...this.defaultOptions };

      // If props.options is provided, merge it with the default options
      if (this.$props.options) {
        // Deep merge for nested properties
        Object.keys(this.$props.options).forEach(key => {
          if (typeof this.$props.options[key] === 'object' && !Array.isArray(this.$props.options[key])) {
            mergedOptions[key] = {
              ...(mergedOptions[key] || {}),
              ...this.$props.options[key]
            };

            // Special handling for axes to ensure thresholds are properly merged
            if (key === 'axes' && this.$props.options.axes) {
              Object.keys(this.$props.options.axes).forEach(axisKey => {
                if (typeof this.$props.options.axes[axisKey] === 'object') {
                  mergedOptions.axes[axisKey] = {
                    ...(mergedOptions.axes[axisKey] || {}),
                    ...this.$props.options.axes[axisKey]
                  };
                }
              });
            }
          } else {
            mergedOptions[key] = this.$props.options[key];
          }
        });
      }

      // Update loading state
      if (mergedOptions.data) {
        mergedOptions.data.loading = this.loading;
      }

      return mergedOptions;
    }
  },
  methods: {

  },
  mounted() {
  }
};
</script>

<style scoped>
/* Filled style */
.action-box {
  background-color: rgb(66, 66, 66);
  border: 1px solid #ff3a3a;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
