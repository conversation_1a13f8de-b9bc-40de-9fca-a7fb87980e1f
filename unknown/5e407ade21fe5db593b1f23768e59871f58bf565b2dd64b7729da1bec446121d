<template>
  <div class="dashboard-container">
    <!-- Inherit the MainHeader component -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <main class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">PQE Dashboard</h1>
      </div>

      <!-- Tabs for PQE Owner and QE Dashboards -->
      <div class="dashboard-tabs">
        <cv-tabs aria-label="PQE Dashboard Tabs">
          <cv-tab id="pqe-owner-tab" label="PQE Owner Dashboard">
            <div class="tab-content">
              <!-- PQE Owner Selection -->
              <div class="pqe-selection-container">
                <label for="pqe-owner-dropdown" class="selection-label">Select PQE Owner:</label>
                <cv-dropdown
                  id="pqe-owner-dropdown"
                  v-model="selectedPQEOwner"
                  @change="handlePQEOwnerChange"
                  class="pqe-dropdown"
                >
                  <cv-dropdown-item
                    v-for="owner in pqeOwners"
                    :key="owner"
                    :value="owner"
                  >
                    {{ owner }}
                  </cv-dropdown-item>
                </cv-dropdown>
              </div>

              <!-- PQE Owner Dashboard Component -->
              <PQEOwnerDashboard
                v-if="selectedPQEOwner"
                :pqeOwner="selectedPQEOwner"
                @update-action-tracker="updateActionTracker"
              />
              <div v-else class="select-pqe-message">
                Please select a PQE owner to view their dashboard.
              </div>
            </div>
          </cv-tab>
          <cv-tab id="qe-tab" label="QE Dashboard">
            <div class="tab-content">
              <!-- QE Dashboard Component -->
              <QEDashboard @select-pqe="selectPQEFromQEDashboard" />
            </div>
          </cv-tab>
        </cv-tabs>
      </div>
    </main>
  </div>
</template>

<script>
import MainHeader from '@/components/MainHeader/MainHeader';
import PQEOwnerDashboard from './PQEOwnerDashboard';
import QEDashboard from './QEDashboard';
import { CvTabs, CvTab, CvDropdown, CvDropdownItem } from '@carbon/vue';

export default {
  name: 'PQEDashboardPage',
  components: {
    MainHeader,
    PQEOwnerDashboard,
    QEDashboard,
    CvTabs,
    CvTab,
    CvDropdown,
    CvDropdownItem
  },
  data() {
    return {
      expandedSideNav: false,
      useFixed: false,
      selectedPQEOwner: '',
      pqeOwners: [],
      isLoading: false
    };
  },
  mounted() {
    this.loadPQEOwners();
  },
  methods: {
    async loadPQEOwners() {
      this.isLoading = true;
      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch PQE owners from the API
        const response = await fetch('/api-statit2/get_pqe_owners', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({})
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.pqeOwners = data.pqe_owners || [];
          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);

          // If Albert G. is in the list, select him by default
          const defaultOwner = this.pqeOwners.find(owner => owner === 'Albert G.');
          if (defaultOwner) {
            this.selectedPQEOwner = defaultOwner;
          } else if (this.pqeOwners.length > 0) {
            // Otherwise select the first owner
            this.selectedPQEOwner = this.pqeOwners[0];
          }
        } else {
          console.error('Failed to load PQE owners:', data.message);
        }
      } catch (error) {
        console.error('Error loading PQE owners:', error);
      } finally {
        this.isLoading = false;
      }
    },

    handlePQEOwnerChange() {
      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);
    },

    updateActionTracker(actionData) {
      console.log('Updating action tracker with:', actionData);

      // Call the API to update the action tracker
      this.isLoading = true;

      fetch('/api-statit2/update_pqe_action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthConfig().headers
        },
        body: JSON.stringify(actionData)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.status_res === 'success') {
            console.log('Action tracker updated successfully');
          } else {
            console.error('Failed to update action tracker:', data.message);
          }
        })
        .catch(error => {
          console.error('Error updating action tracker:', error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    selectPQEFromQEDashboard(pqeOwner) {
      console.log(`Selecting PQE owner from QE Dashboard: ${pqeOwner}`);

      // Set the selected PQE owner
      this.selectedPQEOwner = pqeOwner;

      // Switch to the PQE Owner Dashboard tab
      // Find the tab element and click it
      this.$nextTick(() => {
        const tabElement = document.getElementById('pqe-owner-tab');
        if (tabElement) {
          tabElement.click();
        }
      });
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    }
  }
};
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: #161616;
}

.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.page-title {
  color: #f4f4f4;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0;
}

.dashboard-tabs {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tab-content {
  padding: 1.5rem;
}

.pqe-selection-container {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: #333333;
  padding: 1rem;
  border-radius: 8px;
}

.selection-label {
  color: #f4f4f4;
  margin-right: 1rem;
  font-size: 0.875rem;
  white-space: nowrap;
}

.pqe-dropdown {
  width: 300px;
}

.select-pqe-message {
  color: #8d8d8d;
  font-size: 1rem;
  text-align: center;
  padding: 2rem;
  background-color: #333333;
  border-radius: 8px;
}
</style>
