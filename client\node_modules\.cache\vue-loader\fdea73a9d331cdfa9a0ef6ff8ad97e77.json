{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue", "mtime": 1748529056975}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IEN2RHJvcGRvd24sIEN2RHJvcGRvd25JdGVtLCBDdklubGluZUxvYWRpbmcgfSBmcm9tICdAY2FyYm9uL3Z1ZSc7CmltcG9ydCB7IFN0YWNrZWRCYXJDaGFydCB9IGZyb20gJ0BjYXJib24vY2hhcnRzLXZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0ZhaWxzU3RhY2tlZEJhckNoYXJ0JywKICBjb21wb25lbnRzOiB7CiAgICBDdkRyb3Bkb3duLAogICAgQ3ZEcm9wZG93bkl0ZW0sCiAgICBDdklubGluZUxvYWRpbmcsCiAgICBTdGFja2VkQmFyQ2hhcnQKICB9LAogIHByb3BzOiB7CiAgICBwcWVPd25lcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgYnJlYWtvdXRHcm91cHM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNMb2FkaW5nOiBmYWxzZSwKICAgICAgY2hhcnREYXRhOiBbXSwKICAgICAgY2hhcnRPcHRpb25zOiB7fSwKICAgICAgY2hhcnRUeXBlOiAncm9vdENhdXNlJywgLy8gJ3ZhbGlkYXRpb24nLCAncm9vdENhdXNlJywgJ3N1cHBsaWVyJywgJ3NlY3RvcicsICd2aW50YWdlJwogICAgICB0aW1lUmFuZ2U6ICc2bW9udGgnLCAvLyAnM21vbnRoJywgJzZtb250aCcKICAgICAgc2VsZWN0ZWRHcm91cDogJ2FsbCcsIC8vICdhbGwnIG9yIGEgc3BlY2lmaWMgZ3JvdXAgbmFtZQogICAgICBtb250aHM6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCdGYWlsc1N0YWNrZWRCYXJDaGFydCBjb21wb25lbnQgbW91bnRlZCcpOwogICAgaWYgKHRoaXMucHFlT3duZXIpIHsKICAgICAgdGhpcy5sb2FkQ2hhcnREYXRhKCk7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgcHFlT3duZXI6IHsKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKG5ld1ZhbHVlKSB7CiAgICAgICAgaWYgKG5ld1ZhbHVlKSB7CiAgICAgICAgICB0aGlzLmxvYWRDaGFydERhdGEoKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBjaGFydFR5cGUoKSB7CiAgICAgIHRoaXMubG9hZENoYXJ0RGF0YSgpOwogICAgfSwKICAgIHRpbWVSYW5nZSgpIHsKICAgICAgdGhpcy5sb2FkQ2hhcnREYXRhKCk7CiAgICB9LAogICAgc2VsZWN0ZWRHcm91cCgpIHsKICAgICAgdGhpcy5sb2FkQ2hhcnREYXRhKCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CgogICAgYXN5bmMgbG9hZENoYXJ0RGF0YSgpIHsKICAgICAgY29uc29sZS5sb2coYExvYWRpbmcgY2hhcnQgZGF0YSBmb3IgUFFFIG93bmVyOiAke3RoaXMucHFlT3duZXJ9LCBDaGFydCBUeXBlOiAke3RoaXMuY2hhcnRUeXBlfSwgVGltZSBSYW5nZTogJHt0aGlzLnRpbWVSYW5nZX0sIEdyb3VwOiAke3RoaXMuc2VsZWN0ZWRHcm91cH1gKTsKICAgICAgY29uc29sZS5sb2coJ0JyZWFrb3V0IGdyb3VwczonLCB0aGlzLmJyZWFrb3V0R3JvdXBzKTsKICAgICAgdGhpcy5pc0xvYWRpbmcgPSB0cnVlOwoKICAgICAgdHJ5IHsKICAgICAgICAvLyBHZXQgYXV0aGVudGljYXRpb24gY29uZmlnCiAgICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRBdXRoQ29uZmlnKCk7CgogICAgICAgIC8vIERldGVybWluZSB0aGUgbnVtYmVyIG9mIG1vbnRocyB0byBmZXRjaCBiYXNlZCBvbiB0aGUgdGltZSByYW5nZQogICAgICAgIGNvbnN0IG1vbnRoc1RvRmV0Y2ggPSB0aGlzLnRpbWVSYW5nZSA9PT0gJzNtb250aCcgPyAzIDogNjsKCiAgICAgICAgLy8gRmV0Y2ggY2hhcnQgZGF0YSBmcm9tIHRoZSBBUEkKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpLXN0YXRpdDIvZ2V0X3BxZV9jaGFydF9kYXRhJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLmNvbmZpZy5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBwcWVPd25lcjogdGhpcy5wcWVPd25lciwKICAgICAgICAgICAgY2hhcnRUeXBlOiB0aGlzLmNoYXJ0VHlwZSwKICAgICAgICAgICAgbW9udGhzVG9GZXRjaDogbW9udGhzVG9GZXRjaCwKICAgICAgICAgICAgZ3JvdXBOYW1lOiB0aGlzLnNlbGVjdGVkR3JvdXAgPT09ICdhbGwnID8gbnVsbCA6IHRoaXMuc2VsZWN0ZWRHcm91cAogICAgICAgICAgfSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggY2hhcnQgZGF0YTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgLy8gUHJvY2VzcyB0aGUgY2hhcnQgZGF0YQogICAgICAgICAgdGhpcy5wcm9jZXNzQ2hhcnREYXRhKGRhdGEuY2hhcnRfZGF0YSB8fCBbXSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNoYXJ0IGRhdGE6JywgZGF0YS5tZXNzYWdlKTsKICAgICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICAgIHRoaXMubG9hZFNhbXBsZUNoYXJ0RGF0YSgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGNoYXJ0IGRhdGE6JywgZXJyb3IpOwogICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICB0aGlzLmxvYWRTYW1wbGVDaGFydERhdGEoKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzTG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVDaGFydERhdGEoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nIHNhbXBsZSBjaGFydCBkYXRhJyk7CgogICAgICAvLyBHZW5lcmF0ZSBzYW1wbGUgbW9udGhzIChsYXN0IDYgb3IgMyBtb250aHMpCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IG1vbnRocyA9IFtdOwogICAgICBjb25zdCBtb250aHNUb0dlbmVyYXRlID0gdGhpcy50aW1lUmFuZ2UgPT09ICczbW9udGgnID8gMyA6IDY7CgogICAgICBmb3IgKGxldCBpID0gbW9udGhzVG9HZW5lcmF0ZSAtIDE7IGkgPj0gMDsgaS0tKSB7CiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKG5vdy5nZXRGdWxsWWVhcigpLCBub3cuZ2V0TW9udGgoKSAtIGksIDEpOwogICAgICAgIGNvbnN0IG1vbnRoTmFtZSA9IGRhdGUudG9Mb2NhbGVTdHJpbmcoJ2RlZmF1bHQnLCB7IG1vbnRoOiAnc2hvcnQnIH0pOwogICAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCkudG9TdHJpbmcoKS5zbGljZSgtMik7CiAgICAgICAgbW9udGhzLnB1c2goYCR7bW9udGhOYW1lfSAnJHt5ZWFyfWApOwogICAgICB9CgogICAgICB0aGlzLm1vbnRocyA9IG1vbnRoczsKCiAgICAgIC8vIEdlbmVyYXRlIHNhbXBsZSBkYXRhIGJhc2VkIG9uIGNoYXJ0IHR5cGUKICAgICAgaWYgKHRoaXMuY2hhcnRUeXBlID09PSAndmFsaWRhdGlvbicpIHsKICAgICAgICB0aGlzLmdlbmVyYXRlVmFsaWRhdGlvbkNoYXJ0RGF0YSgpOwogICAgICB9IGVsc2UgaWYgKHRoaXMuY2hhcnRUeXBlID09PSAncm9vdENhdXNlJykgewogICAgICAgIHRoaXMuZ2VuZXJhdGVSb290Q2F1c2VDaGFydERhdGEoKTsKICAgICAgfQoKICAgICAgLy8gRmlsdGVyIGRhdGEgYnkgc2VsZWN0ZWQgZ3JvdXAgaWYgbmVlZGVkCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkR3JvdXAgIT09ICdhbGwnKSB7CiAgICAgICAgdGhpcy5maWx0ZXJEYXRhQnlHcm91cCgpOwogICAgICB9CgogICAgICBjb25zb2xlLmxvZygnR2VuZXJhdGVkIGNoYXJ0IGRhdGE6JywgdGhpcy5jaGFydERhdGEpOwogICAgfSwKCiAgICBmaWx0ZXJEYXRhQnlHcm91cCgpIHsKICAgICAgLy8gSWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZCwgZmlsdGVyIHRoZSBjaGFydCBkYXRhCiAgICAgIGNvbnNvbGUubG9nKGBGaWx0ZXJpbmcgY2hhcnQgZGF0YSBieSBncm91cDogJHt0aGlzLnNlbGVjdGVkR3JvdXB9YCk7CgogICAgICBpZiAodGhpcy5zZWxlY3RlZEdyb3VwICE9PSAnYWxsJykgewogICAgICAgIC8vIENyZWF0ZSBhIG5ldyBmaWx0ZXJlZCBkYXRhc2V0IHRoYXQgb25seSBpbmNsdWRlcyBkYXRhIHBvaW50cyBmb3IgdGhlIHNlbGVjdGVkIGdyb3VwCiAgICAgICAgY29uc3QgZmlsdGVyZWREYXRhID0gW107CgogICAgICAgIC8vIEZvciBlYWNoIGRhdGEgcG9pbnQsIGNoZWNrIGlmIGl0IGJlbG9uZ3MgdG8gdGhlIHNlbGVjdGVkIGdyb3VwCiAgICAgICAgdGhpcy5jaGFydERhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIC8vIEZvciB2YWxpZGF0aW9uIGNoYXJ0IHR5cGUsIHdlIG5lZWQgdG8gYWRkIHRoZSBncm91cCBuYW1lIHRvIHRoZSBkYXRhCiAgICAgICAgICBpZiAodGhpcy5jaGFydFR5cGUgPT09ICd2YWxpZGF0aW9uJykgewogICAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgaXRlbSB3aXRoIHRoZSBncm91cCBuYW1lIHByZWZpeGVkCiAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW0gPSB7IC4uLml0ZW0gfTsKICAgICAgICAgICAgbmV3SXRlbS5ncm91cCA9IGAke3RoaXMuc2VsZWN0ZWRHcm91cH0gLSAke2l0ZW0uZ3JvdXB9YDsKICAgICAgICAgICAgZmlsdGVyZWREYXRhLnB1c2gobmV3SXRlbSk7CiAgICAgICAgICB9CiAgICAgICAgICAvLyBGb3Igb3RoZXIgY2hhcnQgdHlwZXMsIG9ubHkgaW5jbHVkZSBpdGVtcyB0aGF0IGNvbnRhaW4gdGhlIHNlbGVjdGVkIGdyb3VwIG5hbWUKICAgICAgICAgIGVsc2UgaWYgKGl0ZW0uZ3JvdXAuaW5jbHVkZXModGhpcy5zZWxlY3RlZEdyb3VwKSkgewogICAgICAgICAgICBmaWx0ZXJlZERhdGEucHVzaChpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgY29uc29sZS5sb2coYEZpbHRlcmVkIGZyb20gJHt0aGlzLmNoYXJ0RGF0YS5sZW5ndGh9IHRvICR7ZmlsdGVyZWREYXRhLmxlbmd0aH0gZGF0YSBwb2ludHNgKTsKICAgICAgICB0aGlzLmNoYXJ0RGF0YSA9IGZpbHRlcmVkRGF0YTsKICAgICAgfQogICAgfSwKCiAgICBnZW5lcmF0ZVZhbGlkYXRpb25DaGFydERhdGEoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSBbXTsKCiAgICAgIC8vIEZvciBlYWNoIG1vbnRoLCBnZW5lcmF0ZSB2YWxpZGF0ZWQgYW5kIHVudmFsaWRhdGVkIGNvdW50cwogICAgICB0aGlzLm1vbnRocy5mb3JFYWNoKG1vbnRoID0+IHsKICAgICAgICAvLyBHZW5lcmF0ZSByYW5kb20gdmFsdWVzCiAgICAgICAgY29uc3QgdmFsaWRhdGVkID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNTApICsgMjA7CiAgICAgICAgY29uc3QgdW52YWxpZGF0ZWQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzMCkgKyA1OwoKICAgICAgICAvLyBBZGQgdmFsaWRhdGVkIGRhdGEgcG9pbnQKICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgZ3JvdXA6ICdWYWxpZGF0ZWQnLAogICAgICAgICAgZGF0ZTogbW9udGgsCiAgICAgICAgICB2YWx1ZTogdmFsaWRhdGVkCiAgICAgICAgfSk7CgogICAgICAgIC8vIEFkZCB1bnZhbGlkYXRlZCBkYXRhIHBvaW50CiAgICAgICAgZGF0YS5wdXNoKHsKICAgICAgICAgIGdyb3VwOiAnVW52YWxpZGF0ZWQnLAogICAgICAgICAgZGF0ZTogbW9udGgsCiAgICAgICAgICB2YWx1ZTogdW52YWxpZGF0ZWQKICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICAvLyBTb3J0IGRhdGEgYnkgZGF0ZQogICAgICB0aGlzLnNvcnRDaGFydERhdGFCeURhdGUoZGF0YSk7CgogICAgICB0aGlzLmNoYXJ0RGF0YSA9IGRhdGE7CiAgICAgIHRoaXMudXBkYXRlQ2hhcnRPcHRpb25zKCk7CiAgICB9LAoKICAgIHNvcnRDaGFydERhdGFCeURhdGUoZGF0YSkgewogICAgICAvLyBTb3J0IGRhdGEgYnkgZGF0ZSB0byBlbnN1cmUgcHJvcGVyIG9yZGVyaW5nCiAgICAgIGRhdGEuc29ydCgoYSwgYikgPT4gewogICAgICAgIC8vIEV4dHJhY3QgbW9udGggYW5kIHllYXIgZnJvbSBkYXRlIHN0cmluZyAoZS5nLiwgIkp1biAnMjQiKQogICAgICAgIGNvbnN0IFthTW9udGgsIGFZZWFyXSA9IGEuZGF0ZS5zcGxpdCgiICIpOwogICAgICAgIGNvbnN0IFtiTW9udGgsIGJZZWFyXSA9IGIuZGF0ZS5zcGxpdCgiICIpOwoKICAgICAgICAvLyBDb21wYXJlIHllYXJzIGZpcnN0CiAgICAgICAgaWYgKGFZZWFyICE9PSBiWWVhcikgewogICAgICAgICAgcmV0dXJuIGFZZWFyLmxvY2FsZUNvbXBhcmUoYlllYXIpOwogICAgICAgIH0KCiAgICAgICAgLy8gSWYgeWVhcnMgYXJlIHRoZSBzYW1lLCBjb21wYXJlIG1vbnRocwogICAgICAgIGNvbnN0IG1vbnRocyA9IFsnSmFuJywgJ0ZlYicsICdNYXInLCAnQXByJywgJ01heScsICdKdW4nLCAnSnVsJywgJ0F1ZycsICdTZXAnLCAnT2N0JywgJ05vdicsICdEZWMnXTsKICAgICAgICByZXR1cm4gbW9udGhzLmluZGV4T2YoYU1vbnRoKSAtIG1vbnRocy5pbmRleE9mKGJNb250aCk7CiAgICAgIH0pOwogICAgfSwKCiAgICBnZW5lcmF0ZVJvb3RDYXVzZUNoYXJ0RGF0YSgpIHsKICAgICAgY29uc3QgZGF0YSA9IFtdOwogICAgICAvLyBNYXRjaCB0aGUgcm9vdCBjYXVzZXMgZnJvbSB0aGUgTWV0aXMgWEZhY3RvcnMgZm9ybWF0CiAgICAgIGNvbnN0IHJvb3RDYXVzZXMgPSBbJ0tQQVJTMScsICdESUFHJywgJ09USEVSJywgJ0xJTksnLCAnRkRCJywgJ0RBUCcsICdDT0RFJywgJ0JJT1MnLCAnSExBJ107CgogICAgICAvLyBGb3IgZWFjaCBtb250aCwgZ2VuZXJhdGUgZGF0YSBmb3IgZWFjaCByb290IGNhdXNlCiAgICAgIHRoaXMubW9udGhzLmZvckVhY2gobW9udGggPT4gewogICAgICAgIC8vIEdlbmVyYXRlIGEgdG90YWwgdmFsdWUgZm9yIHRoZSBtb250aCAodGhpcyB3aWxsIGJlIHVzZWQgdG8gY2FsY3VsYXRlIHhGYWN0b3IpCiAgICAgICAgY29uc3QgdG90YWxWYWx1ZSA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMCkgKyA1MDsKCiAgICAgICAgLy8gR2VuZXJhdGUgYSB0YXJnZXQgdmFsdWUgKGV4cGVjdGVkIGZhaWx1cmVzKQogICAgICAgIGNvbnN0IHRhcmdldFZhbHVlID0gTWF0aC5mbG9vcih0b3RhbFZhbHVlICogMC43KTsKCiAgICAgICAgLy8gRGlzdHJpYnV0ZSB0aGUgdG90YWwgdmFsdWUgYW1vbmcgdGhlIHJvb3QgY2F1c2VzCiAgICAgICAgbGV0IHJlbWFpbmluZ1ZhbHVlID0gdG90YWxWYWx1ZTsKCiAgICAgICAgcm9vdENhdXNlcy5mb3JFYWNoKChjYXVzZSwgaW5kZXgpID0+IHsKICAgICAgICAgIC8vIEZvciB0aGUgbGFzdCBjYXVzZSwgdXNlIHRoZSByZW1haW5pbmcgdmFsdWUgdG8gZW5zdXJlIHRoZSBzdW0gZXF1YWxzIHRvdGFsVmFsdWUKICAgICAgICAgIGxldCB2YWx1ZTsKICAgICAgICAgIGlmIChpbmRleCA9PT0gcm9vdENhdXNlcy5sZW5ndGggLSAxKSB7CiAgICAgICAgICAgIHZhbHVlID0gcmVtYWluaW5nVmFsdWU7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyBHZW5lcmF0ZSBhIHJhbmRvbSBwb3J0aW9uIG9mIHRoZSByZW1haW5pbmcgdmFsdWUKICAgICAgICAgICAgY29uc3QgcG9ydGlvbiA9IE1hdGgucmFuZG9tKCkgKiAwLjU7IC8vIFVwIHRvIDUwJSBvZiByZW1haW5pbmcKICAgICAgICAgICAgdmFsdWUgPSBNYXRoLmZsb29yKHJlbWFpbmluZ1ZhbHVlICogcG9ydGlvbik7CiAgICAgICAgICAgIHJlbWFpbmluZ1ZhbHVlIC09IHZhbHVlOwogICAgICAgICAgfQoKICAgICAgICAgIC8vIE9ubHkgYWRkIGRhdGEgcG9pbnRzIHdpdGggbm9uLXplcm8gdmFsdWVzCiAgICAgICAgICBpZiAodmFsdWUgPiAwKSB7CiAgICAgICAgICAgIC8vIEFkZCBkYXRhIHBvaW50CiAgICAgICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICAgICAgZ3JvdXA6IGNhdXNlLAogICAgICAgICAgICAgIGRhdGU6IG1vbnRoLAogICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgLy8gQWRkIGEgcmVmZXJlbmNlIGxpbmUgZm9yIHRoZSB0YXJnZXQgdmFsdWUKICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgZ3JvdXA6ICdUYXJnZXQnLAogICAgICAgICAgZGF0ZTogbW9udGgsCiAgICAgICAgICB2YWx1ZTogdGFyZ2V0VmFsdWUKICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICAvLyBTb3J0IGRhdGEgYnkgZGF0ZQogICAgICB0aGlzLnNvcnRDaGFydERhdGFCeURhdGUoZGF0YSk7CgogICAgICB0aGlzLmNoYXJ0RGF0YSA9IGRhdGE7CiAgICAgIHRoaXMudXBkYXRlQ2hhcnRPcHRpb25zKCdyb290Q2F1c2UnKTsKICAgIH0sCgoKCiAgICBwcm9jZXNzQ2hhcnREYXRhKGNoYXJ0RGF0YSkgewogICAgICAvLyBQcm9jZXNzIHRoZSBjaGFydCBkYXRhIGZyb20gdGhlIEFQSQogICAgICAvLyBTb3J0IGRhdGEgYnkgZGF0ZQogICAgICB0aGlzLnNvcnRDaGFydERhdGFCeURhdGUoY2hhcnREYXRhKTsKICAgICAgdGhpcy5jaGFydERhdGEgPSBjaGFydERhdGE7CiAgICAgIHRoaXMudXBkYXRlQ2hhcnRPcHRpb25zKCk7CiAgICB9LAoKICAgIHVwZGF0ZUNoYXJ0T3B0aW9ucyhjaGFydFR5cGUgPSBudWxsKSB7CiAgICAgIC8vIFNldCBjaGFydCBvcHRpb25zIGJhc2VkIG9uIHRoZSBjaGFydCB0eXBlCiAgICAgIGNvbnN0IHRpdGxlID0gdGhpcy5nZXRDaGFydFRpdGxlKCk7CiAgICAgIGNvbnNvbGUubG9nKGBVcGRhdGluZyBjaGFydCBvcHRpb25zIHdpdGggdGl0bGU6ICR7dGl0bGV9YCk7CgogICAgICAvLyBVc2UgdGhlIHByb3ZpZGVkIGNoYXJ0IHR5cGUgb3IgdGhlIGN1cnJlbnQgY2hhcnQgdHlwZQogICAgICBjb25zdCB0eXBlID0gY2hhcnRUeXBlIHx8IHRoaXMuY2hhcnRUeXBlOwoKICAgICAgLy8gQmFzZSBjaGFydCBvcHRpb25zCiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7CiAgICAgICAgdGl0bGU6IHRpdGxlLAogICAgICAgIGF4ZXM6IHsKICAgICAgICAgIGxlZnQ6IHsKICAgICAgICAgICAgbWFwc1RvOiAndmFsdWUnLAogICAgICAgICAgICB0aXRsZTogJ0NvdW50JywKICAgICAgICAgICAgc2NhbGVUeXBlOiAnbGluZWFyJywKICAgICAgICAgICAgaW5jbHVkZVplcm86IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICBib3R0b206IHsKICAgICAgICAgICAgbWFwc1RvOiAnZGF0ZScsCiAgICAgICAgICAgIHRpdGxlOiAnTW9udGgnLAogICAgICAgICAgICBzY2FsZVR5cGU6ICdsYWJlbHMnCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBoZWlnaHQ6ICc0MDBweCcsCiAgICAgICAgd2lkdGg6ICcxMDAlJywKICAgICAgICByZXNpemFibGU6IHRydWUsCiAgICAgICAgdGhlbWU6ICdnOTAnLCAvLyBEYXJrIHRoZW1lIHRvIG1hdGNoIHRoZSBpbXBvcnRlZCBzdHlsZXMKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIGFsaWdubWVudDogJ2NlbnRlcicsCiAgICAgICAgICBwb3NpdGlvbjogJ2JvdHRvbScKICAgICAgICB9LAogICAgICAgIGNvbG9yOiB7CiAgICAgICAgICBzY2FsZTogdGhpcy5nZXRDb2xvclNjYWxlKHR5cGUpCiAgICAgICAgfSwKICAgICAgICB0b29sYmFyOiB7CiAgICAgICAgICBlbmFibGVkOiB0cnVlCiAgICAgICAgfSwKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBncm91cE1hcHNUbzogJ2dyb3VwJwogICAgICAgIH0sCiAgICAgICAgc3RhY2tlZDogdHJ1ZQogICAgICB9OwoKICAgICAgLy8gRm9yIHJvb3QgY2F1c2UgYW5hbHlzaXMsIGFkZCBzcGVjaWFsIGhhbmRsaW5nIGZvciB0aGUgdGFyZ2V0IGxpbmUKICAgICAgaWYgKHR5cGUgPT09ICdyb290Q2F1c2UnKSB7CiAgICAgICAgLy8gQWRkIHNwZWNpYWwgaGFuZGxpbmcgZm9yIHRoZSB0YXJnZXQgbGluZQogICAgICAgIG9wdGlvbnMuZGF0YS5ncm91cHMgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICdUYXJnZXQnLAogICAgICAgICAgICB0eXBlOiAnbGluZScKICAgICAgICAgIH0KICAgICAgICBdOwoKICAgICAgICAvLyBBZGQgY29ycmVzcG9uZGluZyBkYXRhc2V0cyB0byBtYWtlIHRoZSB0YXJnZXQgbGluZSB3b3JrCiAgICAgICAgb3B0aW9ucy5kYXRhLmNvcnJlc3BvbmRpbmdEYXRhc2V0cyA9IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICBzZWxlY3Q6IChkYXRhKSA9PiBkYXRhLmdyb3VwID09PSAnVGFyZ2V0JwogICAgICAgICAgfQogICAgICAgIF07CiAgICAgIH0KCiAgICAgIHRoaXMuY2hhcnRPcHRpb25zID0gb3B0aW9uczsKCiAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBvcHRpb25zIHVwZGF0ZWQ6JywgdGhpcy5jaGFydE9wdGlvbnMpOwogICAgfSwKCiAgICBnZXRDaGFydFRpdGxlKCkgewogICAgICAvLyBCYXNlIHRpdGxlIGJhc2VkIG9uIGNoYXJ0IHR5cGUKICAgICAgbGV0IGJhc2VUaXRsZSA9ICcnOwogICAgICBzd2l0Y2ggKHRoaXMuY2hhcnRUeXBlKSB7CiAgICAgICAgY2FzZSAndmFsaWRhdGlvbic6CiAgICAgICAgICBiYXNlVGl0bGUgPSAnVmFsaWRhdGVkIHZzIFVudmFsaWRhdGVkIEZhaWxzJzsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ3Jvb3RDYXVzZSc6CiAgICAgICAgICBiYXNlVGl0bGUgPSAnRmFpbHMgYnkgUm9vdCBDYXVzZSc7CiAgICAgICAgICBicmVhazsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYmFzZVRpdGxlID0gJ0ZhaWxzIEFuYWx5c2lzJzsKICAgICAgfQoKICAgICAgLy8gQWRkIGdyb3VwIG5hbWUgaWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZAogICAgICBpZiAodGhpcy5zZWxlY3RlZEdyb3VwICE9PSAnYWxsJykgewogICAgICAgIHJldHVybiBgJHtiYXNlVGl0bGV9IC0gJHt0aGlzLnNlbGVjdGVkR3JvdXB9YDsKICAgICAgfQoKICAgICAgcmV0dXJuIGJhc2VUaXRsZTsKICAgIH0sCgogICAgZ2V0Q29sb3JTY2FsZShjaGFydFR5cGUgPSBudWxsKSB7CiAgICAgIC8vIFVzZSB0aGUgcHJvdmlkZWQgY2hhcnQgdHlwZSBvciB0aGUgY3VycmVudCBjaGFydCB0eXBlCiAgICAgIGNvbnN0IHR5cGUgPSBjaGFydFR5cGUgfHwgdGhpcy5jaGFydFR5cGU7CgogICAgICAvLyBSZXR1cm4gdGhlIGFwcHJvcHJpYXRlIGNvbG9yIHNjYWxlIGJhc2VkIG9uIHRoZSBjaGFydCB0eXBlCiAgICAgIHN3aXRjaCAodHlwZSkgewogICAgICAgIGNhc2UgJ3ZhbGlkYXRpb24nOgogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgJ1ZhbGlkYXRlZCc6ICcjMjRhMTQ4JywKICAgICAgICAgICAgJ1VudmFsaWRhdGVkJzogJyNmZjgzMmInCiAgICAgICAgICB9OwogICAgICAgIGNhc2UgJ3Jvb3RDYXVzZSc6CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAnS1BBUlMxJzogJyMwZjYyZmUnLAogICAgICAgICAgICAnRElBRyc6ICcjNjkyOWM0JywKICAgICAgICAgICAgJ09USEVSJzogJyMxMTkyZTgnLAogICAgICAgICAgICAnTElOSyc6ICcjOGEzZmZjJywKICAgICAgICAgICAgJ0ZEQic6ICcjZWU1Mzk2JywKICAgICAgICAgICAgJ0RBUCc6ICcjZmY3ZWI2JywKICAgICAgICAgICAgJ0NPREUnOiAnI2ZhNGQ1NicsCiAgICAgICAgICAgICdCSU9TJzogJyNkMTI3NzEnLAogICAgICAgICAgICAnSExBJzogJyMwOGJkYmEnLAogICAgICAgICAgICAnVGFyZ2V0JzogJyNmMWMyMWInIC8vIFllbGxvdyBmb3IgdGFyZ2V0IGxpbmUKICAgICAgICAgIH07CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfSwKCiAgICBnZXRBdXRoQ29uZmlnKCkgewogICAgICAvLyBHZXQgYXV0aGVudGljYXRpb24gdG9rZW4gZnJvbSBsb2NhbFN0b3JhZ2Ugb3IgVnVleCBzdG9yZQogICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8IHRoaXMuJHN0b3JlLnN0YXRlLnRva2VuOwoKICAgICAgcmV0dXJuIHsKICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gCiAgICAgICAgfQogICAgICB9OwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["FailsStackedBarChart.vue"], "names": [], "mappings": ";AAiEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FailsStackedBarChart.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\n  <div class=\"fails-chart-container\">\n    <div class=\"chart-controls\">\n      <div class=\"control-group\">\n        <label for=\"chart-type-dropdown\" class=\"control-label\">View By:</label>\n        <cv-dropdown\n          id=\"chart-type-dropdown\"\n          v-model=\"chartType\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"validation\">Validated/Unvalidated</cv-dropdown-item>\n          <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n      <div class=\"control-group\">\n        <label for=\"time-range-dropdown\" class=\"control-label\">Time Range:</label>\n        <cv-dropdown\n          id=\"time-range-dropdown\"\n          v-model=\"timeRange\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n          <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n      <div class=\"control-group\" v-if=\"breakoutGroups && breakoutGroups.length > 0\">\n        <label for=\"group-dropdown\" class=\"control-label\">Group:</label>\n        <cv-dropdown\n          id=\"group-dropdown\"\n          v-model=\"selectedGroup\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n          <cv-dropdown-item\n            v-for=\"group in breakoutGroups\"\n            :key=\"group.name\"\n            :value=\"group.name\"\n          >\n            {{ group.name }}\n          </cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n    </div>\n\n    <div class=\"chart-container\">\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <cv-inline-loading\n          status=\"active\"\n          loading-text=\"Loading chart data...\"\n        ></cv-inline-loading>\n      </div>\n      <div v-else-if=\"chartData.length > 0\" class=\"chart-wrapper\">\n        <StackedBarChart\n          :data=\"chartData\"\n          :options=\"chartOptions\"\n        ></StackedBarChart>\n      </div>\n      <div v-else class=\"no-data-message\">\n        No data available for the selected criteria.\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { CvDropdown, CvDropdownItem, CvInlineLoading } from '@carbon/vue';\nimport { StackedBarChart } from '@carbon/charts-vue';\n\nexport default {\n  name: 'FailsStackedBarChart',\n  components: {\n    CvDropdown,\n    CvDropdownItem,\n    CvInlineLoading,\n    StackedBarChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    },\n    breakoutGroups: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      isLoading: false,\n      chartData: [],\n      chartOptions: {},\n      chartType: 'rootCause', // 'validation', 'rootCause', 'supplier', 'sector', 'vintage'\n      timeRange: '6month', // '3month', '6month'\n      selectedGroup: 'all', // 'all' or a specific group name\n      months: []\n    };\n  },\n  mounted() {\n    console.log('FailsStackedBarChart component mounted');\n    if (this.pqeOwner) {\n      this.loadChartData();\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue) {\n        if (newValue) {\n          this.loadChartData();\n        }\n      }\n    },\n    chartType() {\n      this.loadChartData();\n    },\n    timeRange() {\n      this.loadChartData();\n    },\n    selectedGroup() {\n      this.loadChartData();\n    }\n  },\n  methods: {\n\n    async loadChartData() {\n      console.log(`Loading chart data for PQE owner: ${this.pqeOwner}, Chart Type: ${this.chartType}, Time Range: ${this.timeRange}, Group: ${this.selectedGroup}`);\n      console.log('Breakout groups:', this.breakoutGroups);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Determine the number of months to fetch based on the time range\n        const monthsToFetch = this.timeRange === '3month' ? 3 : 6;\n\n        // Fetch chart data from the API\n        const response = await fetch('/api-statit2/get_pqe_chart_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            chartType: this.chartType,\n            monthsToFetch: monthsToFetch,\n            groupName: this.selectedGroup === 'all' ? null : this.selectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch chart data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Process the chart data\n          this.processChartData(data.chart_data || []);\n        } else {\n          console.error('Failed to load chart data:', data.message);\n          // Use sample data for development\n          this.loadSampleChartData();\n        }\n      } catch (error) {\n        console.error('Error loading chart data:', error);\n        // Use sample data for development\n        this.loadSampleChartData();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleChartData() {\n      console.log('Loading sample chart data');\n\n      // Generate sample months (last 6 or 3 months)\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.timeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const monthName = date.toLocaleString('default', { month: 'short' });\n        const year = date.getFullYear().toString().slice(-2);\n        months.push(`${monthName} '${year}`);\n      }\n\n      this.months = months;\n\n      // Generate sample data based on chart type\n      if (this.chartType === 'validation') {\n        this.generateValidationChartData();\n      } else if (this.chartType === 'rootCause') {\n        this.generateRootCauseChartData();\n      }\n\n      // Filter data by selected group if needed\n      if (this.selectedGroup !== 'all') {\n        this.filterDataByGroup();\n      }\n\n      console.log('Generated chart data:', this.chartData);\n    },\n\n    filterDataByGroup() {\n      // If a specific group is selected, filter the chart data\n      console.log(`Filtering chart data by group: ${this.selectedGroup}`);\n\n      if (this.selectedGroup !== 'all') {\n        // Create a new filtered dataset that only includes data points for the selected group\n        const filteredData = [];\n\n        // For each data point, check if it belongs to the selected group\n        this.chartData.forEach(item => {\n          // For validation chart type, we need to add the group name to the data\n          if (this.chartType === 'validation') {\n            // Create a new item with the group name prefixed\n            const newItem = { ...item };\n            newItem.group = `${this.selectedGroup} - ${item.group}`;\n            filteredData.push(newItem);\n          }\n          // For other chart types, only include items that contain the selected group name\n          else if (item.group.includes(this.selectedGroup)) {\n            filteredData.push(item);\n          }\n        });\n\n        console.log(`Filtered from ${this.chartData.length} to ${filteredData.length} data points`);\n        this.chartData = filteredData;\n      }\n    },\n\n    generateValidationChartData() {\n      const data = [];\n\n      // For each month, generate validated and unvalidated counts\n      this.months.forEach(month => {\n        // Generate random values\n        const validated = Math.floor(Math.random() * 50) + 20;\n        const unvalidated = Math.floor(Math.random() * 30) + 5;\n\n        // Add validated data point\n        data.push({\n          group: 'Validated',\n          date: month,\n          value: validated\n        });\n\n        // Add unvalidated data point\n        data.push({\n          group: 'Unvalidated',\n          date: month,\n          value: unvalidated\n        });\n      });\n\n      // Sort data by date\n      this.sortChartDataByDate(data);\n\n      this.chartData = data;\n      this.updateChartOptions();\n    },\n\n    sortChartDataByDate(data) {\n      // Sort data by date to ensure proper ordering\n      data.sort((a, b) => {\n        // Extract month and year from date string (e.g., \"Jun '24\")\n        const [aMonth, aYear] = a.date.split(\" \");\n        const [bMonth, bYear] = b.date.split(\" \");\n\n        // Compare years first\n        if (aYear !== bYear) {\n          return aYear.localeCompare(bYear);\n        }\n\n        // If years are the same, compare months\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        return months.indexOf(aMonth) - months.indexOf(bMonth);\n      });\n    },\n\n    generateRootCauseChartData() {\n      const data = [];\n      // Match the root causes from the Metis XFactors format\n      const rootCauses = ['KPARS1', 'DIAG', 'OTHER', 'LINK', 'FDB', 'DAP', 'CODE', 'BIOS', 'HLA'];\n\n      // For each month, generate data for each root cause\n      this.months.forEach(month => {\n        // Generate a total value for the month (this will be used to calculate xFactor)\n        const totalValue = Math.floor(Math.random() * 100) + 50;\n\n        // Generate a target value (expected failures)\n        const targetValue = Math.floor(totalValue * 0.7);\n\n        // Distribute the total value among the root causes\n        let remainingValue = totalValue;\n\n        rootCauses.forEach((cause, index) => {\n          // For the last cause, use the remaining value to ensure the sum equals totalValue\n          let value;\n          if (index === rootCauses.length - 1) {\n            value = remainingValue;\n          } else {\n            // Generate a random portion of the remaining value\n            const portion = Math.random() * 0.5; // Up to 50% of remaining\n            value = Math.floor(remainingValue * portion);\n            remainingValue -= value;\n          }\n\n          // Only add data points with non-zero values\n          if (value > 0) {\n            // Add data point\n            data.push({\n              group: cause,\n              date: month,\n              value: value\n            });\n          }\n        });\n\n        // Add a reference line for the target value\n        data.push({\n          group: 'Target',\n          date: month,\n          value: targetValue\n        });\n      });\n\n      // Sort data by date\n      this.sortChartDataByDate(data);\n\n      this.chartData = data;\n      this.updateChartOptions('rootCause');\n    },\n\n\n\n    processChartData(chartData) {\n      // Process the chart data from the API\n      // Sort data by date\n      this.sortChartDataByDate(chartData);\n      this.chartData = chartData;\n      this.updateChartOptions();\n    },\n\n    updateChartOptions(chartType = null) {\n      // Set chart options based on the chart type\n      const title = this.getChartTitle();\n      console.log(`Updating chart options with title: ${title}`);\n\n      // Use the provided chart type or the current chart type\n      const type = chartType || this.chartType;\n\n      // Base chart options\n      const options = {\n        title: title,\n        axes: {\n          left: {\n            mapsTo: 'value',\n            title: 'Count',\n            scaleType: 'linear',\n            includeZero: true\n          },\n          bottom: {\n            mapsTo: 'date',\n            title: 'Month',\n            scaleType: 'labels'\n          }\n        },\n        height: '400px',\n        width: '100%',\n        resizable: true,\n        theme: 'g90', // Dark theme to match the imported styles\n        legend: {\n          alignment: 'center',\n          position: 'bottom'\n        },\n        color: {\n          scale: this.getColorScale(type)\n        },\n        toolbar: {\n          enabled: true\n        },\n        data: {\n          groupMapsTo: 'group'\n        },\n        stacked: true\n      };\n\n      // For root cause analysis, add special handling for the target line\n      if (type === 'rootCause') {\n        // Add special handling for the target line\n        options.data.groups = [\n          {\n            name: 'Target',\n            type: 'line'\n          }\n        ];\n\n        // Add corresponding datasets to make the target line work\n        options.data.correspondingDatasets = [\n          {\n            type: 'line',\n            select: (data) => data.group === 'Target'\n          }\n        ];\n      }\n\n      this.chartOptions = options;\n\n      console.log('Chart options updated:', this.chartOptions);\n    },\n\n    getChartTitle() {\n      // Base title based on chart type\n      let baseTitle = '';\n      switch (this.chartType) {\n        case 'validation':\n          baseTitle = 'Validated vs Unvalidated Fails';\n          break;\n        case 'rootCause':\n          baseTitle = 'Fails by Root Cause';\n          break;\n        default:\n          baseTitle = 'Fails Analysis';\n      }\n\n      // Add group name if a specific group is selected\n      if (this.selectedGroup !== 'all') {\n        return `${baseTitle} - ${this.selectedGroup}`;\n      }\n\n      return baseTitle;\n    },\n\n    getColorScale(chartType = null) {\n      // Use the provided chart type or the current chart type\n      const type = chartType || this.chartType;\n\n      // Return the appropriate color scale based on the chart type\n      switch (type) {\n        case 'validation':\n          return {\n            'Validated': '#24a148',\n            'Unvalidated': '#ff832b'\n          };\n        case 'rootCause':\n          return {\n            'KPARS1': '#0f62fe',\n            'DIAG': '#6929c4',\n            'OTHER': '#1192e8',\n            'LINK': '#8a3ffc',\n            'FDB': '#ee5396',\n            'DAP': '#ff7eb6',\n            'CODE': '#fa4d56',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba',\n            'Target': '#f1c21b' // Yellow for target line\n          };\n        default:\n          return {};\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.fails-chart-container {\n  width: 100%;\n}\n\n.chart-controls {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  padding: 1rem 0;\n  min-height: 400px;\n}\n\n.chart-wrapper {\n  height: 400px;\n  width: 100%;\n  overflow: hidden;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n</style>\n"]}]}