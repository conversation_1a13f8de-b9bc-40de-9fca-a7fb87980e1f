<template>
  <div class="dashboard-container">
    <!-- Inherit the MainHeader component -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <main id="main-content" class="main-content">
      <!-- Welcome Section -->
      <div class="welcome-section">
        <h1 class="welcome-title">Welcome, {{ name }}!</h1>
        <p class="welcome-subtitle">Here's your dashboard overview</p>
      </div>

      <!-- Sections Grid Layout -->
      <div class="sections-grid">
        <!-- Section 1: Work Tasks -->
        <div class="section tasks-section">
          <div class="section-header">
            <h2>Work Tasks</h2>
            <div class="badge">{{ todoList.length }}</div>
          </div>
          <div class="section-content">
            <div class="cards-container">
              <cv-tile
                v-for="(item, index) in todoList"
                :key="index"
                class="one-card"
                kind="clickable"
                @click="navigateCard(item)"
              >
                <div class="card-icon">
                  <span class="icon-placeholder">✓</span>
                </div>
                <p class="card-content">{{ item }}</p>
              </cv-tile>
            </div>
            <p class="helper-text">Click items to complete actions</p>
          </div>
        </div>

        <!-- Section 2: Alerts -->
        <div class="section alerts-section">
          <div class="section-header">
            <h2>Alerts</h2>
            <div class="badge alert">{{ alerts.length }}</div>
          </div>
          <div class="section-content">
            <div class="cards-container">
              <cv-tile
                v-for="(alert, index) in alerts"
                :key="index"
                class="one-card alert-card non-clickable"
              >
                <div class="card-icon alert-icon">
                  <span class="icon-placeholder">⚠</span>
                </div>
                <p class="card-content">{{ alert }}</p>
              </cv-tile>
            </div>
          </div>
        </div>

        <!-- Section 3: Messages and Requests -->
        <div class="section messages-section">
          <div class="section-header">
            <h2>Messages & Requests</h2>
            <div class="badge">{{ messages.length }}</div>
          </div>
          <div class="section-content">
            <div class="cards-container">
              <cv-tile
                v-for="(message, index) in messages"
                :key="index"
                class="one-card message-card non-clickable"
              >
                <div class="card-icon message-icon">
                  <span class="icon-placeholder">✉</span>
                </div>
                <p class="card-content">{{ message }}</p>
              </cv-tile>
            </div>
          </div>
        </div>

        <!-- Section 4: Important Dates -->
        <div class="section dates-section">
          <div class="section-header">
            <h2>Important Dates</h2>
          </div>
          <div class="section-content">
            <div class="cards-container">
              <cv-tile
                v-for="(date, index) in importantDates"
                :key="index"
                class="one-card date-card non-clickable"
              >
                <div class="card-icon date-icon">
                  <span class="icon-placeholder">📅</span>
                </div>
                <p class="card-content">{{ date }}</p>
              </cv-tile>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>



<script>
import MainHeader from '@/components/MainHeader'; // Import the MainHeader component
// No need for Carbon icons import, using Unicode symbols instead

export default {
  name: 'MenuPage',
  components: {
    MainHeader
  },
  data() {
    return {
      name: 'User',
      todoList: ['Validate x defects', 'Validate with AI', 'commodity over threshold'],
      alerts: ['X Trend noticed on Parthenon cards in FUL OP 2225. see more ->', 'New version update expected 11/5'],
      messages: ['Message from (name)', 'Request from Dev team'],
      importantDates: ['QRR: Oct 25', 'zMfg Yield Review: Oct 30'],
      expandedSideNav: true,
      useFixed: false,
    };
  },
  methods: {
    navigateTo(route) {
      this.$router.push(route);
    },
    navigateCard(item) {
      if (item.includes('Validate with AI')) {
        this.navigateTo('/validation2');
      } else if (item.includes('Validate x defects')) {
        this.navigateTo('/validations');
      } else if (item.includes('over threshold')) {
        this.navigateTo('/commodity-overview');
      }
    },
  },
};
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  background-color: #161616;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.main-content {
  padding: 1rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  flex: 1;
  overflow: hidden;
}

/* Welcome section styling */
.welcome-section {
  text-align: center;
  margin-bottom: 1rem;
  width: 100%;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #333333;
}

.welcome-title {
  color: #f4f4f4;
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  font-weight: 400;
}

.welcome-subtitle {
  color: #8d8d8d;
  font-size: 0.875rem;
}

/* Grid layout */
.sections-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 1400px;
  flex: 1;
  overflow: hidden;
}

/* Section styling */
.section {
  background-color: #262626;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #333333;
  height: 100%;
}

.section:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #333333;
  border-bottom: 1px solid #444444;
}

.section-header h2 {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 400;
  margin: 0;
}

.section-content {
  padding: 0.75rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Badge styling */
.badge {
  background-color: #0f62fe;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge.alert {
  background-color: #fa4d56;
}

/* Cards styling */
.cards-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-grow: 1;
  overflow-y: auto;
}

.one-card {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #333333;
  border-radius: 6px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  min-height: 40px;
}

.one-card:hover {
  background-color: #3d3d3d;
}

.one-card.alert-card {
  border-left-color: #fa4d56;
}

.one-card.message-card {
  border-left-color: #33b1ff;
}

.one-card.date-card {
  border-left-color: #6fdc8c;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  color: #0f62fe;
  flex-shrink: 0;
}

.icon-placeholder {
  font-size: 1rem;
  line-height: 1;
}

.card-icon.alert-icon {
  color: #fa4d56;
}

.card-icon.message-icon {
  color: #33b1ff;
}

.card-icon.date-icon {
  color: #6fdc8c;
}

.card-content {
  font-size: 0.8rem;
  color: #f4f4f4;
  margin: 0;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.helper-text {
  font-size: 0.7rem;
  color: #8d8d8d;
  text-align: center;
  margin-top: 0.5rem;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .main-content {
    padding: 0.75rem 1.5rem;
  }

  .welcome-section {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .welcome-subtitle {
    font-size: 0.75rem;
  }

  .section-header h2 {
    font-size: 0.9rem;
  }
}

@media (max-width: 1024px) {
  .sections-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }

  .section {
    max-height: 200px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 0.5rem 1rem;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .section-header {
    padding: 0.5rem 0.75rem;
  }

  .section-content {
    padding: 0.5rem;
  }

  .one-card {
    padding: 0.4rem 0.5rem;
    min-height: 36px;
  }

  .card-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.4rem;
  }

  .icon-placeholder {
    font-size: 0.875rem;
  }

  .card-content {
    font-size: 0.75rem;
  }
}
</style>
