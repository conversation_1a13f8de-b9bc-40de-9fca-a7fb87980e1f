const xlsx = require('xlsx');
const path = require('path');
const fs = require('fs');

// Create sample validation data
const sampleData = [
  {
    DEFECT_ID: '12345',
    DESCRIPTION: 'Bent cable on connector J3',
    STAGE: 'Assembly',
    MFSDATE: '2023-05-01'
  },
  {
    DEFECT_ID: '12346',
    DESCRIPTION: 'REF code 0x4F error during boot sequence',
    STAGE: 'Test',
    MFSDATE: '2023-05-02'
  },
  {
    DEFECT_ID: '12347',
    DESCRIPTION: 'Scratched surface on card edge',
    STAGE: 'Inspection',
    MFSDATE: '2023-05-03'
  },
  {
    DEFECT_ID: '12348',
    DESCRIPTION: 'Memory test failure at address 0x8000',
    STAGE: 'Test',
    MFSDATE: '2023-05-04'
  },
  {
    DEFECT_ID: '12349',
    DESCRIPTION: 'Missing screw on heatsink assembly',
    STAGE: 'Assembly',
    MFSDATE: '2023-05-05'
  },
  {
    DEFECT_ID: '12350',
    DESCRIPTION: 'Firmware version mismatch error',
    STAGE: 'Test',
    MFSDATE: '2023-05-06'
  },
  {
    DEFECT_ID: '12351',
    DESCRIPTION: 'Broken pin on connector P5',
    STAGE: 'Inspection',
    MFSDATE: '2023-05-07'
  },
  {
    DEFECT_ID: '12352',
    DESCRIPTION: 'System crash during stress test',
    STAGE: 'Test',
    MFSDATE: '2023-05-08'
  },
  {
    DEFECT_ID: '12353',
    DESCRIPTION: 'Loose connection on power supply',
    STAGE: 'Assembly',
    MFSDATE: '2023-05-09'
  },
  {
    DEFECT_ID: '12354',
    DESCRIPTION: 'Network interface failure',
    STAGE: 'Test',
    MFSDATE: '2023-05-10'
  },
  // Add more recent entries for testing different time filters
  {
    DEFECT_ID: '12355',
    DESCRIPTION: 'Damaged thermal pad on CPU',
    STAGE: 'Assembly',
    MFSDATE: new Date().toISOString().split('T')[0] // Today
  },
  {
    DEFECT_ID: '12356',
    DESCRIPTION: 'Boot sequence error code 0x3F',
    STAGE: 'Test',
    MFSDATE: new Date().toISOString().split('T')[0] // Today
  },
  {
    DEFECT_ID: '12357',
    DESCRIPTION: 'Misaligned connector on motherboard',
    STAGE: 'Inspection',
    MFSDATE: (() => {
      const date = new Date();
      date.setDate(date.getDate() - 2); // 2 days ago
      return date.toISOString().split('T')[0];
    })()
  },
  {
    DEFECT_ID: '12358',
    DESCRIPTION: 'Memory test failure at address 0x9000',
    STAGE: 'Test',
    MFSDATE: (() => {
      const date = new Date();
      date.setDate(date.getDate() - 3); // 3 days ago
      return date.toISOString().split('T')[0];
    })()
  },
  {
    DEFECT_ID: '12359',
    DESCRIPTION: 'Loose heatsink mounting',
    STAGE: 'Assembly',
    MFSDATE: (() => {
      const date = new Date();
      date.setDate(date.getDate() - 5); // 5 days ago
      return date.toISOString().split('T')[0];
    })()
  }
];

// Create a new workbook
const workbook = xlsx.utils.book_new();

// Convert the data to a worksheet
const worksheet = xlsx.utils.json_to_sheet(sampleData);

// Add the worksheet to the workbook
xlsx.utils.book_append_sheet(workbook, worksheet, 'Validation Data');

// Define the output file path
const outputPath = path.join(__dirname, 'app', 'excel', 'validation_data.xlsx');

// Ensure the directory exists
const dir = path.dirname(outputPath);
if (!fs.existsSync(dir)) {
  fs.mkdirSync(dir, { recursive: true });
}

// Write the workbook to a file
xlsx.writeFile(workbook, outputPath);

console.log(`Sample validation data created at: ${outputPath}`);
