/**
 * Log controller for handling client-side logs
 * Writes logs to the appropriate file based on the log type
 */

const logger = require('../utils/logger');

/**
 * Handle client-side logs
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
exports.handleLog = (req, res) => {
  try {
    const { type, message } = req.body;

    if (!type || !message) {
      return res.status(400).json({ 
        status: 'error', 
        message: 'Missing required parameters: type and message' 
      });
    }

    // Log to the appropriate file based on the log type
    if (type === 'info') {
      // Extract source from message if available
      const sourceMatch = message.match(/\[(.*?)\]/);
      const source = sourceMatch && sourceMatch[1] ? sourceMatch[1] : 'Client';
      
      // Log the message
      logger.logInfo(message, 'Client');
    } else if (type === 'error') {
      // Extract source from message if available
      const sourceMatch = message.match(/\[(.*?)\]/);
      const source = sourceMatch && sourceMatch[1] ? sourceMatch[1] : 'Client';
      
      // Log the message
      logger.logError(message, null, 'Client');
    } else {
      return res.status(400).json({ 
        status: 'error', 
        message: 'Invalid log type. Must be "info" or "error"' 
      });
    }

    // Return success response
    return res.status(200).json({ 
      status: 'success', 
      message: 'Log recorded successfully' 
    });
  } catch (error) {
    console.error('Error handling client log:', error);
    return res.status(500).json({ 
      status: 'error', 
      message: 'Internal server error' 
    });
  }
};
