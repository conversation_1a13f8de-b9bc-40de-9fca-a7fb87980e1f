<template>
  <div ref="chartHolder"></div>
  <!-- <ccv-heatmap-chart ref="chartHolder" :data="data" :options="options"></ccv-heatmap-chart> -->
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { HeatmapChart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'HeatMap',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    // loading: {
    //   type: Boolean,
    //   default: true
    // },
  },
  data() {
    return {
      chart: null,
      chartRendered: false,
      options: {
        title: 'Heatmap',
        height: '400px',
        axes: {
          left: {
            title: 'Group',
            mapsTo: 'group',
            scaleType: 'labels',
          },
          bottom: {
            title: 'Period',
            mapsTo: 'Period',
            scaleType: 'labels',
            // timeScale: {
            //   timeInterval: 'monthly'
            // },
            
          },
        },
        heatmap: {
          colorLegend: {
            title: 'XFactor',
          },
        },
        color: {
          gradient: {
            enabled: true,
            colors: [
              '#00ff00', // Pure green for 0
              '#ffff00', // Yellow for 1
              '#ff0000', // Red for values above 1.2
            ],
          },
        },
        toolbar: {
          enabled: true,
        },
        // data: {
        //   loading: this.loading
        // },
      },
    };
  },

  mounted() {
  
  const chartHolder = this.$refs.chartHolder;

  // Initialize the chart
  this.chart = new HeatmapChart(chartHolder, {
    data: this.data,
    options: this.options,
  });

  

  this.updateLegend();
  this.updateHeatmapColors();


  // this.$nextTick(() => {
  //   this.updateLegend();
  //   this.updateHeatmapColors();
  // });

  // Add a resize event listener
  window.addEventListener('resize', this.handleResize);

},

  methods: {
    handleResize() {
      // Update the chart's data and options without destroying it
    if (this.chart) {
      // Clear existing SVG elements in the chart holder
      const oldChart = this.$refs.chartHolder.querySelector('svg');
      if (oldChart) {
        // Clear the old chart's content without removing styles
        while (oldChart.firstChild) {
          oldChart.removeChild(oldChart.firstChild);
          
        }
      }
    

      // add a delay before updating legends and colors
      setTimeout(() => {
        this.$nextTick(() => {
          this.updateLegend();
          this.updateHeatmapColors();
          
        });
      }, 50); // Adjust the timeout duration if needed
    } else {
      // Create a new HeatmapChart if it doesn't exist
      const chartHolder = this.$refs.chartHolder;
      
      this.chart = new HeatmapChart(chartHolder, {
        data: this.data,
        options: this.options,
      });

      this.$nextTick(() => {
        this.updateLegend();
        this.updateHeatmapColors();
      });
    }
  },

    updateLegend() {
      // Select the legend text elements and update their content
      const legendLabels = document.querySelectorAll(".legend .tick text");

      if (legendLabels.length > 2) {
        // Force the first label to 0 and the last to 1.2 (red)
        legendLabels[0].textContent = '0'; // Corresponds to pure green
        legendLabels[legendLabels.length - 1].textContent = '1.2'; // Corresponds to pure red
        // if (legendLabels[1]) {
        //   legendLabels[1].remove(); // Remove the second legend label
        // }

        if (legendLabels[1]) {
          legendLabels[1].textContent = '1.0';
        }
      }

      // Change the title color to white
      const titleTextElement = document.querySelector(".legend-title text");
      if (titleTextElement) {
        titleTextElement.style.setProperty('fill', '#c6c6c6', 'important'); // Use !important as a fallback
      }
    },

    updateHeatmapColors() {
      // Select all heatmap cell groups
      const cells = document.querySelectorAll('g.cell');

      cells.forEach((cell) => {
        const rect = cell.querySelector('rect');

        if (rect) {
          const value = parseFloat(rect.getAttribute('aria-label'));

          let color;
          if (value < 1) {
            const greenToYellowRatio = value / 1;
            const redShade = Math.floor(255 * greenToYellowRatio);
            const greenShade = 255;
            color = `rgb(${redShade}, ${greenShade}, 0)`;
          } else if (value <= 1.2) {
            const yellowToRedRatio = (value - 1) / 0.2;
            const redShade = Math.floor(255 * yellowToRedRatio);
            color = `rgb(255, ${255 - redShade}, 0)`;
          } else {
            color = '#ff0000';
          }

          rect.style.fill = color;

          const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          text.setAttribute('x', rect.getAttribute('x') * 1 + rect.getAttribute('width') / 2);
          text.setAttribute('y', rect.getAttribute('y') * 1 + rect.getAttribute('height') / 2);
          text.setAttribute('dy', '0.35em');
          text.setAttribute('text-anchor', 'middle');
          text.style.fill = 'black';
          text.style.fontSize = '12px';
          text.textContent = value.toFixed(2);
          cell.appendChild(text);
        }
      });
    },
  },
};
</script>
