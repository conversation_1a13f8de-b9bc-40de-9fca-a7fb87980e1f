<template>
  <div class="metis-xfactors-container">
    <MainHeader title="Metis XFactors Analysis" />

    <div class="bx--grid">
      <div class="bx--row">
        <div class="bx--col-lg-16">
          <!-- Global Date Controls -->
          <cv-tile class="global-controls-tile">
            <div class="global-controls">
              <div class="date-controls-container">
                <div class="date-controls-layout">
                  <div class="custom-range-section">
                    <div class="date-inputs">
                      <div class="date-section-label">Start Date:</div>
                      <div class="date-dropdown-container">
                        <cv-dropdown
                          id="start-month"
                          v-model="startMonthStr"
                          @change="updateStartDate"
                          class="month-dropdown"
                        >
                          <cv-dropdown-item
                            v-for="(month, index) in months"
                            :key="index"
                            :value="String(index+1)"
                          >
                            {{ month }}
                          </cv-dropdown-item>
                        </cv-dropdown>
                        <cv-dropdown
                          id="start-year"
                          v-model="startYear"
                          @change="updateStartDate"
                          class="year-dropdown"
                        >
                          <cv-dropdown-item
                            v-for="year in availableYears"
                            :key="year"
                            :value="year"
                          >
                            {{ year }}
                          </cv-dropdown-item>
                        </cv-dropdown>
                      </div>

                      <div class="date-section-label">End Date:</div>
                      <div class="date-dropdown-container">
                        <cv-dropdown
                          id="end-month"
                          v-model="endMonthStr"
                          @change="updateEndDate"
                          class="month-dropdown"
                        >
                          <cv-dropdown-item
                            v-for="(month, index) in months"
                            :key="index"
                            :value="String(index+1)"
                          >
                            {{ month }}
                          </cv-dropdown-item>
                        </cv-dropdown>
                        <cv-dropdown
                          id="end-year"
                          v-model="endYear"
                          @change="updateEndDate"
                          class="year-dropdown"
                        >
                          <cv-dropdown-item
                            v-for="year in availableYears"
                            :key="year"
                            :value="year"
                          >
                            {{ year }}
                          </cv-dropdown-item>
                        </cv-dropdown>
                      </div>
                    </div>

                    <div class="analyze-button-container">
                      <cv-button @click="analyzeAllData">Analyze Data</cv-button>
                    </div>
                  </div>

                  <div class="date-separator">
                    <span class="or-text">OR</span>
                  </div>

                  <div class="quick-select-section">
                    <div class="date-section-label">Quick Select:</div>
                    <cv-dropdown
                      id="quick-select"
                      v-model="selectedTimeRange"
                      @change="applyTimeRange"
                      class="quick-select-dropdown"
                    >
                      <cv-dropdown-item value="">Custom Range</cv-dropdown-item>
                      <cv-dropdown-item value="last-month">Last Month</cv-dropdown-item>
                      <cv-dropdown-item value="last-3-months">Last 3 Months</cv-dropdown-item>
                      <cv-dropdown-item value="last-6-months">Last 6 Months</cv-dropdown-item>
                      <cv-dropdown-item value="ytd">Year to Date</cv-dropdown-item>
                    </cv-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </cv-tile>

          <!-- Tab Navigation -->
          <cv-tabs v-model="activeTab">
            <!-- Heatmap Tab (renamed from Dashboard) -->
            <cv-tab label="Heatmap" :selected="activeTab === 0">
              <cv-tile class="content-tile">
                <div class="dashboard-controls">
                  <div class="dashboard-header">
                    <h3>XFactor Heatmap</h3>
                  </div>

                  <div class="last-updated-text">
                    Last Updated: {{ new Date().toLocaleString() }}
                  </div>
                </div>

                <div class="dashboard-content">
                  <!-- Critical Issues Summary -->
                  <div class="dashboard-critical-issues-summary" v-if="dashboardData.length > 0 && currentMonthCriticalIssues > 0">
                    <h4>
                      <span class="critical-count">{{ currentMonthCriticalIssues }}</span> critical issues detected this month ({{ getCurrentMonthName() }})
                    </h4>
                  </div>

                  <div class="heatmap-container" v-if="dashboardData.length > 0">
                    <div class="heatmap-header">
                      <h4>XFactor Status Heatmap</h4>

                      <div class="heatmap-controls">
                        <div class="filter-container">
                          <div class="filter-type">
                            <label for="filterType">Filter By:</label>
                            <cv-dropdown
                              id="filterType"
                              v-model="selectedFilterType"
                              @change="onFilterTypeChange"
                              class="carbon-dropdown filter-type-dropdown"
                            >
                              <cv-dropdown-item
                                v-for="type in filterTypes"
                                :key="type.value"
                                :value="type.value"
                              >
                                {{ type.text }}
                              </cv-dropdown-item>
                            </cv-dropdown>
                          </div>

                          <div class="owner-filter">
                            <label for="ownerSelect">{{ selectedFilterType === 'group' ? 'Commodity' : selectedFilterType === 'dev_owner' ? 'Dev Owner' : 'PQE Owner' }}:</label>
                            <cv-dropdown
                              id="ownerSelect"
                              v-model="selectedOwner"
                              @change="onOwnerChange"
                              class="carbon-dropdown owner-dropdown"
                            >
                              <cv-dropdown-item
                                v-for="option in ownerOptions[selectedFilterType]"
                                :key="option"
                                :value="option"
                              >
                                {{ option }}
                              </cv-dropdown-item>
                            </cv-dropdown>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="heatmap-legend">
                      <div class="legend-item">
                        <div class="status-indicator" style="background-color: #0062ff;"></div>
                        <span>Normal</span>
                      </div>
                      <div class="legend-item">
                        <div class="status-indicator" style="background-color: #ff9a00;"></div>
                        <span>Sustained Problem</span>
                      </div>
                      <div class="legend-item">
                        <div class="status-indicator" style="background-color: #fa4d56;"></div>
                        <span>Short-Term Spike</span>
                      </div>
                      <div class="legend-item">
                        <div class="status-indicator" style="background-color: #da1e28;"></div>
                        <span>Critical (Both)</span>
                      </div>
                    </div>

                    <div class="heatmap-table-container">
                      <table class="heatmap-table">
                        <thead>
                          <tr>
                            <th>Breakout Group</th>
                            <th v-for="(month, index) in dashboardMonths" :key="index">{{ month }}</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(row, rowIndex) in dashboardData" :key="rowIndex">
                            <td
                              class="breakout-name"
                              @mouseover="showAiSummaryTooltip($event)"
                              @mouseout="hideAiSummaryTooltip()"
                              @click="getAiSummary(row)"
                            >
                              <div class="breakout-name-content">
                                {{ row.breakoutName }}
                                <span class="ai-summary-indicator" v-if="row.breakoutName === loadingAiSummaryFor">
                                  <span class="loading-dots"></span>
                                </span>
                              </div>
                            </td>
                            <td
                              v-for="(cell, cellIndex) in row.months"
                              :key="cellIndex"
                              :class="getCellClass(cell)"
                              @mouseover="showCellTooltip(cell, $event)"
                              @mouseout="hideCellTooltip()"
                              @click="selectBreakoutFromDashboardWithMonth(row.breakoutName, cell.month)"
                              style="cursor: pointer;"
                            >
                              <div class="cell-content">
                                <span v-if="cell.xFactor !== null && cell.xFactor !== undefined">{{ cell.xFactor.toFixed(1) }}</span>
                                <span v-else>0.0</span>
                              </div>
                            </td>
                            <td class="view-data-cell">
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="selectBreakoutFromDashboard(row.breakoutName)"
                              >
                                View Data
                              </cv-button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div class="no-data-message" v-if="dashboardNoDataMessage">
                    {{ dashboardNoDataMessage }}
                  </div>
                </div>

                <!-- Cell tooltip -->
                <div class="cell-tooltip" v-if="showTooltip" :style="tooltipStyle">
                  <div class="tooltip-header">
                    <strong>{{ tooltipData.breakoutName }}</strong>
                    <span>{{ tooltipData.month }}</span>
                  </div>
                  <div class="tooltip-content">
                    <p>XFactor: {{ tooltipData.xFactor !== null && tooltipData.xFactor !== undefined ? tooltipData.xFactor.toFixed(2) : '0.00' }}</p>
                    <p>Target Rate: {{ typeof tooltipData.targetRate === 'number' ? tooltipData.targetRate.toFixed(6) : tooltipData.targetRate }}</p>
                    <p>Status: {{ tooltipData.status || 'Normal' }}</p>
                    <p v-if="tooltipData.duration">Duration: {{ tooltipData.duration }} months</p>
                    <p>Defects: {{ tooltipData.defects !== null && tooltipData.defects !== undefined ? tooltipData.defects : 0 }}</p>
                    <p>Volume: {{ tooltipData.volume !== null && tooltipData.volume !== undefined ? tooltipData.volume : 0 }}</p>
                    <p v-if="tooltipData.criticalIssues > 0" class="critical-issues-tooltip">
                      <strong>{{ tooltipData.criticalIssues }} critical issues detected</strong>
                    </p>
                    <p class="click-to-view-tooltip">Click to view detailed analysis</p>
                  </div>
                </div>
              </cv-tile>
            </cv-tab>

            <!-- XFactor Analysis Tab (renamed to Overall) -->
            <cv-tab label="Overall" :selected="activeTab === 1">
              <cv-tile class="content-tile">
                <div class="controls-section">
                  <div class="selected-date-range">
                    <p>Selected Date Range: {{ formatDateRange(startDate, endDate) }}</p>
                  </div>
                </div>

                <div class="breakout-selection">
                  <h4>Select Breakout Group</h4>
                  <div class="breakout-dropdown">
                    <cv-dropdown
                      v-model="selectedBreakout"
                      @change="handleBreakoutChange"
                      :disabled="isLoading || breakoutNames.length === 0"
                    >
                      <cv-dropdown-item value="">All Breakout Groups ({{ breakoutNames.length }} total)</cv-dropdown-item>
                      <cv-dropdown-item
                        v-for="name in breakoutNames"
                        :key="name"
                        :value="name"
                      >
                        {{ name }}
                      </cv-dropdown-item>
                    </cv-dropdown>
                    <div v-if="isLoading" class="loading-indicator">Loading...</div>
                    <div v-else-if="breakoutNames.length === 0 && !isLoading" class="no-data-message">
                      No breakout groups available
                    </div>
                  </div>

                  <!-- Part Numbers Section for Main Tab -->
                  <div class="part-numbers-section" v-if="selectedBreakout && mainTabPartNumbers.length > 0">
                    <h4>Part Numbers in {{ selectedBreakout }}</h4>
                    <p class="part-count">Total: {{ mainTabPartNumbers.length }} part numbers</p>
                    <div class="part-numbers-container">
                      <div v-for="(pn, index) in mainTabPartNumbers" :key="index" class="part-number-item">
                        {{ pn }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="charts-section">
                  <div class="chart-container" v-if="chartData.length > 0">
                    <h4>XFactor Trend Analysis</h4>
                    <div class="chart-wrapper">
                      <LineChart
                        :data="chartData"
                        :options="chartOptions"
                        :loading="isLoading"
                      />
                    </div>

                    <div class="threshold-info">
                      <div class="threshold-item">
                        <div class="threshold-color sustained"></div>
                        <span>Sustained Problem Threshold (X-Factor > 1.5 for 3+ months)</span>
                      </div>
                      <div class="threshold-item">
                        <div class="threshold-color spike"></div>
                        <span>Short-Term Spike Threshold (X-Factor > 3.0)</span>
                      </div>
                    </div>

                    <div class="info-button" @click="showInfoModal = true">
                      <span>ⓘ</span>
                    </div>
                  </div>
                </div>

                <div class="alerts-container" v-if="alerts.length > 0">
                  <h4>Alerts</h4>
                  <table class="alerts-table">
                    <thead>
                      <tr>
                        <th>Breakout Group</th>
                        <th>Status</th>
                        <th>Period</th>
                        <th>X-Factor</th>
                        <th>Defects</th>
                        <th>Volume</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(alert, index) in alerts" :key="index">
                        <td>{{ alert.breakoutName }}</td>
                        <td>
                          <div class="status-indicator" :class="getStatusClass(alert.status)">
                            {{ alert.status }}
                          </div>
                        </td>
                        <td>{{ alert.period }}</td>
                        <td>{{ alert.xFactor.toFixed(2) }}</td>
                        <td>{{ alert.defects }}</td>
                        <td>{{ alert.volume }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div class="no-data-message" v-if="noDataMessage">
                  {{ noDataMessage }}
                </div>
              </cv-tile>
            </cv-tab>



            <!-- PQE Dashboard Tab -->
            <cv-tab label="PQE Dashboard" :selected="activeTab === 2">
              <PQEDashboard />
            </cv-tab>

            <!-- Group Tab -->
            <cv-tab label="Group" :selected="activeTab === 3">
              <cv-tile class="content-tile">
                <div class="group2-tab-container">
                  <div class="group2-tab-header">
                    <h4>Advanced Group Analysis</h4>
                    <p>Analyze breakout groups with different time periods and analysis types</p>
                  </div>

                  <div class="group2-tab-controls">
                    <div class="breakout-group-select">
                      <label for="group2-tab-select">Select Breakout Group:</label>
                      <cv-dropdown
                        id="group2-tab-select"
                        v-model="group2TabSelectedGroup"
                        :disabled="isGroup2TabLoading || breakoutNames.length === 0"
                      >
                        <cv-dropdown-item
                          v-for="name in breakoutNames"
                          :key="name"
                          :value="name"
                        >
                          {{ name }}
                        </cv-dropdown-item>
                      </cv-dropdown>
                    </div>
                  </div>

                  <div class="group2-tab-content" v-if="group2TabSelectedGroup">
                    <div class="analysis-grid">
                      <table class="analysis-table">
                        <thead>
                          <tr>
                            <th>Analysis Type</th>
                            <th>1 Month</th>
                            <th>3 Months</th>
                            <th>6 Months</th>
                            <th>Custom</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>Vintage Analysis</td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewVintageAnalysis(1)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewVintageAnalysis(3)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewVintageAnalysis(6)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewCustomVintageAnalysis()"
                              >
                                View
                              </cv-button>
                            </td>
                          </tr>
                          <tr>
                            <td>Root Cause Analysis</td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewRootCauseAnalysis(1)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewRootCauseAnalysis(3)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewRootCauseAnalysis(6)"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                @click="viewCustomRootCauseAnalysis()"
                              >
                                View
                              </cv-button>
                            </td>
                          </tr>
                          <tr>
                            <td>Supplier Analysis</td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                :disabled="true"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                :disabled="true"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                :disabled="true"
                              >
                                View
                              </cv-button>
                            </td>
                            <td>
                              <cv-button
                                kind="tertiary"
                                size="small"
                                :disabled="true"
                                @click="viewCustomSupplierAnalysis()"
                              >
                                View
                              </cv-button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <!-- Vintage Analysis Section -->
                    <div class="vintage-section" v-if="vintageChartData.length > 0 || isVintageDataLoading">
                      <div class="section-header">
                        <h4>Vintage Analysis - {{ group2TabSelectedGroup }} ({{ vintageTimeRange }})</h4>
                      </div>

                      <!-- AI Summary Section -->
                      <div class="ai-summary-section" v-if="vintageAiSummary">
                        <div class="ai-summary-content">
                          <p>{{ vintageAiSummary }}</p>
                        </div>
                      </div>
                      <div class="ai-summary-loading" v-else-if="isVintageAiLoading">
                        <div class="loading-spinner"></div>
                        <span>Generating AI analysis...</span>
                      </div>

                      <!-- Chart Section -->
                      <div class="vintage-chart-container" v-if="vintageChartData.length > 0">
                        <h5>Vintage Months</h5>
                        <div class="chart-wrapper">
                          <StackedBarChart
                            :data="vintageChartData"
                            :loading="isVintageDataLoading"
                            :height="'400px'"
                            :options="vintageChartOptions"
                            @bar-click="handleVintageBarClick"
                          />
                        </div>
                      </div>
                      <div class="no-data-message" v-else-if="isVintageDataLoading">
                        Loading vintage data...
                      </div>
                      <div class="no-data-message" v-else>
                        No vintage data available for this time period
                      </div>
                    </div>

                    <!-- Root Cause Analysis Section -->
                    <div class="root-cause-section" v-if="rootCauseChartData.length > 0 || isRootCauseDataLoading">
                      <div class="section-header">
                        <h4>Root Cause Analysis - {{ group2TabSelectedGroup }} ({{ rootCauseTimeRange }})</h4>
                      </div>

                      <!-- AI Summary Section - Moved above chart -->
                      <div class="ai-summary-section" v-if="rootCauseAiSummary">
                        <div class="ai-summary-content">
                          <p>{{ rootCauseAiSummary }}</p>
                        </div>
                      </div>
                      <div class="ai-summary-loading" v-else-if="isRootCauseAiLoading">
                        <div class="loading-spinner"></div>
                        <span>Generating AI analysis...</span>
                      </div>

                      <!-- Chart Section -->
                      <div class="root-cause-chart-container" v-if="rootCauseChartData.length > 0">
                        <h5>Root Cause Categories</h5>
                        <div class="chart-wrapper">
                          <StackedBarChart
                            :data="rootCauseChartData"
                            :loading="isRootCauseDataLoading"
                            :height="'400px'"
                            :options="rootCauseChartOptions"
                            @bar-click="handleRootCauseBarClick"
                          />
                        </div>
                      </div>
                      <div class="no-data-message" v-else-if="isRootCauseDataLoading">
                        Loading root cause data...
                      </div>
                      <div class="no-data-message" v-else>
                        No root cause data available for this time period
                      </div>

                      <!-- Critical Issues Section -->
                      <div class="critical-issues-section" v-if="hasCriticalRootCauseIssue">
                        <h5>Critical Issues Detected</h5>

                        <!-- Critical Issues List -->
                        <div v-for="issue in criticalIssues" :key="issue.id" class="critical-issue-item">
                          <div class="critical-issue-header" @click="toggleCriticalIssue(issue.id)">
                            <cv-tag
                              :kind="issue.severity === 'high' ? 'red' : 'magenta'"
                              :label="issue.severity === 'high' ? 'High Severity' : 'Medium Severity'"
                            />
                            <span class="critical-issue-title">{{ issue.category }}</span>
                            <cv-tag
                              kind="cool-gray"
                              class="month-tag"
                              :label="issue.month"
                            />
                            <span class="critical-issue-multiplier">
                              <template v-if="issue.increaseMultiplier === '(new)'">
                                ({{ issue.increaseMultiplier }})
                              </template>
                              <template v-else>
                                ({{ issue.increaseMultiplier }}x spike)
                              </template>
                            </span>
                            <span class="critical-issue-status" v-if="hasCriticalIssueBeenUpdated(issue.id)">
                              <cv-tag kind="green" label="Updated" />
                            </span>
                            <span class="expand-icon">{{ isIssueExpanded(issue.id) ? '▼' : '▶' }}</span>
                          </div>

                          <div v-if="isIssueExpanded(issue.id)" class="critical-issue-content">
                            <!-- AI Description -->
                            <div class="critical-issue-ai-description">
                              <p>{{ issue.description }}</p>
                            </div>

                            <!-- Update Form -->
                            <div class="critical-issue-update-form">
                              <h6>Provide an update on this issue:</h6>
                              <cv-text-area
                                :value="getCriticalIssueUpdateText(issue.id)"
                                @input="updateCriticalIssueText(issue.id, $event)"
                                placeholder="Enter your update on this critical issue..."
                                :label="'Update for ' + issue.category"
                                hide-label
                                rows="3"
                              />
                              <cv-button
                                @click="saveCriticalIssueUpdate(issue.id, getCriticalIssueUpdateText(issue.id))"
                                :disabled="!getCriticalIssueUpdateText(issue.id).trim()"
                                class="save-update-button"
                              >
                                Save Update
                              </cv-button>
                            </div>

                            <!-- Previous Updates -->
                            <div class="previous-updates" v-if="getCriticalIssueHistory(issue.id).length > 0">
                              <h6>Previous Updates</h6>
                              <cv-structured-list condensed>
                                <template slot="headings">
                                  <cv-structured-list-heading>Date</cv-structured-list-heading>
                                  <cv-structured-list-heading>Update</cv-structured-list-heading>
                                </template>
                                <template slot="items">
                                  <cv-structured-list-row
                                    v-for="(update, index) in getCriticalIssueHistory(issue.id)"
                                    :key="index"
                                  >
                                    <cv-structured-list-cell>{{ formatDate(update.timestamp) }}</cv-structured-list-cell>
                                    <cv-structured-list-cell>{{ update.content }}</cv-structured-list-cell>
                                  </cv-structured-list-row>
                                </template>
                              </cv-structured-list>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="no-data-message" v-if="!group2TabSelectedGroup">
                    Please select a breakout group to analyze
                  </div>
                </div>
              </cv-tile>
            </cv-tab>




          </cv-tabs>
        </div>
      </div>
    </div>

    <!-- Info Modal -->
    <div class="info-modal" v-if="showInfoModal">
      <div class="modal-content">
        <span class="close-button" @click="showInfoModal = false">&times;</span>
        <h3>About XFactor Analysis</h3>
        <p>
          The XFactor is a measure that compares the current defect rate to the target defect rate.
          It helps identify when a part group is experiencing unusual quality issues.
        </p>
        <h4>How XFactor is Calculated:</h4>
        <p>
          XFactor = Current Period Defect Rate / Target Rate
        </p>
        <p>
          Target rates are defined for each breakout group in the breakout_targets.xlsx file.
        </p>
        <h4>Alert Thresholds:</h4>
        <ul>
          <li><strong>Short-Term Spike:</strong> XFactor > 3.0 in a single month</li>
          <li><strong>Sustained Problem:</strong> XFactor > 1.5 for three or more consecutive months</li>
        </ul>
        <p>
          The data is grouped by "Full Breakout Name" from the Metis test data, allowing for analysis
          of quality trends across different part categories.
        </p>
      </div>
    </div>

    <!-- AI Summary Modal -->
    <div class="info-modal" v-if="showAiSummaryModal">
      <div class="modal-content ai-summary-modal">
        <span class="close-button" @click="showAiSummaryModal = false">&times;</span>
        <h3>AI Summary: {{ aiSummaryBreakoutName }}</h3>
        <div v-if="isLoadingAiSummary" class="ai-summary-loading">
          <div class="loading-spinner"></div>
          <p>Generating AI summary...</p>
        </div>
        <div v-else class="ai-summary-content">
          <p v-html="aiSummaryText"></p>
        </div>
      </div>
    </div>

    <!-- AI Summary Tooltip -->
    <div class="ai-tooltip" v-if="showAiTooltip" :style="aiTooltipStyle">
      <div class="ai-tooltip-content">
        <span>AI summary, click to see</span>
      </div>
    </div>

    <!-- Failure Modes Modal -->
    <cv-modal
      :visible="showFailureModesModal"
      @modal-hidden="showFailureModesModal = false"
      class="failure-modes-modal"
    >
      <template slot="title">
        Failure Modes Analysis - {{ selectedCategory }} ({{ selectedMonth }})
      </template>
      <template slot="content">
        <div class="failure-modes-content">
          <div class="failure-modes-chart-container" v-if="failureModesChartData.length > 0">
            <HBarChart
              :data="failureModesChartData"
              :loading="isFailureModesLoading"
              :height="'400px'"
              :title="`Failure Modes for ${selectedCategory} (${selectedMonth})`"
            />
          </div>
          <div class="no-data-message" v-else-if="isFailureModesLoading">
            Loading failure modes data...
          </div>
          <div class="no-data-message" v-else>
            No failure modes data available for this category and month.
          </div>
        </div>
      </template>
      <template slot="secondary-button">
        Close
      </template>
    </cv-modal>

    <!-- Custom Date Modal -->
    <cv-modal
      :visible="showCustomDateModal"
      @modal-hidden="hideCustomDateModal"
      class="custom-date-modal"
    >
      <template slot="title">
        Custom Date Range for {{ customDateAnalysisType }} Analysis
      </template>
      <template slot="content">
        <div class="custom-date-content">
          <div class="date-inputs">
            <div class="date-section">
              <div class="date-section-label">Start Date:</div>
              <div class="date-dropdown-container">
                <cv-dropdown
                  id="custom-start-month"
                  v-model="customStartMonthStr"
                  class="month-dropdown"
                >
                  <cv-dropdown-item
                    v-for="(month, index) in months"
                    :key="index"
                    :value="String(index+1)"
                  >
                    {{ month }}
                  </cv-dropdown-item>
                </cv-dropdown>
                <cv-dropdown
                  id="custom-start-year"
                  v-model="customStartYear"
                  class="year-dropdown"
                >
                  <cv-dropdown-item
                    v-for="year in availableYears"
                    :key="year"
                    :value="year"
                  >
                    {{ year }}
                  </cv-dropdown-item>
                </cv-dropdown>
              </div>
            </div>

            <div class="date-section">
              <div class="date-section-label">End Date:</div>
              <div class="date-dropdown-container">
                <cv-dropdown
                  id="custom-end-month"
                  v-model="customEndMonthStr"
                  class="month-dropdown"
                >
                  <cv-dropdown-item
                    v-for="(month, index) in months"
                    :key="index"
                    :value="String(index+1)"
                  >
                    {{ month }}
                  </cv-dropdown-item>
                </cv-dropdown>
                <cv-dropdown
                  id="custom-end-year"
                  v-model="customEndYear"
                  class="year-dropdown"
                >
                  <cv-dropdown-item
                    v-for="year in availableYears"
                    :key="year"
                    :value="year"
                  >
                    {{ year }}
                  </cv-dropdown-item>
                </cv-dropdown>
              </div>
            </div>
          </div>

          <div class="custom-date-buttons">
            <cv-button @click="applyCustomDateRange">Apply</cv-button>
            <cv-button kind="secondary" @click="hideCustomDateModal">Cancel</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>


  </div>
</template>

<script>
import MainHeader from '@/components/MainHeader';
import LineChart from '@/components/LineChart/LineChart.vue';
import StackedBarChart from '@/components/StackedBarChart.vue';
import HBarChart from '@/components/HBarChart';
import PQEDashboard from '@/components/PQEDashboard/PQEDashboard.vue';
import axios from 'axios';
import { formatDashboardSummaryPrompt } from '@/utils/watsonxPrompts';
import logger from '@/utils/logger';
import {
  CvDropdown,
  CvDropdownItem,
  CvButton,
  CvModal,
  CvTile,
  CvTag,
  CvTextArea,
  CvStructuredList,
  CvStructuredListRow,
  CvStructuredListCell,
  CvStructuredListHeading
} from '@carbon/vue';

export default {
  name: 'MetisXFactors',
  components: {
    MainHeader,
    LineChart,
    StackedBarChart,
    HBarChart,
    PQEDashboard,
    CvDropdown,
    CvDropdownItem,
    CvButton,
    CvModal,
    CvTile,
    CvTag,
    CvTextArea,
    CvStructuredList,
    CvStructuredListRow,
    CvStructuredListCell,
    CvStructuredListHeading
  },
  data() {
    return {
      // Modal visibility
      showCustomDateModal: false,

      // Main tab data
      startDate: '',
      endDate: '',
      minDate: '2024-01', // January 2024
      maxDate: new Date().toISOString().split('T')[0].substring(0, 7), // YYYY-MM format
      // Month and year dropdowns
      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      availableYears: [],
      startMonth: 1,
      startMonthStr: '1',
      startYear: '2024', // String for Carbon Vue components
      endMonth: new Date().getMonth() + 1,
      endMonthStr: String(new Date().getMonth() + 1),
      endYear: String(new Date().getFullYear()), // String for Carbon Vue components
      selectedTimeRange: '',
      breakoutNames: [],
      selectedBreakout: '',
      xFactorData: {},
      chartData: [],
      alerts: [],
      mainTabPartNumbers: [],
      showInfoModal: false,
      noDataMessage: '',
      isLoading: false,

      // Breakout tab data
      breakoutTabSelectedGroup: '',
      breakoutTabStartDate: '',
      breakoutTabEndDate: '',
      breakoutTabTimeRange: 'last-month',
      // Breakout tab month and year dropdowns
      breakoutTabStartMonth: 1,
      breakoutTabStartYear: '2024', // String for Carbon Vue components
      breakoutTabEndMonth: new Date().getMonth() + 1,
      breakoutTabEndYear: String(new Date().getFullYear()), // String for Carbon Vue components
      breakoutTabChartData: [],
      breakoutTabBarChartData: [],
      breakoutTabAlerts: [],
      breakoutTabPartNumbers: [],
      breakoutTabNoDataMessage: '',
      isBreakoutTabLoading: false,
      breakoutTabAllXFactorData: {},

      // Dashboard tab data
      dashboardData: [],
      dashboardMonths: [],
      dashboardNoDataMessage: '',
      isDashboardLoading: false,
      showTooltip: false,
      tooltipStyle: {
        top: '0px',
        left: '0px'
      },
      tooltipData: {
        breakoutName: '',
        month: '',
        xFactor: null,
        status: '',
        duration: null,
        defects: null,
        volume: null,
        criticalIssues: 0
      },
      currentMonthCriticalIssues: 0,
      // Owner filter dropdowns
      filterTypes: [
        { value: 'group', text: 'Commodity' },
        { value: 'dev_owner', text: 'Dev Owner' },
        { value: 'pqe_owner', text: 'PQE Owner' }
      ],
      selectedFilterType: 'group',
      ownerOptions: {
        group: [],
        dev_owner: [],
        pqe_owner: []
      },
      selectedOwner: 'All',

      // AI Summary data
      showAiTooltip: false,
      aiTooltipStyle: {
        top: '0px',
        left: '0px'
      },
      showAiSummaryModal: false,
      aiSummaryBreakoutName: '',
      aiSummaryText: '',
      isLoadingAiSummary: false,
      loadingAiSummaryFor: '',

      // AI Test tab data
      selectedAiModel: 'ibm/granite-13b-instruct-v2',
      aiTemperature: 0.7,
      aiPrompt: '',
      aiResponse: '',
      isAiLoading: false,

      // Category Analysis tab data
      categoryTabSelectedGroup: '',
      categoryTabChartData: [],
      categoryTabCategories: [],
      categoryTabPartNumbers: [],
      categoryTabNoDataMessage: '',
      isCategoryTabLoading: false,
      categoryChartOptions: {
        title: 'Defect Categories by Month',
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true,
            // Domain will be set dynamically based on data
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        bars: {width: 60},
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true
        },
        tooltip: {
          enabled: true
        },
        color: {
          scale: {}
        },
        data: {
          onclick: (data) => {
            console.log('Chart clicked from options:', data);
            this.handleCategoryBarClick(data);
          }
        }
      },

      // Group 2 Tab data
      group2TabSelectedGroup: '',
      group2TabNoDataMessage: '',
      isGroup2TabLoading: false,

      // Custom Date Modal data
      customDateAnalysisType: '',
      customStartMonthStr: String(new Date().getMonth() + 1),
      customStartYear: String(new Date().getFullYear()),
      customEndMonthStr: String(new Date().getMonth() + 1),
      customEndYear: String(new Date().getFullYear()),

      // Vintage Analysis data
      vintageTimeRange: '',
      vintageMonths: 1,
      vintageStartDate: '',
      vintageEndDate: '',
      vintageChartData: [],
      isVintageDataLoading: false,
      vintageAiSummary: '',
      isVintageAiLoading: false,
      hasCriticalVintageIssue: false,
      vintageChartOptions: {
        title: 'Vintage Months by Month',
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true
            // Domain will be set dynamically based on data
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        color: {
          scale: {}  // Will be populated dynamically based on vintage months
        },
        data: {
          onclick: (data) => {
            console.log('Vintage chart clicked:', data);
            this.handleVintageBarClick(data);
          }
        }
      },

      // Root Cause Analysis data
      rootCauseTimeRange: '',
      rootCauseMonths: 1,
      rootCauseStartDate: '',
      rootCauseEndDate: '',
      rootCauseChartData: [],
      isRootCauseDataLoading: false,
      rootCauseAiSummary: '',
      isRootCauseAiLoading: false,
      hasCriticalRootCauseIssue: false,
      criticalIssueDescription: '',
      criticalIssues: [], // Array of critical issues with details
      criticalIssueUpdates: {}, // Object mapping issue ID to updates
      expandedIssues: {}, // Track which issues are expanded
      rootCauseChartOptions: {
        title: 'Root Cause Categories by Month',
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true
            // Domain will be set dynamically based on data
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        bars: {width: 60},
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true
        },
        tooltip: {
          enabled: true,
          customHTML: (dataPoints) => {
            if (!dataPoints || dataPoints.length === 0) return '';

            const dataPoint = dataPoints[0];
            const isCritical = dataPoint.data && dataPoint.data.isCritical;
            const category = dataPoint.group;

            return `
              <div class="custom-tooltip">
                <p><strong>${category}</strong>${isCritical ? ' <span style="color: #fa4d56;">(Critical)</span>' : ''}</p>
                <p>Month: ${dataPoint.key}</p>
                <p>Fail Rate: ${dataPoint.value.toFixed(2)}%</p>
                <p>Defects: ${dataPoint.data && dataPoint.data.defects ? dataPoint.data.defects : 0}</p>
                <p>Volume: ${dataPoint.data && dataPoint.data.volume ? dataPoint.data.volume.toLocaleString() : 'N/A'}</p>
              </div>
            `;
          }
        },
        color: {
          scale: {}  // Will be populated dynamically based on categories
        },
        data: {
          onclick: (data) => {
            console.log('Root cause chart clicked:', data);
            this.handleRootCauseBarClick(data);
          }
        }
      },

      // Failure Modes Modal data
      showFailureModesModal: false,
      selectedMonth: '',
      selectedCategory: '',

      // Selected month for dashboard navigation
      selectedDashboardMonth: '',

      // Active tab index for programmatic tab switching
      activeTab: 0,
      failureModesChartData: [],
      isFailureModesLoading: false,
      failureModesChartOptions: {
        title: 'Failure Modes Analysis',
        axes: {
          left: {
            title: 'Count',
            mapsTo: 'value',
            scaleType: 'linear'
          },
          right: {
            title: 'Cumulative %',
            mapsTo: 'percentage',
            scaleType: 'linear',
            domain: [0, 100]
          },
          bottom: {
            title: 'Failure Mode',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true
        },
        tooltip: {
          enabled: true,
          customHTML: (dataPoints) => {
            // Find the Count data point
            const countPoint = dataPoints.find(point => point.group === 'Count');
            // Find the Percentage data point
            const percentPoint = dataPoints.find(point => point.group === 'Cumulative %');

            if (!countPoint) return '';

            return `
              <div class="custom-tooltip">
                <p><strong>${countPoint.key}</strong></p>
                <p>Count: ${countPoint.value}</p>
                ${percentPoint ? `<p>Cumulative %: ${percentPoint.value.toFixed(1)}%</p>` : ''}
                ${countPoint.data.category ? `<p>Category: ${countPoint.data.category}</p>` : ''}
              </div>
            `;
          }
        },
        color: {
          scale: {
            'Count': '#0f62fe',
            'Cumulative %': '#da1e28'
          }
        },
        comboChartTypes: [
          {
            type: 'simple-bar',
            options: {
              fillColors: ['#0f62fe']
            },
            correspondingDatasets: ['Count']
          },
          {
            type: 'line',
            options: {
              points: {
                enabled: true,
                radius: 5
              },
              strokeWidth: 2
            },
            correspondingDatasets: ['Cumulative %']
          }
        ]
      },
      chartOptions: {
        title: 'XFactor Trend by Breakout Group',
        axes: {
          bottom: {
            title: 'Period',
            mapsTo: 'date',
            scaleType: 'time',
            domain: [new Date('2024-01-01'), new Date('2025-12-31')], // Set explicit domain to show all dates
            ticks: {
              number: 12 // Show more ticks for better readability
            },
            formatters: {
              tick: (date) => {
                // Format the date to show month and year
                const d = new Date(date);
                return d.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
              }
            }
          },
          left: {
            title: 'XFactor',
            mapsTo: 'value',
            scaleType: 'linear',
            domain: [0, 5],
            thresholds: [
              {
                value: 1.5,
                label: 'Sustained Problem Threshold',
                fillColor: 'rgba(255, 204, 0, 0.2)',
                strokeColor: '#FFCC00'
              },
              {
                value: 3.0,
                label: 'Short-Term Spike Threshold',
                fillColor: 'rgba(255, 0, 0, 0.2)',
                strokeColor: '#FF0000'
              }
            ]
          }
        },
        curve: 'curveMonotoneX',
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true,
          truncation: {
            type: 'end',
            threshold: 20
          }
        },
        tooltip: {
          enabled: true,
          customHTML: (dataPoints) => {
            const dataPoint = dataPoints[0];
            if (!dataPoint) return '';

            // Ensure we're working with a proper date object
            const date = dataPoint.date instanceof Date ? dataPoint.date : new Date(dataPoint.date);
            const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });

            // Get target rate if available
            const targetRate = dataPoint.data && dataPoint.data.targetRate ?
              dataPoint.data.targetRate :
              (this.xFactorData[dataPoint.group] && this.xFactorData[dataPoint.group].targetRate ?
                this.xFactorData[dataPoint.group].targetRate : 'N/A');

            return `
              <div class="custom-tooltip">
                <p><strong>${dataPoint.group}</strong></p>
                <p>Period: ${formattedDate}</p>
                <p>XFactor: ${dataPoint.value.toFixed(2)}</p>
                <p>Target Rate: ${typeof targetRate === 'number' ? targetRate.toFixed(6) : targetRate}</p>
                ${dataPoint.data.defects ? `<p>Defects: ${dataPoint.data.defects}</p>` : ''}
                ${dataPoint.data.volume ? `<p>Volume: ${dataPoint.data.volume}</p>` : ''}
              </div>
            `;
          }
        },
        color: {
          scale: {
            'Ariel HLA': '#0F62FE',
            'Parthenon Base': '#6929C4',
            'Metis': '#1192E8',
            'Themis': '#005D5D',
            'Felis HLA': '#9F1853',
            'Petra HLA': '#FA4D56',
            'Orion Base': '#570408',
            'Optic': '#198038',
            'Victoria Crypto HLA': '#002D9C'
          }
        },
        data: {
          loading: this.isLoading
        },
        zoomBar: {
          top: {
            enabled: true
          }
        }
      }
    };
  },
  watch: {
    activeTab(newVal) {
      console.log(`Active tab changed to: ${newVal}`);

      // If switching to the Group tab and we have a selected breakout group,
      // make sure the data is loaded
      if (newVal === 3 && this.group2TabSelectedGroup) {
        console.log(`Loading Group tab data for ${this.group2TabSelectedGroup}`);

        // If we have a selected month, use that to determine the time period
        if (this.selectedDashboardMonth) {
          console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);
          this.viewRootCauseAnalysis(1);
        } else {
          console.log('No specific month selected, showing 3 months of data');
          this.viewRootCauseAnalysis(3);
        }
      }
    },

    selectedProcess(newVal) {
      console.log(`Process changed to: ${newVal}`);
      this.loadBreakoutGroups();
    },

    group2TabSelectedGroup(newVal) {
      console.log(`Group tab selected group changed to: ${newVal}`);
      if (newVal && this.activeTab === 3) {
        // If we're on the Group tab and a group is selected, load the data
        this.analyzeGroup2Data();
      }
    }
  },
  created() {
    // Set default date range (last 6 months)
    const today = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);

    // Initialize available years (2024 to 2025)
    // Including 2025 since the API returns data for 2025
    // Convert years to strings for Carbon Vue components
    for (let year = 2024; year <= 2025; year++) {
      this.availableYears.push(String(year));
    }

    // Set default values for dropdowns
    this.startMonth = sixMonthsAgo.getMonth() + 1; // 1-based month
    this.startMonthStr = String(this.startMonth);
    this.startYear = String(sixMonthsAgo.getFullYear()); // Convert to string for Carbon Vue
    this.endMonth = today.getMonth() + 1; // 1-based month
    this.endMonthStr = String(this.endMonth);
    this.endYear = String(today.getFullYear()); // Convert to string for Carbon Vue

    // Format dates as YYYY-MM for internal use
    this.startDate = this.formatMonthDate(sixMonthsAgo);
    this.endDate = this.formatMonthDate(today);

    // Set default date range for breakout tab (last month)
    const lastMonth = new Date();
    lastMonth.setMonth(today.getMonth() - 1);

    // Set default values for breakout tab dropdowns
    this.breakoutTabStartMonth = lastMonth.getMonth() + 1; // 1-based month
    this.breakoutTabStartYear = String(lastMonth.getFullYear()); // Convert to string for Carbon Vue
    this.breakoutTabEndMonth = today.getMonth() + 1; // 1-based month
    this.breakoutTabEndYear = String(today.getFullYear()); // Convert to string for Carbon Vue

    this.breakoutTabStartDate = this.formatMonthDate(lastMonth);
    this.breakoutTabEndDate = this.formatMonthDate(today);

    // Set min and max dates in the correct format
    this.minDate = '2024-01'; // January 2024
    this.maxDate = this.formatMonthDate(today);

    // Load breakout names and owners data
    this.loadBreakoutNames();
    this.loadOwners();

    // Automatically analyze data on page load
    this.$nextTick(() => {
      this.analyzeData();

      // Load dashboard data with last 6 months
      this.loadDashboardData();
    });
  },
  mounted() {
    // Add direct click handler for the chart
    this.$nextTick(() => {
      this.setupChartClickHandlers();
    });
  },
  methods: {
    // Helper method to format date as YYYY-MM for month input
    formatMonthDate(date) {
      const year = date.getFullYear();
      // Month needs to be padded with leading zero if less than 10
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },

    // Helper method to get the first day of a month from YYYY-MM format
    getFirstDayOfMonth(yearMonth) {
      if (!yearMonth || yearMonth.length < 7) return null;

      // Extract year and month directly from the string to avoid timezone issues
      const [year, month] = yearMonth.split('-');

      console.log(`Getting first day of month for ${yearMonth}`);
      console.log(`Year: ${year}, Month: ${month}`);

      // Return the first day of the month in YYYY-MM-DD format
      return `${year}-${month}-01`;
    },

    // Helper method to get the last day of a month from YYYY-MM format
    getLastDayOfMonth(yearMonth) {
      if (!yearMonth || yearMonth.length < 7) return null;

      // Extract year and month directly from the string to avoid timezone issues
      const [year, month] = yearMonth.split('-');

      console.log(`Getting last day of month for ${yearMonth}`);
      console.log(`Year: ${year}, Month: ${month}`);

      // Calculate the last day of the month
      // For month 12 (December), we need to handle the year change
      const nextMonth = parseInt(month) === 12 ? 1 : parseInt(month) + 1;
      const nextMonthYear = parseInt(month) === 12 ? parseInt(year) + 1 : parseInt(year);

      // Create a date for the first day of the next month, then subtract one day
      // Use UTC methods to avoid timezone issues
      const lastDay = new Date(Date.UTC(nextMonthYear, nextMonth - 1, 0));
      const lastDayOfMonth = lastDay.getUTCDate();

      console.log(`Last day of month: ${lastDayOfMonth}`);

      // Return the last day of the month in YYYY-MM-DD format
      return `${year}-${month}-${String(lastDayOfMonth).padStart(2, '0')}`;
    },

    // Helper method to get authentication config
    getAuthConfig() {
      const config = {};
      const token = localStorage.getItem('token');

      // For debugging
      console.log('Token from localStorage:', token ? 'Token exists' : 'No token found');

      if (token) {
        config.headers = {
          'Authorization': `Bearer ${token}`
        };
        console.log('Using authentication token for request');
      } else {
        // For testing purposes, we'll proceed without authentication
        // since we've configured the server to skip authentication for Metis routes
        console.log('No authentication token available, proceeding without authentication');
      }

      return config;
    },

    loadOwners() {
      console.log('Loading owners data...');

      // Get authentication config
      const config = this.getAuthConfig();

      // First, get the Dan OwningGroup values from new_metis_test.xlsx
      console.log('Requesting owning groups from API...');

      axios.post('/api-statit2/get_metis_owning_groups', {}, config)
        .then(response => {
          console.log('Received owning groups response:', response.data.status_res);

          if (response.data.status_res === 'success') {
            // Store the Dan OwningGroup values in the group option
            this.ownerOptions.group = response.data.owning_groups || [];

            // Add "All" option at the beginning
            this.ownerOptions.group.unshift('All');

            console.log(`Loaded ${this.ownerOptions.group.length} commodities from Dan OwningGroup column`);
          } else {
            console.error('Failed to load owning groups:', response.data.error_msg);
          }

          // Now get the dev and PQE owners
          return axios.post('/api-statit2/get_metis_owners', {}, config);
        })
        .then(response => {
          console.log('Received owners data response:', response.data.status_res);

          if (response.data.status_res === 'success') {
            const ownersData = response.data.owners_data;

            // Store the dev and PQE owners in the ownerOptions object
            this.ownerOptions.dev_owner = ownersData.dev_owners || [];
            this.ownerOptions.pqe_owner = ownersData.pqe_owners || [];

            // Add "All" option at the beginning of each list
            this.ownerOptions.dev_owner.unshift('All');
            this.ownerOptions.pqe_owner.unshift('All');

            console.log(`Loaded ${this.ownerOptions.dev_owner.length} dev owners and ${this.ownerOptions.pqe_owner.length} PQE owners`);

            // Set default selection
            this.selectedOwner = 'All';
          } else {
            console.error('Failed to load owners data:', response.data.error_msg);
          }
        })
        .catch(error => {
          console.error('Error loading owners data:', error);
        });
    },

    // For backward compatibility - will be removed in future
    loadOwningGroups() {
      // This method is kept for backward compatibility
      // It now calls the new loadOwners method
      this.loadOwners();
    },

    loadBreakoutNames() {
      console.log('Loading breakout names...');
      this.isLoading = true;
      this.noDataMessage = 'Loading breakout names...';

      // Get authentication config
      const config = this.getAuthConfig();

      // Log the API request
      console.log('Requesting breakout names from API...');

      axios.post('/api-statit2/get_metis_breakout_names', {}, config)
        .then(response => {
          console.log('Received breakout names response:', response.data.status_res);

          if (response.data.status_res === 'success') {
            // Filter out empty or null breakout names
            const filteredNames = response.data.breakout_names
              .filter(name => name && name.trim() !== '')
              .sort((a, b) => a.localeCompare(b)); // Sort alphabetically

            console.log(`Found ${filteredNames.length} valid breakout names out of ${response.data.breakout_names.length} total`);

            // Log some sample breakout names for verification
            if (filteredNames.length > 0) {
              console.log('Sample breakout names from client:');
              filteredNames.slice(0, 5).forEach((name, index) => {
                console.log(`  ${index + 1}. ${name}`);
              });

              this.breakoutNames = filteredNames;
              this.noDataMessage = '';
            } else {
              this.breakoutNames = [];
              this.noDataMessage = 'No valid breakout names found in the data.';
            }
          } else {
            console.error('Error loading breakout names:', response.data);
            this.breakoutNames = [];

            if (response.data.error_msg) {
              this.noDataMessage = `Error loading breakout names: ${response.data.error_msg}`;
            } else {
              this.noDataMessage = 'Error loading breakout names. Please try again.';
            }
          }
          this.isLoading = false;
        })
        .catch(error => {
          console.error('API error when loading breakout names:', error);
          this.breakoutNames = [];

          if (error.response) {
            console.error('Error response data:', error.response.data);

            if (error.response.status === 401) {
              this.noDataMessage = 'Authentication error. Please log in again.';
              // Optionally redirect to login page
              // this.$router.push('/login');
            } else {
              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;
            }
          } else if (error.request) {
            console.error('No response received:', error.request);
            this.noDataMessage = 'No response received from server. Please check your connection.';
          } else {
            console.error('Error message:', error.message);
            this.noDataMessage = `Error: ${error.message}`;
          }

          this.isLoading = false;
        });
    },
    analyzeData() {
      // Validate date range
      if (!this.startDate || !this.endDate) {
        this.noDataMessage = 'Please select a valid date range';
        return;
      }

      // For month inputs, we need to create dates from the first day of each month
      const start = new Date(this.startDate + '-01');
      const end = new Date(this.endDate + '-01');

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        this.noDataMessage = 'Invalid date format';
        return;
      }

      if (start > end) {
        this.noDataMessage = 'Start date must be before end date';
        return;
      }

      this.noDataMessage = 'Loading data...';
      this.isLoading = true;
      this.chartData = [];
      this.alerts = [];

      console.log(`Analyzing data from ${this.startDate} to ${this.endDate}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Use the YYYY-MM format directly for API requests
      const requestData = {
        startDate: this.startDate, // Use the YYYY-MM format directly
        endDate: this.endDate, // Use the YYYY-MM format directly
        exactDateRange: true, // Add flag to indicate exact date range should be used
        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format
      };

      // Calculate base rate period (1 year back from start date by default)
      const baseStartDateObj = new Date(this.startDate + '-01');
      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);
      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];

      // Create a new object with all properties including baseStartDate
      const apiRequestData = {
        ...requestData,
        baseStartDate: baseStartDate
      };

      console.log(`Date range: ${apiRequestData.startDate} to ${apiRequestData.endDate}`);
      console.log(`Base rate calculation period starts at: ${apiRequestData.baseStartDate}`);

      // If a specific breakout group is selected, only query for that one
      if (this.selectedBreakout) {
        apiRequestData.breakoutName = this.selectedBreakout;
        console.log(`Requesting XFactors only for breakout group: ${this.selectedBreakout}`);
      }

      // Log the API request
      console.log(`Requesting XFactors from API for date range: ${this.startDate} to ${this.endDate}`);

      axios.post('/api-statit2/get_metis_xfactors', apiRequestData, config)
        .then(response => {
          console.log('Received response:', response.data.status_res);

          if (response.data.status_res === 'success') {
            this.xFactorData = response.data.xfactors;

            if (Object.keys(this.xFactorData).length === 0) {
              this.noDataMessage = 'No data found for the selected date range. Please try a different range.';
              console.warn('No data found in the response');
            } else {
              console.log(`Received data for ${Object.keys(this.xFactorData).length} breakout groups`);
              this.updateChart();
              this.generateAlerts();

              // If a specific breakout group is selected, get its part numbers
              if (this.selectedBreakout) {
                this.getMainTabPartNumbers();
              }

              this.noDataMessage = '';
            }
          } else {
            console.error('Error analyzing data:', response.data);

            // Check for SQL error message
            if (response.data.sql_error_msg) {
              this.noDataMessage = `Database error: ${response.data.sql_error_msg}`;
            } else {
              this.noDataMessage = 'Error analyzing data. Please try again.';
            }
          }
          this.isLoading = false;
        })
        .catch(error => {
          console.error('API error:', error);

          if (error.response) {
            console.error('Error response data:', error.response.data);

            if (error.response.status === 401) {
              this.noDataMessage = 'Authentication error. Please log in again.';
              // Optionally redirect to login page
              // this.$router.push('/login');
            } else {
              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;
            }
          } else if (error.request) {
            console.error('No response received:', error.request);
            this.noDataMessage = 'No response received from server. Please check your connection.';
          } else {
            console.error('Error message:', error.message);
            this.noDataMessage = `Error: ${error.message}`;
          }

          this.isLoading = false;
        });
    },
    handleBreakoutChange() {
      console.log(`Breakout group changed to: ${this.selectedBreakout || 'All groups'}`);

      // Update the chart with the selected breakout group
      this.updateChart();

      // If a specific breakout group is selected, get its part numbers
      if (this.selectedBreakout) {
        console.log(`Getting part numbers for selected breakout: ${this.selectedBreakout}`);
        this.getMainTabPartNumbers();

        // Also update alerts to only show for the selected breakout
        this.generateAlerts();
      } else {
        // Clear part numbers if no breakout group is selected
        console.log('No breakout group selected, clearing part numbers');
        this.mainTabPartNumbers = [];

        // Show alerts for all breakout groups
        this.generateAlerts();
      }
    },

    // Get part numbers for the selected breakout group in the main tab
    getMainTabPartNumbers() {
      if (!this.selectedBreakout) return;

      console.log(`Getting part numbers for main tab breakout group: ${this.selectedBreakout}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Get part numbers for the selected breakout group
      axios.post('/api-statit2/get_metis_part_numbers', {
        breakoutName: this.selectedBreakout
      }, config)
        .then(response => {
          if (response.data.status_res === 'success' && response.data.pns) {
            this.mainTabPartNumbers = response.data.pns;
            console.log(`Received ${this.mainTabPartNumbers.length} part numbers for ${this.selectedBreakout}`);
          } else {
            console.error('Error getting part numbers for main tab:', response.data);
            this.mainTabPartNumbers = [];
          }
        })
        .catch(error => {
          console.error('API error when getting part numbers for main tab:', error);
          this.mainTabPartNumbers = [];
        });
    },

    updateChart() {
      if (!this.xFactorData || Object.keys(this.xFactorData).length === 0) {
        this.noDataMessage = 'No data available. Please analyze data first.';
        console.warn('No xFactorData available for chart');
        return;
      }

      this.chartData = [];

      // Get all breakout groups or filter by selected breakout
      const breakoutsToShow = this.selectedBreakout
        ? [this.selectedBreakout]
        : Object.keys(this.xFactorData).slice(0, 10); // Limit to top 10 groups for better performance and readability

      console.log(`Showing ${breakoutsToShow.length} breakout groups in chart`);

      if (breakoutsToShow.length === 0) {
        this.noDataMessage = 'No breakout groups available to display.';
        console.warn('No breakout groups to show');
        return;
      }

      // Track min and max dates for chart domain
      let minDate = new Date('2025-12-31');
      let maxDate = new Date('2024-01-01');
      let hasValidData = false;

      breakoutsToShow.forEach(breakoutName => {
        const breakoutData = this.xFactorData[breakoutName];
        if (!breakoutData || !breakoutData.xFactors) {
          console.log(`No data for breakout: ${breakoutName}`);
          return;
        }

        const dataPoints = [];
        const xFactorPeriods = Object.keys(breakoutData.xFactors);

        if (xFactorPeriods.length === 0) {
          console.log(`No periods found for breakout: ${breakoutName}`);
          return;
        }

        console.log(`Processing ${xFactorPeriods.length} periods for ${breakoutName}`);

        Object.entries(breakoutData.xFactors).forEach(([period, data]) => {
          // Add data point for this period
          // Make sure period is in YYYY-MM format and convert to a proper date
          const dateParts = period.split('-');
          if (dateParts.length >= 2) {
            const year = parseInt(dateParts[0]);
            const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed
            const date = new Date(year, month, 1);

            // Validate the date
            if (isNaN(date.getTime())) {
              console.error(`Invalid date created from period ${period}`);
              return;
            }

            // Update min and max dates
            if (date < minDate) minDate = new Date(date);
            if (date > maxDate) maxDate = new Date(date);

            // Ensure xFactor is a valid number
            const xFactor = typeof data.xFactor === 'number' ? data.xFactor : parseFloat(data.xFactor);
            if (isNaN(xFactor)) {
              console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${data.xFactor}`);
              return;
            }

            dataPoints.push({
              date: date,
              value: xFactor,
              group: breakoutName,
              defects: data.defects || 0,
              volume: data.volume || 0,
              targetRate: data.targetRate || (breakoutData.targetRate || 0)
            });

            hasValidData = true;
          } else {
            console.error(`Invalid period format: ${period}`);
          }
        });

        // Sort by date
        dataPoints.sort((a, b) => a.date - b.date);

        console.log(`Added ${dataPoints.length} data points for ${breakoutName}`);

        // Add to chart data
        this.chartData = [...this.chartData, ...dataPoints];
      });

      console.log(`Chart data has ${this.chartData.length} data points for ${breakoutsToShow.length} breakout groups`);

      if (hasValidData) {
        console.log(`Date range in data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);

        // Update chart domain based on actual data
        // Add padding to the date range (1 month before and after)
        minDate.setMonth(minDate.getMonth() - 1);
        maxDate.setMonth(maxDate.getMonth() + 1);

        // Update chart options with dynamic domain
        this.chartOptions.axes.bottom.domain = [minDate, maxDate];

        // Clear any error message
        this.noDataMessage = '';
      } else if (this.chartData.length === 0) {
        this.noDataMessage = 'No data available for the selected criteria.';
        console.warn('No valid data points found for chart');
      }
    },


    generateAlerts() {
      console.log('Generating alerts...');
      this.alerts = [];

      // Filter breakout groups if a specific one is selected
      const breakoutsToProcess = this.selectedBreakout
        ? { [this.selectedBreakout]: this.xFactorData[this.selectedBreakout] }
        : this.xFactorData;

      Object.entries(breakoutsToProcess).forEach(([breakoutName, data]) => {
        if (!data || !data.xFactors) {
          console.log(`No xFactors data for breakout: ${breakoutName}`);
          return;
        }

        // Sort periods chronologically
        const sortedPeriods = Object.keys(data.xFactors).sort();

        if (sortedPeriods.length === 0) {
          console.log(`No periods found for breakout: ${breakoutName}`);
          return;
        }

        console.log(`Processing ${sortedPeriods.length} periods for alerts in ${breakoutName}`);

        // Check for short-term spikes (X-Factor > 3.0)
        sortedPeriods.forEach(period => {
          if (!data.xFactors[period]) {
            console.error(`Missing data for period ${period} in breakout ${breakoutName}`);
            return;
          }

          const xFactor = data.xFactors[period].xFactor;

          // Ensure xFactor is a valid number
          if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {
            console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${xFactor}`);
            return;
          }

          if (xFactor > 3.0) {
            console.log(`Found short-term spike for ${breakoutName} in ${period}: ${xFactor.toFixed(2)}`);
            this.alerts.push({
              breakoutName,
              status: 'Short-Term Spike',
              period,
              xFactor,
              defects: data.xFactors[period].defects || 0,
              volume: data.xFactors[period].volume || 0
            });
          }
        });

        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)
        for (let i = 0; i < sortedPeriods.length - 2; i++) {
          const period1 = sortedPeriods[i];
          const period2 = sortedPeriods[i + 1];
          const period3 = sortedPeriods[i + 2];

          // Ensure all periods have valid data
          if (!data.xFactors[period1] || !data.xFactors[period2] || !data.xFactors[period3]) {
            console.error(`Missing data for one of the periods in sustained problem check: ${period1}, ${period2}, ${period3}`);
            continue;
          }

          const xFactor1 = data.xFactors[period1].xFactor;
          const xFactor2 = data.xFactors[period2].xFactor;
          const xFactor3 = data.xFactors[period3].xFactor;

          // Ensure all xFactors are valid numbers
          if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||
              typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||
              typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {
            console.error(`Invalid xFactor values for sustained problem check in ${breakoutName}`);
            continue;
          }

          if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {
            // Count how many consecutive months have X-Factor > 1.5
            let consecutiveMonths = 3;
            for (let j = i + 3; j < sortedPeriods.length; j++) {
              if (data.xFactors[sortedPeriods[j]] && data.xFactors[sortedPeriods[j]].xFactor > 1.5) {
                consecutiveMonths++;
              } else {
                break;
              }
            }

            console.log(`Found sustained problem for ${breakoutName} from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);

            // Calculate average xFactor for the sustained problem
            const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;

            this.alerts.push({
              breakoutName,
              status: `Sustained Problem (${consecutiveMonths} months)`,
              period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,
              xFactor: avgXFactor,
              defects: data.xFactors[period3].defects || 0,
              volume: data.xFactors[period3].volume || 0
            });

            // Skip the periods we've already included in this sustained problem
            i += consecutiveMonths - 1;
          }
        }
      });

      // Sort alerts by X-Factor (highest first)
      this.alerts.sort((a, b) => b.xFactor - a.xFactor);

      console.log(`Generated ${this.alerts.length} alerts`);
    },
    // New method to handle quick select button clicks
    selectTimeRange(range) {
      this.selectedTimeRange = range;
      this.applyTimeRange();
    },

    applyTimeRange() {
      const today = new Date();
      const currentMonth = today.getMonth() + 1; // 1-based month
      const currentYear = today.getFullYear();

      // Variables for calculations
      let startMonth, startYear, endMonth, endYear;

      switch (this.selectedTimeRange) {
        case 'last-month': {
          // Calculate last month directly without using Date object
          startMonth = currentMonth - 1;
          startYear = currentYear;

          // Handle January case
          if (startMonth === 0) {
            startMonth = 12;
            startYear = currentYear - 1;
          }

          // For last month, both start and end should be the previous month
          endMonth = startMonth;
          endYear = startYear;

          // Update dropdown values
          this.startMonth = startMonth;
          this.startMonthStr = String(startMonth);
          this.startYear = startYear;
          this.endMonth = endMonth;
          this.endMonthStr = String(endMonth);
          this.endYear = endYear;

          // Update date strings directly
          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'last-3-months': {
          // Calculate 2 months ago for a total of 3 months including current month
          startMonth = currentMonth - 2;
          startYear = currentYear;

          // Handle month wrapping
          if (startMonth <= 0) {
            startMonth = startMonth + 12;
            startYear = currentYear - 1;
          }

          endMonth = currentMonth;
          endYear = currentYear;

          // Update dropdown values
          this.startMonth = startMonth;
          this.startMonthStr = String(startMonth);
          this.startYear = startYear;
          this.endMonth = endMonth;
          this.endMonthStr = String(endMonth);
          this.endYear = endYear;

          // Update date strings directly
          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'last-6-months': {
          // Calculate 5 months ago for a total of 6 months including current month
          startMonth = currentMonth - 5;
          startYear = currentYear;

          // Handle month wrapping
          if (startMonth <= 0) {
            startMonth = startMonth + 12;
            startYear = currentYear - 1;
          }

          endMonth = currentMonth;
          endYear = currentYear;

          // Update dropdown values
          this.startMonth = startMonth;
          this.startMonthStr = String(startMonth);
          this.startYear = startYear;
          this.endMonth = endMonth;
          this.endMonthStr = String(endMonth);
          this.endYear = endYear;

          // Update date strings directly
          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'ytd': {
          // Update dropdown values
          this.startMonth = 1; // January
          this.startMonthStr = '1';
          this.startYear = currentYear;
          this.endMonth = currentMonth;
          this.endMonthStr = String(currentMonth);
          this.endYear = currentYear;

          // Update date strings directly
          this.startDate = `${currentYear}-01`;
          this.endDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;
          break;
        }
      }

      // Synchronize with breakout tab
      this.breakoutTabStartDate = this.startDate;
      this.breakoutTabEndDate = this.endDate;
      this.breakoutTabStartMonth = this.startMonth;
      this.breakoutTabStartYear = this.startYear;
      this.breakoutTabEndMonth = this.endMonth;
      this.breakoutTabEndYear = this.endYear;

      // Also update the breakout tab time range to match
      this.breakoutTabTimeRange = this.selectedTimeRange;

      // Analyze data for all tabs when time range changes
      this.analyzeAllData();
    },
    getStatusClass(status) {
      if (status.includes('Short-Term Spike')) {
        return 'status-spike';
      } else if (status.includes('Sustained Problem')) {
        return 'status-sustained';
      }
      return 'status-normal';
    },

    // Breakout tab methods
    applyBreakoutTabTimeRange() {
      const today = new Date();
      const currentMonth = today.getMonth() + 1; // 1-based month
      const currentYear = today.getFullYear();

      // Variables for calculations
      let startMonth, startYear, endMonth, endYear;

      switch (this.breakoutTabTimeRange) {
        case 'last-month': {
          // Calculate last month directly without using Date object
          startMonth = currentMonth - 1;
          startYear = currentYear;

          // Handle January case
          if (startMonth === 0) {
            startMonth = 12;
            startYear = currentYear - 1;
          }

          // For last month, both start and end should be the previous month
          endMonth = startMonth;
          endYear = startYear;

          // Update dropdown values
          this.breakoutTabStartMonth = startMonth;
          this.breakoutTabStartYear = startYear;
          this.breakoutTabEndMonth = endMonth;
          this.breakoutTabEndYear = endYear;

          // Update date strings directly
          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'last-3-months': {
          // Calculate 2 months ago for a total of 3 months including current month
          startMonth = currentMonth - 2;
          startYear = currentYear;

          // Handle month wrapping
          if (startMonth <= 0) {
            startMonth = startMonth + 12;
            startYear = currentYear - 1;
          }

          endMonth = currentMonth;
          endYear = currentYear;

          // Update dropdown values
          this.breakoutTabStartMonth = startMonth;
          this.breakoutTabStartYear = startYear;
          this.breakoutTabEndMonth = endMonth;
          this.breakoutTabEndYear = endYear;

          // Update date strings directly
          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'last-6-months': {
          // Calculate 5 months ago for a total of 6 months including current month
          startMonth = currentMonth - 5;
          startYear = currentYear;

          // Handle month wrapping
          if (startMonth <= 0) {
            startMonth = startMonth + 12;
            startYear = currentYear - 1;
          }

          endMonth = currentMonth;
          endYear = currentYear;

          // Update dropdown values
          this.breakoutTabStartMonth = startMonth;
          this.breakoutTabStartYear = startYear;
          this.breakoutTabEndMonth = endMonth;
          this.breakoutTabEndYear = endYear;

          // Update date strings directly
          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          break;
        }

        case 'custom': {
          // For custom range, don't change the dates automatically
          // The user will input them manually
          console.log('Custom date range selected. Using user-provided dates.');

          // If no dates are set, initialize with reasonable defaults
          if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {
            // Calculate 2 months ago for a total of 3 months including current month
            startMonth = currentMonth - 2;
            startYear = currentYear;

            // Handle month wrapping
            if (startMonth <= 0) {
              startMonth = startMonth + 12;
              startYear = currentYear - 1;
            }

            endMonth = currentMonth;
            endYear = currentYear;

            // Update dropdown values
            this.breakoutTabStartMonth = startMonth;
            this.breakoutTabStartYear = startYear;
            this.breakoutTabEndMonth = endMonth;
            this.breakoutTabEndYear = endYear;

            // Update date strings directly
            this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;
            this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;
          }

          return; // Don't automatically analyze for custom range
        }
      }

      // Automatically analyze the data when time range changes (except for custom)
      this.analyzeBreakoutData();
    },

    analyzeBreakoutData() {
      // Validate date range
      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {
        this.breakoutTabNoDataMessage = 'Please select a valid date range';
        return;
      }

      // For month inputs, we need to create dates from the first day of each month
      const start = new Date(this.breakoutTabStartDate + '-01');
      const end = new Date(this.breakoutTabEndDate + '-01');

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        this.breakoutTabNoDataMessage = 'Invalid date format';
        return;
      }

      if (start > end) {
        this.breakoutTabNoDataMessage = 'Start date must be before end date';
        return;
      }

      this.breakoutTabNoDataMessage = 'Loading data...';
      this.isBreakoutTabLoading = true;
      this.breakoutTabChartData = [];
      this.breakoutTabBarChartData = [];
      this.breakoutTabAlerts = [];
      this.breakoutTabPartNumbers = [];
      this.breakoutTabAllXFactorData = {};

      console.log(`Analyzing breakout data from ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Use the YYYY-MM format directly for API requests
      const startDate = this.breakoutTabStartDate;
      const endDate = this.breakoutTabEndDate;

      // Calculate base rate period (1 year back from start date by default)
      const baseStartDateObj = new Date(this.breakoutTabStartDate + '-01');
      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);
      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];

      console.log(`Breakout tab date range: ${startDate} to ${endDate}`);
      console.log(`Base rate calculation period starts at: ${baseStartDate}`);

      // First, get data for all breakout groups to populate the bar chart
      axios.post('/api-statit2/get_metis_xfactors', {
        startDate: startDate, // Use the YYYY-MM format directly
        endDate: endDate, // Use the YYYY-MM format directly
        baseStartDate: baseStartDate,
        exactDateRange: true, // Add flag to indicate exact date range should be used
        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format
      }, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            this.breakoutTabAllXFactorData = response.data.xfactors;

            if (Object.keys(this.breakoutTabAllXFactorData).length === 0) {
              this.breakoutTabNoDataMessage = 'No data found for the selected date range. Please try a different range.';
              this.isBreakoutTabLoading = false;
              return;
            }

            console.log(`Received data for ${Object.keys(this.breakoutTabAllXFactorData).length} breakout groups`);

            // Process bar chart data for all breakout groups
            this.updateBreakoutBarChart();

            // If a specific breakout group is selected, get its details using the consolidated endpoint
            if (this.breakoutTabSelectedGroup) {
              this.getBreakoutAnalysis();
            } else {
              this.breakoutTabNoDataMessage = 'Please select a breakout group to see detailed analysis';
              this.isBreakoutTabLoading = false;
            }
          } else {
            console.error('Error analyzing breakout data:', response.data);
            this.breakoutTabNoDataMessage = 'Error analyzing data. Please try again.';
            this.isBreakoutTabLoading = false;
          }
        })
        .catch(error => {
          console.error('API error:', error);
          this.breakoutTabNoDataMessage = `Error: ${error.message}`;
          this.isBreakoutTabLoading = false;
        });
    },

    // Get detailed analysis for a specific breakout group using the consolidated endpoint
    getBreakoutAnalysis() {
      if (!this.breakoutTabSelectedGroup) {
        console.error('No breakout group selected for detailed analysis');
        return;
      }

      console.log(`Getting detailed analysis for breakout group: ${this.breakoutTabSelectedGroup}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Use the consolidated endpoint to get all data for this breakout group
      axios.post('/api-statit2/get_metis_breakout_analysis', {
        breakoutName: this.breakoutTabSelectedGroup,
        startDate: this.breakoutTabStartDate,
        endDate: this.breakoutTabEndDate,
        baseStartDate: this.baseStartDate,
        exactDateRange: true,
        useMonthFormat: true
      }, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            console.log('Received detailed analysis for breakout group:', response.data);

            // Update part numbers
            if (response.data.partNumbers) {
              this.breakoutTabPartNumbers = response.data.partNumbers;
              console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);
            }

            // Update XFactor data for line chart
            if (response.data.xFactorData && response.data.xFactorData.xFactors) {
              // Create a structure compatible with the existing updateBreakoutLineChart method
              const breakoutData = {
                xFactors: response.data.xFactorData.xFactors
              };

              // Process the data for the line chart
              this.processBreakoutLineChartData(breakoutData);

              // Generate alerts for this breakout group
              this.generateBreakoutAlerts(breakoutData);
            } else {
              console.error('No XFactor data found in the response');
              this.breakoutTabNoDataMessage = 'No XFactor data found for the selected breakout group';
            }

            this.isBreakoutTabLoading = false;
          } else {
            console.error('Error getting detailed analysis:', response.data);
            this.breakoutTabNoDataMessage = 'Error getting detailed analysis. Please try again.';
            this.isBreakoutTabLoading = false;
          }
        })
        .catch(error => {
          console.error('API error when getting detailed analysis:', error);
          this.breakoutTabNoDataMessage = `Error: ${error.message}`;
          this.isBreakoutTabLoading = false;
        });
    },

    // Get part numbers for the selected breakout group
    getBreakoutPartNumbers() {
      if (!this.breakoutTabSelectedGroup) return;

      console.log(`Getting part numbers for breakout group: ${this.breakoutTabSelectedGroup}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Get part numbers for the selected breakout group
      axios.post('/api-statit2/get_metis_part_numbers', {
        breakoutName: this.breakoutTabSelectedGroup
      }, config)
        .then(response => {
          if (response.data.status_res === 'success' && response.data.pns) {
            this.breakoutTabPartNumbers = response.data.pns;
            console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);
          } else {
            console.error('Error getting part numbers:', response.data);
            this.breakoutTabPartNumbers = [];
          }
          this.isBreakoutTabLoading = false;
        })
        .catch(error => {
          console.error('API error when getting part numbers:', error);
          this.breakoutTabPartNumbers = [];
          this.isBreakoutTabLoading = false;
        });
    },

    // Process data for the line chart from the consolidated endpoint
    processBreakoutLineChartData(breakoutData) {
      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {
        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;
        return;
      }

      // Process chart data
      this.breakoutTabChartData = [];
      const dataPoints = [];

      // Track min and max dates for chart domain
      let minDate = new Date('2025-12-31');
      let maxDate = new Date('2024-01-01');

      Object.entries(breakoutData.xFactors).forEach(([period, data]) => {
        // Make sure period is in YYYY-MM format and convert to a proper date
        const dateParts = period.split('-');
        if (dateParts.length >= 2) {
          const year = parseInt(dateParts[0]);
          const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed
          const date = new Date(year, month, 1);

          // Update min and max dates
          if (date < minDate) minDate = new Date(date);
          if (date > maxDate) maxDate = new Date(date);

          dataPoints.push({
            date: date,
            value: data.xFactor,
            group: this.breakoutTabSelectedGroup,
            defects: data.defects,
            volume: data.volume
          });
        }
      });

      // Sort by date
      dataPoints.sort((a, b) => a.date - b.date);
      this.breakoutTabChartData = dataPoints;

      // Update chart domain based on actual data
      if (dataPoints.length > 0) {
        // Add padding to the date range (1 month before and after)
        minDate.setMonth(minDate.getMonth() - 1);
        maxDate.setMonth(maxDate.getMonth() + 1);

        // Update chart options with dynamic domain
        this.chartOptions.axes.bottom.domain = [minDate, maxDate];

        console.log(`Date range in breakout data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);
      }

      console.log(`Successfully processed line chart data for ${this.breakoutTabSelectedGroup} with ${dataPoints.length} data points`);
    },

    // Update the line chart for the selected breakout group (legacy method, kept for compatibility)
    updateBreakoutLineChart() {
      if (!this.breakoutTabSelectedGroup || !this.breakoutTabAllXFactorData) {
        return;
      }

      const breakoutData = this.breakoutTabAllXFactorData[this.breakoutTabSelectedGroup];

      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {
        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;
        return;
      }

      // Process the data for the line chart
      this.processBreakoutLineChartData(breakoutData);

      // Generate alerts for this breakout group
      this.generateBreakoutAlerts(breakoutData);
    },

    // Update the bar chart with data for all breakout groups
    updateBreakoutBarChart() {
      if (!this.breakoutTabAllXFactorData || Object.keys(this.breakoutTabAllXFactorData).length === 0) {
        return;
      }

      this.breakoutTabBarChartData = [];

      // Calculate average X-Factor for each breakout group
      Object.entries(this.breakoutTabAllXFactorData).forEach(([breakoutName, data]) => {
        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {
          console.log(`No XFactors data for breakout: ${breakoutName}`);
          return;
        }

        let totalXFactor = 0;
        let totalDefects = 0;
        let totalVolume = 0;
        let count = 0;
        let isCritical = false;

        // Calculate average X-Factor and check if any period is critical
        Object.values(data.xFactors).forEach(periodData => {
          totalXFactor += periodData.xFactor;
          totalDefects += periodData.defects;
          totalVolume += periodData.volume;
          count++;

          // Check if this period has a critical X-Factor (> 3.0)
          if (periodData.xFactor > 3.0) {
            isCritical = true;
          }
        });

        if (count > 0) {
          const avgXFactor = totalXFactor / count;

          // Create a simpler group name if it's too long
          const displayName = breakoutName.length > 30
            ? breakoutName.substring(0, 27) + '...'
            : breakoutName;

          this.breakoutTabBarChartData.push({
            group: displayName,
            value: avgXFactor,
            defects: totalDefects,
            volume: totalVolume,
            // Use the 'Critical' group for breakouts with any critical period
            groupMapsTo: isCritical ? 'Critical' : 'XFactor'
          });

          console.log(`Added bar chart data for ${displayName}: XFactor=${avgXFactor.toFixed(2)}, Critical=${isCritical}`);
        }
      });

      // Sort by X-Factor value (highest first)
      this.breakoutTabBarChartData.sort((a, b) => b.value - a.value);

      // Limit to top 15 breakout groups for better visualization
      this.breakoutTabBarChartData = this.breakoutTabBarChartData.slice(0, 15);

      console.log(`Final bar chart data has ${this.breakoutTabBarChartData.length} items`);
    },

    // Handle click events on the chart
    handleBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Bar clicked for breakout group: ${clickedData.group}`);

        // Update the selected breakout group
        this.breakoutTabSelectedGroup = clickedData.group;

        // Get detailed analysis for this breakout group
        this.getBreakoutAnalysis();
      }
    },

    // Update breakout tab start date from dropdowns
    updateBreakoutStartDate() {
      // Format the date as YYYY-MM
      const month = String(this.breakoutTabStartMonth).padStart(2, '0');
      this.breakoutTabStartDate = `${this.breakoutTabStartYear}-${month}`;

      console.log(`Breakout tab start date updated to: ${this.breakoutTabStartDate}`);

      // Validate the date range
      this.validateBreakoutTabDateRange();
    },

    // Update breakout tab end date from dropdowns
    updateBreakoutEndDate() {
      // Format the date as YYYY-MM
      const month = String(this.breakoutTabEndMonth).padStart(2, '0');
      this.breakoutTabEndDate = `${this.breakoutTabEndYear}-${month}`;

      console.log(`Breakout tab end date updated to: ${this.breakoutTabEndDate}`);

      // Validate the date range
      this.validateBreakoutTabDateRange();
    },

    // Validate breakout tab date range and trigger analysis if valid
    validateBreakoutTabDateRange() {
      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {
        this.breakoutTabNoDataMessage = 'Please select both start and end dates';
        console.error('Missing breakout tab start or end date');
        return false;
      }

      // For month inputs, we need to create dates from the first day of each month
      const start = new Date(this.breakoutTabStartDate + '-01');
      const end = new Date(this.breakoutTabEndDate + '-01');

      // Check if dates are valid
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        this.breakoutTabNoDataMessage = 'Invalid date format';
        console.error('Invalid breakout tab date format', {
          start: this.breakoutTabStartDate,
          end: this.breakoutTabEndDate
        });
        return false;
      }

      if (start > end) {
        console.error('Invalid date range: Start date is after end date', {
          start: this.breakoutTabStartDate,
          end: this.breakoutTabEndDate
        });
        this.breakoutTabNoDataMessage = 'Start date must be before end date';
        return false;
      }

      // Set the time range to custom
      this.breakoutTabTimeRange = 'custom';

      // Clear any error message
      this.breakoutTabNoDataMessage = '';

      // Analyze the data with the new date range
      console.log(`Breakout tab date range validated: ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);
      this.analyzeBreakoutData();
      return true;
    },

    // Dashboard methods

    loadDashboardData() {
      console.log('Loading dashboard data...');
      this.dashboardNoDataMessage = 'Loading dashboard data...';
      this.isDashboardLoading = true;
      this.dashboardData = [];

      // Use the global date range
      const startDate = this.startDate;
      const endDate = this.endDate;

      if (!startDate || !endDate) {
        this.dashboardNoDataMessage = 'Please select a valid date range';
        this.isDashboardLoading = false;
        console.error('Invalid date range for dashboard', { startDate, endDate });
        return;
      }

      console.log(`Dashboard date range: ${startDate} to ${endDate}`);

      // Calculate a new start date that is 2 months before the selected start date
      // This allows us to detect critical issues that started before the visible range
      const extendedStartDate = this.getDateMonthsAgo(startDate, 2);
      console.log(`Extended start date (2 months before): ${extendedStartDate}`);

      // Convert to full date format for API
      const apiStartDate = this.getFirstDayOfMonth(extendedStartDate);
      const apiEndDate = this.getLastDayOfMonth(endDate);

      console.log(`Dashboard API date range: ${apiStartDate} to ${apiEndDate}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Calculate base rate period (1 year back from start date)
      const baseStartDateObj = new Date(startDate + '-01');
      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);
      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];

      console.log(`Dashboard base rate period starts at: ${baseStartDate}`);

      // Get data for all breakout groups
      // Use the YYYY-MM format directly for the API to ensure exact month matching
      const requestData = {
        startDate: extendedStartDate, // Use the extended start date (2 months before) for the API
        endDate: endDate, // Use the YYYY-MM format directly
        baseStartDate: baseStartDate,
        exactDateRange: true, // Add flag to indicate exact date range should be used
        useMonthFormat: true, // Add flag to indicate we're using YYYY-MM format
        displayStartDate: startDate // Add the original start date for display purposes
      };

      // Add filter based on selected type and value
      if (this.selectedOwner && this.selectedOwner !== 'All') {
        if (this.selectedFilterType === 'group') {
          requestData.owningGroup = this.selectedOwner;
          console.log(`Filtering dashboard by group: ${this.selectedOwner}`);
        } else if (this.selectedFilterType === 'dev_owner') {
          requestData.filterType = 'dev_owner';
          requestData.owner = this.selectedOwner;
          console.log(`Filtering dashboard by dev owner: ${this.selectedOwner}`);
        } else if (this.selectedFilterType === 'pqe_owner') {
          requestData.filterType = 'pqe_owner';
          requestData.owner = this.selectedOwner;
          console.log(`Filtering dashboard by PQE owner: ${this.selectedOwner}`);
        }
      }

      console.log('Dashboard API request data:', requestData);

      axios.post('/api-statit2/get_metis_xfactors', requestData, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            const xfactorData = response.data.xfactors;
            console.log(`Dashboard received data for ${Object.keys(xfactorData).length} breakout groups`);

            if (Object.keys(xfactorData).length === 0) {
              this.dashboardNoDataMessage = 'No data found for the selected time range';
              this.isDashboardLoading = false;
              console.warn('No data found for dashboard');
              return;
            }

            // Process data for dashboard
            this.processDashboardData(xfactorData, startDate, endDate);
            this.dashboardNoDataMessage = '';
          } else {
            console.error('Error loading dashboard data:', response.data);
            this.dashboardNoDataMessage = 'Error loading dashboard data';
            if (response.data.sql_error_msg) {
              console.error('SQL error:', response.data.sql_error_msg);
              this.dashboardNoDataMessage += `: ${response.data.sql_error_msg}`;
            }
          }
          this.isDashboardLoading = false;
        })
        .catch(error => {
          console.error('API error when loading dashboard data:', error);
          this.dashboardNoDataMessage = `Error: ${error.message}`;
          this.isDashboardLoading = false;
        });
    },

    processDashboardData(xfactorData, startDate, endDate) {
      console.log(`\n=== PROCESSING DASHBOARD DATA ===`);
      console.log(`Input Start Date: ${startDate}`);
      console.log(`Input End Date: ${endDate}`);

      // Extract all unique months from the API response
      const allApiMonths = new Set();

      // Collect all months from the API data
      Object.values(xfactorData).forEach(breakoutData => {
        if (breakoutData.xFactors) {
          Object.keys(breakoutData.xFactors).forEach(month => {
            allApiMonths.add(month);
          });
        }
      });

      // Convert to array and sort
      const apiMonths = Array.from(allApiMonths).sort();
      console.log('All months from API:', apiMonths);

      // Use the display start date if provided in the API response
      const displayStartDate = xfactorData.displayStartDate || startDate;
      console.log(`Using display start date: ${displayStartDate}`);

      // Extract year and month directly from the input strings for comparison
      let startYear, startMonth, endYear, endMonth;

      // Parse start date (use display start date for filtering)
      if (displayStartDate.length === 7) {
        // Format is YYYY-MM
        [startYear, startMonth] = displayStartDate.split('-');
      } else if (displayStartDate.length >= 10) {
        // Format is YYYY-MM-DD or longer
        [startYear, startMonth] = displayStartDate.substring(0, 7).split('-');
      } else {
        console.error(`Invalid start date format: ${displayStartDate}`);
        return;
      }

      // Parse end date
      if (endDate.length === 7) {
        // Format is YYYY-MM
        [endYear, endMonth] = endDate.split('-');
      } else if (endDate.length >= 10) {
        // Format is YYYY-MM-DD or longer
        [endYear, endMonth] = endDate.substring(0, 7).split('-');
      } else {
        console.error(`Invalid end date format: ${endDate}`);
        return;
      }

      // Convert to numbers for comparison
      startYear = parseInt(startYear, 10);
      startMonth = parseInt(startMonth, 10);
      endYear = parseInt(endYear, 10);
      endMonth = parseInt(endMonth, 10);

      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);
      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);

      // Filter API months to only include those within the selected date range
      // Strictly enforce the date range boundaries
      const filteredMonths = apiMonths.filter(month => {
        // Extract year and month for comparison
        const [monthYear, monthMonth] = month.split('-').map(num => parseInt(num, 10));

        // Check if the month is within the exact range (inclusive)
        const isAfterOrEqualStart = (monthYear > startYear) ||
                                   (monthYear === startYear && monthMonth >= startMonth);

        const isBeforeOrEqualEnd = (monthYear < endYear) ||
                                  (monthYear === endYear && monthMonth <= endMonth);

        return isAfterOrEqualStart && isBeforeOrEqualEnd;
      });

      console.log('Filtered months based on selected range:', filteredMonths);

      // Use the filtered months from the API data
      const months = filteredMonths;
      console.log('Using filtered months from API data:', months);
      console.log(`===================================\n`);

      // Format months for display in the table header
      this.dashboardMonths = months.map(date => {
        // Parse the YYYY-MM format directly
        const [year, month] = date.split('-');

        // Create a date object with UTC to avoid timezone issues
        const d = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, 1));

        // Log the date conversion for debugging
        console.log(`Converting date ${date} to formatted month: ${d.toUTCString()}`);

        // Format using the numeric month and year
        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
      });

      console.log('Formatted dashboard months:', this.dashboardMonths);

      // Process data for each breakout group
      const dashboardData = [];

      // Log the number of breakout groups
      console.log(`Processing ${Object.keys(xfactorData).length} breakout groups`);

      Object.entries(xfactorData).forEach(([breakoutName, data]) => {
        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {
          console.log(`No data for breakout group: ${breakoutName}`);
          return;
        }

        console.log(`Processing breakout group: ${breakoutName} with ${Object.keys(data.xFactors).length} data points`);

        const row = {
          breakoutName,
          months: []
        };

        // Initialize cells for each month
        months.forEach(month => {
          row.months.push({
            month,
            breakoutName,
            xFactor: null,
            status: null,
            duration: null,
            defects: null,
            volume: null
          });
        });

        // Log available periods in the data
        console.log(`Available periods for ${breakoutName}:`, Object.keys(data.xFactors));

        // Fill in data for months that have data
        Object.entries(data.xFactors).forEach(([period, monthData]) => {
          // Find the index of this period in our months array
          const monthIndex = months.findIndex(m => m === period);

          if (monthIndex >= 0) {
            console.log(`Found data for ${period} at index ${monthIndex}`);
            row.months[monthIndex].xFactor = monthData.xFactor;
            row.months[monthIndex].defects = monthData.defects;
            row.months[monthIndex].volume = monthData.volume;

            // Determine status
            if (monthData.xFactor > 3.0) {
              row.months[monthIndex].status = 'Short-Term Spike';
            }
          } else {
            console.warn(`Period ${period} not found in months array for ${breakoutName}`);
          }
        });

        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)
        for (let i = 0; i < row.months.length - 2; i++) {
          const cell1 = row.months[i];
          const cell2 = row.months[i + 1];
          const cell3 = row.months[i + 2];

          if (cell1.xFactor > 1.5 && cell2.xFactor > 1.5 && cell3.xFactor > 1.5) {
            // Count consecutive months
            let consecutiveMonths = 3;
            for (let j = i + 3; j < row.months.length; j++) {
              if (row.months[j].xFactor > 1.5) {
                consecutiveMonths++;
              } else {
                break;
              }
            }

            // Mark all cells in the sustained problem
            for (let j = i; j < i + consecutiveMonths; j++) {
              // If already marked as spike, mark as critical (both)
              if (row.months[j].status === 'Short-Term Spike') {
                row.months[j].status = 'Critical';
              } else {
                row.months[j].status = 'Sustained Problem';
              }
              row.months[j].duration = consecutiveMonths;

              // Mark the last month of the sustained problem to make it blink
              if (j === i + consecutiveMonths - 1) {
                row.months[j].isLastMonthOfSustained = true;
                console.log(`Marking last month of sustained problem for ${row.breakoutName} at index ${j}`);
              }
            }

            // Skip the cells we've already processed
            i += consecutiveMonths - 1;
          }
        }

        dashboardData.push(row);
      });

      // Sort by breakout name
      dashboardData.sort((a, b) => a.breakoutName.localeCompare(b.breakoutName));

      this.dashboardData = dashboardData;
      console.log(`Processed dashboard data for ${dashboardData.length} breakout groups`);

      // Count critical issues for the current month
      this.countCurrentMonthCriticalIssues();
    },

    // Count critical issues for the current month
    countCurrentMonthCriticalIssues() {
      if (!this.dashboardData || this.dashboardData.length === 0 || !this.dashboardMonths || this.dashboardMonths.length === 0) {
        this.currentMonthCriticalIssues = 0;
        return;
      }

      // Get the most recent month (last in the array)
      const currentMonthIndex = this.dashboardMonths.length - 1;

      // Count critical issues across all breakout groups for the current month
      let criticalIssueCount = 0;

      this.dashboardData.forEach(row => {
        if (row.months && row.months.length > currentMonthIndex) {
          const cell = row.months[currentMonthIndex];
          if (cell && (cell.status === 'Critical' || cell.status === 'Short-Term Spike')) {
            criticalIssueCount++;
          }
        }
      });

      this.currentMonthCriticalIssues = criticalIssueCount;
      console.log(`Found ${criticalIssueCount} critical issues for the current month (${this.getCurrentMonthName()})`);
    },

    getMonthsBetweenDates(startDate, endDate) {
      console.log(`\n=== GETTING MONTHS BETWEEN DATES ===`);
      console.log(`Input Start Date: ${startDate}`);
      console.log(`Input End Date: ${endDate}`);

      // Generate months between start and end dates
      const result = [];

      // Extract year and month directly from the input strings
      let startYear, startMonth, endYear, endMonth;

      // Parse start date
      if (startDate.length === 7) {
        // Format is YYYY-MM
        [startYear, startMonth] = startDate.split('-');
      } else if (startDate.length >= 10) {
        // Format is YYYY-MM-DD or longer
        [startYear, startMonth] = startDate.substring(0, 7).split('-');
      } else {
        console.error(`Invalid start date format: ${startDate}`);
        return result;
      }

      // Parse end date
      if (endDate.length === 7) {
        // Format is YYYY-MM
        [endYear, endMonth] = endDate.split('-');
      } else if (endDate.length >= 10) {
        // Format is YYYY-MM-DD or longer
        [endYear, endMonth] = endDate.substring(0, 7).split('-');
      } else {
        console.error(`Invalid end date format: ${endDate}`);
        return result;
      }

      // Convert to numbers
      startYear = parseInt(startYear, 10);
      startMonth = parseInt(startMonth, 10);
      endYear = parseInt(endYear, 10);
      endMonth = parseInt(endMonth, 10);

      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);
      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);

      // Validate the date range
      if (startYear > endYear || (startYear === endYear && startMonth > endMonth)) {
        console.error(`Invalid date range: start date is after end date`);
        return result;
      }

      // Generate all months in the range
      let currentYear = startYear;
      let currentMonth = startMonth;

      console.log(`Generating monthly entries from ${startYear}-${startMonth} to ${endYear}-${endMonth}`);

      while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
        // Format as YYYY-MM
        const yearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;
        result.push(yearMonth);
        console.log(`Added month: ${yearMonth}`);

        // Move to the next month
        currentMonth++;
        if (currentMonth > 12) {
          currentMonth = 1;
          currentYear++;
        }
      }

      console.log(`Generated ${result.length} months between dates`);
      console.log(`===================================\n`);
      return result;
    },

    getCellClass(cell) {
      // Only return cell-empty if the cell is completely missing or has no month
      if (!cell || !cell.month) {
        return 'cell-empty';
      }

      if (cell.status === 'Critical') {
        // Only add the blink class if this is the last month of a sustained problem
        if (cell.isLastMonthOfSustained) {
          return 'cell-critical blink';
        } else {
          return 'cell-critical';
        }
      } else if (cell.status === 'Short-Term Spike') {
        return 'cell-spike'; // Removed 'blink' class to prevent flashing
      } else if (cell.status === 'Sustained Problem') {
        // Only add the blink class if this is the last month of a sustained problem
        if (cell.isLastMonthOfSustained) {
          return 'cell-sustained blink';
        } else {
          return 'cell-sustained';
        }
      } else {
        // Always return cell-normal for cells with valid month, even if xFactor is 0
        return 'cell-normal';
      }
    },

    showCellTooltip(cell, event) {
      // Allow tooltips even for cells with 0 defects (xFactor might be 0)
      if (!cell || !cell.month) return;

      // Parse the YYYY-MM format directly
      const [year, month] = cell.month.split('-');

      // Get month name using a simple lookup
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      // Get target rate if available
      const targetRate = this.xFactorData &&
                        this.xFactorData[cell.breakoutName] &&
                        this.xFactorData[cell.breakoutName].targetRate ?
                        this.xFactorData[cell.breakoutName].targetRate : 'N/A';

      // Count critical issues for this cell
      const criticalIssues = this.countCriticalIssuesForCell(cell.breakoutName, cell.month);

      this.tooltipData = {
        breakoutName: cell.breakoutName,
        month: `${monthNames[parseInt(month) - 1]} ${year}`,
        xFactor: cell.xFactor !== undefined ? cell.xFactor : 0,
        targetRate: targetRate,
        status: cell.status || 'Normal',
        duration: cell.duration,
        defects: cell.defects !== undefined ? cell.defects : 0,
        volume: cell.volume !== undefined ? cell.volume : 0,
        criticalIssues: criticalIssues
      };

      // Position tooltip near the mouse
      const offset = 10;
      this.tooltipStyle = {
        top: `${event.clientY + offset}px`,
        left: `${event.clientX + offset}px`
      };

      this.showTooltip = true;
    },

    // Count critical issues for a specific breakout group and month
    countCriticalIssuesForCell(breakoutName, month) {
      // This is a placeholder implementation - in a real implementation,
      // you would query the actual critical issues from root cause, vintage, and supplier analyses

      // For now, we'll use a simple heuristic:
      // If the cell has a status of 'Critical' or 'Short-Term Spike', count it as 1 critical issue
      // In a real implementation, you would need to query the actual critical issues

      // Find the cell in the dashboard data
      const row = this.dashboardData.find(row => row.breakoutName === breakoutName);
      if (!row) return 0;

      const cell = row.months.find(cell => cell.month === month);
      if (!cell) return 0;

      // Count as a critical issue if the status is Critical or Short-Term Spike
      if (cell.status === 'Critical' || cell.status === 'Short-Term Spike') {
        return 1;
      }

      return 0;
    },

    // Get the current month name (most recent month in the dashboard)
    getCurrentMonthName() {
      if (!this.dashboardMonths || this.dashboardMonths.length === 0) {
        return '';
      }

      // Get the most recent month (last in the array)
      return this.dashboardMonths[this.dashboardMonths.length - 1];
    },

    hideCellTooltip() {
      this.showTooltip = false;
    },

    selectBreakoutFromDashboard(breakoutName) {
      console.log(`Navigating to Group tab for ${breakoutName}`);

      // Set the selected breakout group first
      this.group2TabSelectedGroup = breakoutName;

      // Use a more direct approach to select the Group tab
      this.activeTab = 3; // Set the active tab index to 3 (Group tab)

      // Find all tabs and click the Group tab directly
      this.$nextTick(() => {
        // Try different selector approaches
        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');
        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);

        if (tabElements && tabElements.length > 3) {
          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');
          tabElements[3].click();

          // Trigger root cause analysis for this breakout group
          this.$nextTick(() => {
            console.log(`Viewing root cause analysis for ${breakoutName}`);
            this.viewRootCauseAnalysis(3); // Show 3 months of data
          });
        } else {
          // Try an alternative selector
          const altTabElements = document.querySelectorAll('.cv-tab');
          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);

          if (altTabElements && altTabElements.length > 3) {
            console.log('Clicking Group tab using .cv-tab selector');
            altTabElements[3].click();

            // Trigger root cause analysis for this breakout group
            this.$nextTick(() => {
              console.log(`Viewing root cause analysis for ${breakoutName}`);
              this.viewRootCauseAnalysis(3); // Show 3 months of data
            });
          } else {
            console.error('Group tab not found with any selector');
          }
        }
      });
    },

    selectBreakoutFromDashboardWithMonth(breakoutName, month) {
      console.log(`Navigating to Group tab for ${breakoutName} with month ${month}`);

      // Set the selected breakout group first
      this.group2TabSelectedGroup = breakoutName;

      // Store the selected month for potential use in analysis
      this.selectedDashboardMonth = month;

      // Use a more direct approach to select the Group tab
      this.activeTab = 3; // Set the active tab index to 3 (Group tab)

      // Find all tabs and click the Group tab directly
      this.$nextTick(() => {
        // Try different selector approaches
        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');
        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);

        if (tabElements && tabElements.length > 3) {
          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');
          tabElements[3].click();

          // Trigger root cause analysis for this breakout group and month
          this.$nextTick(() => {
            console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);
            this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month
          });
        } else {
          // Try an alternative selector
          const altTabElements = document.querySelectorAll('.cv-tab');
          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);

          if (altTabElements && altTabElements.length > 3) {
            console.log('Clicking Group tab using .cv-tab selector');
            altTabElements[3].click();

            // Trigger root cause analysis for this breakout group and month
            this.$nextTick(() => {
              console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);
              this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month
            });
          } else {
            console.error('Group tab not found with any selector');
          }
        }
      });
    },

    formatDashboardDateRange() {
      // Format the date range for display in the dashboard info section
      const today = new Date();
      let startDate, endDate;

      if (this.dashboardTimeRange === 'custom' && this.dashboardStartDate && this.dashboardEndDate) {
        // Format custom date range
        const startDateObj = new Date(this.dashboardStartDate + '-01');
        const endDateObj = new Date(this.dashboardEndDate + '-01');

        // Format as "MMM YYYY to MMM YYYY" (e.g., "Nov 2024 to May 2025")
        startDate = startDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        endDate = endDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

        // For display in the dashboard info section
        return `${startDate} to ${endDate}`;
      }

      switch (this.dashboardTimeRange) {
        case 'last-month': {
          // For last month, we want the entire previous month
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          startDate = lastMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
          endDate = startDate; // Same month for start and end
          return startDate; // Just return the month name for last-month
        }

        case 'last-3-months': {
          // For last 3 months, we want the current month and previous 2 months
          const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 2, 1);
          startDate = threeMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
          break;
        }

        case 'last-6-months': {
          // For last 6 months, we want the current month and previous 5 months
          const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);
          startDate = sixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
          break;
        }

        default: {
          // Default to last 6 months
          const defaultSixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);
          startDate = defaultSixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        }
      }

      return `${startDate} to ${endDate}`;
    },

    // Format date range for display
    formatDateRange(startDate, endDate) {
      if (!startDate || !endDate) return 'No date range selected';

      const startObj = new Date(startDate + '-01');
      const endObj = new Date(endDate + '-01');

      const startFormatted = startObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      const endFormatted = endObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      return `${startFormatted} to ${endFormatted}`;
    },

    // Analyze data for all tabs
    analyzeAllData() {
      // Validate date range
      if (this.validateDateRange()) {
        console.log('Date range is valid, synchronizing across tabs');

        // Synchronize date ranges across all tabs
        this.breakoutTabStartDate = this.startDate;
        this.breakoutTabEndDate = this.endDate;
        this.breakoutTabStartMonth = this.startMonth;
        this.breakoutTabStartYear = this.startYear;
        this.breakoutTabEndMonth = this.endMonth;
        this.breakoutTabEndYear = this.endYear;

        // Analyze data for main tab
        this.analyzeData();

        // Analyze data for breakout tab if a group is selected
        if (this.breakoutTabSelectedGroup) {
          console.log(`Analyzing data for selected breakout group: ${this.breakoutTabSelectedGroup}`);
          this.analyzeBreakoutData();
        }

        // Load dashboard data with the same date range
        this.loadDashboardData();
      } else {
        console.error('Date range validation failed');
      }
    },

    // Update start date from dropdowns
    updateStartDate() {
      // Convert string to number for internal use
      this.startMonth = parseInt(this.startMonthStr, 10);

      // Ensure startMonth is a valid number
      if (isNaN(this.startMonth)) {
        console.error('Invalid startMonth value:', this.startMonthStr);
        return;
      }

      // Format the date as YYYY-MM
      const month = String(this.startMonth).padStart(2, '0');
      this.startDate = `${this.startYear}-${month}`;

      // Synchronize with breakout tab
      this.breakoutTabStartDate = this.startDate;
      this.breakoutTabStartMonth = this.startMonth;
      this.breakoutTabStartYear = this.startYear;

      // Set time range to custom since user manually selected dates
      this.selectedTimeRange = '';
      this.breakoutTabTimeRange = '';

      // Validate the date range
      this.validateDateRange();
    },

    // Update end date from dropdowns
    updateEndDate() {
      // Convert string to number for internal use
      this.endMonth = parseInt(this.endMonthStr, 10);

      // Ensure endMonth is a valid number
      if (isNaN(this.endMonth)) {
        console.error('Invalid endMonth value:', this.endMonthStr);
        return;
      }

      // Format the date as YYYY-MM
      const month = String(this.endMonth).padStart(2, '0');
      this.endDate = `${this.endYear}-${month}`;

      // Synchronize with breakout tab
      this.breakoutTabEndDate = this.endDate;
      this.breakoutTabEndMonth = this.endMonth;
      this.breakoutTabEndYear = this.endYear;

      // Set time range to custom since user manually selected dates
      this.selectedTimeRange = '';
      this.breakoutTabTimeRange = '';

      // Validate the date range
      this.validateDateRange();
    },

    // Handle filter type change
    onFilterTypeChange() {
      console.log(`Filter type changed to: ${this.selectedFilterType}`);
      // Reset the owner selection when changing filter type
      this.selectedOwner = 'All';
      // Don't reload dashboard data until owner is selected
      console.log('Dashboard data will be reloaded when an owner is selected');
    },

    // AI Summary tooltip methods
    showAiSummaryTooltip(event) {
      // Position the tooltip near the cursor
      const rect = event.target.getBoundingClientRect();
      this.aiTooltipStyle = {
        top: `${rect.bottom + window.scrollY + 5}px`,
        left: `${rect.left + window.scrollX}px`
      };
      this.showAiTooltip = true;
    },

    hideAiSummaryTooltip() {
      this.showAiTooltip = false;
    },

    // Get AI summary for a breakout group using WatsonX.ai
    async getAiSummary(row) {
      logger.logInfo(`Getting AI summary for breakout group: ${row.breakoutName}`, 'getAiSummary');
      this.hideAiSummaryTooltip();
      this.showAiSummaryModal = true;
      this.aiSummaryBreakoutName = row.breakoutName;
      this.isLoadingAiSummary = true;
      this.loadingAiSummaryFor = row.breakoutName;
      this.aiSummaryText = '';

      logger.logInfo('Preparing data for WatsonX.ai...', 'getAiSummary');

      try {
        // Get Action Tracker data for this breakout group
        const actionTrackerData = await this.getActionTrackerData(row.breakoutName);
        logger.logInfo('Action Tracker data retrieved', 'getAiSummary');

        // Prepare data for WatsonX.ai
        const data = this.prepareBreakoutDataForAi(row, actionTrackerData);
        logger.logInfo(`Data preparation complete. Data length: ${data.length}`, 'getAiSummary');

        // Parse the data to extract actionTrackerInsights if available
        const parsedData = JSON.parse(data);
        const actionTrackerInsights = parsedData.actionTrackerInsights;
        logger.logInfo(`Action tracker insights available: ${!!actionTrackerInsights}`, 'getAiSummary');

        // Format the prompt using the template from watsonxPrompts.js
        const prompt = formatDashboardSummaryPrompt(
          data,
          actionTrackerInsights ? JSON.stringify(actionTrackerInsights) : null
        );

        logger.logInfo(`Formatted prompt for WatsonX.ai. Length: ${prompt.length}`, 'getAiSummary');
        logger.logInfo(`Prompt (first 200 chars): ${prompt.substring(0, 200)}...`, 'getAiSummary');

        // Get token from localStorage
        const token = localStorage.getItem('token');
        logger.logInfo(`Auth token available: ${!!token}`, 'getAiSummary');

        // Prepare request data for WatsonX.ai
        const requestData = {
          model_id: 'ibm/granite-13b-instruct-v2', // Using Granite 13B Chat model
          prompt: prompt,
          temperature: 0.3, // Lower temperature for more focused responses
          api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
          project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek
        };

        logger.logInfo('Request data prepared', 'getAiSummary');

        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'getAiSummary');

        // Call WatsonX.ai API using fetch
        const response = await fetch('/api-statit2/watsonx_prompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          },
          body: JSON.stringify(requestData)
        });

        logger.logInfo(`Received response from API. Status: ${response.status}`, 'getAiSummary');

        // Check if response is ok
        if (!response.ok) {
          const errorText = await response.text();
          logger.logError('Error response text', errorText, 'getAiSummary');

          // Try to parse as JSON if possible
          try {
            const errorJson = JSON.parse(errorText);
            logger.logError('Error response JSON', errorJson, 'getAiSummary');
            throw new Error(`HTTP error! Status: ${response.status}, ${JSON.stringify(errorJson)}`);
          } catch (jsonError) {
            // Not JSON, use as text
            throw new Error(`HTTP error! Status: ${response.status}, ${errorText}`);
          }
        }

        // Parse the JSON response
        const responseText = await response.text();
        logger.logInfo('Raw response received', 'getAiSummary');

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          logger.logInfo('Successfully parsed WatsonX.ai response', 'getAiSummary');
        } catch (jsonError) {
          logger.logError('Error parsing JSON response', jsonError, 'getAiSummary');
          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
        }

        // Process the response data
        if (responseData.status === 'success') {
          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'getAiSummary');

          if (responseData.generated_text) {
            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'getAiSummary');
            this.aiSummaryText = responseData.generated_text;
            logger.logInfo('AI summary set successfully', 'getAiSummary');
          } else {
            logger.logError('No generated_text in success response', responseData, 'getAiSummary');
            this.aiSummaryText = 'No summary generated';
          }
        } else {
          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'getAiSummary');
          throw new Error(responseData.error || 'Unknown error occurred');
        }
      } catch (error) {
        logger.logError('Error getting AI summary', error, 'getAiSummary');

        // Create a detailed error message for the user
        let errorMessage = 'Error generating AI summary: ';

        if (error.message) {
          errorMessage += error.message;
        }

        // Add HTTP status code if available
        if (error.response && error.response.status) {
          errorMessage += ` (Status: ${error.response.status})`;
        }

        // Add more details if available
        if (error.response && error.response.data) {
          errorMessage += `<br><br><strong>Details:</strong><br>${JSON.stringify(error.response.data)}`;
        }

        // Add troubleshooting suggestions
        errorMessage += '<br><br><strong>Troubleshooting:</strong><br>' +
          '• Check your internet connection<br>' +
          '• Verify that the WatsonX.ai API key is valid<br>' +
          '• Try again in a few minutes';

        this.aiSummaryText = errorMessage;
      } finally {
        this.isLoadingAiSummary = false;
        this.loadingAiSummaryFor = '';
      }
    },

    // Get Action Tracker data for a specific breakout group
    async getActionTrackerData(breakoutName) {
      console.log('Getting Action Tracker data' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));

      try {
        // Get authentication config for API call
        const config = this.getAuthConfig();

        // Make API call to get action tracker data
        console.log('Fetching action tracker data from API' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));

        // Use fetch instead of axios for API call
        const response = await fetch('/api-statit2/get_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': config.headers && config.headers.Authorization ? config.headers.Authorization : ''
          },
          body: JSON.stringify({ breakoutName })
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log(`Retrieved ${data.items ? data.items.length : 0} action items` + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));

        // If no items property exists, create an empty array
        if (!data.items) {
          data.items = [];
        }

        return data;
      } catch (error) {
        console.error('Error getting Action Tracker data:', error);
        // Return empty data on error
        return { items: [] };
      }
    },

    // Prepare breakout data for AI
    prepareBreakoutDataForAi(row, actionTrackerData = { items: [] }) {
      // Create a simplified data structure for the AI
      const data = {
        breakoutName: row.breakoutName,
        months: [],
        actionTrackerInsights: null
      };

      // Add month data
      row.months.forEach((cell, index) => {
        if (cell && this.dashboardMonths[index]) {
          data.months.push({
            month: this.dashboardMonths[index],
            xFactor: cell.xFactor !== null && cell.xFactor !== undefined ? cell.xFactor : 0,
            status: cell.status || 'Normal',
            defects: cell.defects !== null && cell.defects !== undefined ? cell.defects : 0,
            volume: cell.volume !== null && cell.volume !== undefined ? cell.volume : 0,
            targetRate: cell.targetRate !== null && cell.targetRate !== undefined ? cell.targetRate : 0
          });
        }
      });

      // Add Action Tracker data if available
      if (actionTrackerData && actionTrackerData.items && actionTrackerData.items.length > 0) {
        // Format the Action Tracker data for the AI
        data.actionTrackerInsights = {
          totalItems: actionTrackerData.items.length,
          items: actionTrackerData.items.map(item => ({
            commodity: item.commodity,
            group: item.group,
            partNumber: item.pn,
            test: item.test,
            deadline: item.deadline,
            status: item.status,
            action: item.action,
            assignee: item.assignee,
            notes: item.notes
          }))
        };

        console.log('Including Action Tracker data in AI summary:', data.actionTrackerInsights);
      } else {
        console.log('No Action Tracker data available for this breakout group');
      }

      const jsonData = JSON.stringify(data);
      console.log('Data prepared for AI summary:', data);
      console.log('JSON data being sent to NeuralSeek:', jsonData.substring(0, 200) + (jsonData.length > 200 ? '...' : ''));

      return jsonData;
    },

    // This method has been replaced by direct calls to WatsonX.ai API

    // Handle owner selection change
    onOwnerChange() {
      console.log(`Owner selection changed to: ${this.selectedOwner} (type: ${this.selectedFilterType})`);
      // Reload dashboard data with the new owner filter
      this.loadDashboardData();
    },

    // Validate date range
    validateDateRange() {
      if (!this.startDate || !this.endDate) {
        this.noDataMessage = 'Please select both start and end dates';
        console.error('Missing start or end date');
        return false;
      }

      // For month inputs, we need to create dates from the first day of each month
      const start = new Date(this.startDate + '-01');
      const end = new Date(this.endDate + '-01');

      // Check if dates are valid
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        this.noDataMessage = 'Invalid date format';
        console.error('Invalid date format', { start: this.startDate, end: this.endDate });
        return false;
      }

      if (start > end) {
        this.noDataMessage = 'Start date must be before end date';
        console.error('Start date is after end date', { start: this.startDate, end: this.endDate });
        return false;
      }

      // Set the time range to custom when manually validating
      this.selectedTimeRange = '';

      // Clear any error message
      this.noDataMessage = '';

      console.log(`Date range validated: ${this.startDate} to ${this.endDate}`);
      return true;
    },

    generateBreakoutAlerts(breakoutData) {
      console.log('Generating breakout tab alerts...');
      this.breakoutTabAlerts = [];

      if (!breakoutData || !breakoutData.xFactors) {
        console.log('No xFactors data for breakout tab alerts');
        return;
      }

      // Sort periods chronologically
      const sortedPeriods = Object.keys(breakoutData.xFactors).sort();

      if (sortedPeriods.length === 0) {
        console.log('No periods found for breakout tab alerts');
        return;
      }

      console.log(`Processing ${sortedPeriods.length} periods for breakout tab alerts`);

      // Check for short-term spikes (X-Factor > 3.0)
      sortedPeriods.forEach(period => {
        if (!breakoutData.xFactors[period]) {
          console.error(`Missing data for period ${period} in breakout tab`);
          return;
        }

        const xFactor = breakoutData.xFactors[period].xFactor;

        // Ensure xFactor is a valid number
        if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {
          console.error(`Invalid xFactor in breakout tab for period ${period}: ${xFactor}`);
          return;
        }

        if (xFactor > 3.0) {
          console.log(`Found short-term spike in breakout tab for ${period}: ${xFactor.toFixed(2)}`);
          this.breakoutTabAlerts.push({
            status: 'Short-Term Spike',
            period,
            xFactor,
            defects: breakoutData.xFactors[period].defects || 0,
            volume: breakoutData.xFactors[period].volume || 0
          });
        }
      });

      // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)
      for (let i = 0; i < sortedPeriods.length - 2; i++) {
        const period1 = sortedPeriods[i];
        const period2 = sortedPeriods[i + 1];
        const period3 = sortedPeriods[i + 2];

        // Ensure all periods have valid data
        if (!breakoutData.xFactors[period1] || !breakoutData.xFactors[period2] || !breakoutData.xFactors[period3]) {
          console.error(`Missing data for one of the periods in breakout tab sustained problem check: ${period1}, ${period2}, ${period3}`);
          continue;
        }

        const xFactor1 = breakoutData.xFactors[period1].xFactor;
        const xFactor2 = breakoutData.xFactors[period2].xFactor;
        const xFactor3 = breakoutData.xFactors[period3].xFactor;

        // Ensure all xFactors are valid numbers
        if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||
            typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||
            typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {
          console.error(`Invalid xFactor values for breakout tab sustained problem check`);
          continue;
        }

        if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {
          // Count how many consecutive months have X-Factor > 1.5
          let consecutiveMonths = 3;
          for (let j = i + 3; j < sortedPeriods.length; j++) {
            if (breakoutData.xFactors[sortedPeriods[j]] && breakoutData.xFactors[sortedPeriods[j]].xFactor > 1.5) {
              consecutiveMonths++;
            } else {
              break;
            }
          }

          console.log(`Found sustained problem in breakout tab from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);

          // Calculate average xFactor for the sustained problem
          const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;

          this.breakoutTabAlerts.push({
            status: `Sustained Problem (${consecutiveMonths} months)`,
            period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,
            xFactor: avgXFactor,
            defects: breakoutData.xFactors[period3].defects || 0,
            volume: breakoutData.xFactors[period3].volume || 0
          });

          // Skip the periods we've already included in this sustained problem
          i += consecutiveMonths - 1;
        }
      }

      // Sort alerts by X-Factor (highest first)
      this.breakoutTabAlerts.sort((a, b) => b.xFactor - a.xFactor);

      console.log(`Generated ${this.breakoutTabAlerts.length} breakout tab alerts`);
    },

    // Helper method to get a date X months ago in YYYY-MM format
    getDateMonthsAgo(yearMonth, monthsAgo) {
      const [year, month] = yearMonth.split('-').map(num => parseInt(num, 10));

      // Calculate new month and year
      let newMonth = month - monthsAgo;
      let newYear = year;

      // Handle month wrapping
      while (newMonth <= 0) {
        newMonth += 12;
        newYear -= 1;
      }

      // Format as YYYY-MM
      return `${newYear}-${String(newMonth).padStart(2, '0')}`;
    },

    // Category Analysis tab methods
    analyzeCategoryData() {
      if (!this.categoryTabSelectedGroup) {
        this.categoryTabNoDataMessage = 'Please select a breakout group to analyze';
        return;
      }

      this.categoryTabNoDataMessage = 'Loading category data...';
      this.isCategoryTabLoading = true;
      this.categoryTabChartData = [];
      this.categoryTabCategories = [];
      this.categoryTabPartNumbers = [];

      console.log(`Analyzing category data for ${this.categoryTabSelectedGroup} from ${this.startDate} to ${this.endDate}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Use the global date range
      const requestData = {
        breakoutName: this.categoryTabSelectedGroup,
        startDate: this.startDate,
        endDate: this.endDate
      };

      axios.post('/api-statit2/get_metis_category_analysis', requestData, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            const categoryData = response.data.categoryData;

            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {
              this.categoryTabNoDataMessage = 'No category data found for the selected breakout group and date range';
              this.isCategoryTabLoading = false;
              return;
            }

            // Store the categories and part numbers
            this.categoryTabCategories = categoryData.categories || [];
            this.categoryTabPartNumbers = response.data.partNumbers || [];

            // Set up color scale for categories
            const colorScale = {};
            const colors = [
              '#0f62fe', // blue
              '#6929c4', // purple
              '#1192e8', // cyan
              '#005d5d', // teal
              '#9f1853', // magenta
              '#fa4d56', // red
              '#570408', // maroon
              '#198038', // green
              '#002d9c', // navy
              '#ee538b', // pink
              '#b28600', // yellow
              '#8a3800', // orange
              '#a56eff', // light purple
              '#08bdba', // light teal
              '#bae6ff'  // light blue
            ];

            this.categoryTabCategories.forEach((category, index) => {
              colorScale[category] = colors[index % colors.length];
            });

            // Update chart options with color scale
            this.categoryChartOptions.color.scale = colorScale;

            // Set chart data
            this.categoryTabChartData = categoryData.chartData;
            this.categoryTabNoDataMessage = '';

            console.log(`Loaded ${this.categoryTabChartData.length} category data points for ${this.categoryTabSelectedGroup}`);
            console.log(`Found ${this.categoryTabCategories.length} categories: ${this.categoryTabCategories.join(', ')}`);

            // Calculate the maximum fail rate to set the Y-axis domain
            this.updateChartYAxisDomain();

            // Setup chart click handlers after chart is updated
            this.$nextTick(() => {
              this.setupChartClickHandlers();
            });
          } else {
            console.error('Error analyzing category data:', response.data);
            this.categoryTabNoDataMessage = 'Error analyzing category data. Please try again.';
          }
          this.isCategoryTabLoading = false;
        })
        .catch(error => {
          console.error('API error when analyzing category data:', error);
          this.categoryTabNoDataMessage = `Error: ${error.message}`;
          this.isCategoryTabLoading = false;
        });
    },

    // Handle click on category bar chart
    handleCategoryBarClick(event) {
      console.log('Bar click event received:', event);

      // Check if we have a valid event
      if (!event) {
        console.error('Invalid click event');
        return;
      }

      let clickedData = null;

      // Try to extract data from different event formats
      if (event.datum) {
        // Carbon Charts format
        clickedData = event.datum;
      } else if (event.data) {
        // Alternative format
        clickedData = event.data;
      } else if (event.target && event.target.dataset) {
        // DOM element with dataset
        clickedData = {
          group: event.target.dataset.group,
          key: event.target.dataset.key
        };
      } else if (event.element === 'bar') {
        // Carbon Charts v3 format
        clickedData = event.data;
      }

      console.log('Extracted clicked data:', clickedData);

      // If we couldn't extract data, try to find it in the chart data
      if (!clickedData || (!clickedData.key && !clickedData.group)) {
        console.log('Trying to find data from chart data...');

        // Try to extract data from the event object
        if (event.data && typeof event.data === 'object') {
          // Try different property names that might contain the data
          const possibleDataProps = ['data', 'datum', 'point', 'bar', 'value'];

          for (const prop of possibleDataProps) {
            if (event.data[prop] && event.data[prop].group && event.data[prop].key) {
              clickedData = event.data[prop];
              console.log(`Found data in event.data.${prop}:`, clickedData);
              break;
            }
          }
        }

        // If still no data, use the first data point for the clicked month
        if (!clickedData && event.target) {
          // Try to determine which month was clicked based on position
          const barElement = event.target.closest('.bar');
          if (barElement) {
            const svgElement = barElement.closest('svg');
            if (svgElement) {
              const rect = barElement.getBoundingClientRect();
              const svgRect = svgElement.getBoundingClientRect();
              const relativeX = rect.left - svgRect.left;
              const totalWidth = svgRect.width;

              // Get all unique months
              const months = [...new Set(this.categoryTabChartData.map(d => d.key))].sort();

              // Calculate which month this might be
              const monthIndex = Math.floor((relativeX / totalWidth) * months.length);

              if (monthIndex >= 0 && monthIndex < months.length) {
                const month = months[monthIndex];

                // Find all data points for this month
                const dataForMonth = this.categoryTabChartData.filter(d => d.key === month);

                if (dataForMonth.length > 0) {
                  // Try to determine which category was clicked based on the fill color
                  let categoryIndex = 0;
                  if (barElement.classList.contains('bar')) {
                    // Try to extract category index from class name
                    const fillClass = Array.from(barElement.classList).find(cls => cls.startsWith('fill-'));
                    if (fillClass) {
                      const parts = fillClass.split('-');
                      if (parts.length >= 2) {
                        categoryIndex = parseInt(parts[1]);
                      }
                    }
                  }

                  // Get all unique categories
                  const categories = [...new Set(this.categoryTabChartData.map(d => d.group))];

                  // Use the determined category if valid, otherwise use the first one
                  const category = (categoryIndex >= 0 && categoryIndex < categories.length)
                    ? categories[categoryIndex]
                    : dataForMonth[0].group;

                  // Find the specific data point
                  const dataPoint = this.categoryTabChartData.find(d => d.key === month && d.group === category);

                  if (dataPoint) {
                    clickedData = dataPoint;
                    console.log('Found data point from position analysis:', clickedData);
                  } else {
                    // Fallback to first data point for this month
                    clickedData = dataForMonth[0];
                    console.log('Using first data point for month:', clickedData);
                  }
                }
              }
            }
          }
        }

        // Last resort fallback
        if (!clickedData && this.categoryTabChartData && this.categoryTabChartData.length > 0) {
          // For simplicity, just use the first category and month
          const firstDataPoint = this.categoryTabChartData[0];
          clickedData = firstDataPoint;
          console.log('Using fallback data point:', clickedData);
        } else if (!clickedData) {
          console.error('No chart data available');
          return;
        }
      }

      // Make sure we have the necessary properties
      if (!clickedData.key || !clickedData.group) {
        console.error('Missing key or group in clicked data');
        return;
      }

      console.log(`Category bar clicked: ${clickedData.group}, Month: ${clickedData.key}`);

      // Set selected month and category for the modal
      this.selectedMonth = clickedData.key;
      this.selectedCategory = clickedData.group;

      // Load failure modes data
      this.loadFailureModes(this.categoryTabSelectedGroup, clickedData.key, clickedData.group);

      // Show the modal
      this.showFailureModesModal = true;
    },

    // Load failure modes data for the selected month and category
    loadFailureModes(breakoutName, month, category) {
      this.isFailureModesLoading = true;
      this.failureModesChartData = [];

      console.log(`Loading failure modes for ${breakoutName}, Month: ${month}, Category: ${category}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Prepare request data
      const requestData = {
        breakoutName: breakoutName,
        month: month,
        category: category
      };

      axios.post('/api-statit2/get_metis_failure_modes', requestData, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            const failureModeData = response.data.failureModeData;

            if (!failureModeData || !failureModeData.chartData || failureModeData.chartData.length === 0) {
              console.log('No failure modes data found');
              this.isFailureModesLoading = false;
              return;
            }

            // Get the Count data points
            const countData = failureModeData.chartData.filter(item => item.group === 'Count');

            // Sort by count in descending order
            countData.sort((a, b) => b.value - a.value);

            // Format data for horizontal bar chart
            const hBarData = countData.map(item => ({
              group: "Count",
              key: item.key,
              value: item.value,
              category: item.category || category
            }));

            // Set chart data
            this.failureModesChartData = hBarData;

            console.log(`Created horizontal bar chart with ${hBarData.length} failure modes`);
          } else {
            console.error('Error loading failure modes:', response.data);
          }
          this.isFailureModesLoading = false;
        })
        .catch(error => {
          console.error('API error when loading failure modes:', error);
          this.isFailureModesLoading = false;
        });
    },

    // Get color for category
    getCategoryColor(category) {
      const colors = {
        'Electrical': '#0f62fe',
        'Mechanical': '#6929c4',
        'Thermal': '#1192e8',
        'Material': '#005d5d',
        'Process': '#9f1853',
        'Design': '#fa4d56',
        'Unknown': '#570408',
        'Other': '#198038'
      };

      return colors[category] || '#0f62fe';
    },

    // Group 2 Tab methods
    analyzeGroup2Data() {
      if (!this.group2TabSelectedGroup) {
        this.group2TabNoDataMessage = 'Please select a breakout group to analyze';
        return;
      }

      console.log(`Analyzing Group 2 data for ${this.group2TabSelectedGroup}`);
      this.group2TabNoDataMessage = '';

      // Clear any existing data
      this.rootCauseChartData = [];
      this.vintageChartData = [];
      this.hasCriticalRootCauseIssue = false;
      this.hasCriticalVintageIssue = false;

      // If we have a selected dashboard month, use that to determine the time period
      // Otherwise, default to 3 months
      if (this.selectedDashboardMonth) {
        console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);
        // View root cause analysis for 1 month centered on the selected month
        this.viewRootCauseAnalysis(1);
      } else {
        // Default to 3 months of data
        console.log('No specific month selected, showing 3 months of data');
        this.viewRootCauseAnalysis(3);
      }
    },

    // Custom Date Modal methods
    showCustomDateModal2(analysisType) {
      this.customDateAnalysisType = analysisType;
      this.showCustomDateModal = true;
    },

    hideCustomDateModal() {
      this.showCustomDateModal = false;
    },

    // View custom analysis methods
    viewCustomVintageAnalysis() {
      // Show the custom date modal with vintage analysis type
      this.showCustomDateModal2('vintage');
    },

    viewCustomRootCauseAnalysis() {
      // Show the custom date modal with root cause analysis type
      this.showCustomDateModal2('rootcause');
    },

    viewCustomSupplierAnalysis() {
      // Show the custom date modal with supplier analysis type
      this.showCustomDateModal2('supplier');
    },

    applyCustomDateRange() {
      // Format the dates as YYYY-MM
      const startMonth = String(this.customStartMonthStr).padStart(2, '0');
      const endMonth = String(this.customEndMonthStr).padStart(2, '0');
      const startDate = `${this.customStartYear}-${startMonth}`;
      const endDate = `${this.customEndYear}-${endMonth}`;

      console.log(`Custom date range: ${startDate} to ${endDate}`);

      // Validate the date range
      if (this.validateCustomDateRange(startDate, endDate)) {
        if (this.customDateAnalysisType === 'rootcause') {
          this.viewRootCauseAnalysis(0, startDate, endDate);
        } else if (this.customDateAnalysisType === 'vintage') {
          this.viewVintageAnalysis(0, startDate, endDate);
        } else if (this.customDateAnalysisType === 'supplier') {
          // Placeholder for future supplier analysis implementation
          console.log('Supplier analysis not yet implemented');
        }

        this.hideCustomDateModal();
      }
    },

    validateCustomDateRange(startDate, endDate) {
      // For month inputs, we need to create dates from the first day of each month
      const start = new Date(startDate + '-01');
      const end = new Date(endDate + '-01');

      // Check if dates are valid
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        console.error('Invalid custom date format', {
          start: startDate,
          end: endDate
        });
        return false;
      }

      if (start > end) {
        console.error('Invalid date range: Start date is after end date', {
          start: startDate,
          end: endDate
        });
        return false;
      }

      return true;
    },

    // Vintage Analysis methods
    async viewVintageAnalysis(months, customStartDate, customEndDate) {
      if (!this.group2TabSelectedGroup) {
        console.error('No breakout group selected for vintage analysis');
        return;
      }

      this.isVintageDataLoading = true;
      this.vintageChartData = [];
      this.vintageAiSummary = '';
      this.isVintageAiLoading = true;
      this.hasCriticalVintageIssue = false;

      // Set the date range based on the number of months
      if (customStartDate && customEndDate) {
        // Use custom date range if provided
        this.vintageStartDate = customStartDate;
        this.vintageEndDate = customEndDate;
        this.vintageTimeRange = `${customStartDate} to ${customEndDate}`;
        this.vintageMonths = months || 1; // Default to 1 month if not specified
      } else {
        // Calculate date range based on months parameter
        const endDate = new Date();
        const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');
        const endYear = endDate.getFullYear();
        this.vintageEndDate = `${endYear}-${endMonth}`;

        // Calculate start date by going back the specified number of months
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - (months - 1));
        const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
        const startYear = startDate.getFullYear();
        this.vintageStartDate = `${startYear}-${startMonth}`;

        this.vintageTimeRange = months === 1 ? 'Last Month' : `Last ${months} Months`;
      }

      console.log(`Vintage analysis for ${this.group2TabSelectedGroup} from ${this.vintageStartDate} to ${this.vintageEndDate}`);

      // Get token from store
      const token = this.$store.getters.getToken;

      try {
        // Get vintage data for the selected breakout group using fetch
        const response = await fetch('/api-statit2/get_metis_vintage_analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          },
          body: JSON.stringify({
            breakoutName: this.group2TabSelectedGroup,
            startDate: this.vintageStartDate,
            endDate: this.vintageEndDate
          })
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the JSON response
        const data = await response.json();

        if (data.status_res === 'success') {
          const vintageData = data.vintageData;

          if (!vintageData || !vintageData.chartData || vintageData.chartData.length === 0) {
            console.log('No vintage data found');
            this.isVintageDataLoading = false;
            this.isVintageAiLoading = false;
            return;
          }

          // Process the data for the bar chart
          this.processVintageChartData(vintageData);

          // Generate AI summary
          this.generateVintageAiSummary(vintageData);
        } else {
          console.error('Error getting vintage analysis:', data);
          this.isVintageDataLoading = false;
          this.isVintageAiLoading = false;
        }
      } catch (error) {
        console.error('API error when getting vintage analysis:', error);
        this.isVintageDataLoading = false;
        this.isVintageAiLoading = false;
      }
    },



    processVintageChartData(vintageData) {
      // Extract data from the API response
      const chartData = [];
      const colorScale = {};
      let hasCriticalIssue = false;
      let criticalIssueCount = 0;

      // Get unique vintage months and sort them
      const vintageMonths = vintageData.vintageMonths || [];

      // Generate a color palette for the vintage months
      const colorPalette = [
        '#8a3ffc', '#33b1ff', '#007d79', '#ff7eb6', '#fa4d56',
        '#4589ff', '#d12771', '#d2a106', '#08bdba', '#bae6ff'
      ];

      // Assign colors to each vintage month
      vintageMonths.forEach((month, index) => {
        colorScale[month] = colorPalette[index % colorPalette.length];
      });

      // Update chart options with the color scale
      this.vintageChartOptions.color.scale = colorScale;

      // Process the chart data
      vintageData.chartData.forEach(item => {
        const {
          group,
          key,
          value,
          totalCount,
          volume,
          isAboveTarget,
          totalFailRate,
          targetRate,
          percentage
        } = item;

        // Format the month for display
        const [year, month] = key.split('-');
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const formattedMonth = `${monthNames[parseInt(month) - 1]} ${year}`;

        // Check if this is a critical issue (above target rate)
        const isCritical = isAboveTarget;
        if (isCritical) {
          hasCriticalIssue = true;
          criticalIssueCount++;
        }

        // Get the actual defect count if available
        const defects = totalCount;

        // Format the fail rate as a percentage with 2 decimal places
        const formattedValue = parseFloat(value).toFixed(2);
        const formattedTotalFailRate = parseFloat(totalFailRate).toFixed(2);
        const formattedTargetRate = parseFloat(targetRate * 100).toFixed(2); // Convert target to percentage

        chartData.push({
          group: group, // Vintage month as the group
          key: formattedMonth,
          value: parseFloat(formattedValue), // Convert back to number for the chart
          data: {
            defects: defects,
            volume: volume,
            month: key,
            originalMonth: key,
            vintageMonth: group,
            isCritical: isCritical,
            totalFailRate: formattedTotalFailRate,
            targetRate: formattedTargetRate,
            percentage: percentage,
            formattedValue: `${formattedValue}%`
          }
        });
      });

      // Update the chart data
      this.vintageChartData = chartData;
      this.hasCriticalVintageIssue = hasCriticalIssue;

      // Log summary information
      console.log(`Processed ${chartData.length} data points for vintage analysis`);
      console.log(`Found ${criticalIssueCount} critical issues (months above target rate)`);

      this.isVintageDataLoading = false;
    },

    async generateVintageAiSummary(vintageData) {
      // Get token from store
      const token = this.$store.getters.getToken;

      // Wait a moment to ensure data is processed
      setTimeout(async () => {
        try {
          // Prepare the data for the AI summary
          const summaryData = {
            breakoutGroup: this.group2TabSelectedGroup,
            timeRange: this.vintageTimeRange,
            startDate: this.vintageStartDate,
            endDate: this.vintageEndDate,
            vintageMonths: vintageData.vintageMonths,
            hasCriticalIssues: this.hasCriticalVintageIssue
          };

          // Get additional data for the AI summary
          const totalMonths = vintageData.months ? vintageData.months.length : 0;
          const totalVintageMonths = vintageData.vintageMonths ? vintageData.vintageMonths.length : 0;

          // Calculate average fail rate across all months
          let avgFailRate = 0;
          let maxFailRate = 0;
          let maxFailRateMonth = '';

          if (vintageData.failRates) {
            const failRates = Object.values(vintageData.failRates);
            if (failRates.length > 0) {
              avgFailRate = failRates.reduce((sum, rate) => sum + rate, 0) / failRates.length;

              // Find the month with the highest fail rate
              Object.entries(vintageData.failRates).forEach(([month, rate]) => {
                if (rate > maxFailRate) {
                  maxFailRate = rate;
                  maxFailRateMonth = month;
                }
              });
            }
          }

          // Format the prompt for vintage analysis
          const prompt = `
You are an AI assistant analyzing manufacturing data for IBM. Please provide a brief summary of the vintage month analysis for the following data:

Breakout Group: ${summaryData.breakoutGroup}
Time Period: ${summaryData.timeRange}
Date Range: ${summaryData.startDate} to ${summaryData.endDate}

The data shows the distribution of parts by vintage month (when they were first initialized in the system with Q2FGID = 'INIT').

Additional Data:
- Total months analyzed: ${totalMonths}
- Total unique vintage months: ${totalVintageMonths}
- Average fail rate: ${avgFailRate.toFixed(2)}%
- Highest fail rate: ${maxFailRate.toFixed(2)}% in ${maxFailRateMonth}
- Critical issues detected: ${summaryData.hasCriticalIssues ? 'Yes' : 'No'}

Please provide:
1. A brief overview of the vintage month distribution and fail rates
2. Any notable patterns or trends in the data
3. Insights about how vintage months might relate to quality issues
4. Keep your response concise (3-5 sentences)

Summary:
`;

          // Prepare request data
          const requestData = {
            model_id: 'ibm/granite-13b-instruct-v2',
            prompt: prompt,
            temperature: 0.3,
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'
          };

          // Call WatsonX.ai API using fetch
          const response = await fetch('/api-statit2/watsonx_prompt', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': token ? `Bearer ${token}` : ''
            },
            body: JSON.stringify(requestData)
          });

          // Check if response is ok
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          // Parse the JSON response
          const data = await response.json();

          if (data.status === 'success') {
            this.vintageAiSummary = data.generated_text;
            console.log('AI summary generated successfully for vintage analysis');
          } else {
            console.error('Error generating AI summary for vintage analysis:', data);
            this.vintageAiSummary = 'Unable to generate AI summary at this time.';
          }
        } catch (error) {
          console.error('API error when generating AI summary for vintage analysis:', error);
          this.vintageAiSummary = 'Error generating AI summary. Please try again later.';
        } finally {
          this.isVintageAiLoading = false;
        }
      }, 500); // Wait 500ms to ensure data is processed
    },

    // Handle click events on the vintage chart
    handleVintageBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Vintage bar clicked for vintage month: ${clickedData.group}, month: ${clickedData.key}`);

        // You can implement additional functionality here, such as showing more details
        // about the selected vintage month or filtering the data
      }
    },

    // Root Cause Analysis methods
    viewRootCauseAnalysis(months, customStartDate, customEndDate) {
      if (!this.group2TabSelectedGroup) {
        console.error('No breakout group selected for root cause analysis');
        return;
      }

      // Clear any existing chart data
      this.rootCauseChartData = [];
      this.hasCriticalRootCauseIssue = false;
      this.criticalIssues = [];
      this.criticalIssueDescription = '';

      this.isRootCauseDataLoading = true;
      this.isRootCauseAiLoading = true;
      this.rootCauseChartData = [];
      this.rootCauseAiSummary = '';
      this.hasCriticalRootCauseIssue = false;
      this.criticalIssueUpdate = '';
      this.criticalIssueUpdates = [];

      // Set the time range description
      if (months === 0 && customStartDate && customEndDate) {
        this.rootCauseTimeRange = `Custom: ${this.formatDateForDisplay(customStartDate)} to ${this.formatDateForDisplay(customEndDate)}`;
        this.rootCauseStartDate = customStartDate;
        this.rootCauseEndDate = customEndDate;
      } else {
        this.rootCauseMonths = months;

        // Calculate the date range based on the number of months
        const endDate = new Date();
        const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');
        const endYear = endDate.getFullYear();
        this.rootCauseEndDate = `${endYear}-${endMonth}`;

        // Calculate start date by going back the specified number of months
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - (months - 1));
        const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
        const startYear = startDate.getFullYear();
        this.rootCauseStartDate = `${startYear}-${startMonth}`;

        this.rootCauseTimeRange = months === 1 ? 'Last Month' : `Last ${months} Months`;
      }

      console.log(`Root cause analysis for ${this.group2TabSelectedGroup} from ${this.rootCauseStartDate} to ${this.rootCauseEndDate}`);

      // Get authentication config
      const config = this.getAuthConfig();

      // Get category data for the selected breakout group
      axios.post('/api-statit2/get_metis_category_analysis', {
        breakoutName: this.group2TabSelectedGroup,
        startDate: this.rootCauseStartDate,
        endDate: this.rootCauseEndDate
      }, config)
        .then(response => {
          if (response.data.status_res === 'success') {
            const categoryData = response.data.categoryData;

            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {
              console.log('No category data found');
              this.isRootCauseDataLoading = false;
              return;
            }

            // Process the data for the bar chart
            this.processRootCauseChartData(categoryData);

            // Generate AI summary
            this.generateRootCauseAiSummary(categoryData);
          } else {
            console.error('Error getting category analysis:', response.data);
            this.isRootCauseDataLoading = false;
            this.isRootCauseAiLoading = false;
          }
        })
        .catch(error => {
          console.error('API error when getting category analysis:', error);
          this.isRootCauseDataLoading = false;
          this.isRootCauseAiLoading = false;
        });

      // No need to show a modal as content will be displayed inline
    },



    processRootCauseChartData(categoryData) {
      // Extract all unique categories and months
      const categories = [...new Set(categoryData.chartData.map(item => item.group))];
      const months = [...new Set(categoryData.chartData.map(item => item.key))];

      console.log(`Found ${categories.length} categories: ${categories.join(', ')}`);
      console.log(`Found ${months.length} months: ${months.join(', ')}`);

      // Sort months chronologically
      months.sort((a, b) => new Date(a + '-01') - new Date(b + '-01'));

      // Create a map to store data by category and month
      const dataByMonth = {};
      const previousMonthRates = {};
      let hasCriticalIssue = false;

      // Initialize data structures
      categories.forEach(category => {
        previousMonthRates[category] = [];
      });

      // Process the data
      categoryData.chartData.forEach(item => {
        const { group: category, key: month, value } = item;

        // Store the monthly rates for each category to detect spikes
        previousMonthRates[category].push({
          month,
          value
        });

        // Initialize month data if it doesn't exist
        if (!dataByMonth[month]) {
          dataByMonth[month] = {};
        }

        // Store the value for this category and month
        dataByMonth[month][category] = value;
      });

      // Check for critical issues based on new criteria:
      // 1) Full month fail rate must be above target rate
      // 2) Root cause for a month is 3x the fail rate from cumulative fails in past months
      const criticalIssueDetails = [];

      // Get target rate for this breakout group from the breakout_targets file
      // This will be used to check if the full month fail rate is above target
      let targetRate = 0.001; // Default target rate if not found

      // Try to get the target rate from the data if available
      if (categoryData.targetRate !== undefined) {
        targetRate = categoryData.targetRate;
        console.log(`Using target rate from API data: ${targetRate}`);
      } else {
        // If not available in the data, we'll use a default or try to fetch it
        console.log(`No target rate found in API data, using default: ${targetRate}`);
      }

      // Calculate total fail rate for each month to compare against target
      const monthlyTotalFailRates = {};
      months.forEach(month => {
        let totalFailRate = 0;
        let totalDefects = 0;
        let totalVolume = 0;

        // Sum up all categories for this month
        categories.forEach(category => {
          const categoryData = previousMonthRates[category].find(data => data.month === month);
          if (categoryData) {
            totalFailRate += categoryData.value;

            // If we have actual defect counts, use those
            if (categoryData.defects !== undefined && categoryData.volume !== undefined) {
              totalDefects += categoryData.defects;
              totalVolume = categoryData.volume; // Volume should be the same for all categories in a month
            }
          }
        });

        // Store the total fail rate for this month
        monthlyTotalFailRates[month] = {
          failRate: totalFailRate,
          defects: totalDefects,
          volume: totalVolume,
          isAboveTarget: totalFailRate > (targetRate * 100) // Convert target to percentage for comparison
        };

        console.log(`Month ${month} total fail rate: ${totalFailRate.toFixed(2)}%, target: ${(targetRate * 100).toFixed(2)}%, above target: ${totalFailRate > (targetRate * 100)}`);
      });

      categories.forEach(category => {
        // Sort the monthly rates by date
        previousMonthRates[category].sort((a, b) => {
          return new Date(a.month + '-01') - new Date(b.month + '-01');
        });

        const rates = previousMonthRates[category];

        if (rates.length >= 2) {
          for (let i = 1; i < rates.length; i++) {
            const currentMonth = rates[i];
            const previousMonth = rates[i-1];

            // Check if the full month fail rate is above target
            const isMonthAboveTarget = monthlyTotalFailRates[currentMonth.month] &&
                                      monthlyTotalFailRates[currentMonth.month].isAboveTarget;

            // Only proceed if the month's total fail rate is above target
            if (!isMonthAboveTarget) {
              console.log(`Skipping critical issue check for ${category} in ${currentMonth.month}: month's total fail rate is not above target`);
              continue;
            }

            // Check if this root cause is 3x the fail rate from previous month
            // OR if it's a new root cause that wasn't present before (previousMonth.value is 0)
            // but only if the current month's value is significant (above 0.1%)
            if ((currentMonth.value >= previousMonth.value * 3 && previousMonth.value > 0) ||
                (previousMonth.value === 0 && currentMonth.value > 0.1)) {
              console.log(`Potential critical issue detected in ${category}: ${previousMonth.value.toFixed(2)}% to ${currentMonth.value.toFixed(2)}%`);

              // Get actual defect counts from the category data if available
              const previousDefects = (categoryData.defectCounts &&
                                      categoryData.defectCounts[previousMonth.month] &&
                                      categoryData.defectCounts[previousMonth.month][category]) || 0;
              const currentDefects = (categoryData.defectCounts &&
                                     categoryData.defectCounts[currentMonth.month] &&
                                     categoryData.defectCounts[currentMonth.month][category]) || 0;

              // If actual counts aren't available, estimate based on percentages
              const previousVolume = (categoryData.volumeData &&
                                     categoryData.volumeData[previousMonth.month]) || 10000;
              const currentVolume = (categoryData.volumeData &&
                                    categoryData.volumeData[currentMonth.month]) || 10000;

              const previousFails = previousDefects || Math.round(previousVolume * (previousMonth.value / 100));
              const currentFails = currentDefects || Math.round(currentVolume * (currentMonth.value / 100));

              // This is a critical issue - the month is above target and this root cause has a 3x spike
              console.log(`Confirmed critical issue in ${category}: month is above target and root cause has 3x spike`);
              hasCriticalIssue = true;

              // Store details about this critical issue
              const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
              const [year, monthNum] = currentMonth.month.split('-');
              const monthName = monthNames[parseInt(monthNum) - 1];

              criticalIssueDetails.push({
                category,
                previousValue: previousMonth.value.toFixed(2),
                currentValue: currentMonth.value.toFixed(2),
                increaseMultiplier: previousMonth.value === 0 ? "(new)" : (currentMonth.value / previousMonth.value).toFixed(1),
                month: `${monthName} ${year}`,
                monthShort: currentMonth.month,
                previousFails,
                currentFails,
                failsIncrease: currentFails - previousFails,
                totalFailRate: monthlyTotalFailRates[currentMonth.month].failRate.toFixed(2),
                targetRate: (targetRate * 100).toFixed(2),
                isNew: previousMonth.value === 0
              });

              // We're no longer changing the category name for critical issues
              // Just keep track of which ones are critical for coloring
            }
          }
        }
      });

      // Process critical issues
      if (criticalIssueDetails.length > 0) {
        // Sort by increase multiplier (highest first)
        criticalIssueDetails.sort((a, b) => parseFloat(b.increaseMultiplier) - parseFloat(a.increaseMultiplier));

        // Store all critical issues with unique IDs
        this.criticalIssues = criticalIssueDetails.map((issue, index) => {
          const issueId = `${this.group2TabSelectedGroup.replace(/\s+/g, '-')}-${issue.category.replace(/\s+/g, '-')}-${issue.month.replace(/\s+/g, '-')}`;

          // Generate a description that sounds like it was written by an AI
          let description;
          if (issue.isNew) {
            // New root cause that wasn't present before
            description = `${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant new issue that requires immediate investigation.`;
          } else {
            // Existing root cause with a spike
            description = `${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant deviation from expected performance that requires immediate investigation.`;
          }

          // Initialize update field if it doesn't exist
          if (!this.criticalIssueUpdates[issueId]) {
            this.criticalIssueUpdates[issueId] = {
              updateText: '',
              lastUpdated: null,
              history: []
            };
          }

          // Expand the first critical issue by default
          if (index === 0) {
            this.$set(this.expandedIssues, issueId, true);
          }

          return {
            id: issueId,
            category: issue.category,
            previousValue: issue.previousValue,
            currentValue: issue.currentValue,
            increaseMultiplier: issue.increaseMultiplier,
            month: issue.month,
            description: description,
            severity: parseFloat(issue.increaseMultiplier) > 5 ? 'high' : 'medium'
          };
        });

        // Generate overall description for the most severe issue
        const issue = criticalIssueDetails[0];
        if (issue.isNew) {
          // New root cause that wasn't present before
          this.criticalIssueDescription = `Critical issue detected: ${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;
        } else {
          // Existing root cause with a spike
          this.criticalIssueDescription = `Critical issue detected: ${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;
        }

        // If there are multiple critical issues, add a note
        if (criticalIssueDetails.length > 1) {
          this.criticalIssueDescription += ` Additionally, ${criticalIssueDetails.length - 1} other root cause categories show critical spikes.`;
        }
      } else {
        this.criticalIssues = [];
        this.criticalIssueDescription = '';
      }

      // Format data for stacked bar chart
      const chartData = [];

      // Create a color scale for the categories
      const colorScale = {};

      // Define colors for each category
      const categoryColors = {
        'Electrical': '#0f62fe',
        'Mechanical': '#6929c4',
        'Thermal': '#1192e8',
        'Material': '#005d5d',
        'Process': '#9f1853',
        'Design': '#fa4d56',
        'Unknown': '#570408',
        'Other': '#198038',
        'FLAG': '#8a3ffc',
        'LINK': '#002d9c',
        'NONFAIL': '#009d9a',
        'KRAKEN': '#ee538b',
        'I2C': '#b28600'
      };

      // Assign colors to categories
      categories.forEach(category => {
        // Use predefined color if available, otherwise use a default color
        colorScale[category] = categoryColors[category] || `#${Math.floor(Math.random()*16777215).toString(16)}`;
      });

      // Update the chart options with the color scale
      this.rootCauseChartOptions.color.scale = colorScale;

      // Create data points for each month and category
      months.forEach(month => {
        const monthData = dataByMonth[month] || {};

        // Format the month for display
        const [year, monthNum] = month.split('-');
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const formattedMonth = `${monthNames[parseInt(monthNum) - 1]} ${year}`;

        // Add data for each category in this month
        Object.entries(monthData).forEach(([category, value]) => {
          // Check if this category/month combination is a critical issue
          const criticalIssue = criticalIssueDetails.find(issue =>
            issue.category === category && issue.monthShort === month);
          const isCritical = !!criticalIssue;

          // Get defect counts from the API data if available
          const defects = (categoryData.defectCounts &&
                          categoryData.defectCounts[month] &&
                          categoryData.defectCounts[month][category]) || 0;

          // Get volume data if available
          const volume = (categoryData.volumeData &&
                         categoryData.volumeData[month]) || 10000;

          // Calculate estimated defects if actual count is not available
          const estimatedDefects = defects || Math.round(volume * (value / 100));

          // Instead of changing the category name for critical issues,
          // we'll just store the critical status in the data object
          chartData.push({
            group: category, // Keep the original category name
            key: formattedMonth,
            value: value,
            data: {
              defects: estimatedDefects,
              volume: volume,
              month: month,
              originalMonth: month,
              category: category,
              isCritical: isCritical
            }
          });
        });
      });

      // Update the chart data
      this.rootCauseChartData = chartData;
      this.hasCriticalRootCauseIssue = hasCriticalIssue;

      // Load previous updates if there are critical issues
      if (hasCriticalIssue) {
        this.loadCriticalIssueUpdates();
      }

      this.isRootCauseDataLoading = false;
      console.log(`Processed ${chartData.length} data points for root cause analysis`);
    },

    generateRootCauseAiSummary(categoryData) {
      // Get authentication config
      const config = this.getAuthConfig();

      // Wait a moment to ensure critical issues have been processed
      setTimeout(() => {
        // Prepare the data for the AI summary
        const summaryData = {
          breakoutGroup: this.group2TabSelectedGroup,
          timeRange: this.rootCauseTimeRange,
          startDate: this.rootCauseStartDate,
          endDate: this.rootCauseEndDate,
          categories: categoryData.chartData,
          hasCriticalIssues: this.hasCriticalRootCauseIssue,
          criticalIssuesCount: this.criticalIssues.length,
          criticalIssues: this.criticalIssues.map(issue => ({
            category: issue.category,
            month: issue.month,
            increaseMultiplier: issue.increaseMultiplier,
            isNew: issue.increaseMultiplier === '(new)'
          }))
        };

        // Force the AI to acknowledge critical issues if they exist
        if (this.criticalIssues.length > 0) {
          summaryData.forceCriticalIssueAcknowledgment = true;
        }

        console.log(`Generating AI summary with critical issues: ${this.hasCriticalRootCauseIssue}, count: ${this.criticalIssues.length}`);

        // Get action tracker data for this breakout group
        this.getActionTrackerData(this.group2TabSelectedGroup)
          .then(actionTrackerData => {
            // Format the prompt with the data and action tracker insights
            const prompt = this.formatRootCauseAiPrompt(summaryData, actionTrackerData);

            // Call WatsonX.ai API
            return axios.post('/api-statit2/watsonx_prompt', {
              model_id: 'ibm/granite-13b-instruct-v2',
              prompt: prompt,
              temperature: 0.3,
              api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
              project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'
            }, config);
          })
          .then(response => {
            if (response.data.status === 'success') {
              this.rootCauseAiSummary = response.data.generated_text;
              console.log('AI summary generated successfully');
            } else {
              console.error('Error generating AI summary:', response.data);
              this.rootCauseAiSummary = 'Unable to generate AI summary at this time.';
            }
            this.isRootCauseAiLoading = false;
          })
          .catch(error => {
            console.error('API error when generating AI summary:', error);
            this.rootCauseAiSummary = 'Error generating AI summary. Please try again later.';
            this.isRootCauseAiLoading = false;
          });
      }, 500); // Wait 500ms to ensure critical issues are processed
    },

    formatRootCauseAiPrompt(data, actionTrackerData) {
      // Import the prompt template from watsonxPrompts.js
      const { formatRootCauseAnalysisPrompt } = require('@/utils/watsonxPrompts');

      // Format the prompt with the data
      return formatRootCauseAnalysisPrompt(data, actionTrackerData);
    },

    loadCriticalIssueUpdates() {
      // In a real implementation, this would load updates from a database
      // For now, we'll just use a mock implementation
      console.log('Loading critical issue updates (mock implementation)');

      // We're now using an object instead of an array, so we don't need to reset it here
      // The updates are initialized for each issue in the processRootCauseChartData method
    },

    saveCriticalIssueUpdate(issueId, updateText) {
      // In a real implementation, this would save the update to a database
      if (updateText && updateText.trim()) {
        // Get the current date and time
        const timestamp = new Date();

        // Add the update to the history
        if (!this.criticalIssueUpdates[issueId]) {
          this.criticalIssueUpdates[issueId] = {
            updateText: '',
            lastUpdated: null,
            history: []
          };
        }

        // Add to history
        this.criticalIssueUpdates[issueId].history.push({
          content: updateText,
          timestamp: timestamp
        });

        // Update the last updated timestamp
        this.criticalIssueUpdates[issueId].lastUpdated = timestamp;

        // Clear the update text
        this.criticalIssueUpdates[issueId].updateText = '';

        console.log(`Critical issue update saved for ${issueId}`);

        // In a real implementation, you would save this to a database
        // For example:
        // axios.post('/api-statit2/save_critical_issue_update', {
        //   issueId,
        //   updateText,
        //   timestamp
        // });

        return true;
      }

      return false;
    },

    // Update the text for a critical issue
    updateCriticalIssueText(issueId, text) {
      if (!this.criticalIssueUpdates[issueId]) {
        this.criticalIssueUpdates[issueId] = {
          updateText: text,
          lastUpdated: null,
          history: []
        };
      } else {
        this.criticalIssueUpdates[issueId].updateText = text;
      }
    },

    // Get the update text for a critical issue
    getCriticalIssueUpdateText(issueId) {
      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].updateText) || '';
    },

    // Get the history for a critical issue
    getCriticalIssueHistory(issueId) {
      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].history) || [];
    },

    // Check if a critical issue has been updated
    hasCriticalIssueBeenUpdated(issueId) {
      return this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].lastUpdated !== null;
    },

    // Toggle expanded state of a critical issue
    toggleCriticalIssue(issueId) {
      this.$set(this.expandedIssues, issueId, !this.isIssueExpanded(issueId));
    },

    // Check if a critical issue is expanded
    isIssueExpanded(issueId) {
      return !!this.expandedIssues[issueId];
    },

    formatDate(date) {
      if (!date) return '';

      if (typeof date === 'string') {
        date = new Date(date);
      }

      return date.toLocaleString();
    },

    formatDateForDisplay(dateStr) {
      if (!dateStr) return '';

      const [year, month] = dateStr.split('-');
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      return `${monthNames[parseInt(month) - 1]} ${year}`;
    },

    // Handle click events on the root cause chart
    handleRootCauseBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Root cause bar clicked for category: ${clickedData.key}, month: ${clickedData.group}`);

        // You can implement additional functionality here, such as showing more details
        // about the selected category or filtering the data
      }
    },





    // Setup direct click handlers for the chart
    setupChartClickHandlers() {
      console.log('Setting up chart click handlers');

      // We're now handling clicks in the StackedBarChart component
      // This method is kept for compatibility but doesn't need to do anything
    },

    // Update the Y-axis domain based on the maximum fail rate
    updateChartYAxisDomain() {
      if (!this.categoryTabChartData || this.categoryTabChartData.length === 0) return;

      // Calculate the maximum fail rate across all categories and months
      let maxFailRate = 0;

      // For stacked bar charts, we need to calculate the total for each month
      const monthTotals = {};

      // First, calculate the total for each month
      this.categoryTabChartData.forEach(item => {
        const month = item.key;
        if (!monthTotals[month]) {
          monthTotals[month] = 0;
        }
        monthTotals[month] += item.value || 0;
      });

      // Find the maximum monthly total
      Object.values(monthTotals).forEach(total => {
        if (total > maxFailRate) {
          maxFailRate = total;
        }
      });

      console.log(`Maximum fail rate: ${maxFailRate}%`);

      // Set a reasonable Y-axis domain based on the maximum fail rate
      // Add 10% padding to the top for better visualization
      const yAxisMax = Math.ceil(maxFailRate * 1.1);

      // Ensure the maximum is at least 0.1% for very small values
      const finalMax = Math.max(0.1, yAxisMax);

      console.log(`Setting Y-axis domain to [0, ${finalMax}]`);

      // Update the chart options
      this.categoryChartOptions = {
        ...this.categoryChartOptions,
        axes: {
          ...this.categoryChartOptions.axes,
          left: {
            ...this.categoryChartOptions.axes.left,
            domain: [0, finalMax]
          }
        }
      };
    },

    // AI Test tab methods
    async sendAiPrompt() {
      if (!this.aiPrompt.trim()) {
        logger.logError('Prompt is empty', null, 'sendAiPrompt');
        return;
      }

      this.isAiLoading = true;
      this.aiResponse = '';

      logger.logInfo(`Sending prompt to WatsonX.ai model: ${this.selectedAiModel}`, 'sendAiPrompt');
      logger.logInfo(`Prompt length: ${this.aiPrompt.length}`, 'sendAiPrompt');
      logger.logInfo(`Prompt (first 100 chars): ${this.aiPrompt.substring(0, 100)}...`, 'sendAiPrompt');
      logger.logInfo(`Temperature: ${this.aiTemperature}`, 'sendAiPrompt');

      // Get token from localStorage
      const token = localStorage.getItem('token');
      logger.logInfo(`Auth token available: ${!!token}`, 'sendAiPrompt');

      // Prepare request data
      const requestData = {
        model_id: this.selectedAiModel,
        prompt: this.aiPrompt,
        temperature: parseFloat(this.aiTemperature),
        api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
        project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek
      };

      logger.logInfo('Request data prepared', 'sendAiPrompt');

      try {
        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'sendAiPrompt');

        // Call WatsonX.ai API using fetch
        const response = await fetch('/api-statit2/watsonx_prompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          },
          body: JSON.stringify(requestData)
        });

        logger.logInfo(`Received response from API. Status: ${response.status}`, 'sendAiPrompt');

        // Handle different response statuses
        if (!response.ok) {
          let errorMessage = `HTTP error! Status: ${response.status}`;

          if (response.status === 401) {
            errorMessage = 'Authentication failed. Please check the API key.';
          } else if (response.status === 403) {
            errorMessage = 'Access denied. Please check permissions for the project ID.';
          } else if (response.status === 404) {
            errorMessage = 'Model not found. Please check the model ID.';
          }

          // Try to get more error details from the response
          try {
            const errorText = await response.text();
            logger.logError('Error response text', errorText, 'sendAiPrompt');

            // Try to parse as JSON if possible
            try {
              const errorJson = JSON.parse(errorText);
              logger.logError('Error response JSON', errorJson, 'sendAiPrompt');
              if (errorJson.error) {
                errorMessage += ` - ${errorJson.error}`;
              } else {
                errorMessage += ` - ${errorText}`;
              }
            } catch (jsonError) {
              // Not JSON, use as text
              if (errorText) {
                errorMessage += ` - ${errorText}`;
              }
            }
          } catch (textError) {
            logger.logError('Error parsing error response', textError, 'sendAiPrompt');
          }

          throw new Error(errorMessage);
        }

        // Parse the JSON response
        const responseText = await response.text();
        logger.logInfo('Raw response received', 'sendAiPrompt');

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          logger.logInfo('Successfully parsed WatsonX.ai response', 'sendAiPrompt');
        } catch (jsonError) {
          logger.logError('Error parsing JSON response', jsonError, 'sendAiPrompt');
          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
        }

        if (responseData.status === 'success') {
          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'sendAiPrompt');

          if (responseData.generated_text) {
            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'sendAiPrompt');
            this.aiResponse = responseData.generated_text;
          } else {
            logger.logError('No generated_text in success response', responseData, 'sendAiPrompt');
            this.aiResponse = 'No response generated';
          }
        } else {
          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'sendAiPrompt');
          this.aiResponse = `Error: ${responseData.error || 'Unknown error occurred'}`;
        }
      } catch (error) {
        logger.logError('API error when calling WatsonX.ai', error, 'sendAiPrompt');

        // Provide detailed error information
        let errorMessage = error.message || 'Failed to connect to WatsonX.ai';

        if (error.name === 'TypeError' && errorMessage.includes('Failed to fetch')) {
          errorMessage = 'Network error. Please check your internet connection.';
        }

        this.aiResponse = `Error: ${errorMessage}`;
      } finally {
        this.isAiLoading = false;
      }
    },

    clearAiPrompt() {
      this.aiPrompt = '';
      this.aiResponse = '';
    },

    useTestPrompt() {
      this.aiPrompt = `You are a helpful assistant. Please provide a brief summary of manufacturing quality data.

The XFactor for part group ABC-123 was 2.5 in January, 1.8 in February, and 1.2 in March, showing improvement.
There are 2 open action items for this part group.

Please summarize this trend in 2-3 sentences.`;
    },

    async callWatsonXDirectly() {
      this.isAiLoading = true;
      this.aiResponse = '';

      try {
        // First, get an IAM token
        const iamResponse = await fetch('https://iam.cloud.ibm.com/identity/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          body: new URLSearchParams({
            'grant_type': 'urn:ibm:params:oauth:grant-type:apikey',
            'apikey': 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl'
          })
        });

        if (!iamResponse.ok) {
          throw new Error(`IAM authentication failed: ${iamResponse.status}`);
        }

        const iamData = await iamResponse.json();
        const accessToken = iamData.access_token;

        if (!accessToken) {
          throw new Error('No access token received from IAM');
        }

        // Now call WatsonX.ai directly
        const watsonxResponse = await fetch('/watsonx-direct/ml/v1/text/generation?version=2023-05-29', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          },
          body: JSON.stringify({
            model_id: 'ibm/granite-13b-instruct-v2',
            input: this.aiPrompt,
            parameters: {
              temperature: 0.7,
              max_new_tokens: 1024,
              min_new_tokens: 0,
              decoding_method: 'greedy'
            },
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'
          })
        });

        if (!watsonxResponse.ok) {
          const errorText = await watsonxResponse.text();
          throw new Error(`WatsonX.ai API error: ${watsonxResponse.status} - ${errorText}`);
        }

        const responseData = await watsonxResponse.json();
        console.log('Direct WatsonX.ai response:', responseData);

        if (responseData.results && responseData.results.length > 0) {
          this.aiResponse = responseData.results[0].generated_text || 'No text generated';
        } else {
          this.aiResponse = 'No results returned from WatsonX.ai';
        }
      } catch (error) {
        console.error('Error in direct WatsonX.ai call:', error);
        this.aiResponse = `Error: ${error.message}`;
      } finally {
        this.isAiLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.metis-xfactors-container {
  padding: 20px;
}

/* Tab Styles */
:deep(.bx--tabs) {
  margin-bottom: 20px;
}

:deep(.bx--tabs__nav-item--selected) {
  box-shadow: inset 0 2px 0 0 #0f62fe;
}

:deep(.bx--tab-content) {
  padding: 0;
}

.content-tile {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #262626;
  color: #f4f4f4;
}

/* Common Controls Styles */
.controls-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #333333;
  border-radius: 4px;
  border: 1px solid #393939;
}

.date-controls {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.date-picker {
  display: flex;
  flex-direction: column;
}

.date-picker label {
  margin-bottom: 5px;
  font-size: 14px;
  color: #f4f4f4;
}

.date-picker input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.date-dropdown-container {
  display: flex;
  gap: 8px;
}

.date-dropdown-container select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.date-dropdown-container select:first-child {
  min-width: 120px;
}

.date-dropdown-container select:last-child {
  min-width: 80px;
}

.time-range-dropdown {
  display: flex;
  flex-direction: column;
}

.time-range-dropdown label {
  margin-bottom: 5px;
  font-size: 14px;
  color: #f4f4f4;
}

.time-range-dropdown select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 150px;
}

.analyze-button {
  padding: 8px 16px;
  background-color: #0f62fe;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  height: 38px;
}

.analyze-button:hover {
  background-color: #0353e9;
}

/* Main Tab Styles */
.breakout-selection {
  margin-bottom: 20px;
}

.breakout-dropdown {
  position: relative;
}

.breakout-dropdown select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 300px;
  width: 100%;
}

.breakout-dropdown select:disabled {
  background-color: #f4f4f4;
  color: #999;
  cursor: not-allowed;
}

.breakout-dropdown .loading-indicator {
  margin-top: 5px;
  font-size: 14px;
  color: #0f62fe;
}

.breakout-dropdown .no-data-message {
  margin-top: 5px;
  font-size: 14px;
  color: #da1e28;
}

/* Chart Styles */
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-bottom: 30px;
}

.chart-container, .breakout-chart-container, .breakout-bar-chart-container {
  position: relative;
  background-color: #262626;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
}

.chart-container h4, .breakout-chart-container h4, .breakout-bar-chart-container h4 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #f4f4f4;
}

.chart-description {
  color: #8d8d8d;
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 14px;
}

.chart-wrapper {
  height: 400px;
  margin-bottom: 15px;
}

.threshold-info, .chart-legend {
  display: flex;
  gap: 20px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.threshold-item, .legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.threshold-color, .legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.threshold-color.sustained, .legend-color.normal {
  background-color: #0F62FE;
}

.threshold-color.spike, .legend-color.critical {
  background-color: #DA1E28;
}

:deep(.custom-tooltip) {
  background-color: #262626;
  border: 1px solid #393939;
  padding: 10px;
  border-radius: 4px;
  color: #f4f4f4;
}

:deep(.custom-tooltip p) {
  margin: 5px 0;
}

@media (min-width: 1200px) {
  .charts-section {
    flex-direction: row;
    align-items: stretch;
  }

  .chart-container {
    flex: 1;
    min-width: 0;
  }
}

.info-button {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #0f62fe;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Alerts Styles */
.alerts-container, .breakout-tab-alerts {
  margin-top: 30px;
}

.alerts-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.alerts-table th, .alerts-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.alerts-table th {
  background-color: #f4f4f4;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
}

.status-spike {
  background-color: #ffebeb;
  color: #da1e28;
}

.status-sustained {
  background-color: #fff8e1;
  color: #b28600;
}

.status-normal {
  background-color: #e0f5ea;
  color: #24a148;
}

.status-critical {
  background-color: #990000;
  color: white;
}

/* Dashboard styles */
.dashboard-controls {
  margin-bottom: 30px;
}

.dashboard-header {
  margin-bottom: 20px;
  border-bottom: 2px solid #0062ff;
  padding-bottom: 15px;
}

.dashboard-header h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 500;
  color: #161616;
}

.dashboard-header p {
  margin: 0;
  color: #6f6f6f;
  font-size: 16px;
}

.dashboard-options {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f4f4f4;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-select {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-button {
  padding: 10px 20px;
  background-color: #0062ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-button:hover {
  background-color: #0353e9;
}

.button-icon {
  font-size: 16px;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.heatmap-header h4 {
  margin: 0;
}

.heatmap-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filter-type, .owner-filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-type label, .owner-filter label {
  font-weight: 500;
  color: #f4f4f4;
  white-space: nowrap;
}

/* Carbon dropdown styling */
.carbon-dropdown {
  background-color: white;
}

.filter-type-dropdown {
  min-width: 120px;
}

.owner-dropdown {
  min-width: 180px;
}

.last-updated-text {
  margin-bottom: 15px;
  color: #6f6f6f;
  font-size: 14px;
  text-align: right;
  font-style: italic;
}

.dashboard-date-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
  padding: 15px;
  background-color: #f4f4f4;
  border-radius: 8px;
  width: 100%;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  font-size: 12px;
  color: #6f6f6f;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #161616;
}

.heatmap-container {
  margin-top: 30px;
  background-color: #262626;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.heatmap-container h4 {
  color: #f4f4f4;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
}

.heatmap-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  background-color: #333333;
  padding: 15px;
  border-radius: 6px;
  justify-content: flex-start;
}

.heatmap-legend .legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.heatmap-legend .status-indicator {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
}

.heatmap-legend span {
  color: #f4f4f4;
  font-size: 14px;
}

.heatmap-table-container {
  overflow-x: auto;
  max-height: 600px;
  overflow-y: auto;
  border-radius: 8px;
  margin-top: 20px;
  background-color: #262626;
}

.heatmap-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 2px;
  border: none;
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.heatmap-table th,
.heatmap-table td {
  padding: 12px;
  text-align: center;
  border: none;
  font-size: 14px;
}

.heatmap-table th {
  background-color: #333333;
  color: #f4f4f4;
  position: sticky;
  top: 0;
  z-index: 1;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.heatmap-table .breakout-name {
  text-align: left;
  font-weight: bold;
  position: sticky;
  left: 0;
  background-color: #333333;
  color: #f4f4f4;
  z-index: 2;
  padding-left: 15px;
  min-width: 200px;
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cell-content {
  padding: 10px;
  border-radius: 6px;
  font-weight: bold;
  font-size: 16px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.cell-content:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

.cell-normal {
  background-color: #0062ff;
  color: white;
  opacity: 0.8;
}

.cell-sustained {
  background-color: #ff9a00;
  color: white;
}

.cell-spike {
  background-color: #fa4d56;
  color: white;
}

.cell-critical {
  background-color: #da1e28;
  color: white;
}

.cell-empty {
  background-color: #393939;
  color: #8d8d8d;
}

.no-data {
  color: #8d8d8d;
  font-style: italic;
  font-size: 14px;
}

/* Blinking animation for critical cells */
@keyframes blink {
  0% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }
  50% { opacity: 0.8; box-shadow: 0 0 15px rgba(250, 77, 86, 0.7); }
  100% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.blink {
  animation: blink 2s infinite, pulse 2s infinite;
}

/* Tooltip styles */
.cell-tooltip {
  position: fixed;
  background-color: #161616;
  color: white;
  padding: 16px;
  border-radius: 8px;
  z-index: 1000;
  min-width: 200px;
  max-width: 300px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  border: 1px solid #393939;
  font-size: 14px;
  backdrop-filter: blur(5px);
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.tooltip-header strong {
  font-size: 16px;
  color: #f4f4f4;
}

.tooltip-header span {
  font-size: 14px;
  color: #8d8d8d;
}

.tooltip-content p {
  margin: 8px 0;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
}

.tooltip-content p:before {
  content: "•";
  margin-right: 8px;
  color: #0062ff;
}

.tooltip-content p:nth-child(2):before {
  color: #fa4d56;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #8d8d8d;
}

/* Modal styles */
.info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #262626;
  color: #f4f4f4;
  padding: 20px;
  border-radius: 4px;
  max-width: 600px;
  position: relative;
}

.ai-summary-modal {
  width: 90%;
  max-width: 900px;
}

.ai-summary-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-summary-content {
  line-height: 1.6;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  cursor: pointer;
  color: #aaa;
}

.close-button:hover {
  color: #f4f4f4;
}

.ai-tooltip {
  position: absolute;
  z-index: 999;
  background-color: #0f62fe;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.ai-tooltip-content {
  white-space: nowrap;
}

.breakout-name {
  position: relative;
  cursor: pointer;
}

.breakout-name:hover {
  text-decoration: underline;
  color: #0f62fe;
}

.breakout-name-content {
  display: flex;
  align-items: center;
}

.ai-summary-indicator {
  margin-left: 8px;
}

.loading-dots {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 10px;
}

.loading-dots:after {
  content: '...';
  position: absolute;
  animation: dots 1.5s infinite;
  font-weight: bold;
  color: #0f62fe;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.modal-content h4 {
  margin-top: 15px;
  margin-bottom: 5px;
}

.modal-content p {
  margin-bottom: 10px;
}

.modal-content ul {
  margin-top: 5px;
  padding-left: 20px;
}

/* Breakout Tab Specific Styles */
.breakout-tab-controls {
  margin-bottom: 20px;
}

.breakout-tab-header {
  margin-bottom: 15px;
}

.breakout-tab-header h4 {
  margin-top: 0;
  margin-bottom: 5px;
}

.breakout-tab-header p {
  margin: 0;
  color: #8d8d8d;
}

.content-tile {
  background-color: #262626;
  color: #f4f4f4;
}

.breakout-tab-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
  padding: 15px;
  background-color: #333333;
  border-radius: 4px;
}

.breakout-group-select, .time-range-select {
  display: flex;
  flex-direction: column;
}

.breakout-group-select label, .time-range-select label {
  margin-bottom: 5px;
  font-size: 14px;
}

/* Carbon Dropdown Styling */
.date-dropdown-container {
  display: flex;
  gap: 10px;
}

.month-dropdown, .year-dropdown {
  min-width: 150px;
}

.time-range-dropdown .bx--dropdown {
  min-width: 200px;
}

.breakout-dropdown .bx--dropdown {
  min-width: 250px;
}

.carbon-dropdown {
  margin-bottom: 10px;
}

/* Button styling */
.bx--btn {
  margin-top: 10px;
}

.breakout-tab-date-controls {
  display: flex;
  gap: 15px;
}

.breakout-tab-content {
  margin-top: 20px;
}

/* Part Numbers Section Styles */
.part-numbers-section {
  margin-top: 30px;
  background-color: #262626;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.part-numbers-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #f4f4f4;
}

.part-count {
  color: #8d8d8d;
  margin-bottom: 15px;
  font-size: 14px;
}

.part-numbers-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 10px;
}

.part-number-item {
  background-color: #393939;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  color: #f4f4f4;
  font-family: 'IBM Plex Mono', monospace;
}

/* Date Controls Container Styles */
.date-controls-container {
  margin-bottom: 15px;
  background-color: #333333;
  border-radius: 8px;
  padding: 15px;
}

.date-controls-layout {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  gap: 15px;
}

.custom-range-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-section-label {
  color: #f4f4f4;
  font-size: 14px;
  white-space: nowrap;
}

.date-dropdown-container {
  display: flex;
  gap: 5px;
}

.month-dropdown {
  width: 120px;
}

.year-dropdown {
  width: 90px;
}

.date-section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #f4f4f4;
}

.date-separator {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 0 5px;
}

.date-separator::before, .date-separator::after {
  content: "";
  width: 1px;
  height: 20px;
  background-color: #393939;
  display: none;
}

.or-text {
  padding: 5px 10px;
  font-weight: 600;
  color: #f4f4f4;
  background-color: #333333;
  border-radius: 4px;
  border: 1px solid #393939;
}

.quick-select-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quick-select-dropdown {
  width: 150px;
}

.selected-range {
  background-color: #0f62fe !important;
  border-color: #0f62fe !important;
  color: #ffffff !important;
}

.analyze-button-container {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.selected-date-range {
  color: #f4f4f4;
}

.global-controls-tile {
  background-color: #333333;
  color: #f4f4f4;
}

/* Category Analysis Tab Styles */
.category-tab-controls {
  margin-bottom: 20px;
}

.category-tab-header {
  margin-bottom: 15px;
}

.category-tab-header h4 {
  margin-bottom: 5px;
  font-size: 18px;
}

.category-tab-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.category-chart-container {
  margin-bottom: 20px;
}

.category-tab-part-numbers {
  margin-top: 20px;
  border: 1px solid #393939;
  border-radius: 4px;
  padding: 15px;
  background-color: #262626;
}

.part-numbers-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.part-number-tag {
  background-color: #393939;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
  color: #f4f4f4;
}

/* Failure Modes Modal Styles */
.failure-modes-modal {
  max-width: 800px;
}

.failure-modes-content {
  padding: 20px 0;
}

.failure-modes-chart-container {
  height: 400px;
}

.custom-tooltip {
  background-color: #262626;
  border: 1px solid #393939;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  color: #f4f4f4;
  max-width: 200px;
}

/* Group 2 Tab Styles */
.group2-tab-container {
  padding: 20px 0;
}

.group2-tab-header {
  margin-bottom: 20px;
}

.group2-tab-header h4 {
  margin-bottom: 5px;
  font-size: 18px;
}

.group2-tab-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.analysis-grid {
  margin-top: 20px;
}

.analysis-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #393939;
}

.analysis-table th,
.analysis-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #393939;
}

.analysis-table th {
  background-color: #262626;
  font-weight: 600;
}

.analysis-table td:first-child {
  text-align: left;
  font-weight: 600;
}

/* Custom Date Modal Styles */
.custom-date-content {
  padding: 20px 0;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.date-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-section-label {
  width: 100px;
  font-weight: 600;
}

.custom-date-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Root Cause Section Styles */
.root-cause-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #161616;
  border: 1px solid #393939;
  border-radius: 4px;
}

.section-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #393939;
  padding-bottom: 10px;
}

.section-header h4 {
  font-size: 18px;
  font-weight: 600;
  color: #f4f4f4;
}

.ai-summary-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #161616;
  border-left: 4px solid #0f62fe;
  border-radius: 0 4px 4px 0;
}

.ai-summary-content {
  font-size: 14px;
  line-height: 1.5;
}

.ai-summary-content p {
  margin: 0;
}

.ai-summary-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #262626;
  border: 1px solid #393939;
  border-radius: 4px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0f62fe;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.root-cause-chart-container {
  margin-bottom: 20px;
}

.root-cause-chart-container h5 {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.chart-wrapper {
  background-color: #262626;
  border: 1px solid #393939;
  border-radius: 4px;
  padding: 15px;
}

/* Critical Issues Section Styles */
.critical-issues-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #262626;
  border: 1px solid #fa4d56;
  border-radius: 4px;
}

.critical-issues-section h5 {
  color: #fa4d56;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.critical-issues-summary {
  margin-bottom: 20px;
  line-height: 1.5;
  font-size: 14px;
}

/* Critical Issue Item Styles */
.critical-issue-item {
  margin-bottom: 10px;
  border: 1px solid #393939;
  border-radius: 4px;
  background-color: #161616;
  overflow: hidden;
}

.critical-issue-header {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  padding: 12px 16px;
  cursor: pointer;
  background-color: #161616;
  transition: background-color 0.2s;
}

.critical-issue-header:hover {
  background-color: #262626;
}

.critical-issue-title {
  font-weight: 600;
  min-width: 120px;
}

.month-tag {
  margin-right: 10px;
}

.critical-issue-multiplier {
  color: #fa4d56;
  font-weight: 500;
}

.critical-issue-status {
  margin-left: auto;
}

.expand-icon {
  margin-left: 10px;
  font-size: 12px;
  color: #8d8d8d;
}

.critical-issue-content {
  padding: 16px;
  background-color: #262626;
  border-top: 1px solid #393939;
}

.critical-issue-ai-description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #161616;
  border-left: 4px solid #0f62fe;
  border-radius: 0 4px 4px 0;
}

.critical-issue-ai-description p {
  line-height: 1.5;
  font-size: 14px;
  margin: 0;
}

/* Update Form Styles */
.critical-issue-update-form {
  margin-bottom: 20px;
}

.critical-issue-update-form h6 {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

.save-update-button {
  margin-top: 10px;
}

/* Previous Updates Styles */
.previous-updates {
  margin-top: 20px;
}

.previous-updates h6 {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

/* Carbon Component Overrides */

.cv-tile {
  background-color: #262626;
  padding: 16px;
}

.cv-structured-list {
  margin-top: 10px;
  background-color: #262626;
}

.cv-structured-list-heading {
  font-size: 12px;
  color: #8d8d8d;
}

.cv-structured-list-row {
  border-bottom: 1px solid #393939;
}

.cv-structured-list-cell {
  font-size: 14px;
  padding: 10px;
}

/* Custom tooltip styles */
::v-deep .custom-tooltip {
  background-color: #262626;
  border: 1px solid #393939;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #f4f4f4;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

::v-deep .custom-tooltip p {
  margin: 5px 0;
  line-height: 1.4;
}

::v-deep .custom-tooltip p:first-child {
  margin-top: 0;
  font-weight: 600;
}

::v-deep .custom-tooltip p:last-child {
  margin-bottom: 0;
}

.tooltip-hint {
  font-style: italic;
  color: #8d8d8d;
  margin-top: 8px;
}

/* AI Test Tab Styles */
.ai-test-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.ai-test-header {
  margin-bottom: 10px;
}

.ai-test-header h4 {
  margin-top: 0;
  margin-bottom: 5px;
}

.ai-test-header p {
  color: #8d8d8d;
  margin-top: 0;
}

.ai-test-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #333333;
  border-radius: 4px;
  border: 1px solid #393939;
}

.model-selection, .temperature-control {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.model-selection {
  flex: 1;
  min-width: 250px;
}

.temperature-control {
  flex: 1;
  min-width: 200px;
}

.temperature-control input[type="range"] {
  width: 100%;
  margin-top: 5px;
}

.prompt-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.prompt-container label {
  font-weight: bold;
}

.prompt-container textarea {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #393939;
  background-color: #333333;
  color: #f4f4f4;
  font-family: 'IBM Plex Mono', monospace;
  resize: vertical;
}

.prompt-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.response-container {
  margin-top: 20px;
  min-height: 200px;
  border-radius: 4px;
  border: 1px solid #393939;
  background-color: #333333;
  padding: 15px;
}

.ai-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-response {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ai-response h5 {
  margin-top: 0;
  margin-bottom: 5px;
}

.response-content {
  background-color: #262626;
  border-radius: 4px;
  padding: 15px;
  overflow: auto;
  max-height: 400px;
}

.response-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'IBM Plex Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.no-response-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #8d8d8d;
  font-style: italic;
}

.view-data-cell {
  text-align: center;
  white-space: nowrap;
}

.dashboard-critical-issues-summary {
  background-color: #262626;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-critical-issues-summary h4 {
  margin: 0;
  color: #f4f4f4;
  font-size: 16px;
}

.dashboard-critical-issues-summary .critical-count {
  color: #fa4d56;
  font-weight: bold;
  margin-left: 5px;
}

.critical-issues-tooltip {
  color: #fa4d56;
  font-weight: bold;
  margin-top: 10px;
}

.click-to-view-tooltip {
  color: #0f62fe;
  font-style: italic;
  margin-top: 5px;
}
</style>
