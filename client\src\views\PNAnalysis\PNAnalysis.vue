<style scoped>
.short-dropdown {
  max-width: 200px; /* Adjust the width as needed */
}

.submit-button {
  margin-top: 20px; /* Add some spacing above the button */
}

.flex-container {
  display: flex;
  align-items: flex-start; /* Align items at the top of the container */
  gap: 20px; /* Space between dropdowns and the chart */
}

.left-column {
  margin-top:15px;
  flex: 1; /* Take up 1 part of the space */
  max-width: 300px; /* Optional: Control max width of the left column */
}

.right-column {
  flex: 3; /* Take up 3 parts of the space */
}
</style>

<template>
  
  <cv-grid>
    <template>
    <MainHeader :expandedSideNav=false :useFixed="useFixed" />
    <div class="left-column">
      <h1>PN Analysis</h1>

      <!-- PN Dropdown -->
      <cv-combo-box class="short-dropdown"
        v-model="selectedPN"
        label="PN"
        :options="pnOptions"
      ></cv-combo-box>

      <!-- Area Dropdown -->
      <cv-dropdown class="short-dropdown"
        v-model="selectedArea"
        label="Area"
        :items="areaOptions"
      ></cv-dropdown>

      <!-- Category Dropdown -->
      <cv-dropdown class="short-dropdown"
        v-model="selectedCategory"
        label="Category"
        :items="categoryOptions"
      ></cv-dropdown>

       <!-- Date Select -->
       <cv-date-picker
        v-model="selectedDateRange"
        label="Choose Date Range"
        kind = 'range'
        :cal-options = "calOptions"
        placeholder = "yyyy-mm-dd"
      ></cv-date-picker>

      <!-- Chart Type Dropdown -->
      <cv-dropdown class="short-dropdown"
        v-model="selectedChartType"
        label="Chart Type"
        :items="chartTypeOptions"
      ></cv-dropdown>

      <!-- TODO: add cv-multi-select-->
      <!-- Submit Button -->
      <cv-button @click="handleSubmit" class="submit-button">
        Submit
      </cv-button>
    </div>

    <div class="right-column">
      <cv-tile :light="lightTile">
        <!-- Show loading skeletons while data is being fetched -->
        <div v-if="loading && displayChartType === 'Bar'">
          Loading Bar Chart...
          <BarChart :data = [] :loading="loading"/>
        </div>
        <div v-if="loading && displayChartType === 'Line'" >
          Loading Line Chart...
          <LineChart :data = [] :loading="loading"/>
        </div>

        <!-- Show charts after data is loaded -->
        <BarChart v-if="displayChartType === 'Bar' && bar_chart_data.length > 0" :data="bar_chart_data" @bar-clicked="handleBarClick" :loading="loading"/>
        <LineChart v-if="displayChartType === 'Line' && line_chart_data.length > 0" :data="line_chart_data" @point-clicked="handlePointClick" :loading="loading"/>
      </cv-tile>
    </div>
  </template>
  </cv-grid>
</template>



<script>
import LineChart from '../../components/LineChart'; // Import the LineChart component
import BarChart from '../../components/BarChart'; //import barchart
import MainHeader from '../../components/MainHeader';

export default {
  name: 'PNAnalysis',
  components: {
    LineChart,
    BarChart,
    MainHeader
  },
  data() {
    return {
      selectedPN: "",
      selectedArea: "",
      selectedStartDate: "",
      selectedEndDate: "",
      selectedDateRange: "",
      selectedCategory: "",
      selectedChartType: "",
      pnOptions: [],
      //todo: give options from spreadhseet
      areaOptions: [],
      categoryOptions: [],
      chartTypeOptions: ['Bar', 'Line'],
      line_chart_data: [],
      bar_chart_data: [],
      displayChartType: "",
      chartData: [], 
      lightTile:true,
      loading: true,
      calOptions: {dateFormat: "Y-m-d"}
    };
  },
  mounted() {
    this.get_PNs();
  },
  watch: {
  selectedPN(newPN) {
    if (newPN) {
      this.get_categories();
    }
  },
  selectedArea(newArea) {
    if (newArea) {
      this.get_categories2("area");
      console.log("DAN1")
    }
  },
  selectedCategory(newCategory) {
    if (newCategory) {
      this.get_categories2("category");
      console.log("DAN2")
    }
  },
},
  methods: {
    async load_line_chart() {
      try {
        
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_fail_count_by_category_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.line_chart_data = data.counts_by_period;
          console.log("Received data:", data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }finally {
        this.loading = false;  // Set loading to false once data is loaded
      }
    },

    async load_bar_chart() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_fail_count_by_category_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea, startDate: this.selectedStartDate, endDate: this.selectedEndDate}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.bar_chart_data = data.counts_by_period;
          this.line_chart_data = data.counts_by_period;
          console.log("HI", this.bar_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }finally {
        this.loading = false;  // Set loading to false once data is loaded
      }
    },
    async get_categories() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_categories_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ "PN": this.selectedPN, user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.categoryOptions = data.all_categories;
          this.areaOptions = data.all_stages;
          console.log(this.areaOptions)
          console.log("Received data:", data);
          
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    async get_PNs() {
      try {
        
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_pns", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.pnOptions = data.all_pns;
          
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    async get_categories2(changedVar) {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        let area = null;
        let category = null;

        if (changedVar === "area"){
          area = this.selectedArea
        }else if (changedVar === "category"){
          category = this.selectedCategory
        }
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_categories_db2", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ "PN": this.selectedPN, "area": area, "category": category, user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          if (changedVar === "area"){
            this.categoryOptions = data.new_array;
          } else if (changedVar === "category"){
            this.areaOptions = data.new_array;
          }
          
          console.log("Received data:", data);
          
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    
    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },
    handleBarClick(data) {
      // Update the chart data to only show the clicked bar
      console.log("Bar data received:", data);
      // this.bar_chart_data = [data];
    },
    handlePointClick(data) {
      console.log("Point clicked:", data);
      // Navigate to a new page or display a message
      this.$router.push({ name: 'HelloPage' });
    },

    handleSubmit() {
      this.loading = true; 
      
      this.bar_chart_data = [];
      this.line_chart_data = [];
      console.log('PN:', this.selectedPN);
      console.log('Area:', this.selectedArea);
      console.log('Category:', this.selectedCategory);
      console.log('Chart Type:', this.selectedChartType);
      console.log(`Date Range: Start: ${this.selectedDate}`, );
      this.selectedStartDate = this.selectedDateRange.startDate;
      this.selectedEndDate = this.selectedDateRange.endDate;
      // Update chart data based on selected chart type
      if (this.selectedChartType === 'Bar') {
        //this.chartData = this.bar_chart_data;
        this.displayChartType = 'Bar';
        this.load_bar_chart()
      } else if (this.selectedChartType === 'Line') {
        // this.chartData = this.line_chart_data;
        this.displayChartType = 'Line';
        this.load_line_chart()
      }
    },
  }
};
</script>
