@echo off
echo Starting Vue 3 upgrade process...

cd client

echo Removing node_modules and package-lock.json...
rmdir /s /q node_modules
del package-lock.json

echo Installing Vue 3 and related dependencies...
npm install vue@^3.4.21 vue-router@^4.3.0 vuex@^4.1.0 @vue/compiler-sfc@^3.4.21

echo Installing Carbon components for Vue 3...
npm install @carbon/vue-v11@^1.3.0

echo Installing updated Carbon Charts for Vue 3...
npm install @carbon/charts-vue@^2.0.0

echo Installing updated vee-validate for Vue 3...
npm install vee-validate@^4.12.6

echo Installing remaining dependencies...
npm install

echo Vue 3 upgrade dependencies installed successfully!
echo Please follow the VUE3_MIGRATION_GUIDE.md to complete the migration.
