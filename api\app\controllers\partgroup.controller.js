const path = require('path');
const xls = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');

// Export controller functions
exports.get_part_groups = (req, res) => {
  getPartGroups(req.body, function (err, data) {
    res.status(201).json(data);
  });
};

exports.get_part_group_data = (req, res) => {
  getPartGroupData(req.body, function (err, data) {
    res.status(201).json(data);
  });
};

exports.get_all_excel_data = (req, res) => {
  getAllExcelData(req.body, function (err, data) {
    res.status(201).json(data);
  });
};

// Get all data from the Excel file
function getAllExcelData(values, callback) {
  let responseObj = {
    excel_data: [],
    status_res: null,
    file_name: ''
  };

  try {
    // Check if process is provided
    if (!values.process) {
      responseObj.status_res = "error";
      responseObj.message = "Missing process parameter";
      return callback(null, responseObj);
    }

    // Determine which Excel file to use based on the process and time period
    let filePath = '';
    const timePeriod = values.timePeriod || 'Monthly';

    if (values.process === "FUL") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FUL weekly.xls');
        responseObj.file_name = 'z16 FUL weekly.xls';
      } else {
        filePath = path.join(__dirname, '../excel/z16 FUL monthly.xls');
        responseObj.file_name = 'z16 FUL monthly.xls';
      }
      console.log(`FUL Excel file path (${timePeriod}):`, filePath);
    } else if (values.process === "FAB") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FAB weekly.xls');
        responseObj.file_name = 'z16 FAB weekly.xls';
      } else {
        filePath = path.join(__dirname, '../excel/z16 FAB monthly.xls');
        responseObj.file_name = 'z16 FAB monthly.xls';
      }
      console.log(`FAB Excel file path (${timePeriod}):`, filePath);
    } else {
      responseObj.status_res = "error";
      responseObj.message = "Invalid process selected";
      return callback(null, responseObj);
    }

    // Debug log the file path
    console.log('Attempting to read Excel file for all data:', filePath);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('Excel file does not exist at path:', filePath);
      responseObj.status_res = "error";
      responseObj.message = `Excel file not found: ${filePath}`;
      return callback(null, responseObj);
    }

    console.log('Excel file exists, attempting to read it');

    try {
      // Read the Excel file
      const workbook = xls.readFile(filePath);
      console.log('Excel file read successfully');

      const sheetName = workbook.SheetNames[0];
      console.log('Sheet name:', sheetName);

      const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);
      console.log('Parsed', sheet.length, 'rows from Excel');

      // Log a sample row to debug column names
      if (sheet.length > 0) {
        console.log('Sample row:', JSON.stringify(sheet[0], null, 2));
        console.log('Column names:', Object.keys(sheet[0]));
      }

      // Return all data
      responseObj.excel_data = sheet;
      responseObj.status_res = "success";
      responseObj.total_rows = sheet.length;
      // Log success
      logger.logInfo(`Retrieved all Excel data (${sheet.length} rows) for ${values.process}`, 'partgroup.controller');
      callback(null, responseObj);
      return;
    } catch (error) {
      console.error('Error reading Excel file:', error);
      responseObj.status_res = "error";
      responseObj.message = `Error reading Excel file: ${error.message}`;
      logger.logError(`Error reading Excel file: ${error.message}`, error, 'partgroup.controller');
      callback(null, responseObj);
      return;
    }
  } catch (error) {
    console.error('Error getting Excel data:', error);
    logger.logError(`Error getting Excel data: ${error.message}`, error, 'partgroup.controller');
    responseObj.status_res = "error";
    responseObj.message = error.message;
    callback(null, responseObj);
  }
}

// Get available part groups from the Excel file
function getPartGroups(values, callback) {
  let responseObj = {
    part_groups: [],
    status_res: null
  };

  try {
    // Determine which Excel file to use based on the process and time period
    let filePath = '';
    const timePeriod = values.timePeriod || 'Monthly';

    if (values.process === "FUL") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FUL weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FUL monthly.xls');
      }
      console.log(`FUL Excel file path (${timePeriod}):`, filePath);
    } else if (values.process === "FAB") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FAB weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FAB monthly.xls');
      }
      console.log(`FAB Excel file path (${timePeriod}):`, filePath);
    } else {
      responseObj.status_res = "error";
      responseObj.message = "Invalid process selected";
      return callback(null, responseObj);
    }

    // Debug log the file path
    console.log('Attempting to read Excel file:', filePath);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('Excel file does not exist at path:', filePath);
      responseObj.status_res = "error";
      responseObj.message = `Excel file not found: ${filePath}`;
      return callback(null, responseObj);
    }

    console.log('Excel file exists, attempting to read it');

    // Read the Excel file
    const workbook = xls.readFile(filePath);
    console.log('Excel file read successfully');

    const sheetName = workbook.SheetNames[0];
    console.log('Sheet name:', sheetName);

    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);
    console.log('Parsed', sheet.length, 'rows from Excel');

    // Extract unique part groups (Code Names)
    const partGroups = new Set();
    sheet.forEach(row => {
      if (row["Code Name"]) {
        partGroups.add(row["Code Name"]);
      }
    });

    // Convert Set to Array and sort
    responseObj.part_groups = Array.from(partGroups).sort();
    responseObj.status_res = "success";

    logger.logInfo(`Retrieved ${responseObj.part_groups.length} part groups for ${values.process}`, 'partgroup.controller');
    callback(null, responseObj);
  } catch (error) {
    console.error('Error getting part groups:', error);
    logger.logError(`Error getting part groups: ${error.message}`, error, 'partgroup.controller');
    responseObj.status_res = "error";
    responseObj.message = error.message;
    callback(null, responseObj); // Changed to null to prevent crashing the API
  }
}

// Get data for a specific part group
function getPartGroupData(values, callback) {
  let responseObj = {
    data: [],
    status_res: null
  };

  try {
    // Validate input
    if (!values.process || !values.partGroup) {
      responseObj.status_res = "error";
      responseObj.message = "Missing required parameters";
      return callback(null, responseObj);
    }

    // Determine which Excel file to use based on the process and time period
    let filePath = '';
    let targetsFilePath = path.join(__dirname, '../excel/targets.xlsx');
    let targetsCSVPath = path.join(__dirname, '../excel/targets.csv');
    console.log('Targets CSV path:', targetsCSVPath);

    const timePeriod = values.timePeriod || 'Monthly';

    if (values.process === "FUL") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FUL weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FUL monthly.xls');
      }
      console.log(`FUL Excel file path (${timePeriod}):`, filePath);
    } else if (values.process === "FAB") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FAB weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FAB monthly.xls');
      }
      console.log(`FAB Excel file path (${timePeriod}):`, filePath);
    } else {
      responseObj.status_res = "error";
      responseObj.message = "Invalid process selected";
      return callback(null, responseObj);
    }

    // Debug log the file path
    console.log('Attempting to read Excel file for part group data:', filePath);

    let sheet = [];

    // Try to read Excel file first
    if (fs.existsSync(filePath)) {
      try {
        console.log('Excel file exists, attempting to read it');
        // Read the Excel file
        const workbook = xls.readFile(filePath);
        console.log('Excel file read successfully');

        const sheetName = workbook.SheetNames[0];
        console.log('Sheet name:', sheetName);

        sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);
        console.log('Parsed', sheet.length, 'rows from Excel');
      } catch (err) {
        console.error('Error reading Excel file:', err);
        // Will try CSV next
      }
    } else {
      console.log('Excel file not found, will try CSV');
    }

    // If Excel reading failed or file doesn't exist, show error
    if (sheet.length === 0) {
      console.error('Could not read data from Excel file');
    }

    // If Excel reading failed, return error
    if (sheet.length === 0) {
      console.error('Could not read data from Excel file');
      responseObj.status_res = "error";
      responseObj.message = `Could not read data for ${values.process} from Excel file`;
      return callback(null, responseObj);
    }

    // Read targets file if it exists
    let targetRates = {};
    if (fs.existsSync(targetsFilePath)) {
      try {
        const targetsWorkbook = xls.readFile(targetsFilePath);
        const targetsSheet = xls.utils.sheet_to_json(targetsWorkbook.Sheets[targetsWorkbook.SheetNames[0]]);

        // Extract target rates for each part group
        targetsSheet.forEach(row => {
          // Check for both possible column name formats
          const codeName = row["Code Name"] || row["Name"];
          const targetRate = row["Target Rate"] || row["Target"];
          const process = row["Process"];

          // Only use target rates for the selected process
          if (codeName && targetRate !== undefined && (!process || process === values.process)) {
            // Store the target rate for this part group
            targetRates[codeName] = targetRate;
            console.log(`Found target rate for ${codeName} (${process || 'unknown process'}): ${targetRate}%`);
          }
        });
        console.log('Loaded target rates from Excel:', targetRates);
      } catch (err) {
        console.error('Error reading targets Excel file:', err);
      }
    } else if (fs.existsSync(targetsCSVPath)) {
      try {
        // Read CSV file
        const csvContent = fs.readFileSync(targetsCSVPath, 'utf8');
        const lines = csvContent.split('\n');

        // Parse header to find column indices
        const headers = lines[0].split(',');
        // Check for both possible column name formats
        const codeNameIndex = headers.findIndex(h => h.trim() === 'Code Name' || h.trim() === 'Name');
        const targetRateIndex = headers.findIndex(h => h.trim() === 'Target Rate' || h.trim() === 'Target');
        const processIndex = headers.findIndex(h => h.trim() === 'Process');

        if (codeNameIndex >= 0 && targetRateIndex >= 0) {
          // Parse data rows
          for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) { // Skip empty lines
              const rowValues = lines[i].split(',');
              const codeName = rowValues[codeNameIndex].trim();
              const targetRate = parseFloat(rowValues[targetRateIndex]);
              const process = processIndex >= 0 ? rowValues[processIndex].trim() : null;

              // Only use target rates for the selected process
              if (codeName && !isNaN(targetRate) && (!process || process === values.process)) {
                targetRates[codeName] = targetRate;
                console.log(`Found target rate for ${codeName} (${process || 'unknown process'}): ${targetRate}%`);
              }
            }
          }
          console.log('Loaded target rates from CSV:', targetRates);
        } else {
          console.error('CSV file does not have required columns');
        }
      } catch (err) {
        console.error('Error reading targets CSV file:', err);
      }
    } else {
      console.log('No targets file found, using default target rates');
      // Set default target rates for actual part groups (values are percentages, e.g., 1.2 means 1.2%)
      // These values are taken from the targets.xlsx file
      if (values.process === "FUL") {
        targetRates = {
          'Artemis': 0.39,
          'Centaur-D': 0.14,
          'Diana': 0.5,
          'Parthenon': 0.4,
          'Roosevelt2': 0.9,
          'Saiph': 0.5,
          'Themis': 0.51,
          'Victoria Crypto': 1.9,
          'Zeus PSU': 0.6
        };
      } else { // FAB process
        targetRates = {
          'Artemis': 0.73,
          'Centaur-D': 0.88,
          'Diana': 1.5,
          'Parthenon': 1.6,
          'Roosevelt2': 0.9,
          'Saiph': 2.5,
          'Themis': 0.05,
          'Victoria Crypto': 1.6,
          'Zeus PSU': 0.06
        };
      }
      console.log(`Using default target rates for ${values.process} process`);
    }

    // Filter data for the selected part group
    const partGroupData = sheet.filter(row => row["Code Name"] === values.partGroup);

    // Get target rate for this part group (default to 1.2% if not found)
    // Target rate is already in percentage form in the Excel (1.2 means 1.2%)
    const targetRate = targetRates[values.partGroup] ? targetRates[values.partGroup] / 100 : 0.012;
    console.log(`Target rate for ${values.partGroup}: ${targetRate * 100}%`);

    // Process data using the new model
    const processedData = [];

    partGroupData.forEach(row => {
      // Get the X-factor from the Excel file
      const xFactorScore = row.XF || 1.0;

      // Use actual volume data from the Excel file if available, otherwise generate
      // Log all available properties in the row to debug
      console.log(`Row data for ${row.Period}:`, JSON.stringify(row));

      // Check for Volume column with different capitalizations
      const volume = row.Volume !== undefined ? row.Volume :
                   (row.volume !== undefined ? row.volume :
                   (row.VOLUME !== undefined ? row.VOLUME : Math.floor(100 + Math.random() * 900)));

      // Check for Failures/Fails column with different capitalizations
      const failures = row.Fails !== undefined ? row.Fails :
                     (row.fails !== undefined ? row.fails :
                     (row.FAILS !== undefined ? row.FAILS :
                     (row.Failures !== undefined ? row.Failures :
                     (row.failures !== undefined ? row.failures :
                     (row.FAILURES !== undefined ? row.FAILURES : Math.round(volume * targetRate * xFactorScore))))));

      console.log(`Extracted data for ${row.Period}: volume=${volume}, failures=${failures}`);


      // Calculate expected failures based on the model (volume * target rate)
      const expectedFailures = volume * targetRate;
      console.log(`Expected failures for ${row.Period}: ${volume} * ${targetRate} = ${expectedFailures}`);

      // Calculate simplified deviation score (actual rate - expected rate)
      let deviationScore = null;
      if (volume > 0) {
        const actualRate = (failures / volume) * 100; // Convert to percentage
        const expectedRate = targetRate * 100; // Convert to percentage
        deviationScore = actualRate - expectedRate;
        console.log(`Deviation score for ${row.Period}: ${actualRate.toFixed(2)}% - ${expectedRate.toFixed(2)}% = ${deviationScore.toFixed(2)}`);
      } else {
        console.log(`Skipping deviation score for ${row.Period}: volume is zero`);
      }

      // Format the date correctly - handle different formats (2024-10, Oct, 2024-20 for week 20)
      let formattedDate = row.Period;

      // Check if we're using weekly or monthly data
      const isWeeklyData = values.timePeriod === 'Weekly';

      if (isWeeklyData) {
        // For weekly data, ensure it's in YYYY-WW format
        if (formattedDate && !formattedDate.includes('-')) {
          // If it's just a week number, add the year
          const year = new Date().getFullYear();
          formattedDate = `${year}-${formattedDate.padStart(2, '0')}`;
        }
      } else {
        // For monthly data
        if (formattedDate && formattedDate.length <= 3) {
          // It's a short month name like "Oct", convert to YYYY-MM format
          const monthMap = {
            'Jan': '2025-01',
            'Feb': '2025-02',
            'Mar': '2025-03',
            'Apr': '2025-04',
            'May': '2025-05',
            'Jun': '2025-06',
            'Jul': '2025-07',
            'Aug': '2025-08',
            'Sep': '2025-09',
            'Oct': '2024-10',
            'Nov': '2024-11',
            'Dec': '2024-12'
          };
          formattedDate = monthMap[formattedDate] || formattedDate;
        } else if (formattedDate && formattedDate.includes('2025')) {
          // Handle "Jan 2025" format
          if (formattedDate.startsWith('Jan')) formattedDate = '2025-01';
          else if (formattedDate.startsWith('Feb')) formattedDate = '2025-02';
          else if (formattedDate.startsWith('Mar')) formattedDate = '2025-03';
        }
      }

      // Add to processed data
      processedData.push({
        date: formattedDate,
        volume: volume,
        failures: failures,
        targetRate: targetRate,
        xFactorScore: xFactorScore,
        expectedFailures: expectedFailures,
        deviationScore: deviationScore,
        isLowVolume: expectedFailures < 5
      });
    });

    // Sort by date
    processedData.sort((a, b) => new Date(a.date) - new Date(b.date));

    // Calculate moving averages for deviation scores (3-month average for monthly data)
    processedData.forEach((item, index) => {
      if (index >= 2 && item.deviationScore !== null) {
        // Get previous two months/periods
        const prevItems = processedData.slice(Math.max(0, index - 2), index);

        // Filter out items with null deviation scores
        const validPrevItems = prevItems.filter(i => i.deviationScore !== null);

        // Calculate average if we have valid previous items
        if (validPrevItems.length > 0) {
          const sum = validPrevItems.reduce((acc, i) => acc + i.deviationScore, 0) + item.deviationScore;
          item.avgDeviationScore = sum / (validPrevItems.length + 1);
          console.log(`3-month avg for ${item.date}: (${validPrevItems.map(i => i.deviationScore.toFixed(2)).join(' + ')} + ${item.deviationScore.toFixed(2)}) / ${validPrevItems.length + 1} = ${item.avgDeviationScore.toFixed(2)}`);
        } else {
          item.avgDeviationScore = item.deviationScore;
          console.log(`3-month avg for ${item.date}: Only current month available, using ${item.avgDeviationScore.toFixed(2)}`);
        }
      } else if (item.deviationScore !== null) {
        item.avgDeviationScore = item.deviationScore;
        console.log(`3-month avg for ${item.date}: Less than 3 months available, using ${item.avgDeviationScore.toFixed(2)}`);
      } else {
        item.avgDeviationScore = null;
        console.log(`3-month avg for ${item.date}: No valid deviation score, setting to null`);
      }

      // Track consecutive months above/below thresholds
      if (index > 0 && item.avgDeviationScore !== null) {
        const prev = processedData[index - 1];

        // Initialize counters if this is the first valid item
        if (prev.consecutiveAboveThreshold === undefined) {
          console.log(`Initializing consecutiveAboveThreshold for previous item ${prev.date}`);
          prev.consecutiveAboveThreshold = 0;
        }
        if (prev.consecutiveBelowThreshold === undefined) {
          console.log(`Initializing consecutiveBelowThreshold for previous item ${prev.date}`);
          prev.consecutiveBelowThreshold = 0;
        }

        // Check if above sustained problem threshold (1.5)
        if (item.avgDeviationScore > 1.5) {
          item.consecutiveAboveThreshold = prev.consecutiveAboveThreshold + 1;
          item.consecutiveBelowThreshold = 0;
          console.log(`${item.date}: avgDeviationScore ${item.avgDeviationScore.toFixed(2)} > 1.5, consecutiveAboveThreshold = ${item.consecutiveAboveThreshold}`);
        }
        // Check if below resolved threshold (1.0)
        else if (item.avgDeviationScore < 1.0) {
          item.consecutiveBelowThreshold = prev.consecutiveBelowThreshold + 1;
          item.consecutiveAboveThreshold = 0;
          console.log(`${item.date}: avgDeviationScore ${item.avgDeviationScore.toFixed(2)} < 1.0, consecutiveBelowThreshold = ${item.consecutiveBelowThreshold}`);
        }
        // Between thresholds
        else {
          item.consecutiveAboveThreshold = 0;
          item.consecutiveBelowThreshold = 0;
          console.log(`${item.date}: avgDeviationScore ${item.avgDeviationScore.toFixed(2)} between thresholds, resetting counters`);
        }
      } else {
        // First item or null deviation score
        if (item.avgDeviationScore !== null) {
          if (item.avgDeviationScore > 1.5) {
            item.consecutiveAboveThreshold = 1;
            item.consecutiveBelowThreshold = 0;
            console.log(`${item.date} (first item): avgDeviationScore ${item.avgDeviationScore.toFixed(2)} > 1.5, consecutiveAboveThreshold = 1`);
          } else if (item.avgDeviationScore < 1.0) {
            item.consecutiveBelowThreshold = 1;
            item.consecutiveAboveThreshold = 0;
            console.log(`${item.date} (first item): avgDeviationScore ${item.avgDeviationScore.toFixed(2)} < 1.0, consecutiveBelowThreshold = 1`);
          } else {
            item.consecutiveAboveThreshold = 0;
            item.consecutiveBelowThreshold = 0;
            console.log(`${item.date} (first item): avgDeviationScore ${item.avgDeviationScore.toFixed(2)} between thresholds, both counters = 0`);
          }
        } else {
          item.consecutiveAboveThreshold = 0;
          item.consecutiveBelowThreshold = 0;
          console.log(`${item.date}: No valid avgDeviationScore, both counters = 0`);
        }
      }

      // Determine alert types
      item.isShortTermAlert = item.deviationScore !== null && item.deviationScore > 3 && item.volume >= 30;
      item.isSustainedProblem = item.consecutiveAboveThreshold >= 3;
      item.isProblemResolved = item.consecutiveBelowThreshold >= 3;

      // Log alert determinations
      if (item.isShortTermAlert) {
        console.log(`${item.date}: SHORT-TERM ALERT - deviation score ${item.deviationScore.toFixed(2)} > 3 and volume ${item.volume} >= 30`);
      }
      if (item.isSustainedProblem) {
        console.log(`${item.date}: SUSTAINED PROBLEM - consecutiveAboveThreshold ${item.consecutiveAboveThreshold} >= 3`);
      }
      if (item.isProblemResolved) {
        console.log(`${item.date}: PROBLEM RESOLVED - consecutiveBelowThreshold ${item.consecutiveBelowThreshold} >= 3`);
      }
    });

    // Count alerts
    const shortTermAlerts = processedData.filter(item => item.isShortTermAlert).length;
    const sustainedProblems = processedData.filter(item => item.isSustainedProblem).length;
    const resolvedProblems = processedData.filter(item => item.isProblemResolved).length;

    // Log alert counts
    console.log('\n==== ALERT COUNTS ====');
    console.log(`Short-term alerts: ${shortTermAlerts}`);
    console.log(`Sustained problems: ${sustainedProblems}`);
    console.log(`Resolved problems: ${resolvedProblems}`);
    console.log('=====================\n');

    // Add alert counts to response
    responseObj.data = processedData;
    responseObj.status_res = "success";
    responseObj.alertCounts = {
      shortTermAlerts,
      sustainedProblems,
      resolvedProblems
    };

    logger.logInfo(`Retrieved ${processedData.length} data points for part group ${values.partGroup} | Alerts: ${shortTermAlerts} short-term, ${sustainedProblems} sustained`, 'partgroup.controller');
    callback(null, responseObj);
  } catch (error) {
    console.error('Error getting part group data:', error);
    logger.logError(`Error getting part group data: ${error.message}`, error, 'partgroup.controller');
    responseObj.status_res = "error";
    responseObj.message = error.message;
    callback(null, responseObj); // Changed to null to prevent crashing the API
  }
}

/**
 * Get available date options for a specific process and time period
 * @param {Object} values - Request values
 * @param {Function} callback - Callback function
 */
exports.get_date_options = function (values, callback) {
  const responseObj = {
    status_res: "error",
    message: "",
    date_options: []
  };

  try {
    console.log('Getting date options for process:', values.process, 'and time period:', values.timePeriod);

    // Determine which Excel file to use based on the process and time period
    let filePath = '';
    const timePeriod = values.timePeriod || 'Monthly';

    if (values.process === "FUL") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FUL weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FUL monthly.xls');
      }
      console.log(`FUL Excel file path (${timePeriod}):`, filePath);
    } else if (values.process === "FAB") {
      if (timePeriod === 'Weekly') {
        filePath = path.join(__dirname, '../excel/z16 FAB weekly.xls');
      } else {
        filePath = path.join(__dirname, '../excel/z16 FAB monthly.xls');
      }
      console.log(`FAB Excel file path (${timePeriod}):`, filePath);
    } else {
      responseObj.message = "Invalid process selected";
      return callback(null, responseObj);
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`Excel file not found: ${filePath}`);
      responseObj.message = `Excel file not found: ${filePath}`;
      return callback(null, responseObj);
    }

    // Read the Excel file
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = xls.utils.sheet_to_json(worksheet);

    console.log(`Read ${jsonData.length} rows from Excel file`);

    // Extract unique dates from the Excel data
    const dateOptions = [];
    jsonData.forEach(row => {
      if (row.Period) {
        // Format the date correctly - handle different formats
        let formattedDate = row.Period;

        // Check if we're using weekly or monthly data
        const isWeeklyData = timePeriod === 'Weekly';

        if (isWeeklyData) {
          // For weekly data, ensure it's in YYYY-WW format
          if (formattedDate && !formattedDate.includes('-')) {
            // If it's just a week number, add the year
            // Try to determine the year from the context
            let year;

            // Check if the row has a year indicator
            if (row.Year) {
              year = row.Year;
            } else {
              // Try to infer year from other data
              // For weeks 1-10, check if they might be from previous year
              const weekNum = parseInt(formattedDate);

              // Check if the formattedDate might already include the year (e.g., "202501")
              if (formattedDate.length > 4 && /^\d{6}$/.test(formattedDate)) {
                // Extract year and week from combined format (e.g., "202501")
                year = formattedDate.substring(0, 4);
                formattedDate = formattedDate.substring(4);
                console.log(`Extracted year ${year} and week ${formattedDate} from combined format`);
              } else if (weekNum >= 40 && weekNum <= 52) {
                // Late weeks are likely from previous year
                year = '2024';
              } else if (weekNum >= 1 && weekNum <= 13) {
                // Early weeks could be from next year
                year = '2025'; // Updated to 2025 for newer data
              } else {
                // Default to current year
                year = '2024'; // Updated to 2024 for newer data
              }

              console.log(`Inferred year ${year} for week ${weekNum}`);
            }

            formattedDate = `${year}-${formattedDate.padStart(2, '0')}`;
          }
        } else {
          // For monthly data
          if (formattedDate && formattedDate.length <= 3) {
            // It's a short month name like "Oct", convert to YYYY-MM format
            // Use the actual dates from the z16 FUL monthly file (starting from April 2022)
            const monthMap = {
              'Jan': '2022-01',
              'Feb': '2022-02',
              'Mar': '2022-03',
              'Apr': '2022-04',
              'May': '2022-05',
              'Jun': '2022-06',
              'Jul': '2022-07',
              'Aug': '2022-08',
              'Sep': '2022-09',
              'Oct': '2022-10',
              'Nov': '2022-11',
              'Dec': '2022-12'
            };
            formattedDate = monthMap[formattedDate] || formattedDate;
          } else if (formattedDate && /\d{4}/.test(formattedDate)) {
            // Handle dates with explicit years (e.g., "Jan 2022", "Apr 2022", etc.)
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            // Extract the year and month
            let year = '';
            let month = '';

            // Find the year (4 digits)
            const yearMatch = formattedDate.match(/\d{4}/);
            if (yearMatch) {
              year = yearMatch[0];
            }

            // Find the month abbreviation
            for (const monthName of monthNames) {
              if (formattedDate.includes(monthName)) {
                month = (monthNames.indexOf(monthName) + 1).toString().padStart(2, '0');
                break;
              }
            }

            // If we found both year and month, format as YYYY-MM
            if (year && month) {
              formattedDate = `${year}-${month}`;
              console.log(`Converted "${row.Period}" to "${formattedDate}"`);
            }
          }
        }

        // Add to date options if not already included
        if (formattedDate && !dateOptions.includes(formattedDate)) {
          dateOptions.push(formattedDate);
        }
      }
    });

    // Sort date options
    dateOptions.sort((a, b) => {
      return new Date(a) - new Date(b);
    });

    console.log('Date options:', dateOptions);

    // Return the date options
    responseObj.date_options = dateOptions;
    responseObj.status_res = "success";
    callback(null, responseObj);
  } catch (error) {
    console.error('Error getting date options:', error);
    responseObj.message = error.message;
    callback(null, responseObj);
  }
}