<style scoped lang = "scss">
@import "../../styles/carbon-utils";
/* Modal styling */
.modal-content {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.modal-column {
  flex: 1;
}

.dropdown-column {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.short-dropdown {
  width: 20%;
  margin-bottom: 15px;
}

/* Title and Chevron button styling */
.modal-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chevron-container {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.chevron-button {
  background: none;
  border: none;
  cursor: pointer;
}

.chevron-icon {
  fill: currentColor;
}

/* Label and paragraph styling */
.bx--label {
  margin-bottom: 5px;
  font-weight: bold;
}

.create-checklist__confirm-rows p {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 0.875rem;
}

/* Chart page grid and tile styling */
.chart-page__grid {
  margin-top: $spacing-08;
  padding: 20px;
}

.chart-page__r2 {
  margin-top: 30px;
}

.cv-button {
  background-color: #0f62fe;
  color: #ffffff;
  padding: 8px 12px; /* Adjust the padding for the main button */
  border-radius: 4px;
  font-size: 14px;
}

.view-button {
  margin-top: 5px; /* Adjust as needed */
  margin-bottom: 5px;
  border-radius: 4px;
  font-size: 12px; /* Smaller font size */
  display: inline-flex; /* Use flexbox to center text */
  background-color: red;
}


cv-tile {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Data table styling */
cv-data-table {
  margin-bottom: 20px;
}

/* Alert styling */
.alert-value {
  color: #FF0000;
  font-weight: bold;
}

.alert-status {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 15px;
  font-weight: bold;
}

.alert-count {
  font-size: 1.2rem;
  color: #FF0000;
}

.button-container {
  display: flex;
  gap: 8px;
}

/* Notification styling */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.notification {
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

.success-notification {
  background-color: #24a148;
  color: white;
}



@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
}
</style>


<template>
  <cv-grid class="chart-page__grid">
    <!-- Success notification -->
    <div v-if="notificationVisible" class="notification-container">
      <div class="notification success-notification">
        {{ notificationMessage }}
      </div>
    </div>
    <MainHeader :expandedSideNav=false :useFixed="useFixed" />
    <div class="dropdown-column">
            <cv-dropdown
              class="short-dropdown"
              v-model="selectedMonth"
              label="Select Month"
              :items="monthOptions"
            ></cv-dropdown>
          </div>
          <div class="dropdown-column">
            <cv-dropdown
              class="short-dropdown"
              v-model="selectedProcess"
              label="Select Process"
              :items="processOptions"
            ></cv-dropdown>
          </div>


    <cv-row>
      <cv-column :sm="2" :md="4" :lg="6">
        <cv-tile :light="lightTile">
          <div v-if="loading" >
          Loading Bar Chart...
          <HBarChart2 :data = [] :loading="loading"/>
          </div>
          <!-- Include the BarChart component -->
          <HBarChart2
            v-if="bar_chart_data.length > 0"
            :data="bar_chart_data"
            :period="this.selectedMonth"
            :process="this.selectedProcess"
            :highlighted-item="highlightedItem"
          />
        </cv-tile>
      </cv-column>
    </cv-row>



    <!-- New row for Problematic X-Factors Table -->
    <cv-row class="chart-page__r2">
      <!-- Table for Problematic X-Factors -->
      <cv-column>
        <cv-tile :light="lightTile">
          <h3>Problematic X-Factors (> 2)</h3>
          <div v-if="loading">
            Loading data...
          </div>
          <div v-else-if="problematic_data.length === 0">
            <p>No problematic X-Factors found for the selected period and process.</p>
          </div>
          <div v-else class="alert-status">
            <p><span class="alert-count">{{ problematic_data.length }}</span> items with X-Factor > 2</p>
          </div>
          <cv-data-table v-if="problematic_data.length > 0" title="Action Items" :columns="alertColumns">
            <template slot="data">
              <cv-data-table-row v-for="item in problematic_data" :key="item.codeName">
                <cv-data-table-cell>{{ item.codeName }}</cv-data-table-cell>
                <cv-data-table-cell>
                  <span class="alert-value">{{ item.xFactor.toFixed(2) }}X</span>
                </cv-data-table-cell>
                <cv-data-table-cell>
                  <cv-text-input
                    :label="'Action for ' + item.codeName"
                    :label-hidden="true"
                    :placeholder="'Enter action plan...'"
                    :value="actionInputs[item.codeName] || ''"
                    @input="updateAction(item.codeName, $event)"
                    :disabled="submittedActions[item.codeName]"
                  />
                </cv-data-table-cell>
                <cv-data-table-cell>
                  <div class="button-container">
                    <cv-button
                      kind="secondary"
                      size="small"
                      @click="seeIssue(item.codeName)"
                    >See Issue</cv-button>
                    <cv-button
                      kind="primary"
                      size="small"
                      @click="submitAction(item.codeName)"
                      :disabled="!actionInputs[item.codeName] || submittedActions[item.codeName]"
                    >{{ submittedActions[item.codeName] ? 'Submitted' : 'Submit' }}</cv-button>
                  </div>
                </cv-data-table-cell>
              </cv-data-table-row>
            </template>
          </cv-data-table>
        </cv-tile>
      </cv-column>
    </cv-row>

  </cv-grid>
</template>





<script>

import HBarChart2 from '../../components/HBarChart2'; //import barchart
import MainHeader from '../../components/MainHeader';
import '@carbon/charts/styles.css';
import { CvTextInput, CvButton } from '@carbon/vue';

export default {
  name: 'CommodityOverview',
  components: {
    MainHeader,
    HBarChart2,
    CvTextInput,
    CvButton,
  },
  data() {
    return {
      lightTile: true,
      bar_chart_data: [], // Initialize bar_chart_data as an empty array
      problematic_data: [], // Data for x-factors above 2
      selectedMonth: "2024-12",
      monthOptions: ["2024-10","2024-11", "2024-12", "2025-01", "2025-02"],
      useFixed: true,
      selectedProcess: "FUL",
      processOptions: ["FAB", "FUL"],
      formattedDate: new Date().toISOString().split('T')[0],
      // Table columns for problematic x-factors
      alertColumns: ['Code Name', 'X-Factor', 'Action', ''],
      // Store actions for each problematic x-factor
      actionInputs: {},
      // Track if actions have been submitted
      submittedActions: {},
      // Notification message
      notificationVisible: false,
      notificationMessage: '',
      // Currently highlighted item in the chart
      highlightedItem: null
    };
  },
  mounted() {
    this.load_bar_chart();
  },
  watch: {
    selectedMonth(newMonth) {
    if (newMonth) {
     this.load_bar_chart()
    }
  },
  selectedProcess(newProcess) {
    if (newProcess) {
     this.load_bar_chart()
    }
  },
  },

  methods: {

  async load_bar_chart() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;

        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "populate_xf_chart", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type, action, process: this.selectedProcess }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          console.log("This is selected month", this.selectedMonth)
          this.bar_chart_data = data.data.filter(row => row.period === this.selectedMonth);
          this.loading = false;
          this.selectedCol = "CODENAME"

          // Filter data for x-factors above 2
          this.problematic_data = this.bar_chart_data.filter(row => row.xFactor > 2);

          // Initialize action inputs for each problematic x-factor
          this.problematic_data.forEach(item => {
            if (!this.actionInputs[item.codeName]) {
              this.$set(this.actionInputs, item.codeName, '');
            }
          });

          // Reset submitted actions when data changes
          this.submittedActions = {};

          console.log("Month data:", this.bar_chart_data);
          console.log("Problematic data:", this.problematic_data);
          console.log("Received data:", data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },

    // Handle action input changes
    updateAction(codeName, value) {
      this.$set(this.actionInputs, codeName, value);
      // Clear submitted status when action is edited
      if (this.submittedActions[codeName]) {
        this.$set(this.submittedActions, codeName, false);
      }
    },

    // Submit action for a specific x-factor
    submitAction(codeName) {
      if (this.actionInputs[codeName] && this.actionInputs[codeName].trim() !== '') {
        // In a real application, you would send this to the server
        console.log(`Action submitted for ${codeName}: ${this.actionInputs[codeName]}`);
        // Mark as submitted
        this.$set(this.submittedActions, codeName, true);
        // Show success message
        this.notificationMessage = `Action for ${codeName} submitted successfully!`;
        this.notificationVisible = true;

        // Hide notification after 3 seconds
        setTimeout(() => {
          this.notificationVisible = false;
        }, 3000);
      }
    },

    // Highlight the specific x-factor on the chart
    seeIssue(codeName) {
      // First scroll to the chart section
      const chartElement = document.querySelector('.chart-page__grid');
      if (chartElement) {
        chartElement.scrollIntoView({ behavior: 'smooth' });

        // Set the highlighted item to trigger the HBarChart2 component's watch
        this.highlightedItem = codeName;
        console.log(`Set highlighted item to ${codeName}`);

        // Reset the highlighted item after a delay to allow for future highlighting of the same item
        setTimeout(() => {
          this.highlightedItem = null;
        }, 6000); // Slightly longer than the highlight duration in the component
      }
    }
  }
};
</script>
