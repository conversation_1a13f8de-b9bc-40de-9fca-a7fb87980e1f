<template>
  <div class="qe-dashboard-container">
    <div class="content-wrapper">
      <!-- Header Section -->
      <div class="dashboard-header">
        <h3>QE Dashboard</h3>
        <div class="last-updated-text">
          Last Updated: {{ new Date().toLocaleString() }}
        </div>
      </div>

      <!-- PQE Overview Section -->
      <div class="section-card">
        <div class="section-header">
          <div class="section-title-container">
            <h4 class="section-title">PQE Owner Overview</h4>
            <div class="section-subtitle">Prioritized by critical issues count</div>
          </div>
        </div>

        <div v-if="isLoading" class="loading-container">
          <cv-inline-loading
            status="active"
            loading-text="Loading PQE data..."
          ></cv-inline-loading>
        </div>

        <div v-else-if="pqeOwners.length > 0" class="pqe-owners-grid">
          <div
            v-for="pqe in sortedPQEOwners"
            :key="pqe.name"
            class="pqe-owner-card"
            :class="getPriorityClass(pqe)"
          >
            <div class="pqe-owner-header">
              <h5 class="pqe-owner-name">{{ pqe.name }}</h5>
              <div class="pqe-owner-metrics">
                <div class="metric-item">
                  <div class="metric-label">Critical</div>
                  <div class="metric-value" :class="getCriticalIssuesClass(pqe.criticalIssues)">
                    {{ pqe.criticalIssues }}
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Outstanding</div>
                  <div class="metric-value" :class="getOutstandingClass(pqe.outstandingIssuesCount)">
                    {{ pqe.outstandingIssuesCount }}
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Resolved</div>
                  <div class="metric-value" :class="getResolvedClass(pqe.resolvedIssuesCount)">
                    {{ pqe.resolvedIssuesCount }}
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">Pending</div>
                  <div class="metric-value" :class="getPendingClass(pqe.pendingActionTrackerCount)">
                    {{ pqe.pendingActionTrackerCount }}
                  </div>
                </div>
              </div>
            </div>

            <div class="pqe-owner-details">
              <div class="groups-section">
                <div class="groups-header">
                  <div class="groups-title">Critical Breakout Groups</div>
                  <div class="groups-count">{{ pqe.criticalGroups.length }}</div>
                </div>
                <div class="groups-list">
                  <div
                    v-for="group in pqe.criticalGroups.slice(0, 3)"
                    :key="group.name"
                    class="group-item"
                    :class="getGroupHighlightClass(group.xFactor)"
                    @click.stop="viewGroupCriticalIssues(pqe, group.name)"
                  >
                    <div class="group-name">{{ group.name }}</div>
                    <div class="group-metrics">
                      <span class="group-metric" title="Outstanding Issues">
                        <span class="metric-icon">O:</span>{{ group.outstandingIssuesCount }}
                      </span>
                      <span class="group-metric" title="Resolved Issues">
                        <span class="metric-icon">R:</span>{{ group.resolvedIssuesCount }}
                      </span>
                      <span class="group-xfactor" :class="getXFactorClass(group.xFactor)">
                        {{ group.xFactor.toFixed(1) }}x
                      </span>
                    </div>
                  </div>
                  <div v-if="pqe.criticalGroups.length > 3" class="more-groups">
                    + {{ pqe.criticalGroups.length - 3 }} more
                  </div>
                </div>
              </div>

              <!-- Removed collective X-Factor as per requirements -->
            </div>

            <div class="pqe-owner-footer">
              <div class="button-group">
                <cv-button
                  kind="primary"
                  size="small"
                  class="issues-button"
                  @click.stop="viewCriticalIssues(pqe)"
                  :disabled="pqe.criticalIssues === 0 && pqe.outstandingIssuesCount === 0 && pqe.resolvedIssuesCount === 0"
                >
                  Show Issues
                </cv-button>
                <cv-button
                  kind="ghost"
                  size="small"
                  class="view-button"
                  @click.stop="viewPQEDashboard(pqe.name)"
                  :disabled="pqe.pendingActionTrackerCount === 0"
                >
                  Pending Action Tracker Updates ({{ pqe.pendingActionTrackerCount }})
                </cv-button>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-data-message">
          No PQE owners found. Please check the breakout_targets Excel file.
        </div>
      </div>

      <!-- Summary Statistics Section -->
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-icon critical-issues">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z"></path>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-value">{{ totalCriticalIssues }}</div>
            <div class="summary-label">Total Critical Issues</div>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon unvalidated">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z"></path>
              <path d="M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z"></path>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-value">{{ totalUnvalidated }}</div>
            <div class="summary-label">Unvalidated Fails</div>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon critical-groups">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z"></path>
            </svg>
          </div>
          <div class="summary-content">
            <div class="summary-value">{{ totalCriticalGroups }}</div>
            <div class="summary-label">Critical Breakout Groups</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Issues Modal -->
    <cv-modal
      :visible="showCriticalIssuesModal"
      @modal-hidden="closeCriticalIssuesModal"
      class="issues-modal"
      :size="'lg'"
    >
      <template slot="title">
        <span v-if="selectedPQE && selectedGroupName">
          Issues for {{ selectedGroupName }} ({{ selectedPQE.name }})
        </span>
        <span v-else-if="selectedPQE">
          Issues for {{ selectedPQE.name }}
        </span>
        <span v-else>
          Issues
        </span>
      </template>
      <template slot="content">
        <div v-if="selectedPQE" class="modal-content">
          <!-- Issue Type Tabs -->
          <div class="issue-type-tabs">
            <cv-button
              kind="ghost"
              size="small"
              class="tab-button"
              :class="{ 'active': activeIssueTab === 'critical' }"
              @click="activeIssueTab = 'critical'"
            >
              Critical ({{ criticalIssues.length }})
            </cv-button>
            <cv-button
              kind="ghost"
              size="small"
              class="tab-button"
              :class="{ 'active': activeIssueTab === 'outstanding' }"
              @click="activeIssueTab = 'outstanding'"
            >
              Outstanding ({{ outstandingIssues.length }})
            </cv-button>
            <cv-button
              kind="ghost"
              size="small"
              class="tab-button"
              :class="{ 'active': activeIssueTab === 'resolved' }"
              @click="activeIssueTab = 'resolved'"
            >
              Resolved ({{ resolvedIssues.length }})
            </cv-button>
          </div>

          <!-- Critical Issues -->
          <div v-if="activeIssueTab === 'critical'" class="issues-list">
            <div v-if="criticalIssues.length === 0" class="no-issues-message">
              No critical issues found.
            </div>
            <div
              v-for="issue in criticalIssues"
              :key="issue.id"
              class="issue-card"
              @click="toggleIssueExpanded(issue)"
              :class="{ 'expanded': isIssueExpanded(issue.id) }"
            >
              <div class="issue-header">
                <div class="issue-tags">
                  <cv-tag
                    :kind="issue.severity === 'high' ? 'red' : 'magenta'"
                    :label="issue.severity === 'high' ? 'High' : 'Medium'"
                  />
                  <cv-tag
                    :kind="getAnalysisTypeTagKind(issue.analysisType)"
                    class="analysis-tag"
                    :label="issue.analysisType"
                  />
                </div>
                <span class="issue-title">{{ issue.category }}</span>
                <div class="issue-metadata">
                  <cv-tag
                    kind="cool-gray"
                    class="month-tag"
                    :label="issue.month"
                  />
                  <span class="issue-multiplier" :class="issue.severity === 'high' ? 'high-severity' : 'medium-severity'">
                    {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}
                  </span>
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isIssueExpanded(issue.id) }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>

              <div class="issue-content" v-if="isIssueExpanded(issue.id)">
                <div class="ai-description">
                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                </div>

                <!-- Action Comment Text Box -->
                <div class="action-comment">
                  <cv-text-area
                    v-model="issue.comment"
                    label="Action Comments"
                    placeholder="Add your comments or action plan here..."
                    :helper-text="issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'"
                  ></cv-text-area>
                </div>

                <div class="issue-actions">
                  <cv-button
                    kind="primary"
                    size="small"
                    @click.stop="updateIssue(issue)"
                  >
                    Update
                  </cv-button>
                </div>
              </div>
            </div>
          </div>

          <!-- Outstanding Issues -->
          <div v-if="activeIssueTab === 'outstanding'" class="issues-list">
            <div v-if="outstandingIssues.length === 0" class="no-issues-message">
              No outstanding issues found.
            </div>
            <div
              v-for="issue in outstandingIssues"
              :key="issue.id"
              class="issue-card outstanding-issue-card"
              @click="toggleOutstandingIssueExpanded(issue)"
              :class="{ 'expanded': isOutstandingIssueExpanded(issue.id) }"
            >
              <div class="issue-header">
                <div class="issue-tags">
                  <cv-tag
                    kind="blue"
                    label="Outstanding"
                  />
                  <cv-tag
                    :kind="getAnalysisTypeTagKind(issue.analysisType)"
                    class="analysis-tag"
                    :label="issue.analysisType"
                  />
                </div>
                <span class="issue-title">{{ issue.category }}</span>
                <div class="issue-metadata">
                  <cv-tag
                    kind="cool-gray"
                    class="month-tag"
                    :label="issue.month"
                  />
                  <span class="issue-multiplier" :class="parseFloat(issue.currentPerformance) > 1.3 ? 'medium-severity' : 'low-severity'">
                    {{ issue.currentPerformance }}
                  </span>
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isOutstandingIssueExpanded(issue.id) }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>

              <div class="issue-content" v-if="isOutstandingIssueExpanded(issue.id)">
                <div class="acceptance-details">
                  <div class="acceptance-date">
                    <strong>Accepted on:</strong> {{ issue.acceptanceDate }}
                  </div>
                  <div class="accepted-by">
                    <strong>Accepted by:</strong> {{ issue.acceptedBy }}
                  </div>
                </div>

                <div class="ai-description">
                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                </div>

                <!-- Action Comment Text Box -->
                <div class="action-comment">
                  <cv-text-area
                    v-model="issue.comment"
                    label="Action Comments"
                    placeholder="Add your comments or action plan here..."
                    :helper-text="issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'"
                  ></cv-text-area>
                </div>

                <div class="issue-actions">
                  <cv-button
                    kind="primary"
                    size="small"
                    @click.stop="updateIssue(issue)"
                  >
                    Update
                  </cv-button>
                </div>
              </div>
            </div>
          </div>

          <!-- Resolved Issues -->
          <div v-if="activeIssueTab === 'resolved'" class="issues-list">
            <div v-if="resolvedIssues.length === 0" class="no-issues-message">
              No resolved issues found.
            </div>
            <div
              v-for="issue in resolvedIssues"
              :key="issue.id"
              class="issue-card resolved-issue-card"
              @click="toggleResolvedIssueExpanded(issue)"
              :class="{ 'expanded': isResolvedIssueExpanded(issue.id) }"
            >
              <div class="issue-header">
                <div class="issue-tags">
                  <cv-tag
                    kind="green"
                    label="Resolved"
                  />
                  <cv-tag
                    :kind="getAnalysisTypeTagKind(issue.analysisType)"
                    class="analysis-tag"
                    :label="issue.analysisType"
                  />
                </div>
                <span class="issue-title">{{ issue.category }}</span>
                <div class="issue-metadata">
                  <cv-tag
                    kind="cool-gray"
                    class="month-tag"
                    :label="issue.month"
                  />
                  <span class="issue-multiplier" :class="issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-severity'">
                    Current: {{ issue.currentPerformance }}
                  </span>
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isResolvedIssueExpanded(issue.id) }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>

              <div class="issue-content" v-if="isResolvedIssueExpanded(issue.id)">
                <div class="resolution-details">
                  <div class="resolution-date">
                    <strong>Resolved on:</strong> {{ issue.resolutionDate }}
                  </div>
                  <div class="original-severity">
                    <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}
                    ({{ issue.increaseMultiplier }}x)
                  </div>
                </div>

                <div class="ai-description">
                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                </div>

                <!-- Action Comment Text Box -->
                <div class="action-comment">
                  <cv-text-area
                    v-model="issue.comment"
                    label="Action Comments"
                    placeholder="Add your comments or action plan here..."
                    :helper-text="issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'"
                  ></cv-text-area>
                </div>

                <div class="issue-actions">
                  <cv-button
                    kind="primary"
                    size="small"
                    @click.stop="updateIssue(issue)"
                  >
                    Update
                  </cv-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="loading-container">
          <cv-inline-loading
            status="active"
            loading-text="Loading issues..."
          ></cv-inline-loading>
        </div>
      </template>
      <template slot="footer">
        <cv-button
          kind="secondary"
          @click="closeCriticalIssuesModal"
        >
          Close
        </cv-button>
      </template>
    </cv-modal>
  </div>
</template>

<script>
import { CvButton, CvInlineLoading, CvModal, CvTag, CvTextArea } from '@carbon/vue';

export default {
  name: 'QEDashboard',
  components: {
    CvButton,
    CvInlineLoading,
    CvModal,
    CvTag,
    CvTextArea
  },
  data() {
    return {
      isLoading: true,
      pqeOwners: [],
      showCriticalIssuesModal: false,
      selectedPQE: null,
      selectedGroupName: null,
      criticalIssues: [],
      outstandingIssues: [],
      resolvedIssues: [],
      expandedIssueIds: [],
      expandedOutstandingIssueIds: [],
      expandedResolvedIssueIds: [],
      activeIssueTab: 'critical'
    };
  },
  computed: {
    // Sort PQE owners by critical issues count (highest first)
    sortedPQEOwners() {
      return [...this.pqeOwners].sort((a, b) => b.criticalIssues - a.criticalIssues);
    },
    // Calculate total critical issues across all PQEs
    totalCriticalIssues() {
      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalIssues, 0);
    },
    // Calculate total unvalidated fails across all PQEs
    totalUnvalidated() {
      return this.pqeOwners.reduce((total, pqe) => total + pqe.unvalidatedCount, 0);
    },
    // Calculate total critical breakout groups across all PQEs
    totalCriticalGroups() {
      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalGroups.length, 0);
    }
  },
  mounted() {
    this.loadPQEOwnersData();
  },
  methods: {
    async loadPQEOwnersData() {
      this.isLoading = true;
      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch PQE owners data from the API
        const response = await fetch('/api-statit2/get_all_pqe_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({})
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.pqeOwners = data.pqe_owners || [];
          console.log(`Loaded data for ${this.pqeOwners.length} PQE owners`);
        } else {
          console.error('Failed to load PQE data:', data.message);
          // Use sample data for development
          this.loadSampleData();
        }
      } catch (error) {
        console.error('Error loading PQE data:', error);
        // Use sample data for development
        this.loadSampleData();
      } finally {
        this.isLoading = false;
      }
    },

    loadSampleData() {
      // Sample data for development
      this.pqeOwners = [
        {
          name: 'Albert G.',
          criticalIssues: 4,
          unvalidatedCount: 12,
          collectiveXFactor: 3.2,
          criticalGroups: [
            { name: 'Fan Themis', xFactor: 3.2 },
            { name: 'Victoria Crypto', xFactor: 1.8 },
            { name: 'Quantum Nexus', xFactor: 1.6 }
          ]
        },
        {
          name: 'Sarah L.',
          criticalIssues: 2,
          unvalidatedCount: 8,
          collectiveXFactor: 2.5,
          criticalGroups: [
            { name: 'Stellar Core', xFactor: 2.5 },
            { name: 'Nebula Drive', xFactor: 1.7 }
          ]
        },
        {
          name: 'Michael T.',
          criticalIssues: 1,
          unvalidatedCount: 5,
          collectiveXFactor: 1.9,
          criticalGroups: [
            { name: 'Pulsar Matrix', xFactor: 1.9 }
          ]
        },
        {
          name: 'Jennifer K.',
          criticalIssues: 0,
          unvalidatedCount: 3,
          collectiveXFactor: 0.9,
          criticalGroups: []
        }
      ];
    },

    viewPQEDashboard(pqeName) {
      // Emit event to select the PQE owner in the parent component
      this.$emit('select-pqe', pqeName);
    },

    viewCriticalIssues(pqe) {
      this.selectedPQE = pqe;
      this.selectedGroupName = null; // Reset group name when viewing all critical issues
      this.activeIssueTab = 'critical'; // Set active tab to critical issues
      this.loadAllIssues(pqe.name);
      this.showCriticalIssuesModal = true;
    },

    closeCriticalIssuesModal() {
      this.showCriticalIssuesModal = false;
      this.selectedPQE = null;
      this.selectedGroupName = null;
      this.criticalIssues = [];
      this.outstandingIssues = [];
      this.resolvedIssues = [];
      this.expandedIssueIds = [];
      this.expandedOutstandingIssueIds = [];
      this.expandedResolvedIssueIds = [];
    },

    async loadAllIssues(pqeOwner) {
      console.log(`Loading all issues for PQE owner: ${pqeOwner}`);

      // Load all issue types in parallel
      await Promise.all([
        this.loadCriticalIssues(pqeOwner),
        this.loadOutstandingIssues(pqeOwner),
        this.loadResolvedIssues(pqeOwner)
      ]);
    },

    async loadCriticalIssues(pqeOwner) {
      console.log(`Loading critical issues for PQE owner: ${pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch critical issues from the API
        const response = await fetch('/api-statit2/get_pqe_critical_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.criticalIssues = data.critical_issues || [];
          console.log(`Loaded ${this.criticalIssues.length} critical issues`);
        } else {
          console.error('Failed to load critical issues:', data.message);
          // Use sample data for development
          this.loadSampleCriticalIssues(pqeOwner);
        }
      } catch (error) {
        console.error('Error loading critical issues:', error);
        // Use sample data for development
        this.loadSampleCriticalIssues(pqeOwner);
      }
    },

    loadSampleCriticalIssues(pqeOwner) {
      // Use our helper method to get sample issues for this PQE owner
      this.criticalIssues = this.getSampleIssuesForPQE(pqeOwner);
    },

    async loadOutstandingIssues(pqeOwner) {
      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch outstanding issues from the API
        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.outstandingIssues = data.outstanding_issues || [];
          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues`);
        } else {
          console.error('Failed to load outstanding issues:', data.message);
          // Use sample data for development
          this.loadSampleOutstandingIssues(pqeOwner);
        }
      } catch (error) {
        console.error('Error loading outstanding issues:', error);
        // Use sample data for development
        this.loadSampleOutstandingIssues(pqeOwner);
      }
    },

    loadSampleOutstandingIssues(pqeOwner) {
      // Sample data for development
      if (pqeOwner === 'Albert G.') {
        this.outstandingIssues = [
          {
            id: 'oi1',
            category: 'Fan Themis',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.4',
            aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',
            comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',
            analysisType: 'Root Cause',
            acceptanceDate: '2024-04-10',
            currentPerformance: '1.3x',
            acceptedBy: 'Engineering Team'
          },
          {
            id: 'oi2',
            category: 'Victoria Crypto',
            month: '2024-05',
            severity: 'medium',
            increaseMultiplier: '1.3',
            aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known supplier issue that has been accepted as within tolerance. Monitoring for any significant changes.',
            comment: 'Supplier variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',
            analysisType: 'Supplier',
            acceptanceDate: '2024-03-22',
            currentPerformance: '1.2x',
            acceptedBy: 'Quality Team'
          }
        ];
      } else if (pqeOwner === 'Sarah L.') {
        this.outstandingIssues = [
          {
            id: 'oi3',
            category: 'Stellar Core',
            month: '2024-05',
            severity: 'medium',
            increaseMultiplier: '1.3',
            aiDescription: 'Stellar Core showing 1.3x increase in failure rate for units manufactured in Q1 2024. This vintage issue has been accepted as a known limitation. Monitoring for any significant changes.',
            comment: 'Q1 2024 units have a known limitation in the power management system. Engineering has accepted this for this vintage. Monitoring for any significant changes.',
            analysisType: 'Vintage',
            acceptanceDate: '2024-04-05',
            currentPerformance: '1.2x',
            acceptedBy: 'Engineering Team'
          }
        ];
      } else {
        this.outstandingIssues = [];
      }
    },

    async loadResolvedIssues(pqeOwner) {
      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch resolved issues from the API
        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.resolvedIssues = data.resolved_issues || [];
          console.log(`Loaded ${this.resolvedIssues.length} resolved issues`);
        } else {
          console.error('Failed to load resolved issues:', data.message);
          // Use sample data for development
          this.loadSampleResolvedIssues(pqeOwner);
        }
      } catch (error) {
        console.error('Error loading resolved issues:', error);
        // Use sample data for development
        this.loadSampleResolvedIssues(pqeOwner);
      }
    },

    loadSampleResolvedIssues(pqeOwner) {
      // Sample data for development
      if (pqeOwner === 'Albert G.') {
        this.resolvedIssues = [
          {
            id: 'ri1',
            category: 'Fan Themis',
            month: '2024-05',
            severity: 'high',
            increaseMultiplier: '2.8',
            aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',
            comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',
            analysisType: 'Root Cause',
            resolutionDate: '2024-05-15',
            currentPerformance: '0.9x'
          },
          {
            id: 'ri2',
            category: 'Victoria Crypto',
            month: '2024-04',
            severity: 'medium',
            increaseMultiplier: '1.7',
            aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific supplier batch. Supplier corrected their process.',
            comment: 'Worked with supplier to improve their quality control. Monitoring new batches closely.',
            analysisType: 'Supplier',
            resolutionDate: '2024-04-28',
            currentPerformance: '0.8x'
          }
        ];
      } else if (pqeOwner === 'Sarah L.') {
        this.resolvedIssues = [
          {
            id: 'ri3',
            category: 'Stellar Core',
            month: '2024-05',
            severity: 'high',
            increaseMultiplier: '2.5',
            aiDescription: 'Stellar Core showed 2.5x spike in May 2024. Root cause was identified as a thermal issue in the power delivery subsystem. Issue was resolved with a design change.',
            comment: 'Implemented design change to improve thermal performance. Monitoring field data.',
            analysisType: 'Root Cause',
            resolutionDate: '2024-05-22',
            currentPerformance: '0.8x'
          }
        ];
      } else {
        this.resolvedIssues = [];
      }
    },

    toggleIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedIssueIds.splice(index, 1);
      }
    },

    toggleOutstandingIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedOutstandingIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedOutstandingIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedOutstandingIssueIds.splice(index, 1);
      }
    },

    toggleResolvedIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedResolvedIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedResolvedIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedResolvedIssueIds.splice(index, 1);
      }
    },

    isIssueExpanded(issueId) {
      return this.expandedIssueIds.includes(issueId);
    },

    isOutstandingIssueExpanded(issueId) {
      return this.expandedOutstandingIssueIds.includes(issueId);
    },

    isResolvedIssueExpanded(issueId) {
      return this.expandedResolvedIssueIds.includes(issueId);
    },

    async updateIssue(issue) {
      console.log('Update issue:', issue);

      if (!this.selectedPQE) {
        console.error('No PQE owner selected');
        return;
      }

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Call the API to update the action tracker
        const response = await fetch('/api-statit2/update_pqe_action', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            issueId: issue.id,
            category: issue.category,
            comment: issue.comment,
            severity: issue.severity,
            pqeOwner: this.selectedPQE.name,
            month: issue.month,
            analysisType: issue.analysisType
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          alert(`Action tracker updated for issue: ${issue.category}`);
        } else {
          console.error('Failed to update action tracker:', data.message);
          alert('Failed to update action tracker. Please try again.');
        }
      } catch (error) {
        console.error('Error updating action tracker:', error);
        alert('Error updating action tracker. Please try again.');
      }
    },

    getPriorityClass(pqe) {
      if (pqe.collectiveXFactor >= 3.0) return 'high-priority';
      if (pqe.collectiveXFactor >= 1.5) return 'medium-priority';
      return 'normal-priority';
    },

    getCriticalIssuesClass(count) {
      if (count >= 3) return 'high-value';
      if (count >= 1) return 'medium-value';
      return 'normal-value';
    },

    getUnvalidatedClass(count) {
      if (count >= 10) return 'high-value';
      if (count >= 5) return 'medium-value';
      return 'normal-value';
    },

    getOutstandingClass(count) {
      if (count >= 4) return 'high-value';
      if (count >= 2) return 'medium-value';
      return 'normal-value';
    },

    getResolvedClass(count) {
      if (count >= 5) return 'high-value';
      if (count >= 3) return 'medium-value';
      return 'normal-value';
    },

    getPendingClass(count) {
      if (count >= 3) return 'high-value';
      if (count >= 1) return 'medium-value';
      return 'normal-value';
    },

    getXFactorClass(xFactor) {
      if (xFactor >= 3.0) return 'high-value';
      if (xFactor >= 1.5) return 'medium-value';
      return 'normal-value';
    },

    getGroupHighlightClass(xFactor) {
      if (xFactor >= 3.0) return 'high-highlight';
      if (xFactor >= 1.5) return 'medium-highlight';
      return 'low-highlight';
    },

    viewGroupCriticalIssues(pqe, groupName) {
      this.selectedPQE = pqe;
      this.selectedGroupName = groupName;
      this.activeIssueTab = 'critical'; // Set active tab to critical issues
      this.loadAllIssuesForGroup(pqe.name, groupName);
      this.showCriticalIssuesModal = true;
    },

    async loadAllIssuesForGroup(pqeOwner, groupName) {
      console.log(`Loading all issues for PQE owner: ${pqeOwner}, group: ${groupName}`);

      // Load all issue types in parallel
      await Promise.all([
        this.loadCriticalIssuesForGroup(pqeOwner, groupName),
        this.loadOutstandingIssuesForGroup(pqeOwner, groupName),
        this.loadResolvedIssuesForGroup(pqeOwner, groupName)
      ]);
    },

    async loadCriticalIssuesForGroup(pqeOwner, groupName) {
      console.log(`Loading critical issues for PQE owner: ${pqeOwner}, group: ${groupName}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch critical issues from the API
        const response = await fetch('/api-statit2/get_pqe_critical_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner,
            groupName: groupName
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // If we have a specific group name, filter issues to only include those for this group
          if (groupName) {
            const allIssues = data.critical_issues || [];
            this.criticalIssues = allIssues.filter(issue =>
              issue.category.includes(groupName) || groupName.includes(issue.category)
            );
          } else {
            this.criticalIssues = data.critical_issues || [];
          }
          console.log(`Loaded ${this.criticalIssues.length} critical issues for group ${groupName}`);
        } else {
          console.error('Failed to load critical issues:', data.message);
          // Use sample data for development
          this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);
        }
      } catch (error) {
        console.error('Error loading critical issues:', error);
        // Use sample data for development
        this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);
      }
    },

    loadSampleCriticalIssuesForGroup(pqeOwner, groupName) {
      // Get all sample issues for this PQE owner
      const allSampleIssues = this.getSampleIssuesForPQE(pqeOwner);

      // Filter to only include issues for this group
      this.criticalIssues = allSampleIssues.filter(issue =>
        issue.category.includes(groupName) || groupName.includes(issue.category)
      );
    },

    async loadOutstandingIssuesForGroup(pqeOwner, groupName) {
      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}, group: ${groupName}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch outstanding issues from the API
        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner,
            groupName: groupName
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter issues to only include those for this group
          const allIssues = data.outstanding_issues || [];
          this.outstandingIssues = allIssues.filter(issue =>
            issue.category.includes(groupName) || groupName.includes(issue.category)
          );
          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for group ${groupName}`);
        } else {
          console.error('Failed to load outstanding issues:', data.message);
          // Use sample data for development
          this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);
        }
      } catch (error) {
        console.error('Error loading outstanding issues:', error);
        // Use sample data for development
        this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);
      }
    },

    loadSampleOutstandingIssuesForGroup(pqeOwner, groupName) {
      // Load all sample outstanding issues for this PQE owner
      this.loadSampleOutstandingIssues(pqeOwner);

      // Filter to only include issues for this group
      this.outstandingIssues = this.outstandingIssues.filter(issue =>
        issue.category.includes(groupName) || groupName.includes(issue.category)
      );
    },

    async loadResolvedIssuesForGroup(pqeOwner, groupName) {
      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}, group: ${groupName}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch resolved issues from the API
        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: pqeOwner,
            groupName: groupName
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter issues to only include those for this group
          const allIssues = data.resolved_issues || [];
          this.resolvedIssues = allIssues.filter(issue =>
            issue.category.includes(groupName) || groupName.includes(issue.category)
          );
          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for group ${groupName}`);
        } else {
          console.error('Failed to load resolved issues:', data.message);
          // Use sample data for development
          this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);
        }
      } catch (error) {
        console.error('Error loading resolved issues:', error);
        // Use sample data for development
        this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);
      }
    },

    loadSampleResolvedIssuesForGroup(pqeOwner, groupName) {
      // Load all sample resolved issues for this PQE owner
      this.loadSampleResolvedIssues(pqeOwner);

      // Filter to only include issues for this group
      this.resolvedIssues = this.resolvedIssues.filter(issue =>
        issue.category.includes(groupName) || groupName.includes(issue.category)
      );
    },

    getSampleIssuesForPQE(pqeOwner) {
      // Sample data for development based on PQE owner
      if (pqeOwner === 'Albert G.') {
        return [
          {
            id: 'ci1',
            category: 'Fan Themis',
            month: '2024-06',
            severity: 'high',
            increaseMultiplier: '3.2',
            aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',
            comment: '',
            analysisType: 'Root Cause'
          },
          {
            id: 'ci2',
            category: 'Victoria Crypto',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.8',
            aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',
            comment: '',
            analysisType: 'Root Cause'
          },
          {
            id: 'ci3',
            category: 'Fan Themis',
            month: '2024-06',
            severity: 'high',
            increaseMultiplier: '2.5',
            aiDescription: 'Fan Themis showing higher failure rates for units manufactured in March 2024. Vintage analysis indicates a 2.5x increase in failures for this manufacturing period.',
            comment: '',
            analysisType: 'Vintage'
          },
          {
            id: 'ci7',
            category: 'Fan Themis',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.7',
            aiDescription: 'Fan Themis units from Supplier XYZ showing higher failure rates. Quality audit recommended.',
            comment: '',
            analysisType: 'Supplier'
          },
          {
            id: 'ci8',
            category: 'Victoria Crypto',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.6',
            aiDescription: 'Victoria Crypto units in North sector showing higher failure rates. Site-specific issue suspected.',
            comment: '',
            analysisType: 'Sector'
          }
        ];
      }
      else if (pqeOwner === 'Sarah L.') {
        return [
          {
            id: 'ci4',
            category: 'Stellar Core',
            month: '2024-06',
            severity: 'high',
            increaseMultiplier: '2.8',
            aiDescription: 'Stellar Core showing 2.8x spike in June 2024. Root cause appears to be related to power delivery issues.',
            comment: '',
            analysisType: 'Root Cause'
          },
          {
            id: 'ci5',
            category: 'Nebula Drive',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.7',
            aiDescription: 'Nebula Drive has sustained problem with 1.7x target rate for 2 consecutive months. Consistent pattern of thermal issues.',
            comment: '',
            analysisType: 'Supplier'
          },
          {
            id: 'ci9',
            category: 'Stellar Core',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.9',
            aiDescription: 'Stellar Core units manufactured in April 2024 showing higher failure rates.',
            comment: '',
            analysisType: 'Vintage'
          }
        ];
      }
      else {
        return [
          {
            id: 'ci6',
            category: 'Generic Component',
            month: '2024-06',
            severity: 'medium',
            increaseMultiplier: '1.6',
            aiDescription: 'Generic component showing 1.6x target rate. Further investigation needed.',
            comment: '',
            analysisType: 'Sector'
          }
        ];
      }
    },

    getAnalysisTypeTagKind(analysisType) {
      // Return different tag colors based on analysis type
      switch (analysisType) {
        case 'Root Cause':
          return 'purple';
        case 'Vintage':
          return 'teal';
        case 'Sector':
          return 'blue';
        case 'Supplier':
          return 'green';
        default:
          return 'gray';
      }
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    }
  }
};
</script>

<style scoped>
.qe-dashboard-container {
  color: #f4f4f4;
}

.content-wrapper {
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 400;
}

.last-updated-text {
  font-size: 0.875rem;
  color: #8d8d8d;
}

.section-card {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 400;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-top: 0.25rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.pqe-owners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.pqe-owner-card {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  border-left: 4px solid transparent;
}

.pqe-owner-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.pqe-owner-card.high-priority {
  border-left-color: #fa4d56;
}

.pqe-owner-card.medium-priority {
  border-left-color: #ff832b;
}

.pqe-owner-card.normal-priority {
  border-left-color: #24a148;
}

.pqe-owner-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.pqe-owner-name {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.pqe-owner-metrics {
  display: flex;
  gap: 1rem;
}

.metric-item {
  text-align: center;
}

.metric-label {
  font-size: 0.75rem;
  color: #8d8d8d;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1rem;
  font-weight: 600;
}

.metric-value.high-value {
  color: #fa4d56;
}

.metric-value.medium-value {
  color: #ff832b;
}

.metric-value.normal-value {
  color: #24a148;
}

.pqe-owner-details {
  flex-grow: 1;
  margin-bottom: 1rem;
}

.groups-section {
  margin-bottom: 1rem;
}

.groups-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.groups-title {
  font-size: 0.875rem;
  color: #c6c6c6;
}

.groups-count {
  font-size: 0.75rem;
  background-color: #393939;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
}

.groups-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #262626;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 3px solid transparent;
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.group-item.high-highlight {
  border-left-color: #fa4d56;
  background-color: rgba(250, 77, 86, 0.1);
}

.group-item.medium-highlight {
  border-left-color: #ff832b;
  background-color: rgba(255, 131, 43, 0.1);
}

.group-item.low-highlight {
  border-left-color: #24a148;
  background-color: rgba(36, 161, 72, 0.1);
}

.group-name {
  font-size: 0.875rem;
}

.group-xfactor {
  font-size: 0.875rem;
  font-weight: 600;
}

.more-groups {
  font-size: 0.75rem;
  color: #8d8d8d;
  text-align: center;
  margin-top: 0.5rem;
}

.collective-xfactor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #262626;
  border-radius: 4px;
  margin-top: 1rem;
}

.xfactor-label {
  font-size: 0.875rem;
}

.xfactor-value {
  font-size: 1rem;
  font-weight: 600;
}

.pqe-owner-footer {
  display: flex;
  justify-content: flex-end;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.view-button, .issues-button {
  width: 100%;
}

.summary-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.summary-card {
  background-color: #262626;
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  border: 1px solid #333333;
}

.summary-icon {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.summary-icon.critical-issues {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.summary-icon.unvalidated {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.summary-icon.critical-groups {
  background-color: rgba(15, 98, 254, 0.1);
  color: #0f62fe;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: #8d8d8d;
}

.no-data-message, .no-issues-message {
  color: #8d8d8d;
  font-size: 1rem;
  text-align: center;
  padding: 2rem;
}

/* Critical Issues Modal Styles */
.issues-modal {
  max-width: 800px;
}

.issue-type-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #333333;
  padding-bottom: 1rem;
}

.tab-button {
  min-width: 120px;
}

.tab-button.active {
  background-color: #0f62fe;
  color: #ffffff;
}

.outstanding-issue-card {
  border-left: 4px solid #0f62fe;
}

.resolved-issue-card {
  border-left: 4px solid #24a148;
}

.acceptance-details, .resolution-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #c6c6c6;
}

.issue-multiplier.good-performance {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.issue-multiplier.low-severity {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.group-metrics {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.group-metric {
  font-size: 0.75rem;
  color: #c6c6c6;
  background-color: #333333;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.metric-icon {
  font-weight: 600;
  margin-right: 0.125rem;
}

.modal-content {
  padding: 1rem 0;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-card {
  background-color: #333333;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.issue-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.issue-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  position: relative;
}

.issue-tags {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-title {
  flex-grow: 1;
  font-weight: 500;
}

.issue-metadata {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-multiplier {
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.issue-multiplier.high-severity {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.issue-multiplier.medium-severity {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.expand-indicator {
  transition: transform 0.2s ease;
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}

.issue-content {
  padding: 0 1rem 1rem;
  border-top: 1px solid #444444;
}

.ai-description {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #262626;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.5;
}

.action-comment {
  margin-bottom: 1rem;
}

.issue-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
</style>
