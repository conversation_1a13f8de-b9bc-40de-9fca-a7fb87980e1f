const xlsx = require('xlsx');
const path = require('path');

// Path to the Excel file
const filePath = path.join(__dirname, 'new_metis_test.xlsx');

// Read the Excel file
const workbook = xlsx.readFile(filePath);

// Get the first sheet
const sheetName = workbook.SheetNames[0];
const sheet = workbook.Sheets[sheetName];

// Convert sheet to JSON
const jsonData = xlsx.utils.sheet_to_json(sheet);

// Print the first row to see the structure
console.log('Column Headers:');
if (jsonData.length > 0) {
  console.log(Object.keys(jsonData[0]));
}

// Print a few rows of data
console.log('\nSample Data (first 3 rows):');
console.log(jsonData.slice(0, 3));

// Check if "Full Breakout Name" column exists
const hasFullBreakoutName = jsonData.length > 0 && jsonData[0].hasOwnProperty('Full Breakout Name');
console.log('\nHas "Full Breakout Name" column:', hasFullBreakoutName);

// If it exists, get unique values from that column
if (hasFullBreakoutName) {
  const uniqueBreakoutNames = [...new Set(jsonData.map(row => row['Full Breakout Name']))];
  console.log('\nUnique "Full Breakout Name" values:');
  console.log(uniqueBreakoutNames);
}
