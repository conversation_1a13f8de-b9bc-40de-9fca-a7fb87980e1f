import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

const getDefaultState = () => {
  return {
    user: null,
    token: null,
    authenticated: null,
    manager_uid: null,
    user_type: null,
    user_id: null,
    table_act_list: [],
    email: null,
    ismanager: null,
    isdelegate: null,
    iscertifier: false,
    istrainer: false,
  }
}

export default new Vuex.Store({
  plugins: [
    createPersistedState({
      storage: window.sessionStorage
    })
  ],
  state: {
    user: localStorage.getItem('user') || null,
    token: localStorage.getItem('token') === 'true' || null,
    authenticated: localStorage.getItem('authenticated') || null,
    manager_uid: localStorage.getItem('manager_uid') || null,
    user_type: localStorage.getItem('user_type') || null,
    ismanager: localStorage.getItem('ismanager') || null,
    user_id: localStorage.getItem('user_talent_id') || null,
    email: localStorage.getItem('email') || null,
    isdelegate: localStorage.getItem('isdelegate') || null,
    iscertifier: false,
    istrainer: false,
    table_act_list: []
  },
  mutations: {
    // set corresponding user info
    setUser(state, user) {
      state.user = user
    },
    setToken(state, token) {
      state.token = token
    },
    setAuthenticated(state, authenticated) {
      state.authenticated = authenticated
    },
    setManagerName(state, manager_uid) {
      state.manager_uid = manager_uid
    },
    setUserType(state, user_type) {
      state.user_type = user_type
    },
    setIsManager(state, ismanager) {
      state.ismanager = ismanager
    },
    setIsDelegate(state, isdelegate) {
      state.isdelegate = isdelegate
    },
    setIsCertifier(state, iscertifier) {
      state.iscertifier = iscertifier
    },
    setIsTrainer(state, istrainer) {
      state.istrainer = istrainer
    },
    setEmail(state, email) {
      state.email = email
    },
    resetState(state) {
      Object.assign(state, getDefaultState())
    },
    setUserID(state, user_id) {
      state.user_id = user_id
    },
    addToMyArray(state, object) {
      //console.log('item added: '+ object.key + ':' + object.value )
      state.table_act_list.push(object)
      console.log(object)

    },
  },
  actions: {
    resetCartState({ commit }) {
      commit('resetState')
    },
    addObject({ commit }, object) {
      commit('addToMyArray', object);
    }
  },
  modules: {},
  getters: {
    // get corresponding user info
    isLoggedIn(state) {
      return !!state.token
    },
    getUser(state) {
      return state.user
    },
    getAuth(state) {
      return state.authenticated
    },
    getIsManager(state) {
      return state.ismanager
    },
    getManager_uid(state) {
      return state.manager_uid
    },
    getIsDelegate(state) {
      return state.isdelegate
    },
    getIsCertifier(state) {
      return state.iscertifier
    },
    getIsTrainer(state) {
      return state.istrainer
    },
    getUser_type(state) {
      return state.user_type
    },
    getUser_ID(state) {
      return state.user_id
    },
    getToken(state) {
      return state.token
    },
    getValueByKey: (state) => (key) => {
      const matchedObject = state.table_act_list.find((obj) => obj.key === key);
      return matchedObject ? matchedObject.value : null;
    },
    getEmail(state) {
      return state.email
    }
  }
})
