{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue?vue&type=style&index=0&id=c59387d2&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue", "mtime": 1748529056975}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5mYWlscy1jaGFydC1jb250YWluZXIgewogIHdpZHRoOiAxMDAlOwp9CgouY2hhcnQtY29udHJvbHMgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxcmVtOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMXJlbTsKfQoKLmNvbnRyb2wtZ3JvdXAgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDAuNXJlbTsKfQoKLmNvbnRyb2wtbGFiZWwgewogIGNvbG9yOiAjOGQ4ZDhkOwogIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKfQoKLmNvbnRyb2wtZHJvcGRvd24gewogIHdpZHRoOiAyMDBweDsKfQoKLmNoYXJ0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMXJlbSAwOwogIG1pbi1oZWlnaHQ6IDQwMHB4Owp9CgouY2hhcnQtd3JhcHBlciB7CiAgaGVpZ2h0OiA0MDBweDsKICB3aWR0aDogMTAwJTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoubG9hZGluZy1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBoZWlnaHQ6IDQwMHB4Owp9Cgoubm8tZGF0YS1tZXNzYWdlIHsKICBjb2xvcjogIzhkOGQ4ZDsKICBmb250LXNpemU6IDFyZW07CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDJyZW07Cn0K"}, {"version": 3, "sources": ["FailsStackedBarChart.vue"], "names": [], "mappings": ";AAieA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "FailsStackedBarChart.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\n  <div class=\"fails-chart-container\">\n    <div class=\"chart-controls\">\n      <div class=\"control-group\">\n        <label for=\"chart-type-dropdown\" class=\"control-label\">View By:</label>\n        <cv-dropdown\n          id=\"chart-type-dropdown\"\n          v-model=\"chartType\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"validation\">Validated/Unvalidated</cv-dropdown-item>\n          <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n      <div class=\"control-group\">\n        <label for=\"time-range-dropdown\" class=\"control-label\">Time Range:</label>\n        <cv-dropdown\n          id=\"time-range-dropdown\"\n          v-model=\"timeRange\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n          <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n      <div class=\"control-group\" v-if=\"breakoutGroups && breakoutGroups.length > 0\">\n        <label for=\"group-dropdown\" class=\"control-label\">Group:</label>\n        <cv-dropdown\n          id=\"group-dropdown\"\n          v-model=\"selectedGroup\"\n          class=\"control-dropdown\"\n        >\n          <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n          <cv-dropdown-item\n            v-for=\"group in breakoutGroups\"\n            :key=\"group.name\"\n            :value=\"group.name\"\n          >\n            {{ group.name }}\n          </cv-dropdown-item>\n        </cv-dropdown>\n      </div>\n    </div>\n\n    <div class=\"chart-container\">\n      <div v-if=\"isLoading\" class=\"loading-container\">\n        <cv-inline-loading\n          status=\"active\"\n          loading-text=\"Loading chart data...\"\n        ></cv-inline-loading>\n      </div>\n      <div v-else-if=\"chartData.length > 0\" class=\"chart-wrapper\">\n        <StackedBarChart\n          :data=\"chartData\"\n          :options=\"chartOptions\"\n        ></StackedBarChart>\n      </div>\n      <div v-else class=\"no-data-message\">\n        No data available for the selected criteria.\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { CvDropdown, CvDropdownItem, CvInlineLoading } from '@carbon/vue';\nimport { StackedBarChart } from '@carbon/charts-vue';\n\nexport default {\n  name: 'FailsStackedBarChart',\n  components: {\n    CvDropdown,\n    CvDropdownItem,\n    CvInlineLoading,\n    StackedBarChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    },\n    breakoutGroups: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      isLoading: false,\n      chartData: [],\n      chartOptions: {},\n      chartType: 'rootCause', // 'validation', 'rootCause', 'supplier', 'sector', 'vintage'\n      timeRange: '6month', // '3month', '6month'\n      selectedGroup: 'all', // 'all' or a specific group name\n      months: []\n    };\n  },\n  mounted() {\n    console.log('FailsStackedBarChart component mounted');\n    if (this.pqeOwner) {\n      this.loadChartData();\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue) {\n        if (newValue) {\n          this.loadChartData();\n        }\n      }\n    },\n    chartType() {\n      this.loadChartData();\n    },\n    timeRange() {\n      this.loadChartData();\n    },\n    selectedGroup() {\n      this.loadChartData();\n    }\n  },\n  methods: {\n\n    async loadChartData() {\n      console.log(`Loading chart data for PQE owner: ${this.pqeOwner}, Chart Type: ${this.chartType}, Time Range: ${this.timeRange}, Group: ${this.selectedGroup}`);\n      console.log('Breakout groups:', this.breakoutGroups);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Determine the number of months to fetch based on the time range\n        const monthsToFetch = this.timeRange === '3month' ? 3 : 6;\n\n        // Fetch chart data from the API\n        const response = await fetch('/api-statit2/get_pqe_chart_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            chartType: this.chartType,\n            monthsToFetch: monthsToFetch,\n            groupName: this.selectedGroup === 'all' ? null : this.selectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch chart data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Process the chart data\n          this.processChartData(data.chart_data || []);\n        } else {\n          console.error('Failed to load chart data:', data.message);\n          // Use sample data for development\n          this.loadSampleChartData();\n        }\n      } catch (error) {\n        console.error('Error loading chart data:', error);\n        // Use sample data for development\n        this.loadSampleChartData();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleChartData() {\n      console.log('Loading sample chart data');\n\n      // Generate sample months (last 6 or 3 months)\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.timeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const monthName = date.toLocaleString('default', { month: 'short' });\n        const year = date.getFullYear().toString().slice(-2);\n        months.push(`${monthName} '${year}`);\n      }\n\n      this.months = months;\n\n      // Generate sample data based on chart type\n      if (this.chartType === 'validation') {\n        this.generateValidationChartData();\n      } else if (this.chartType === 'rootCause') {\n        this.generateRootCauseChartData();\n      }\n\n      // Filter data by selected group if needed\n      if (this.selectedGroup !== 'all') {\n        this.filterDataByGroup();\n      }\n\n      console.log('Generated chart data:', this.chartData);\n    },\n\n    filterDataByGroup() {\n      // If a specific group is selected, filter the chart data\n      console.log(`Filtering chart data by group: ${this.selectedGroup}`);\n\n      if (this.selectedGroup !== 'all') {\n        // Create a new filtered dataset that only includes data points for the selected group\n        const filteredData = [];\n\n        // For each data point, check if it belongs to the selected group\n        this.chartData.forEach(item => {\n          // For validation chart type, we need to add the group name to the data\n          if (this.chartType === 'validation') {\n            // Create a new item with the group name prefixed\n            const newItem = { ...item };\n            newItem.group = `${this.selectedGroup} - ${item.group}`;\n            filteredData.push(newItem);\n          }\n          // For other chart types, only include items that contain the selected group name\n          else if (item.group.includes(this.selectedGroup)) {\n            filteredData.push(item);\n          }\n        });\n\n        console.log(`Filtered from ${this.chartData.length} to ${filteredData.length} data points`);\n        this.chartData = filteredData;\n      }\n    },\n\n    generateValidationChartData() {\n      const data = [];\n\n      // For each month, generate validated and unvalidated counts\n      this.months.forEach(month => {\n        // Generate random values\n        const validated = Math.floor(Math.random() * 50) + 20;\n        const unvalidated = Math.floor(Math.random() * 30) + 5;\n\n        // Add validated data point\n        data.push({\n          group: 'Validated',\n          date: month,\n          value: validated\n        });\n\n        // Add unvalidated data point\n        data.push({\n          group: 'Unvalidated',\n          date: month,\n          value: unvalidated\n        });\n      });\n\n      // Sort data by date\n      this.sortChartDataByDate(data);\n\n      this.chartData = data;\n      this.updateChartOptions();\n    },\n\n    sortChartDataByDate(data) {\n      // Sort data by date to ensure proper ordering\n      data.sort((a, b) => {\n        // Extract month and year from date string (e.g., \"Jun '24\")\n        const [aMonth, aYear] = a.date.split(\" \");\n        const [bMonth, bYear] = b.date.split(\" \");\n\n        // Compare years first\n        if (aYear !== bYear) {\n          return aYear.localeCompare(bYear);\n        }\n\n        // If years are the same, compare months\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        return months.indexOf(aMonth) - months.indexOf(bMonth);\n      });\n    },\n\n    generateRootCauseChartData() {\n      const data = [];\n      // Match the root causes from the Metis XFactors format\n      const rootCauses = ['KPARS1', 'DIAG', 'OTHER', 'LINK', 'FDB', 'DAP', 'CODE', 'BIOS', 'HLA'];\n\n      // For each month, generate data for each root cause\n      this.months.forEach(month => {\n        // Generate a total value for the month (this will be used to calculate xFactor)\n        const totalValue = Math.floor(Math.random() * 100) + 50;\n\n        // Generate a target value (expected failures)\n        const targetValue = Math.floor(totalValue * 0.7);\n\n        // Distribute the total value among the root causes\n        let remainingValue = totalValue;\n\n        rootCauses.forEach((cause, index) => {\n          // For the last cause, use the remaining value to ensure the sum equals totalValue\n          let value;\n          if (index === rootCauses.length - 1) {\n            value = remainingValue;\n          } else {\n            // Generate a random portion of the remaining value\n            const portion = Math.random() * 0.5; // Up to 50% of remaining\n            value = Math.floor(remainingValue * portion);\n            remainingValue -= value;\n          }\n\n          // Only add data points with non-zero values\n          if (value > 0) {\n            // Add data point\n            data.push({\n              group: cause,\n              date: month,\n              value: value\n            });\n          }\n        });\n\n        // Add a reference line for the target value\n        data.push({\n          group: 'Target',\n          date: month,\n          value: targetValue\n        });\n      });\n\n      // Sort data by date\n      this.sortChartDataByDate(data);\n\n      this.chartData = data;\n      this.updateChartOptions('rootCause');\n    },\n\n\n\n    processChartData(chartData) {\n      // Process the chart data from the API\n      // Sort data by date\n      this.sortChartDataByDate(chartData);\n      this.chartData = chartData;\n      this.updateChartOptions();\n    },\n\n    updateChartOptions(chartType = null) {\n      // Set chart options based on the chart type\n      const title = this.getChartTitle();\n      console.log(`Updating chart options with title: ${title}`);\n\n      // Use the provided chart type or the current chart type\n      const type = chartType || this.chartType;\n\n      // Base chart options\n      const options = {\n        title: title,\n        axes: {\n          left: {\n            mapsTo: 'value',\n            title: 'Count',\n            scaleType: 'linear',\n            includeZero: true\n          },\n          bottom: {\n            mapsTo: 'date',\n            title: 'Month',\n            scaleType: 'labels'\n          }\n        },\n        height: '400px',\n        width: '100%',\n        resizable: true,\n        theme: 'g90', // Dark theme to match the imported styles\n        legend: {\n          alignment: 'center',\n          position: 'bottom'\n        },\n        color: {\n          scale: this.getColorScale(type)\n        },\n        toolbar: {\n          enabled: true\n        },\n        data: {\n          groupMapsTo: 'group'\n        },\n        stacked: true\n      };\n\n      // For root cause analysis, add special handling for the target line\n      if (type === 'rootCause') {\n        // Add special handling for the target line\n        options.data.groups = [\n          {\n            name: 'Target',\n            type: 'line'\n          }\n        ];\n\n        // Add corresponding datasets to make the target line work\n        options.data.correspondingDatasets = [\n          {\n            type: 'line',\n            select: (data) => data.group === 'Target'\n          }\n        ];\n      }\n\n      this.chartOptions = options;\n\n      console.log('Chart options updated:', this.chartOptions);\n    },\n\n    getChartTitle() {\n      // Base title based on chart type\n      let baseTitle = '';\n      switch (this.chartType) {\n        case 'validation':\n          baseTitle = 'Validated vs Unvalidated Fails';\n          break;\n        case 'rootCause':\n          baseTitle = 'Fails by Root Cause';\n          break;\n        default:\n          baseTitle = 'Fails Analysis';\n      }\n\n      // Add group name if a specific group is selected\n      if (this.selectedGroup !== 'all') {\n        return `${baseTitle} - ${this.selectedGroup}`;\n      }\n\n      return baseTitle;\n    },\n\n    getColorScale(chartType = null) {\n      // Use the provided chart type or the current chart type\n      const type = chartType || this.chartType;\n\n      // Return the appropriate color scale based on the chart type\n      switch (type) {\n        case 'validation':\n          return {\n            'Validated': '#24a148',\n            'Unvalidated': '#ff832b'\n          };\n        case 'rootCause':\n          return {\n            'KPARS1': '#0f62fe',\n            'DIAG': '#6929c4',\n            'OTHER': '#1192e8',\n            'LINK': '#8a3ffc',\n            'FDB': '#ee5396',\n            'DAP': '#ff7eb6',\n            'CODE': '#fa4d56',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba',\n            'Target': '#f1c21b' // Yellow for target line\n          };\n        default:\n          return {};\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.fails-chart-container {\n  width: 100%;\n}\n\n.chart-controls {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  padding: 1rem 0;\n  min-height: 400px;\n}\n\n.chart-wrapper {\n  height: 400px;\n  width: 100%;\n  overflow: hidden;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n</style>\n"]}]}