{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue", "mtime": 1748528022241}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7CiAgQ3ZCdXR0b24sCiAgQ3ZNb2RhbCwKICBDdlRhZywKICBDdlRleHRBcmVhLAogIEN2SW5saW5lTG9hZGluZywKICBDdkRyb3Bkb3duLAogIEN2RHJvcGRvd25JdGVtCn0gZnJvbSAnQGNhcmJvbi92dWUnOwoKLy8gSW1wb3J0IENhcmJvbiBDaGFydHMKaW1wb3J0IHsgQ29tYm9DaGFydCB9IGZyb20gIkBjYXJib24vY2hhcnRzIjsKaW1wb3J0ICJAY2FyYm9uL2NoYXJ0cy9zdHlsZXMuY3NzIjsKCi8vIEltcG9ydCBSb290IENhdXNlIENoYXJ0IGNvbXBvbmVudAppbXBvcnQgUm9vdENhdXNlQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL1Jvb3RDYXVzZUNoYXJ0L1Jvb3RDYXVzZUNoYXJ0LnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BRRURhc2hib2FyZCcsCiAgY29tcG9uZW50czogewogICAgQ3ZCdXR0b24sCiAgICBDdk1vZGFsLAogICAgQ3ZUYWcsCiAgICBDdlRleHRBcmVhLAogICAgQ3ZJbmxpbmVMb2FkaW5nLAogICAgQ3ZEcm9wZG93biwKICAgIEN2RHJvcGRvd25JdGVtLAogICAgUm9vdENhdXNlQ2hhcnQKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyBDcml0aWNhbCBJc3N1ZXMgRGF0YQogICAgICBuZXdDcml0aWNhbElzc3VlczogW10sCiAgICAgIHVucmVzb2x2ZWRDcml0aWNhbElzc3VlczogW10sCiAgICAgIGV4cGFuZGVkSXNzdWVJZHM6IFtdLCAvLyBUcmFjayB3aGljaCBpc3N1ZXMgYXJlIGV4cGFuZGVkCiAgICAgIGlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZDogZmFsc2UsIC8vIFRyYWNrIGlmIGNyaXRpY2FsIGlzc3VlcyBzZWN0aW9uIGlzIGV4cGFuZGVkCgogICAgICAvLyBGaWx0ZXJpbmcKICAgICAgc2VsZWN0ZWRGaWx0ZXJzOiB7CiAgICAgICAgc2V2ZXJpdHk6IFtdLAogICAgICAgIGFuYWx5c2lzVHlwZTogW10KICAgICAgfSwKICAgICAgc2V2ZXJpdHlGaWx0ZXI6ICdhbGwnLAogICAgICBhbmFseXNpc1R5cGVGaWx0ZXI6ICdhbGwnLAoKICAgICAgLy8gQWR2YW5jZWQgQW5hbHlzaXMKICAgICAgc2VsZWN0ZWRBbmFseXNpc1R5cGU6ICdSb290IENhdXNlJywgLy8gRGVmYXVsdCBhbmFseXNpcyB0eXBlCiAgICAgIHNlbGVjdGVkQnJlYWtvdXRHcm91cDogbnVsbCwgLy8gV2lsbCBiZSBzZXQgd2hlbiBhIGJyZWFrb3V0IGdyb3VwIGlzIHNlbGVjdGVkCgogICAgICAvLyBDaGFydCBpbnN0YW5jZXMKICAgICAgY2F0ZWdvcnlTdW1tYXJ5Q2hhcnQ6IG51bGwsCiAgICAgIHZhbGlkYXRpb25DaGFydDogbnVsbCwKCiAgICAgIC8vIEN1cnJlbnQgTW9udGgKICAgICAgY3VycmVudE1vbnRoOiAnJywgLy8gV2lsbCBiZSBzZXQgdG8gY3VycmVudCBtb250aCBpbiBZWVlZLU1NIGZvcm1hdAoKICAgICAgLy8gTG9hZGluZyBTdGF0ZXMKICAgICAgaXNMb2FkaW5nQnJlYWtvdXRHcm91cHM6IGZhbHNlLAogICAgICBpc0xvYWRpbmdUcmFja2VkSXRlbXM6IGZhbHNlLAoKICAgICAgLy8gVmFsaWRhdGlvbiBEYXRhCiAgICAgIHZhbGlkYXRlZENvdW50OiAwLAogICAgICB1bnZhbGlkYXRlZENvdW50OiAwLAogICAgICB2YWxpZGF0aW9uQ2hhcnREYXRhOiBbXSwKCiAgICAgIC8vIFJvb3QgQ2F1c2UgQ2hhcnQgRGF0YQogICAgICByb290Q2F1c2VDaGFydERhdGE6IFtdLAogICAgICBpc1Jvb3RDYXVzZURhdGFMb2FkaW5nOiBmYWxzZSwKICAgICAgdmFsaWRhdGlvbkNoYXJ0T3B0aW9uczogewogICAgICAgIHRpdGxlOiAnRGFpbHkgU3RhdHVzJywKICAgICAgICBheGVzOiB7CiAgICAgICAgICBsZWZ0OiB7CiAgICAgICAgICAgIHRpdGxlOiAnQ291bnQnLAogICAgICAgICAgICBtYXBzVG86ICd2YWx1ZScsCiAgICAgICAgICAgIGRvbWFpbjogWzAsIDE1MF0sIC8vIEluY3JlYXNlZCByYW5nZSB0byBhY2NvbW1vZGF0ZSB2b2x1bWUgZGF0YQogICAgICAgICAgICBzY2FsZVR5cGU6ICdsaW5lYXInCiAgICAgICAgICB9LAogICAgICAgICAgcmlnaHQ6IHsKICAgICAgICAgICAgdGl0bGU6ICdZaWVsZCAoJSknLAogICAgICAgICAgICBtYXBzVG86ICd2YWx1ZScsCiAgICAgICAgICAgIGRvbWFpbjogWzAsIDEwMF0sCiAgICAgICAgICAgIHNjYWxlVHlwZTogJ2xpbmVhcicsCiAgICAgICAgICAgIGNvcnJlc3BvbmRpbmdEYXRhc2V0czogWwogICAgICAgICAgICAgICdEYWlseSBZaWVsZCcsCiAgICAgICAgICAgICAgJ0N1bXVsYXRpdmUgWWllbGQnLAogICAgICAgICAgICAgICdUYXJnZXQgWWllbGQnCiAgICAgICAgICAgIF0KICAgICAgICAgIH0sCiAgICAgICAgICBib3R0b206IHsKICAgICAgICAgICAgdGl0bGU6ICdEYXknLAogICAgICAgICAgICBtYXBzVG86ICdkYXRlJywKICAgICAgICAgICAgc2NhbGVUeXBlOiAndGltZScKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGhlaWdodDogJzM1MHB4JywKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIGFsaWdubWVudDogJ2NlbnRlcicsCiAgICAgICAgICBlbmFibGVkOiB0cnVlLAogICAgICAgICAgZm9udENvbG9yOiAnI2Y0ZjRmNCcgLy8gV2hpdGUgdGV4dCBmb3IgbGVnZW5kCiAgICAgICAgfSwKICAgICAgICBjb2xvcjogewogICAgICAgICAgc2NhbGU6IHsKICAgICAgICAgICAgJ1ZhbGlkYXRlZCc6ICcjMDA2MmZmJywgLy8gQmx1ZQogICAgICAgICAgICAnVW52YWxpZGF0ZWQnOiAnI2ZmODMyYicsIC8vIE9yYW5nZQogICAgICAgICAgICAnVm9sdW1lJzogJyMyNGExNDgnLCAvLyBHcmVlbgogICAgICAgICAgICAnRGFpbHkgWWllbGQnOiAnIzY5MjljNCcsIC8vIFB1cnBsZQogICAgICAgICAgICAnQ3VtdWxhdGl2ZSBZaWVsZCc6ICcjMTE5MmU4JywgLy8gTGlnaHQgYmx1ZQogICAgICAgICAgICAnVGFyZ2V0IFlpZWxkJzogJyNmYTRkNTYnIC8vIFJlZAogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgdG9vbGJhcjogewogICAgICAgICAgZW5hYmxlZDogZmFsc2UKICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIGVuYWJsZWQ6IHRydWUKICAgICAgICB9LAogICAgICAgIGFuaW1hdGlvbnM6IHRydWUsCiAgICAgICAgZGF0YTogewogICAgICAgICAgZ3JvdXBNYXBzVG86ICdncm91cCcKICAgICAgICB9LAogICAgICAgIHRoZW1lOiAnZzEwMCcsIC8vIFVzZSBkYXJrZXN0IHRoZW1lIGZvciBiZXR0ZXIgY29udHJhc3QKICAgICAgICBncmlkOiB7CiAgICAgICAgICB4OiB7CiAgICAgICAgICAgIGVuYWJsZWQ6IGZhbHNlCiAgICAgICAgICB9LAogICAgICAgICAgeTogewogICAgICAgICAgICBlbmFibGVkOiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBjb21ib0NoYXJ0VHlwZXM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdHlwZTogJ3N0YWNrZWQtYmFyJywKICAgICAgICAgICAgY29ycmVzcG9uZGluZ0RhdGFzZXRzOiBbJ1ZhbGlkYXRlZCcsICdVbnZhbGlkYXRlZCddLAogICAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgICAgZmlsbE9wYWNpdHk6IDAuOAogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgIGNvcnJlc3BvbmRpbmdEYXRhc2V0czogWydEYWlseSBZaWVsZCcsICdDdW11bGF0aXZlIFlpZWxkJywgJ1RhcmdldCBZaWVsZCddLAogICAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgICAgcG9pbnRzOiB7CiAgICAgICAgICAgICAgICByYWRpdXM6IDMKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHN0cm9rZVdpZHRoOiAyCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIHRocmVzaG9sZHM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgYXhpczogJ3JpZ2h0JywKICAgICAgICAgICAgdmFsdWU6IDk1LCAvLyBUYXJnZXQgeWllbGQgdGhyZXNob2xkCiAgICAgICAgICAgIGxhYmVsOiAnVGFyZ2V0IFlpZWxkICg5NSUpJywKICAgICAgICAgICAgZmlsbENvbG9yOiAnI2ZhNGQ1NicsCiAgICAgICAgICAgIG9wYWNpdHk6IDAuMQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKCiAgICAgIC8vIEJyZWFrb3V0IEdyb3VwcyBEYXRhCiAgICAgIGJyZWFrb3V0R3JvdXBzOiBbXSwKICAgICAgc2hvd0FsbEJyZWFrb3V0R3JvdXBzOiBmYWxzZSwgLy8gVG9nZ2xlIGZvciBzaG93aW5nIGFsbCBicmVha291dCBncm91cHMKICAgICAgc2hvd0FsbEFsZXJ0czogZmFsc2UsIC8vIFRvZ2dsZSBmb3Igc2hvd2luZyBhbGwgYWxlcnRzIChpbmNsdWRpbmcgbWVkaXVtIHByaW9yaXR5KQoKICAgICAgLy8gSXRlbSBUcmFja2luZyBEYXRhCiAgICAgIHRyYWNrZWRJdGVtczogW10sCgogICAgICAvLyBNb2RhbCBEYXRhCiAgICAgIHNob3dJc3N1ZU1vZGFsOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRJc3N1ZTogbnVsbCwKICAgICAgaXNzdWVDaGFydERhdGE6IFtdLAogICAgICBpc3N1ZUNoYXJ0T3B0aW9uczogewogICAgICAgIHRpdGxlOiAnSXNzdWUgRGV0YWlscycsCiAgICAgICAgYXhlczogewogICAgICAgICAgbGVmdDogewogICAgICAgICAgICB0aXRsZTogJ1gtRmFjdG9yJywKICAgICAgICAgICAgbWFwc1RvOiAndmFsdWUnLAogICAgICAgICAgICBkb21haW46IFswLCA0XQogICAgICAgICAgfSwKICAgICAgICAgIGJvdHRvbTogewogICAgICAgICAgICB0aXRsZTogJ01vbnRoJywKICAgICAgICAgICAgbWFwc1RvOiAnbW9udGgnLAogICAgICAgICAgICBzY2FsZVR5cGU6ICdsYWJlbHMnCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBoZWlnaHQ6ICczMDBweCcsCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBlbmFibGVkOiBmYWxzZQogICAgICAgIH0sCiAgICAgICAgY29sb3I6IHsKICAgICAgICAgIHNjYWxlOiB7CiAgICAgICAgICAgICdYLUZhY3Rvcic6ICcjMDA2MmZmJwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgdG9vbGJhcjogewogICAgICAgICAgZW5hYmxlZDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwKICAgICAgICAgIGN1c3RvbUhUTUw6IChkYXRhKSA9PiB7CiAgICAgICAgICAgIHJldHVybiBgCiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0icGFkZGluZzogMTBweDsgYmFja2dyb3VuZDogI2Y0ZjRmNDsgYm9yZGVyLXJhZGl1czogNHB4OyI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogNXB4OyI+JHtkYXRhWzBdLmRhdGEubW9udGh9PC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PlgtRmFjdG9yOiAke2RhdGFbMF0udmFsdWUudG9GaXhlZCgyKX08L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgYDsKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGFuaW1hdGlvbnM6IHRydWUsCiAgICAgICAgdGhyZXNob2xkczogWwogICAgICAgICAgewogICAgICAgICAgICB2YWx1ZTogMS41LAogICAgICAgICAgICBsYWJlbDogJ1N1c3RhaW5lZCBQcm9ibGVtIFRocmVzaG9sZCcsCiAgICAgICAgICAgIGZpbGxDb2xvcjogJyNmZjlhMDAnLAogICAgICAgICAgICBvcGFjaXR5OiAwLjEKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHZhbHVlOiAzLjAsCiAgICAgICAgICAgIGxhYmVsOiAnQ3JpdGljYWwgU3Bpa2UgVGhyZXNob2xkJywKICAgICAgICAgICAgZmlsbENvbG9yOiAnI2ZhNGQ1NicsCiAgICAgICAgICAgIG9wYWNpdHk6IDAuMQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmxvYWREYXNoYm9hcmREYXRhKCk7CiAgfSwKICBjb21wdXRlZDogewogICAgLy8gRmlsdGVyZWQgY3JpdGljYWwgaXNzdWVzIGJhc2VkIG9uIHNlbGVjdGVkIGZpbHRlcnMKICAgIGZpbHRlcmVkQ3JpdGljYWxJc3N1ZXMoKSB7CiAgICAgIC8vIElmIG5vIGZpbHRlcnMgYXJlIHNlbGVjdGVkLCByZXR1cm4gYWxsIGlzc3VlcwogICAgICBpZiAodGhpcy5zZWxlY3RlZEZpbHRlcnMuc2V2ZXJpdHkubGVuZ3RoID09PSAwICYmIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICByZXR1cm4gdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXM7CiAgICAgIH0KCiAgICAgIC8vIEFwcGx5IGZpbHRlcnMKICAgICAgcmV0dXJuIHRoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLmZpbHRlcihpc3N1ZSA9PiB7CiAgICAgICAgLy8gQ2hlY2sgaWYgaXNzdWUgcGFzc2VzIHNldmVyaXR5IGZpbHRlcgogICAgICAgIGNvbnN0IHBhc3Nlc1NldmVyaXR5RmlsdGVyID0gdGhpcy5zZWxlY3RlZEZpbHRlcnMuc2V2ZXJpdHkubGVuZ3RoID09PSAwIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLnNldmVyaXR5LmluY2x1ZGVzKGlzc3VlLnNldmVyaXR5KTsKCiAgICAgICAgLy8gQ2hlY2sgaWYgaXNzdWUgcGFzc2VzIGFuYWx5c2lzIHR5cGUgZmlsdGVyCiAgICAgICAgY29uc3QgcGFzc2VzQW5hbHlzaXNGaWx0ZXIgPSB0aGlzLnNlbGVjdGVkRmlsdGVycy5hbmFseXNpc1R5cGUubGVuZ3RoID09PSAwIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZS5pbmNsdWRlcyhpc3N1ZS5hbmFseXNpc1R5cGUpOwoKICAgICAgICAvLyBJc3N1ZSBtdXN0IHBhc3MgYm90aCBmaWx0ZXJzCiAgICAgICAgcmV0dXJuIHBhc3Nlc1NldmVyaXR5RmlsdGVyICYmIHBhc3Nlc0FuYWx5c2lzRmlsdGVyOwogICAgICB9KTsKICAgIH0sCgogICAgLy8gQ2hlY2sgaWYgYW55IGZpbHRlcnMgYXJlIGFjdGl2ZQogICAgaXNGaWx0ZXJzQWN0aXZlKCkgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZEZpbHRlcnMuc2V2ZXJpdHkubGVuZ3RoID4gMCB8fCB0aGlzLnNlbGVjdGVkRmlsdGVycy5hbmFseXNpc1R5cGUubGVuZ3RoID4gMDsKICAgIH0sCgogICAgLy8gU29ydCBicmVha291dCBncm91cHMgYnkgeEZhY3RvciAoaGlnaGVzdCBmaXJzdCkgYW5kIGZpbHRlciBjcml0aWNhbCBvbmVzCiAgICBzb3J0ZWRDcml0aWNhbEJyZWFrb3V0R3JvdXBzKCkgewogICAgICByZXR1cm4gWy4uLnRoaXMuYnJlYWtvdXRHcm91cHNdCiAgICAgICAgLmZpbHRlcihncm91cCA9PiBncm91cC54RmFjdG9yID49IDEuNSkgLy8gT25seSBncm91cHMgd2l0aCB4RmFjdG9yID49IDEuNSBhcmUgY29uc2lkZXJlZCBjcml0aWNhbAogICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnhGYWN0b3IgLSBhLnhGYWN0b3IpOyAvLyBTb3J0IGJ5IHhGYWN0b3IgaW4gZGVzY2VuZGluZyBvcmRlcgogICAgfSwKCiAgICAvLyBHZXQgbm9ybWFsIGJyZWFrb3V0IGdyb3VwcyAobm9uLWNyaXRpY2FsKQogICAgbm9ybWFsQnJlYWtvdXRHcm91cHMoKSB7CiAgICAgIHJldHVybiBbLi4udGhpcy5icmVha291dEdyb3Vwc10KICAgICAgICAuZmlsdGVyKGdyb3VwID0+IGdyb3VwLnhGYWN0b3IgPCAxLjUpIC8vIE9ubHkgZ3JvdXBzIHdpdGggeEZhY3RvciA8IDEuNQogICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnhGYWN0b3IgLSBhLnhGYWN0b3IpOyAvLyBTb3J0IGJ5IHhGYWN0b3IgaW4gZGVzY2VuZGluZyBvcmRlcgogICAgfSwKCiAgICAvLyBHZXQgaGlnaCBwcmlvcml0eSBncm91cHMgKHhGYWN0b3IgPj0gMy4wKQogICAgc29ydGVkSGlnaFByaW9yaXR5R3JvdXBzKCkgewogICAgICByZXR1cm4gWy4uLnRoaXMuYnJlYWtvdXRHcm91cHNdCiAgICAgICAgLmZpbHRlcihncm91cCA9PiBncm91cC54RmFjdG9yID49IDMuMCkgLy8gT25seSBncm91cHMgd2l0aCB4RmFjdG9yID49IDMuMCAoY3JpdGljYWwgc3Bpa2UpCiAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIueEZhY3RvciAtIGEueEZhY3Rvcik7IC8vIFNvcnQgYnkgeEZhY3RvciBpbiBkZXNjZW5kaW5nIG9yZGVyCiAgICB9LAoKICAgIC8vIEdldCBtZWRpdW0gcHJpb3JpdHkgZ3JvdXBzICgxLjUgPD0geEZhY3RvciA8IDMuMCkKICAgIHNvcnRlZE1lZGl1bVByaW9yaXR5R3JvdXBzKCkgewogICAgICByZXR1cm4gWy4uLnRoaXMuYnJlYWtvdXRHcm91cHNdCiAgICAgICAgLmZpbHRlcihncm91cCA9PiBncm91cC54RmFjdG9yID49IDEuNSAmJiBncm91cC54RmFjdG9yIDwgMy4wKSAvLyBHcm91cHMgd2l0aCAxLjUgPD0geEZhY3RvciA8IDMuMCAoc3VzdGFpbmVkIHByb2JsZW0pCiAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIueEZhY3RvciAtIGEueEZhY3Rvcik7IC8vIFNvcnQgYnkgeEZhY3RvciBpbiBkZXNjZW5kaW5nIG9yZGVyCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBsb2FkRGFzaGJvYXJkRGF0YSgpIHsKICAgICAgdGhpcy5sb2FkQ3JpdGljYWxJc3N1ZXMoKTsKICAgICAgdGhpcy5sb2FkVmFsaWRhdGlvbkNvdW50cygpOwogICAgICB0aGlzLmxvYWRCcmVha291dEdyb3VwcygpOwogICAgICB0aGlzLmxvYWRUcmFja2VkSXRlbXMoKTsKICAgICAgdGhpcy5sb2FkUm9vdENhdXNlRGF0YSgpOwogICAgfSwKCiAgICBsb2FkQ3JpdGljYWxJc3N1ZXMoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nIGNyaXRpY2FsIGlzc3VlcyBkYXRhJyk7CgogICAgICAvLyBVc2UgaGFyZGNvZGVkIHZhbHVlcyB0byBtYXRjaCB0aGUgVUkgd2l0aCBjYXRlZ29yaWVzCiAgICAgIGNvbnN0IGNyaXRpY2FsSXNzdWVzID0gWwogICAgICAgIHsKICAgICAgICAgIGlkOiAnY2kxJywKICAgICAgICAgIGNhdGVnb3J5OiAnRmFuIFRoZW1pcycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJywKICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzMuMicsCiAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnRmFuIFRoZW1pcyBzaG93aW5nIDMuMnggc3Bpa2UgaW4gSnVuZSAyMDI0IHdpdGggMTIgZmFpbHMgdnMgNCBleHBlY3RlZC4gUm9vdCBjYXVzZSBhcHBlYXJzIHRvIGJlIGFpcmZsb3cgaXNzdWVzIGluIHRoZSBuZXcgZGVzaWduLicsCiAgICAgICAgICBjb21tZW50OiAnJywKICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1Jvb3QgQ2F1c2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ2NpMicsCiAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLAogICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS44JywKICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdWaWN0b3JpYSBDcnlwdG8gaGFzIHN1c3RhaW5lZCBwcm9ibGVtIHdpdGggMS44eCB0YXJnZXQgcmF0ZSBmb3IgMyBjb25zZWN1dGl2ZSBtb250aHMuIENvbnNpc3RlbnQgcGF0dGVybiBvZiBwb3dlciBkZWxpdmVyeSBpc3N1ZXMuJywKICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgYW5hbHlzaXNUeXBlOiAnUm9vdCBDYXVzZScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAnY2kzJywKICAgICAgICAgIGNhdGVnb3J5OiAnRmFuIFRoZW1pcycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJywKICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzIuNScsCiAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnRmFuIFRoZW1pcyBzaG93aW5nIGhpZ2hlciBmYWlsdXJlIHJhdGVzIGZvciB1bml0cyBtYW51ZmFjdHVyZWQgaW4gTWFyY2ggMjAyNC4gVmludGFnZSBhbmFseXNpcyBpbmRpY2F0ZXMgYSAyLjV4IGluY3JlYXNlIGluIGZhaWx1cmVzIGZvciB0aGlzIG1hbnVmYWN0dXJpbmcgcGVyaW9kLicsCiAgICAgICAgICBjb21tZW50OiAnJywKICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1ZpbnRhZ2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ2NpNCcsCiAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLAogICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS42JywKICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdWaWN0b3JpYSBDcnlwdG8gdW5pdHMgZnJvbSBzdXBwbGllciBBQkMgc2hvd2luZyAxLjZ4IGhpZ2hlciBmYWlsdXJlIHJhdGVzIGNvbXBhcmVkIHRvIG90aGVyIHN1cHBsaWVycy4gUXVhbGl0eSBhdWRpdCByZWNvbW1lbmRlZC4nLAogICAgICAgICAgY29tbWVudDogJycsCiAgICAgICAgICBhbmFseXNpc1R5cGU6ICdTdXBwbGllcicKICAgICAgICB9CiAgICAgIF07CgogICAgICAvLyBTZXQgbmV3IGNyaXRpY2FsIGlzc3VlcyB0byAwIHRvIG1hdGNoIHRoZSBVSQogICAgICB0aGlzLm5ld0NyaXRpY2FsSXNzdWVzID0gW107CgogICAgICAvLyBTZXQgdW5yZXNvbHZlZCBjcml0aWNhbCBpc3N1ZXMKICAgICAgdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMgPSBjcml0aWNhbElzc3VlczsKCiAgICAgIC8vIFNldCBjcml0aWNhbCBpc3N1ZXMgc2VjdGlvbiB0byBjb2xsYXBzZWQgYnkgZGVmYXVsdAogICAgICB0aGlzLmlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZCA9IGZhbHNlOwoKICAgICAgY29uc29sZS5sb2coJ0NyaXRpY2FsIGlzc3VlcyBzZXQ6JywgewogICAgICAgIG5ldzogdGhpcy5uZXdDcml0aWNhbElzc3VlcywKICAgICAgICB1bnJlc29sdmVkOiB0aGlzLnVucmVzb2x2ZWRDcml0aWNhbElzc3VlcwogICAgICB9KTsKICAgIH0sCgoKCiAgICBsb2FkVmFsaWRhdGlvbkNvdW50cygpIHsKICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgdmFsaWRhdGlvbiBjb3VudHMgYW5kIGNoYXJ0IGRhdGEnKTsKCiAgICAgIC8vIFVzZSBoYXJkY29kZWQgdmFsdWVzIHRvIG1hdGNoIHRoZSBVSQogICAgICB0aGlzLnZhbGlkYXRlZENvdW50ID0gMTI1OwogICAgICB0aGlzLnVudmFsaWRhdGVkQ291bnQgPSAzNzsKCiAgICAgIC8vIEdlbmVyYXRlIG1vY2sgZGF0YSBmb3IgdGhlIHZhbGlkYXRpb24gY2hhcnQKICAgICAgdGhpcy5nZW5lcmF0ZVZhbGlkYXRpb25DaGFydERhdGEoKTsKCiAgICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW9uIGNvdW50cyBzZXQ6JywgewogICAgICAgIHZhbGlkYXRlZENvdW50OiB0aGlzLnZhbGlkYXRlZENvdW50LAogICAgICAgIHVudmFsaWRhdGVkQ291bnQ6IHRoaXMudW52YWxpZGF0ZWRDb3VudAogICAgICB9KTsKCiAgICAgIC8vIEluaXRpYWxpemUgdGhlIHZhbGlkYXRpb24gY2hhcnQgYWZ0ZXIgZGF0YSBpcyBsb2FkZWQKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuaW5pdFZhbGlkYXRpb25DaGFydCgpOwogICAgICB9KTsKICAgIH0sCgogICAgZ2VuZXJhdGVWYWxpZGF0aW9uQ2hhcnREYXRhKCkgewogICAgICAvLyBHZW5lcmF0ZSBtb2NrIGRhdGEgZm9yIHRoZSB2YWxpZGF0aW9uIGNoYXJ0CiAgICAgIGNvbnN0IGRhdGEgPSBbXTsKCiAgICAgIC8vIEdlbmVyYXRlIGRhdGEgZm9yIHRoZSBsYXN0IDE0IGRheXMKICAgICAgZm9yIChsZXQgaSA9IDEzOyBpID49IDA7IGktLSkgewogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpOwoKICAgICAgICAvLyBGb3JtYXQgZGF0ZSBmb3IgZGlzcGxheSBpbiBiYW5kIHNjYWxlCiAgICAgICAgY29uc3QgZGF0ZVN0ciA9IGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdzaG9ydCcsIGRheTogJ251bWVyaWMnIH0pOwoKICAgICAgICAvLyBHZW5lcmF0ZSByYW5kb20gdmFsdWVzIGZvciB2YWxpZGF0ZWQgYW5kIHVudmFsaWRhdGVkIGRlZmVjdHMKICAgICAgICBjb25zdCB2YWxpZGF0ZWQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMikgKyAzOyAvLyAzLTE1IHZhbGlkYXRlZCBkZWZlY3RzCiAgICAgICAgY29uc3QgdW52YWxpZGF0ZWQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA4KTsgLy8gMC04IHVudmFsaWRhdGVkIGRlZmVjdHMKCiAgICAgICAgLy8gQ2FsY3VsYXRlIGRhaWx5IHlpZWxkIChhc3N1bWluZyAxMDAgdW5pdHMgcHJvY2Vzc2VkIHBlciBkYXkpCiAgICAgICAgY29uc3QgdW5pdHNQZXJEYXkgPSAxMDA7CiAgICAgICAgY29uc3QgZGFpbHlZaWVsZCA9ICgodW5pdHNQZXJEYXkgLSAodmFsaWRhdGVkICsgdW52YWxpZGF0ZWQpKSAvIHVuaXRzUGVyRGF5KSAqIDEwMDsKCiAgICAgICAgLy8gR2VuZXJhdGUgdm9sdW1lIGRhdGEgKHVuaXRzIHByb2Nlc3NlZCBwZXIgZGF5KQogICAgICAgIGNvbnN0IHZvbHVtZSA9IHVuaXRzUGVyRGF5ICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNTApIC0gMjU7IC8vIDc1LTEyNSB1bml0cwoKICAgICAgICAvLyBDcmVhdGUgZGF0YSBwb2ludHMgd2l0aCBhbGwgdmFsdWVzCiAgICAgICAgLy8gRGVmZWN0IGRhdGEgZm9yIGdyb3VwZWQgYmFyIGNoYXJ0CiAgICAgICAgZGF0YS5wdXNoKHsKICAgICAgICAgIGRhdGU6IGRhdGVTdHIsCiAgICAgICAgICBncm91cDogJ1ZhbGlkYXRlZCcsCiAgICAgICAgICB2YWx1ZTogdmFsaWRhdGVkCiAgICAgICAgfSk7CgogICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICBkYXRlOiBkYXRlU3RyLAogICAgICAgICAgZ3JvdXA6ICdVbnZhbGlkYXRlZCcsCiAgICAgICAgICB2YWx1ZTogdW52YWxpZGF0ZWQKICAgICAgICB9KTsKCiAgICAgICAgLy8gVm9sdW1lIGRhdGEgZm9yIGdyb3VwZWQgYmFyIGNoYXJ0CiAgICAgICAgZGF0YS5wdXNoKHsKICAgICAgICAgIGRhdGU6IGRhdGVTdHIsCiAgICAgICAgICBncm91cDogJ1ZvbHVtZScsCiAgICAgICAgICB2YWx1ZTogdm9sdW1lCiAgICAgICAgfSk7CgogICAgICAgIC8vIFlpZWxkIGRhdGEgZm9yIGxpbmUgY2hhcnQKICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgZGF0ZTogZGF0ZVN0ciwKICAgICAgICAgIGdyb3VwOiAnRGFpbHkgWWllbGQnLAogICAgICAgICAgdmFsdWU6IGRhaWx5WWllbGQKICAgICAgICB9KTsKCiAgICAgICAgLy8gQ2FsY3VsYXRlIGN1bXVsYXRpdmUgeWllbGQgKHNpbXBsaWZpZWQgZm9yIHRoaXMgZXhhbXBsZSkKICAgICAgICBjb25zdCBjdW11bGF0aXZlWWllbGQgPSA5MCArIChNYXRoLnJhbmRvbSgpICogOCk7IC8vIDkwLTk4JSByYW5nZQoKICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgZGF0ZTogZGF0ZVN0ciwKICAgICAgICAgIGdyb3VwOiAnQ3VtdWxhdGl2ZSBZaWVsZCcsCiAgICAgICAgICB2YWx1ZTogY3VtdWxhdGl2ZVlpZWxkCiAgICAgICAgfSk7CgogICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICBkYXRlOiBkYXRlU3RyLAogICAgICAgICAgZ3JvdXA6ICdUYXJnZXQgWWllbGQnLAogICAgICAgICAgdmFsdWU6IDk1IC8vIFRhcmdldCB5aWVsZCBpcyA5NSUKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgLy8gU2V0IHRoZSB2YWxpZGF0aW9uIGNoYXJ0IGRhdGEKICAgICAgdGhpcy52YWxpZGF0aW9uQ2hhcnREYXRhID0gZGF0YTsKICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYXRpb24gY2hhcnQgZGF0YSBnZW5lcmF0ZWQ6JywgZGF0YSk7CiAgICB9LAoKICAgIGluaXRWYWxpZGF0aW9uQ2hhcnQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdJbml0aWFsaXppbmcgdmFsaWRhdGlvbiBjaGFydC4uLicpOwogICAgICB0cnkgewogICAgICAgIC8vIERlc3Ryb3kgZXhpc3RpbmcgY2hhcnQgaWYgaXQgZXhpc3RzCiAgICAgICAgaWYgKHRoaXMudmFsaWRhdGlvbkNoYXJ0KSB7CiAgICAgICAgICBjb25zb2xlLmxvZygnRGVzdHJveWluZyBleGlzdGluZyBjaGFydCcpOwogICAgICAgICAgdGhpcy52YWxpZGF0aW9uQ2hhcnQuZGVzdHJveSgpOwogICAgICAgIH0KCiAgICAgICAgLy8gR2V0IHRoZSBjaGFydCBjb250YWluZXIgZWxlbWVudAogICAgICAgIGNvbnN0IGNoYXJ0Q29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3ZhbGlkYXRpb25DaGFydCcpOwogICAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBjb250YWluZXI6JywgY2hhcnRDb250YWluZXIpOwogICAgICAgIGlmICghY2hhcnRDb250YWluZXIpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1ZhbGlkYXRpb24gY2hhcnQgY29udGFpbmVyIG5vdCBmb3VuZCcpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KCiAgICAgICAgLy8gTG9nIHZhbGlkYXRpb24gY2hhcnQgZGF0YQogICAgICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW9uIGNoYXJ0IGRhdGEgbGVuZ3RoOicsIHRoaXMudmFsaWRhdGlvbkNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICAgIGNvbnNvbGUubG9nKCdTYW1wbGUgZGF0YTonLCB0aGlzLnZhbGlkYXRpb25DaGFydERhdGEuc2xpY2UoMCwgNSkpOwoKICAgICAgICAvLyBDcmVhdGUgYSBzaW1wbGlmaWVkIGNoYXJ0IGNvbmZpZ3VyYXRpb24KICAgICAgICBjb25zdCBjaGFydE9wdGlvbnMgPSB7CiAgICAgICAgICB0aXRsZTogJ0RhaWx5IFN0YXR1cycsCiAgICAgICAgICBheGVzOiB7CiAgICAgICAgICAgIGxlZnQ6IHsKICAgICAgICAgICAgICB0aXRsZTogJ0NvdW50JywKICAgICAgICAgICAgICBtYXBzVG86ICd2YWx1ZScsCiAgICAgICAgICAgICAgZG9tYWluOiBbMCwgMTUwXSwKICAgICAgICAgICAgICBzY2FsZVR5cGU6ICdsaW5lYXInCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHJpZ2h0OiB7CiAgICAgICAgICAgICAgdGl0bGU6ICdZaWVsZCAoJSknLAogICAgICAgICAgICAgIG1hcHNUbzogJ3ZhbHVlJywKICAgICAgICAgICAgICBkb21haW46IFswLCAxMDBdLAogICAgICAgICAgICAgIHNjYWxlVHlwZTogJ2xpbmVhcicsCiAgICAgICAgICAgICAgY29ycmVzcG9uZGluZ0RhdGFzZXRzOiBbCiAgICAgICAgICAgICAgICAnRGFpbHkgWWllbGQnLAogICAgICAgICAgICAgICAgJ0N1bXVsYXRpdmUgWWllbGQnLAogICAgICAgICAgICAgICAgJ1RhcmdldCBZaWVsZCcKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGJvdHRvbTogewogICAgICAgICAgICAgIHRpdGxlOiAnRGF5JywKICAgICAgICAgICAgICBtYXBzVG86ICdkYXRlJywKICAgICAgICAgICAgICBzY2FsZVR5cGU6ICdiYW5kJwogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgaGVpZ2h0OiAnMzUwcHgnLAogICAgICAgICAgd2lkdGg6ICcxMDAlJywKICAgICAgICAgIGxlZ2VuZDogewogICAgICAgICAgICBhbGlnbm1lbnQ6ICdjZW50ZXInLAogICAgICAgICAgICBlbmFibGVkOiB0cnVlLAogICAgICAgICAgICBmb250Q29sb3I6ICcjZjRmNGY0JwogICAgICAgICAgfSwKICAgICAgICAgIGNvbG9yOiB7CiAgICAgICAgICAgIHNjYWxlOiB7CiAgICAgICAgICAgICAgJ1ZhbGlkYXRlZCc6ICcjMDA2MmZmJywgLy8gQmx1ZQogICAgICAgICAgICAgICdVbnZhbGlkYXRlZCc6ICcjZmY4MzJiJywgLy8gT3JhbmdlCiAgICAgICAgICAgICAgJ1ZvbHVtZSc6ICcjMjRhMTQ4JywgLy8gR3JlZW4KICAgICAgICAgICAgICAnRGFpbHkgWWllbGQnOiAnIzY5MjljNCcsIC8vIFB1cnBsZQogICAgICAgICAgICAgICdDdW11bGF0aXZlIFlpZWxkJzogJyMxMTkyZTgnLCAvLyBMaWdodCBibHVlCiAgICAgICAgICAgICAgJ1RhcmdldCBZaWVsZCc6ICcjZmE0ZDU2JyAvLyBSZWQKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHRvb2xiYXI6IHsKICAgICAgICAgICAgZW5hYmxlZDogZmFsc2UKICAgICAgICAgIH0sCiAgICAgICAgICBkYXRhOiB7CiAgICAgICAgICAgIGdyb3VwTWFwc1RvOiAnZ3JvdXAnCiAgICAgICAgICB9LAogICAgICAgICAgdGhlbWU6ICdnMTAwJywKICAgICAgICAgIGdyaWQ6IHsKICAgICAgICAgICAgeDogewogICAgICAgICAgICAgIGVuYWJsZWQ6IGZhbHNlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHk6IHsKICAgICAgICAgICAgICBlbmFibGVkOiB0cnVlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBjb21ib0NoYXJ0VHlwZXM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIHR5cGU6ICdncm91cGVkLWJhcicsCiAgICAgICAgICAgICAgY29ycmVzcG9uZGluZ0RhdGFzZXRzOiBbJ1ZhbGlkYXRlZCcsICdVbnZhbGlkYXRlZCcsICdWb2x1bWUnXQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgIGNvcnJlc3BvbmRpbmdEYXRhc2V0czogWydEYWlseSBZaWVsZCcsICdDdW11bGF0aXZlIFlpZWxkJywgJ1RhcmdldCBZaWVsZCddLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIHBvaW50czogewogICAgICAgICAgICAgICAgICByYWRpdXM6IDAKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBzdHJva2VXaWR0aDogMgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgXSwKICAgICAgICAgIHRocmVzaG9sZHM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIGF4aXM6ICdyaWdodCcsCiAgICAgICAgICAgICAgdmFsdWU6IDk1LAogICAgICAgICAgICAgIGxhYmVsOiAnVGFyZ2V0IFlpZWxkICg5NSUpJywKICAgICAgICAgICAgICBmaWxsQ29sb3I6ICcjZmE0ZDU2JywKICAgICAgICAgICAgICBvcGFjaXR5OiAwLjEKICAgICAgICAgICAgfQogICAgICAgICAgXSwKICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgZW5hYmxlZDogdHJ1ZSwKICAgICAgICAgICAgY3VzdG9tSFRNTDogKGRhdGEpID0+IHsKICAgICAgICAgICAgICBsZXQgaHRtbCA9IGA8ZGl2IHN0eWxlPSJwYWRkaW5nOiAxMHB4OyBiYWNrZ3JvdW5kOiAjMjYyNjI2OyBjb2xvcjogI2Y0ZjRmNDsgYm9yZGVyLXJhZGl1czogNHB4OyBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgwLCAwLCAwLCAwLjMpOyI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogNXB4OyI+JHtkYXRhWzBdLmRhdGV9PC9kaXY+YDsKCiAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICAgICAgbGV0IGRpc3BsYXlWYWx1ZTsKICAgICAgICAgICAgICAgIGNvbnN0IGdyb3VwID0gaXRlbS5ncm91cDsKCiAgICAgICAgICAgICAgICBpZiAoZ3JvdXAuaW5jbHVkZXMoJ1lpZWxkJykpIHsKICAgICAgICAgICAgICAgICAgLy8gRm9ybWF0IHlpZWxkIHZhbHVlcyBhcyBwZXJjZW50YWdlcwogICAgICAgICAgICAgICAgICBkaXNwbGF5VmFsdWUgPSBgJHtpdGVtLnZhbHVlLnRvRml4ZWQoMSl9JWA7CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGdyb3VwID09PSAnVm9sdW1lJykgewogICAgICAgICAgICAgICAgICAvLyBGb3JtYXQgdm9sdW1lIGFzIGludGVnZXIgd2l0aCAndW5pdHMnIGxhYmVsCiAgICAgICAgICAgICAgICAgIGRpc3BsYXlWYWx1ZSA9IGAke01hdGgucm91bmQoaXRlbS52YWx1ZSl9IHVuaXRzYDsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIC8vIEZvcm1hdCBkZWZlY3QgY291bnRzIGFzIGludGVnZXJzCiAgICAgICAgICAgICAgICAgIGRpc3BsYXlWYWx1ZSA9IE1hdGgucm91bmQoaXRlbS52YWx1ZSk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgaHRtbCArPSBgPGRpdiBzdHlsZT0ibWFyZ2luOiA0cHggMDsiPgogICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogMTJweDsgaGVpZ2h0OiAxMnB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAke2NoYXJ0T3B0aW9ucy5jb2xvci5zY2FsZVtncm91cF19OyBtYXJnaW4tcmlnaHQ6IDZweDsgYm9yZGVyLXJhZGl1czogNTAlOyI+PC9zcGFuPgogICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IDUwMDsiPiR7Z3JvdXB9Ojwvc3Bhbj4gJHtkaXNwbGF5VmFsdWV9CiAgICAgICAgICAgICAgICA8L2Rpdj5gOwogICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBodG1sICs9IGA8L2Rpdj5gOwogICAgICAgICAgICAgIHJldHVybiBodG1sOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfTsKCiAgICAgICAgLy8gQ3JlYXRlIHRoZSBjb21ibyBjaGFydCB3aXRoIHRoZSBzaW1wbGlmaWVkIGNvbmZpZ3VyYXRpb24KICAgICAgICB0aGlzLnZhbGlkYXRpb25DaGFydCA9IG5ldyBDb21ib0NoYXJ0KAogICAgICAgICAgY2hhcnRDb250YWluZXIsCiAgICAgICAgICB7CiAgICAgICAgICAgIGRhdGE6IHRoaXMudmFsaWRhdGlvbkNoYXJ0RGF0YSwKICAgICAgICAgICAgb3B0aW9uczogY2hhcnRPcHRpb25zCiAgICAgICAgICB9CiAgICAgICAgKTsKCiAgICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYXRpb24gY2hhcnQgaW5pdGlhbGl6ZWQgd2l0aCBDb21ib0NoYXJ0Jyk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5pdGlhbGl6aW5nIHZhbGlkYXRpb24gY2hhcnQ6JywgZXJyb3IpOwoKICAgICAgICAvLyBDcmVhdGUgYSBmYWxsYmFjayBkaXNwbGF5IGlmIHRoZSBjaGFydCBmYWlscwogICAgICAgIGNvbnN0IGNoYXJ0Q29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3ZhbGlkYXRpb25DaGFydCcpOwogICAgICAgIGlmIChjaGFydENvbnRhaW5lcikgewogICAgICAgICAgY29uc29sZS5sb2coJ0NyZWF0aW5nIGZhbGxiYWNrIGRpc3BsYXkgZm9yIHZhbGlkYXRpb24gY2hhcnQnKTsKICAgICAgICAgIGNoYXJ0Q29udGFpbmVyLmlubmVySFRNTCA9IGAKICAgICAgICAgICAgPGRpdiBzdHlsZT0iaGVpZ2h0OiAxMDAlOyBkaXNwbGF5OiBmbGV4OyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgYWxpZ24taXRlbXM6IGNlbnRlcjsgYmFja2dyb3VuZC1jb2xvcjogIzE2MTYxNjsgYm9yZGVyLXJhZGl1czogOHB4OyBwYWRkaW5nOiAxNnB4OyI+CiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6ICNmNGY0ZjQ7IG1hcmdpbi1ib3R0b206IDE2cHg7IGZvbnQtd2VpZ2h0OiA2MDA7IGZvbnQtc2l6ZTogMXJlbTsiPkRhaWx5IFN0YXR1czwvZGl2PgogICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiAjYzZjNmM2OyBtYXJnaW4tYm90dG9tOiAxNnB4OyBmb250LXNpemU6IDAuODc1cmVtOyI+Q2hhcnQgY291bGQgbm90IGJlIGRpc3BsYXllZC4gU2hvd2luZyBzdW1tYXJ5IGRhdGEgaW5zdGVhZC48L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBtYXJnaW4tYm90dG9tOiAxMnB4OyI+CiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogMTJweDsgaGVpZ2h0OiAxMnB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA2MmZmOyBtYXJnaW4tcmlnaHQ6IDhweDsgYm9yZGVyLXJhZGl1czogNTAlOyI+PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjZjRmNGY0OyI+VmFsaWRhdGVkOiA8c3Ryb25nPiR7dGhpcy52YWxpZGF0ZWRDb3VudH08L3N0cm9uZz48L3NwYW4+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgbWFyZ2luLWJvdHRvbTogMTJweDsiPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImRpc3BsYXk6IGlubGluZS1ibG9jazsgd2lkdGg6IDEycHg7IGhlaWdodDogMTJweDsgYmFja2dyb3VuZC1jb2xvcjogI2ZmODMyYjsgbWFyZ2luLXJpZ2h0OiA4cHg7IGJvcmRlci1yYWRpdXM6IDUwJTsiPjwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogI2Y0ZjRmNDsiPlVudmFsaWRhdGVkOiA8c3Ryb25nPiR7dGhpcy51bnZhbGlkYXRlZENvdW50fTwvc3Ryb25nPjwvc3Bhbj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBtYXJnaW4tYm90dG9tOiAxMnB4OyI+CiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogMTJweDsgaGVpZ2h0OiAxMnB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMjRhMTQ4OyBtYXJnaW4tcmlnaHQ6IDhweDsgYm9yZGVyLXJhZGl1czogNTAlOyI+PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjZjRmNGY0OyI+Vm9sdW1lOiA8c3Ryb25nPjEwMCB1bml0czwvc3Ryb25nPjwvc3Bhbj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyI+CiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogMTJweDsgaGVpZ2h0OiAxMnB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjNjkyOWM0OyBtYXJnaW4tcmlnaHQ6IDhweDsgYm9yZGVyLXJhZGl1czogNTAlOyI+PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjZjRmNGY0OyI+Q3VycmVudCBZaWVsZDogPHN0cm9uZz45NC4yJTwvc3Ryb25nPjwvc3Bhbj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICBgOwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkQnJlYWtvdXRHcm91cHMoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nIGJyZWFrb3V0IGdyb3VwcyBmcm9tIEFQSSBmb3IgY3VycmVudCBtb250aCcpOwogICAgICB0cnkgewogICAgICAgIHRoaXMuaXNMb2FkaW5nQnJlYWtvdXRHcm91cHMgPSB0cnVlOwoKICAgICAgICAvLyBHZXQgdGhlIGN1cnJlbnQgbW9udGggaW4gWVlZWS1NTSBmb3JtYXQKICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IGAke25vdy5nZXRGdWxsWWVhcigpfS0ke1N0cmluZyhub3cuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyl9YDsKICAgICAgICB0aGlzLmN1cnJlbnRNb250aCA9IGN1cnJlbnRNb250aDsKCiAgICAgICAgY29uc29sZS5sb2coYEN1cnJlbnQgbW9udGg6ICR7dGhpcy5jdXJyZW50TW9udGh9YCk7CgogICAgICAgIC8vIE1ha2UgQVBJIGNhbGwgdG8gZ2V0IGJyZWFrb3V0IGdyb3VwcyBkYXRhCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfYnJlYWtvdXRfZ3JvdXBzJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIG1vbnRoOiBjdXJyZW50TW9udGggLy8gUGFzcyB0aGUgY3VycmVudCBtb250aCB0byBnZXQgc3BlY2lmaWMgZGF0YQogICAgICAgICAgfSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggYnJlYWtvdXQgZ3JvdXBzOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsKCiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnICYmIGRhdGEuYnJlYWtvdXRfZ3JvdXBzKSB7CiAgICAgICAgICAvLyBBZGQgY3VycmVudCBtb250aCBmbGFnIHRvIGVhY2ggYnJlYWtvdXQgZ3JvdXAKICAgICAgICAgIHRoaXMuYnJlYWtvdXRHcm91cHMgPSBkYXRhLmJyZWFrb3V0X2dyb3Vwcy5tYXAoZ3JvdXAgPT4gKHsKICAgICAgICAgICAgLi4uZ3JvdXAsCiAgICAgICAgICAgIC8vIElmIHN1c3RhaW5lZFhGYWN0b3IgaXMgbm90IHByb3ZpZGVkLCB1c2UgYSBkZWZhdWx0IHZhbHVlCiAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IGdyb3VwLnN1c3RhaW5lZFhGYWN0b3IgfHwgKGdyb3VwLnN0YXR1cyA9PT0gJ1N1c3RhaW5lZCBQcm9ibGVtJyA/IGdyb3VwLnhGYWN0b3IgKiAwLjkgOiBncm91cC54RmFjdG9yICogMC41KSwKICAgICAgICAgICAgaXNDdXJyZW50TW9udGg6IHRydWUsIC8vIEFsbCBncm91cHMgZnJvbSB0aGUgQVBJIGFyZSBmb3IgdGhlIGN1cnJlbnQgbW9udGgKICAgICAgICAgICAgaW5IZWF0bWFwOiB0cnVlIC8vIEluZGljYXRlIHRoYXQgdGhlc2UgZ3JvdXBzIGFyZSBhbHNvIGluIHRoZSBoZWF0bWFwCiAgICAgICAgICB9KSk7CgogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMuYnJlYWtvdXRHcm91cHMubGVuZ3RofSBicmVha291dCBncm91cHMgZm9yICR7Y3VycmVudE1vbnRofWApOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyBGYWxsYmFjayB0byBzYW1wbGUgZGF0YSBpZiBBUEkgZmFpbHMKICAgICAgICAgIGNvbnNvbGUud2FybignTm8gYnJlYWtvdXQgZ3JvdXBzIGZvdW5kIGluIEFQSSByZXNwb25zZSwgdXNpbmcgc2FtcGxlIGRhdGEnKTsKICAgICAgICAgIHRoaXMuYnJlYWtvdXRHcm91cHMgPSBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBuYW1lOiAnRmFuIFRoZW1pcycsCiAgICAgICAgICAgICAgc3RhdHVzOiAnU2hvcnQtVGVybSBTcGlrZScsCiAgICAgICAgICAgICAgeEZhY3RvcjogMy4yLAogICAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IDEuMiwKICAgICAgICAgICAgICBpc0N1cnJlbnRNb250aDogdHJ1ZSwKICAgICAgICAgICAgICBpbkhlYXRtYXA6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICdWaWN0b3JpYSBDcnlwdG8nLAogICAgICAgICAgICAgIHN0YXR1czogJ1N1c3RhaW5lZCBQcm9ibGVtJywKICAgICAgICAgICAgICB4RmFjdG9yOiAxLjgsCiAgICAgICAgICAgICAgc3VzdGFpbmVkWEZhY3RvcjogMS43LAogICAgICAgICAgICAgIGlzQ3VycmVudE1vbnRoOiB0cnVlLAogICAgICAgICAgICAgIGluSGVhdG1hcDogdHJ1ZQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ1F1YW50dW0gTmV4dXMnLAogICAgICAgICAgICAgIHN0YXR1czogJ1N1c3RhaW5lZCBQcm9ibGVtJywKICAgICAgICAgICAgICB4RmFjdG9yOiAxLjYsCiAgICAgICAgICAgICAgc3VzdGFpbmVkWEZhY3RvcjogMS42LAogICAgICAgICAgICAgIGlzQ3VycmVudE1vbnRoOiB0cnVlLAogICAgICAgICAgICAgIGluSGVhdG1hcDogdHJ1ZQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ09yaW9uIEJhc2UnLAogICAgICAgICAgICAgIHN0YXR1czogJ05vcm1hbCcsCiAgICAgICAgICAgICAgeEZhY3RvcjogMC45LAogICAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IDAuOCwKICAgICAgICAgICAgICBpc0N1cnJlbnRNb250aDogdHJ1ZSwKICAgICAgICAgICAgICBpbkhlYXRtYXA6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICdTdGVsbGFyIENvcmUnLAogICAgICAgICAgICAgIHN0YXR1czogJ05vcm1hbCcsCiAgICAgICAgICAgICAgeEZhY3RvcjogMC44LAogICAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IDAuNywKICAgICAgICAgICAgICBpc0N1cnJlbnRNb250aDogdHJ1ZSwKICAgICAgICAgICAgICBpbkhlYXRtYXA6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICdOZWJ1bGEgRHJpdmUnLAogICAgICAgICAgICAgIHN0YXR1czogJ05vcm1hbCcsCiAgICAgICAgICAgICAgeEZhY3RvcjogMC43LAogICAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IDAuNywKICAgICAgICAgICAgICBpc0N1cnJlbnRNb250aDogdHJ1ZSwKICAgICAgICAgICAgICBpbkhlYXRtYXA6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICdQdWxzYXIgTWF0cml4JywKICAgICAgICAgICAgICBzdGF0dXM6ICdOb3JtYWwnLAogICAgICAgICAgICAgIHhGYWN0b3I6IDAuNiwKICAgICAgICAgICAgICBzdXN0YWluZWRYRmFjdG9yOiAwLjYsCiAgICAgICAgICAgICAgaXNDdXJyZW50TW9udGg6IHRydWUsCiAgICAgICAgICAgICAgaW5IZWF0bWFwOiB0cnVlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBuYW1lOiAnUXVhc2FyIExpbmsnLAogICAgICAgICAgICAgIHN0YXR1czogJ05vcm1hbCcsCiAgICAgICAgICAgICAgeEZhY3RvcjogMC41LAogICAgICAgICAgICAgIHN1c3RhaW5lZFhGYWN0b3I6IDAuNSwKICAgICAgICAgICAgICBpc0N1cnJlbnRNb250aDogdHJ1ZSwKICAgICAgICAgICAgICBpbkhlYXRtYXA6IHRydWUKICAgICAgICAgICAgfQogICAgICAgICAgXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBicmVha291dCBncm91cHM6JywgZXJyb3IpOwogICAgICAgIC8vIEZhbGxiYWNrIHRvIGVtcHR5IGFycmF5CiAgICAgICAgdGhpcy5icmVha291dEdyb3VwcyA9IFtdOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaXNMb2FkaW5nQnJlYWtvdXRHcm91cHMgPSBmYWxzZTsKICAgICAgfQoKICAgICAgY29uc29sZS5sb2coJ0JyZWFrb3V0IGdyb3VwcyBzZXQ6JywgdGhpcy5icmVha291dEdyb3Vwcyk7CiAgICB9LAoKICAgIGdldEFpRGVzY3JpcHRpb25Gb3JJc3N1ZShpc3N1ZSkgewogICAgICAvLyBUaGlzIHdvdWxkIGNhbGwgV2F0c29uWC5haSB0byBnZXQgYW4gQUkgZGVzY3JpcHRpb24gZm9yIHRoZSBpc3N1ZQogICAgICAvLyBGb3Igbm93LCB3ZSdsbCB1c2UgdGhlIGV4aXN0aW5nIGRlc2NyaXB0aW9uCiAgICAgIGNvbnNvbGUubG9nKGBHZXR0aW5nIEFJIGRlc2NyaXB0aW9uIGZvciBpc3N1ZTogJHtpc3N1ZS5pZH1gKTsKICAgIH0sCgogICAgZ2V0U3RhdHVzQ2xhc3Moc3RhdHVzKSB7CiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7CiAgICAgICAgY2FzZSAnU2hvcnQtVGVybSBTcGlrZSc6CiAgICAgICAgICByZXR1cm4gJ3N0YXR1cy1zcGlrZSc7CiAgICAgICAgY2FzZSAnU3VzdGFpbmVkIFByb2JsZW0nOgogICAgICAgICAgcmV0dXJuICdzdGF0dXMtc3VzdGFpbmVkJzsKICAgICAgICBjYXNlICdDcml0aWNhbCAoQm90aCknOgogICAgICAgICAgcmV0dXJuICdzdGF0dXMtY3JpdGljYWwnOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gJ3N0YXR1cy1ub3JtYWwnOwogICAgICB9CiAgICB9LAoKICAgIHZpZXdJc3N1ZURldGFpbHMoaXNzdWUpIHsKICAgICAgY29uc29sZS5sb2coYFZpZXdpbmcgZGV0YWlscyBmb3IgaXNzdWU6ICR7aXNzdWUuaWR9YCk7CiAgICAgIHRoaXMuc2VsZWN0ZWRJc3N1ZSA9IHsgLi4uaXNzdWUgfTsKICAgICAgdGhpcy5sb2FkSXNzdWVDaGFydERhdGEoaXNzdWUpOwogICAgICB0aGlzLnNob3dJc3N1ZU1vZGFsID0gdHJ1ZTsKICAgIH0sCgogICAgbG9hZElzc3VlQ2hhcnREYXRhKGlzc3VlKSB7CiAgICAgIGNvbnNvbGUubG9nKGBMb2FkaW5nIGNoYXJ0IGRhdGEgZm9yIGlzc3VlOiAke2lzc3VlLmlkfWApOwoKICAgICAgLy8gRm9yIG5vdywgd2UnbGwgY3JlYXRlIHNvbWUgc2FtcGxlIGRhdGEKICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbWUgZnJvbSB0aGUgQVBJCiAgICAgIGNvbnN0IG1vbnRocyA9IFtdOwogICAgICBjb25zdCB2YWx1ZXMgPSBbXTsKCiAgICAgIC8vIEdldCB0aGUgaXNzdWUgbW9udGgKICAgICAgY29uc3QgaXNzdWVEYXRlID0gbmV3IERhdGUoaXNzdWUubW9udGggKyAnLTAxJyk7CgogICAgICAvLyBDcmVhdGUgZGF0YSBmb3IgNiBtb250aHMgYmVmb3JlIGFuZCBhZnRlciB0aGUgaXNzdWUgbW9udGgKICAgICAgZm9yIChsZXQgaSA9IC02OyBpIDw9IDY7IGkrKykgewogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShpc3N1ZURhdGUpOwogICAgICAgIGRhdGUuc2V0TW9udGgoZGF0ZS5nZXRNb250aCgpICsgaSk7CiAgICAgICAgY29uc3QgbW9udGhTdHIgPSBkYXRlLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgNyk7IC8vIFlZWVktTU0gZm9ybWF0CgogICAgICAgIC8vIEJhc2UgdmFsdWUgdGhhdCBpbmNyZWFzZXMgYXQgdGhlIGlzc3VlIG1vbnRoCiAgICAgICAgbGV0IHZhbHVlID0gMC41OwogICAgICAgIGlmIChpIDwgMCkgewogICAgICAgICAgdmFsdWUgPSAwLjUgKyAoaSAqIDAuMDUpOyAvLyBTbGlnaHQgdmFyaWF0aW9uIGJlZm9yZSBpc3N1ZQogICAgICAgIH0gZWxzZSBpZiAoaSA9PT0gMCkgewogICAgICAgICAgLy8gVGhlIHNwaWtlIGF0IHRoZSBpc3N1ZSBtb250aAogICAgICAgICAgdmFsdWUgPSBpc3N1ZS5pbmNyZWFzZU11bHRpcGxpZXIgPT09ICcobmV3KScgPyAxLjAgOiBwYXJzZUZsb2F0KGlzc3VlLmluY3JlYXNlTXVsdGlwbGllcik7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIEdyYWR1YWwgZGVjcmVhc2UgYWZ0ZXIgdGhlIHNwaWtlCiAgICAgICAgICB2YWx1ZSA9IHBhcnNlRmxvYXQoaXNzdWUuaW5jcmVhc2VNdWx0aXBsaWVyKSAqIE1hdGgucG93KDAuOCwgaSk7CiAgICAgICAgfQoKICAgICAgICBtb250aHMucHVzaChtb250aFN0cik7CiAgICAgICAgdmFsdWVzLnB1c2goewogICAgICAgICAgbW9udGg6IG1vbnRoU3RyLAogICAgICAgICAgdmFsdWU6IHZhbHVlCiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIFNldCB0aGUgY2hhcnQgZGF0YQogICAgICB0aGlzLmlzc3VlQ2hhcnREYXRhID0gdmFsdWVzOwoKICAgICAgLy8gVXBkYXRlIGNoYXJ0IG9wdGlvbnMKICAgICAgdGhpcy5pc3N1ZUNoYXJ0T3B0aW9ucyA9IHsKICAgICAgICB0aXRsZTogYCR7aXNzdWUuY2F0ZWdvcnl9IC0gJHtpc3N1ZS5tb250aH1gLAogICAgICAgIGF4ZXM6IHsKICAgICAgICAgIGxlZnQ6IHsKICAgICAgICAgICAgdGl0bGU6ICdYLUZhY3RvcicsCiAgICAgICAgICAgIG1hcHNUbzogJ3ZhbHVlJywKICAgICAgICAgICAgZG9tYWluOiBbMCwgTWF0aC5tYXgoLi4udmFsdWVzLm1hcCh2ID0+IHYudmFsdWUpKSAqIDEuMl0KICAgICAgICAgIH0sCiAgICAgICAgICBib3R0b206IHsKICAgICAgICAgICAgdGl0bGU6ICdNb250aCcsCiAgICAgICAgICAgIG1hcHNUbzogJ21vbnRoJywKICAgICAgICAgICAgc2NhbGVUeXBlOiAnbGFiZWxzJwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgaGVpZ2h0OiAnMzAwcHgnLAogICAgICAgIGNvbG9yOiB7CiAgICAgICAgICBzY2FsZTogewogICAgICAgICAgICAnWC1GYWN0b3InOiAnIzAwNjJmZicKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH07CiAgICB9LAoKICAgIGNsb3NlSXNzdWVNb2RhbCgpIHsKICAgICAgY29uc29sZS5sb2coJ0Nsb3NpbmcgaXNzdWUgbW9kYWwnKTsKICAgICAgdGhpcy5zaG93SXNzdWVNb2RhbCA9IGZhbHNlOwogICAgICB0aGlzLnNlbGVjdGVkSXNzdWUgPSBudWxsOwogICAgfSwKCiAgICB1cGRhdGVJc3N1ZSgpIHsKICAgICAgY29uc29sZS5sb2coYFVwZGF0aW5nIGlzc3VlOiAke3RoaXMuc2VsZWN0ZWRJc3N1ZS5pZH1gKTsKCiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjYWxsIGFuIEFQSSB0byB1cGRhdGUgdGhlIGlzc3VlCiAgICAgIC8vIEZvciBub3csIHdlJ2xsIGp1c3QgdXBkYXRlIHRoZSBsb2NhbCBkYXRhCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMuZmluZEluZGV4KGlzc3VlID0+IGlzc3VlLmlkID09PSB0aGlzLnNlbGVjdGVkSXNzdWUuaWQpOwogICAgICBpZiAoaW5kZXggIT09IC0xKSB7CiAgICAgICAgdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXNbaW5kZXhdLmNvbW1lbnQgPSB0aGlzLnNlbGVjdGVkSXNzdWUuY29tbWVudDsKICAgICAgICBjb25zb2xlLmxvZyhgVXBkYXRlZCBpc3N1ZSAke3RoaXMuc2VsZWN0ZWRJc3N1ZS5pZH0gd2l0aCBjb21tZW50OiAke3RoaXMuc2VsZWN0ZWRJc3N1ZS5jb21tZW50fWApOwogICAgICB9CgogICAgICB0aGlzLmNsb3NlSXNzdWVNb2RhbCgpOwogICAgfSwKCiAgICBnb1RvVmFsaWRhdGlvblBhZ2UoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdOYXZpZ2F0aW5nIHRvIHZhbGlkYXRpb25zIHBhZ2UnKTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy92YWxpZGF0aW9ucycpOwogICAgfSwKCiAgICBnb1RvR3JvdXBBbmFseXNpcygpIHsKICAgICAgY29uc29sZS5sb2coYE5hdmlnYXRpbmcgdG8gR3JvdXAgdGFiIGZvciAke3RoaXMuc2VsZWN0ZWRBbmFseXNpc1R5cGV9IGFuYWx5c2lzYCk7CgogICAgICAvLyBGaW5kIHRoZSBwYXJlbnQgTWV0aXNYRmFjdG9ycyBjb21wb25lbnQKICAgICAgbGV0IHBhcmVudCA9IHRoaXMuJHBhcmVudDsKICAgICAgd2hpbGUgKHBhcmVudCAmJiBwYXJlbnQuJG9wdGlvbnMubmFtZSAhPT0gJ01ldGlzWEZhY3RvcnMnKSB7CiAgICAgICAgcGFyZW50ID0gcGFyZW50LiRwYXJlbnQ7CiAgICAgIH0KCiAgICAgIGlmIChwYXJlbnQpIHsKICAgICAgICBjb25zb2xlLmxvZygnRm91bmQgTWV0aXNYRmFjdG9ycyBwYXJlbnQgY29tcG9uZW50LCBuYXZpZ2F0aW5nIHRvIEdyb3VwIHRhYicpOwoKICAgICAgICAvLyBJZiBhIGJyZWFrb3V0IGdyb3VwIGlzIHNlbGVjdGVkLCB1c2UgaXQKICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEJyZWFrb3V0R3JvdXApIHsKICAgICAgICAgIC8vIE5hdmlnYXRlIHRvIHRoZSBHcm91cCB0YWIgd2l0aCB0aGUgc2VsZWN0ZWQgYnJlYWtvdXQgZ3JvdXAKICAgICAgICAgIHBhcmVudC5zZWxlY3RCcmVha291dEZyb21EYXNoYm9hcmQodGhpcy5zZWxlY3RlZEJyZWFrb3V0R3JvdXApOwoKICAgICAgICAgIC8vIEFmdGVyIG5hdmlnYXRpb24sIHRyaWdnZXIgdGhlIGFwcHJvcHJpYXRlIGFuYWx5c2lzIGJhc2VkIG9uIHRoZSBzZWxlY3RlZCB0eXBlCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkQW5hbHlzaXNUeXBlID09PSAnUm9vdCBDYXVzZScpIHsKICAgICAgICAgICAgICBwYXJlbnQudmlld1Jvb3RDYXVzZUFuYWx5c2lzKDMpOyAvLyBTaG93IDMgbW9udGhzIG9mIGRhdGEKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIElmIG5vIGJyZWFrb3V0IGdyb3VwIGlzIHNlbGVjdGVkLCB1c2UgdGhlIGZpcnN0IG9uZSBmcm9tIHRoZSBicmVha291dCBncm91cHMKICAgICAgICAgIGlmICh0aGlzLmJyZWFrb3V0R3JvdXBzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEJyZWFrb3V0R3JvdXAgPSB0aGlzLmJyZWFrb3V0R3JvdXBzWzBdLm5hbWU7CiAgICAgICAgICAgIHRoaXMuZ29Ub0dyb3VwQW5hbHlzaXMoKTsgLy8gQ2FsbCB0aGlzIG1ldGhvZCBhZ2FpbiB3aXRoIHRoZSBzZWxlY3RlZCBicmVha291dCBncm91cAogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcignTm8gYnJlYWtvdXQgZ3JvdXBzIGF2YWlsYWJsZScpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdDb3VsZCBub3QgZmluZCBNZXRpc1hGYWN0b3JzIHBhcmVudCBjb21wb25lbnQnKTsKICAgICAgfQogICAgfSwKCiAgICB2aWV3QnJlYWtvdXREZXRhaWxzKGdyb3VwLCB0YXJnZXRUYWIgPSAnZ3JvdXAnKSB7CiAgICAgIGNvbnNvbGUubG9nKGBWaWV3aW5nIGRldGFpbHMgZm9yIGJyZWFrb3V0IGdyb3VwOiAke2dyb3VwLm5hbWV9IGluICR7dGFyZ2V0VGFifSB0YWJgKTsKCiAgICAgIC8vIFNldCB0aGUgc2VsZWN0ZWQgYnJlYWtvdXQgZ3JvdXAKICAgICAgdGhpcy5zZWxlY3RlZEJyZWFrb3V0R3JvdXAgPSBncm91cC5uYW1lOwoKICAgICAgLy8gRmluZCB0aGUgcGFyZW50IE1ldGlzWEZhY3RvcnMgY29tcG9uZW50CiAgICAgIGxldCBwYXJlbnQgPSB0aGlzLiRwYXJlbnQ7CiAgICAgIHdoaWxlIChwYXJlbnQgJiYgcGFyZW50LiRvcHRpb25zLm5hbWUgIT09ICdNZXRpc1hGYWN0b3JzJykgewogICAgICAgIHBhcmVudCA9IHBhcmVudC4kcGFyZW50OwogICAgICB9CgogICAgICBpZiAocGFyZW50KSB7CiAgICAgICAgaWYgKHRhcmdldFRhYiA9PT0gJ2hlYXRtYXAnKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygnRm91bmQgTWV0aXNYRmFjdG9ycyBwYXJlbnQgY29tcG9uZW50LCBuYXZpZ2F0aW5nIHRvIEhlYXRtYXAgdGFiJyk7CiAgICAgICAgICAvLyBTZXQgdGhlIGFjdGl2ZSB0YWIgdG8gSGVhdG1hcCAoaW5kZXggMCkgYW5kIHRoZW4gc2VsZWN0IHRoZSBicmVha291dCBncm91cAogICAgICAgICAgcGFyZW50LmFjdGl2ZVRhYiA9IDA7IC8vIEhlYXRtYXAgdGFiIGluZGV4CiAgICAgICAgICBwYXJlbnQuc2VsZWN0QnJlYWtvdXRGcm9tRGFzaGJvYXJkKGdyb3VwLm5hbWUpOwoKICAgICAgICAgIC8vIElmIHRoZXJlJ3MgYSBzcGVjaWZpYyBtZXRob2QgdG8gaGlnaGxpZ2h0IHRoaXMgZ3JvdXAgaW4gdGhlIGhlYXRtYXAsIGNhbGwgaXQKICAgICAgICAgIGlmIChwYXJlbnQuaGlnaGxpZ2h0R3JvdXBJbkhlYXRtYXApIHsKICAgICAgICAgICAgcGFyZW50LiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgcGFyZW50LmhpZ2hsaWdodEdyb3VwSW5IZWF0bWFwKGdyb3VwLm5hbWUpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIE1ldGlzWEZhY3RvcnMgcGFyZW50IGNvbXBvbmVudCwgbmF2aWdhdGluZyB0byBHcm91cCB0YWInKTsKICAgICAgICAgIC8vIERlZmF1bHQgYmVoYXZpb3IgLSBuYXZpZ2F0ZSB0byBHcm91cCB0YWIKICAgICAgICAgIHBhcmVudC5zZWxlY3RCcmVha291dEZyb21EYXNoYm9hcmQoZ3JvdXAubmFtZSk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0NvdWxkIG5vdCBmaW5kIE1ldGlzWEZhY3RvcnMgcGFyZW50IGNvbXBvbmVudCcpOwogICAgICB9CiAgICB9LAoKICAgIC8vIE5hdmlnYXRlIHRvIGhlYXRtYXAgdGFiIGFuZCBoaWdobGlnaHQgdGhpcyBncm91cAogICAgdmlld0JyZWFrb3V0SW5IZWF0bWFwKGdyb3VwKSB7CiAgICAgIHRoaXMudmlld0JyZWFrb3V0RGV0YWlscyhncm91cCwgJ2hlYXRtYXAnKTsKICAgIH0sCgogICAgZm9ybWF0TW9udGgoZGF0ZVN0cikgewogICAgICAvLyBGb3JtYXQgWVlZWS1NTSB0byBNTU0gZm9ybWF0IChlLmcuLCAyMDI0LTA2IHRvIEp1bikKICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHIgKyAnLTAxJyk7IC8vIEFkZCBkYXkgdG8gbWFrZSBhIHZhbGlkIGRhdGUKICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ3Nob3J0JyB9KTsKICAgIH0sCgogICAgLy8gVG9nZ2xlIGNyaXRpY2FsIGlzc3VlcyBzZWN0aW9uIGV4cGFuZGVkL2NvbGxhcHNlZAogICAgdG9nZ2xlQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZCgpIHsKICAgICAgY29uc29sZS5sb2coJ1RvZ2dsaW5nIGNyaXRpY2FsIGlzc3VlcyBleHBhbmRlZCBzdGF0ZScpOwogICAgICB0aGlzLmlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZCA9ICF0aGlzLmlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZDsKICAgIH0sCgogICAgLy8gSGFuZGxlIHNldmVyaXR5IGZpbHRlciBkcm9wZG93biBjaGFuZ2UKICAgIGhhbmRsZVNldmVyaXR5RmlsdGVyQ2hhbmdlKCkgewogICAgICBjb25zb2xlLmxvZyhgU2V2ZXJpdHkgZmlsdGVyIGNoYW5nZWQgdG86ICR7dGhpcy5zZXZlcml0eUZpbHRlcn1gKTsKCiAgICAgIGlmICh0aGlzLnNldmVyaXR5RmlsdGVyID09PSAnYWxsJykgewogICAgICAgIC8vIENsZWFyIHNldmVyaXR5IGZpbHRlcnMKICAgICAgICB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eSA9IFtdOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIFNldCB0aGUgc2VsZWN0ZWQgc2V2ZXJpdHkKICAgICAgICB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eSA9IFt0aGlzLnNldmVyaXR5RmlsdGVyXTsKICAgICAgfQogICAgfSwKCiAgICAvLyBIYW5kbGUgYW5hbHlzaXMgdHlwZSBmaWx0ZXIgZHJvcGRvd24gY2hhbmdlCiAgICBoYW5kbGVBbmFseXNpc0ZpbHRlckNoYW5nZSgpIHsKICAgICAgY29uc29sZS5sb2coYEFuYWx5c2lzIGZpbHRlciBjaGFuZ2VkIHRvOiAke3RoaXMuYW5hbHlzaXNUeXBlRmlsdGVyfWApOwoKICAgICAgaWYgKHRoaXMuYW5hbHlzaXNUeXBlRmlsdGVyID09PSAnYWxsJykgewogICAgICAgIC8vIENsZWFyIGFuYWx5c2lzIHR5cGUgZmlsdGVycwogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFtdOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIFNldCB0aGUgc2VsZWN0ZWQgYW5hbHlzaXMgdHlwZQogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFt0aGlzLmFuYWx5c2lzVHlwZUZpbHRlcl07CiAgICAgIH0KICAgIH0sCgogICAgLy8gQ2xlYXIgYWxsIGZpbHRlcnMKICAgIGNsZWFyRmlsdGVycygpIHsKICAgICAgY29uc29sZS5sb2coJ0NsZWFyaW5nIGFsbCBmaWx0ZXJzJyk7CiAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLnNldmVyaXR5ID0gW107CiAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFtdOwogICAgICB0aGlzLnNldmVyaXR5RmlsdGVyID0gJ2FsbCc7CiAgICAgIHRoaXMuYW5hbHlzaXNUeXBlRmlsdGVyID0gJ2FsbCc7CiAgICB9LAoKICAgIGluaXRDYXRlZ29yeVN1bW1hcnlDaGFydCgpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyBEZXN0cm95IGV4aXN0aW5nIGNoYXJ0IGlmIGl0IGV4aXN0cwogICAgICAgIGlmICh0aGlzLmNhdGVnb3J5U3VtbWFyeUNoYXJ0KSB7CiAgICAgICAgICB0aGlzLmNhdGVnb3J5U3VtbWFyeUNoYXJ0LmRlc3Ryb3koKTsKICAgICAgICB9CgogICAgICAgIC8vIEdldCB0aGUgY2hhcnQgY29udGFpbmVyIGVsZW1lbnQKICAgICAgICBjb25zdCBjaGFydENvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjYXRlZ29yeVN1bW1hcnlDaGFydCcpOwogICAgICAgIGlmICghY2hhcnRDb250YWluZXIpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0NhdGVnb3J5IHN1bW1hcnkgY2hhcnQgY29udGFpbmVyIG5vdCBmb3VuZCcpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KCiAgICAgICAgLy8gQ3JlYXRlIG1vY2sgZGF0YSBmb3IgdGhlIHNlbGVjdGVkIGNhdGVnb3J5CiAgICAgICAgY29uc3QgY2hhcnREYXRhID0gdGhpcy5nZW5lcmF0ZUNhdGVnb3J5U3VtbWFyeURhdGEoKTsKCiAgICAgICAgLy8gQ3JlYXRlIGNoYXJ0IG9wdGlvbnMKICAgICAgICBjb25zdCBvcHRpb25zID0gewogICAgICAgICAgdGl0bGU6IGAke3RoaXMuc2VsZWN0ZWRDYXRlZ29yeX0gQW5hbHlzaXMgU3VtbWFyeWAsCiAgICAgICAgICBheGVzOiB7CiAgICAgICAgICAgIGxlZnQ6IHsKICAgICAgICAgICAgICB0aXRsZTogJ1gtRmFjdG9yJywKICAgICAgICAgICAgICBtYXBzVG86ICd2YWx1ZScsCiAgICAgICAgICAgICAgZG9tYWluOiBbMCwgNF0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgYm90dG9tOiB7CiAgICAgICAgICAgICAgdGl0bGU6ICdNb250aCcsCiAgICAgICAgICAgICAgbWFwc1RvOiAnZGF0ZScsCiAgICAgICAgICAgICAgc2NhbGVUeXBlOiAnbGFiZWxzJwogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgaGVpZ2h0OiAnMzAwcHgnLAogICAgICAgICAgd2lkdGg6ICcxMDAlJywKICAgICAgICAgIGxlZ2VuZDogewogICAgICAgICAgICBhbGlnbm1lbnQ6ICdjZW50ZXInLAogICAgICAgICAgICBlbmFibGVkOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgc2NhbGU6IHsKICAgICAgICAgICAgICAnWC1GYWN0b3InOiAnIzAwNjJmZicsCiAgICAgICAgICAgICAgJ1RocmVzaG9sZCc6ICcjZmY5YTAwJwogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgdG9vbGJhcjogewogICAgICAgICAgICBlbmFibGVkOiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIGRhdGE6IHsKICAgICAgICAgICAgZ3JvdXBNYXBzVG86ICdncm91cCcKICAgICAgICAgIH0sCiAgICAgICAgICB0aGVtZTogJ2c5MCcsIC8vIFVzZSBkYXJrIHRoZW1lIHRvIG1hdGNoIHRoZSBVSQogICAgICAgICAgdGhyZXNob2xkczogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgdmFsdWU6IDEuNSwKICAgICAgICAgICAgICBsYWJlbDogJ1N1c3RhaW5lZCBQcm9ibGVtIFRocmVzaG9sZCcsCiAgICAgICAgICAgICAgZmlsbENvbG9yOiAnI2ZmOWEwMCcsCiAgICAgICAgICAgICAgb3BhY2l0eTogMC4xCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICB2YWx1ZTogMy4wLAogICAgICAgICAgICAgIGxhYmVsOiAnQ3JpdGljYWwgU3Bpa2UgVGhyZXNob2xkJywKICAgICAgICAgICAgICBmaWxsQ29sb3I6ICcjZmE0ZDU2JywKICAgICAgICAgICAgICBvcGFjaXR5OiAwLjEKICAgICAgICAgICAgfQogICAgICAgICAgXQogICAgICAgIH07CgogICAgICAgIC8vIENyZWF0ZSB0aGUgY2hhcnQKICAgICAgICB0aGlzLmNhdGVnb3J5U3VtbWFyeUNoYXJ0ID0gbmV3IENvbWJvQ2hhcnQoY2hhcnRDb250YWluZXIsIHsKICAgICAgICAgIGRhdGE6IGNoYXJ0RGF0YSwKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgLi4ub3B0aW9ucywKICAgICAgICAgICAgY29tYm9DaGFydFR5cGVzOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgICAgY29ycmVzcG9uZGluZ0RhdGFzZXRzOiBbJ1gtRmFjdG9yJywgJ1RocmVzaG9sZCddLAogICAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgICBwb2ludHM6IHsKICAgICAgICAgICAgICAgICAgICByYWRpdXM6IDMKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg6IDIKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgY29uc29sZS5sb2coJ0NhdGVnb3J5IHN1bW1hcnkgY2hhcnQgaW5pdGlhbGl6ZWQnKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgY2F0ZWdvcnkgc3VtbWFyeSBjaGFydDonLCBlcnJvcik7CgogICAgICAgIC8vIENyZWF0ZSBhIHNpbXBsZSBmYWxsYmFjayBkaXNwbGF5IGZvciB0aGUgZGF0YQogICAgICAgIGNvbnN0IGNoYXJ0Q29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2NhdGVnb3J5U3VtbWFyeUNoYXJ0Jyk7CiAgICAgICAgaWYgKGNoYXJ0Q29udGFpbmVyKSB7CiAgICAgICAgICBjaGFydENvbnRhaW5lci5pbm5lckhUTUwgPSBgCiAgICAgICAgICAgIDxkaXYgc3R5bGU9ImhlaWdodDogMTAwJTsgZGlzcGxheTogZmxleDsgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IGFsaWduLWl0ZW1zOiBjZW50ZXI7Ij4KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJjb2xvcjogI2Y0ZjRmNDsgbWFyZ2luLWJvdHRvbTogMTZweDsiPiR7dGhpcy5zZWxlY3RlZENhdGVnb3J5fSBBbmFseXNpcyBTdW1tYXJ5PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6ICMwMDYyZmY7Ij5ObyBjaGFydCBkYXRhIGF2YWlsYWJsZTwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIGA7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIGdlbmVyYXRlQ2F0ZWdvcnlTdW1tYXJ5RGF0YSgpIHsKICAgICAgLy8gR2VuZXJhdGUgbW9jayBkYXRhIGZvciB0aGUgY2F0ZWdvcnkgc3VtbWFyeSBjaGFydAogICAgICBjb25zdCBkYXRhID0gW107CiAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsKCiAgICAgIC8vIEdlbmVyYXRlIGRhdGEgZm9yIHRoZSBsYXN0IDYgbW9udGhzCiAgICAgIGZvciAobGV0IGkgPSA1OyBpID49IDA7IGktLSkgewogICAgICAgIGNvbnN0IG1vbnRoRGF0ZSA9IG5ldyBEYXRlKGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCksIGN1cnJlbnREYXRlLmdldE1vbnRoKCkgLSBpLCAxKTsKICAgICAgICBjb25zdCBtb250aFN0ciA9IHRoaXMuZm9ybWF0TW9udGgobW9udGhEYXRlLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgNykpOwoKICAgICAgICAvLyBBZGQgWC1GYWN0b3IgZGF0YSBwb2ludAogICAgICAgIGNvbnN0IHhGYWN0b3IgPSBpID09PSAwID8KICAgICAgICAgICh0aGlzLnNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdSb290IENhdXNlJyA/IDMuMiA6CiAgICAgICAgICAgdGhpcy5zZWxlY3RlZENhdGVnb3J5ID09PSAnVmludGFnZScgPyAyLjUgOgogICAgICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ1N1cHBsaWVyJyA/IDEuNiA6IDEuMCkgOgogICAgICAgICAgTWF0aC5yYW5kb20oKSAqIDEuNSArIDAuNTsgLy8gUmFuZG9tIGJldHdlZW4gMC41LTIuMCBmb3IgcHJldmlvdXMgbW9udGhzCgogICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICBkYXRlOiBtb250aFN0ciwKICAgICAgICAgIGdyb3VwOiAnWC1GYWN0b3InLAogICAgICAgICAgdmFsdWU6IHhGYWN0b3IKICAgICAgICB9KTsKCiAgICAgICAgLy8gQWRkIHRocmVzaG9sZCByZWZlcmVuY2UgbGluZQogICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICBkYXRlOiBtb250aFN0ciwKICAgICAgICAgIGdyb3VwOiAnVGhyZXNob2xkJywKICAgICAgICAgIHZhbHVlOiAxLjUKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgcmV0dXJuIGRhdGE7CiAgICB9LAoKICAgIC8vIFRoZXNlIG1ldGhvZHMgYXJlIG5vIGxvbmdlciBuZWVkZWQgd2l0aCB0aGUgbmV3IGZpbHRlcmluZyBhcHByb2FjaAogICAgLy8gYnV0IHdlJ2xsIGtlZXAgdGhlbSBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eQogICAgZ2V0Q3JpdGljYWxDb3VudEJ5Q2F0ZWdvcnkoY2F0ZWdvcnkpIHsKICAgICAgcmV0dXJuIHRoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLmZpbHRlcihpc3N1ZSA9PiBpc3N1ZS5hbmFseXNpc1R5cGUgPT09IGNhdGVnb3J5KS5sZW5ndGg7CiAgICB9LAoKICAgIHRvZ2dsZUlzc3VlRXhwYW5kZWQoaXNzdWUpIHsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmV4cGFuZGVkSXNzdWVJZHMuaW5kZXhPZihpc3N1ZS5pZCk7CiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsKICAgICAgICAvLyBBZGQgdG8gZXhwYW5kZWQgbGlzdAogICAgICAgIHRoaXMuZXhwYW5kZWRJc3N1ZUlkcy5wdXNoKGlzc3VlLmlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyBSZW1vdmUgZnJvbSBleHBhbmRlZCBsaXN0CiAgICAgICAgdGhpcy5leHBhbmRlZElzc3VlSWRzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCgogICAgaXNJc3N1ZUV4cGFuZGVkKGlzc3VlSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRJc3N1ZUlkcy5pbmNsdWRlcyhpc3N1ZUlkKTsKICAgIH0sCgogICAgZ2V0WEZhY3RvclNldmVyaXR5Q2xhc3MoeEZhY3RvcikgewogICAgICBpZiAoeEZhY3RvciA+PSAzLjApIHsKICAgICAgICByZXR1cm4gJ3NldmVyaXR5LWNyaXRpY2FsJzsKICAgICAgfSBlbHNlIGlmICh4RmFjdG9yID49IDEuNSkgewogICAgICAgIHJldHVybiAnc2V2ZXJpdHktd2FybmluZyc7CiAgICAgIH0gZWxzZSBpZiAoeEZhY3RvciA+PSAxLjApIHsKICAgICAgICByZXR1cm4gJ3NldmVyaXR5LWNhdXRpb24nOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc2V2ZXJpdHktbm9ybWFsJzsKICAgICAgfQogICAgfSwKCiAgICAvLyBJdGVtIHRyYWNraW5nIG1ldGhvZHMKICAgIGFzeW5jIGxvYWRUcmFja2VkSXRlbXMoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nIHRyYWNrZWQgaXRlbXMgZnJvbSBBY3Rpb24gVHJhY2tlciBBUEknKTsKICAgICAgdGhpcy5pc0xvYWRpbmdUcmFja2VkSXRlbXMgPSB0cnVlOwoKICAgICAgdHJ5IHsKICAgICAgICAvLyBHZXQgdGhlIGNyaXRpY2FsIGJyZWFrb3V0IGdyb3VwcyBmcm9tIHRoZSBjdXJyZW50IG1vbnRoCiAgICAgICAgY29uc3QgY3JpdGljYWxCcmVha291dEdyb3VwcyA9IHRoaXMuc29ydGVkQ3JpdGljYWxCcmVha291dEdyb3Vwcy5tYXAoZ3JvdXAgPT4gZ3JvdXAubmFtZSk7CiAgICAgICAgY29uc29sZS5sb2coJ0NyaXRpY2FsIGJyZWFrb3V0IGdyb3VwcyBmb3IgZmlsdGVyaW5nIGFjdGlvbiBpdGVtczonLCBjcml0aWNhbEJyZWFrb3V0R3JvdXBzKTsKCiAgICAgICAgLy8gTWFrZSBBUEkgY2FsbCB0byBnZXQgYWN0aW9uIHRyYWNrZXIgZGF0YQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfYWN0aW9uX3RyYWNrZXJfZGF0YScsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICAvLyBGaWx0ZXIgYnkgYnJlYWtvdXQgZ3JvdXBzIHdpdGggaXNzdWVzIGluIHRoZSBjdXJyZW50IG1vbnRoCiAgICAgICAgICAgIGJyZWFrb3V0TmFtZXM6IGNyaXRpY2FsQnJlYWtvdXRHcm91cHMKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGFjdGlvbiB0cmFja2VyIGRhdGE6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycgJiYgZGF0YS5pdGVtcyAmJiBkYXRhLml0ZW1zLmxlbmd0aCA+IDApIHsKICAgICAgICAgIC8vIEZvcm1hdCB0aGUgYWN0aW9uIHRyYWNrZXIgaXRlbXMgZm9yIGRpc3BsYXkKICAgICAgICAgIHRoaXMudHJhY2tlZEl0ZW1zID0gZGF0YS5pdGVtcy5tYXAoaXRlbSA9PiAoewogICAgICAgICAgICBpZDogaXRlbS5pZCwKICAgICAgICAgICAgbmFtZTogaXRlbS5hY3Rpb24gJiYgaXRlbS5hY3Rpb24ubGVuZ3RoID4gNTAgPyBpdGVtLmFjdGlvbi5zdWJzdHJpbmcoMCwgNTApICsgJy4uLicgOiBpdGVtLmFjdGlvbiB8fCBgQWN0aW9uIGZvciAke2l0ZW0uZ3JvdXB9YCwKICAgICAgICAgICAgc3RhdHVzOiBpdGVtLnN0YXR1cyB8fCAnSW4gUHJvZ3Jlc3MnLAogICAgICAgICAgICBvd25lcjogaXRlbS5hc3NpZ25lZSB8fCAnVW5hc3NpZ25lZCcsCiAgICAgICAgICAgIGR1ZURhdGU6IGl0ZW0uZGVhZGxpbmUgfHwgJ05vIGRlYWRsaW5lJywKICAgICAgICAgICAgcmVsYXRlZElzc3VlOiBpdGVtLmdyb3VwIHx8ICdVbmtub3duJywKICAgICAgICAgICAgZGVzY3JpcHRpb246IGl0ZW0ubm90ZXMgfHwgJ05vIGRlc2NyaXB0aW9uIGF2YWlsYWJsZScsCiAgICAgICAgICAgIHByb2dyZXNzOiBpdGVtLnByb2dyZXNzIHx8IDAsCiAgICAgICAgICAgIC8vIEFkZCBhIGZsYWcgdG8gaW5kaWNhdGUgaWYgdGhpcyBpdGVtIGlzIHJlbGF0ZWQgdG8gYSBjcml0aWNhbCBicmVha291dCBncm91cAogICAgICAgICAgICBpc0NyaXRpY2FsOiBjcml0aWNhbEJyZWFrb3V0R3JvdXBzLmluY2x1ZGVzKGl0ZW0uZ3JvdXApCiAgICAgICAgICB9KSk7CgogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMudHJhY2tlZEl0ZW1zLmxlbmd0aH0gdHJhY2tlZCBpdGVtcyBmcm9tIEFQSWApOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oJ05vIGFjdGlvbiB0cmFja2VyIGl0ZW1zIGZvdW5kIGluIEFQSSByZXNwb25zZSBvciBBUEkgY2FsbCBmYWlsZWQsIHVzaW5nIHNhbXBsZSBkYXRhJyk7CgogICAgICAgICAgLy8gU2FtcGxlIGRhdGEgYXMgZmFsbGJhY2sKICAgICAgICAgIGNvbnN0IGFsbEFjdGlvbkl0ZW1zID0gWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICdpdGVtMScsCiAgICAgICAgICAgICAgbmFtZTogJ0ZhbiBUaGVtaXMgQWlyZmxvdyBJbnZlc3RpZ2F0aW9uJywKICAgICAgICAgICAgICBzdGF0dXM6ICdJbiBQcm9ncmVzcycsCiAgICAgICAgICAgICAgb3duZXI6ICdKb2huIFNtaXRoJywKICAgICAgICAgICAgICBkdWVEYXRlOiAnMjAyNC0wNy0xNScsCiAgICAgICAgICAgICAgcmVsYXRlZElzc3VlOiAnRmFuIFRoZW1pcycsCiAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdJbnZlc3RpZ2F0aW9uIGludG8gYWlyZmxvdyBpc3N1ZXMgaW4gdGhlIG5ldyBGYW4gVGhlbWlzIGRlc2lnbiB0aGF0IGNhdXNlZCBhIDMuMnggc3Bpa2UgaW4gZmFpbHVyZXMuJywKICAgICAgICAgICAgICBwcm9ncmVzczogNjUsCiAgICAgICAgICAgICAgaXNDcml0aWNhbDogY3JpdGljYWxCcmVha291dEdyb3Vwcy5pbmNsdWRlcygnRmFuIFRoZW1pcycpCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBpZDogJ2l0ZW0yJywKICAgICAgICAgICAgICBuYW1lOiAnVmljdG9yaWEgQ3J5cHRvIFBvd2VyIERlbGl2ZXJ5IEZpeCcsCiAgICAgICAgICAgICAgc3RhdHVzOiAnUGVuZGluZyBSZXZpZXcnLAogICAgICAgICAgICAgIG93bmVyOiAnSmFuZSBEb2UnLAogICAgICAgICAgICAgIGR1ZURhdGU6ICcyMDI0LTA3LTEwJywKICAgICAgICAgICAgICByZWxhdGVkSXNzdWU6ICdWaWN0b3JpYSBDcnlwdG8nLAogICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnSW1wbGVtZW50YXRpb24gb2YgcG93ZXIgZGVsaXZlcnkgaW1wcm92ZW1lbnRzIHRvIGFkZHJlc3MgdGhlIHN1c3RhaW5lZCBwcm9ibGVtIGluIFZpY3RvcmlhIENyeXB0byB1bml0cy4nLAogICAgICAgICAgICAgIHByb2dyZXNzOiA4NSwKICAgICAgICAgICAgICBpc0NyaXRpY2FsOiBjcml0aWNhbEJyZWFrb3V0R3JvdXBzLmluY2x1ZGVzKCdWaWN0b3JpYSBDcnlwdG8nKQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICdpdGVtMycsCiAgICAgICAgICAgICAgbmFtZTogJ1N1cHBsaWVyIEFCQyBRdWFsaXR5IEF1ZGl0JywKICAgICAgICAgICAgICBzdGF0dXM6ICdDb21wbGV0ZWQnLAogICAgICAgICAgICAgIG93bmVyOiAnUm9iZXJ0IEpvaG5zb24nLAogICAgICAgICAgICAgIGR1ZURhdGU6ICcyMDI0LTA2LTMwJywKICAgICAgICAgICAgICByZWxhdGVkSXNzdWU6ICdWaWN0b3JpYSBDcnlwdG8nLAogICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnUXVhbGl0eSBhdWRpdCBvZiBTdXBwbGllciBBQkMgdG8gYWRkcmVzcyBoaWdoZXIgZmFpbHVyZSByYXRlcyBpbiBWaWN0b3JpYSBDcnlwdG8gdW5pdHMuJywKICAgICAgICAgICAgICBwcm9ncmVzczogMTAwLAogICAgICAgICAgICAgIGlzQ3JpdGljYWw6IGNyaXRpY2FsQnJlYWtvdXRHcm91cHMuaW5jbHVkZXMoJ1ZpY3RvcmlhIENyeXB0bycpCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBpZDogJ2l0ZW00JywKICAgICAgICAgICAgICBuYW1lOiAnUXVhbnR1bSBOZXh1cyBUaGVybWFsIEFuYWx5c2lzJywKICAgICAgICAgICAgICBzdGF0dXM6ICdJbiBQcm9ncmVzcycsCiAgICAgICAgICAgICAgb3duZXI6ICdFbWlseSBDaGVuJywKICAgICAgICAgICAgICBkdWVEYXRlOiAnMjAyNC0wNy0yMCcsCiAgICAgICAgICAgICAgcmVsYXRlZElzc3VlOiAnUXVhbnR1bSBOZXh1cycsCiAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdUaGVybWFsIGFuYWx5c2lzIG9mIFF1YW50dW0gTmV4dXMgY29tcG9uZW50cyB0byBhZGRyZXNzIG92ZXJoZWF0aW5nIGlzc3Vlcy4nLAogICAgICAgICAgICAgIHByb2dyZXNzOiA0MCwKICAgICAgICAgICAgICBpc0NyaXRpY2FsOiBjcml0aWNhbEJyZWFrb3V0R3JvdXBzLmluY2x1ZGVzKCdRdWFudHVtIE5leHVzJykKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIGlkOiAnaXRlbTUnLAogICAgICAgICAgICAgIG5hbWU6ICdOZWJ1bGEgRHJpdmUgRmlybXdhcmUgVXBkYXRlJywKICAgICAgICAgICAgICBzdGF0dXM6ICdCbG9ja2VkJywKICAgICAgICAgICAgICBvd25lcjogJ01pY2hhZWwgQnJvd24nLAogICAgICAgICAgICAgIGR1ZURhdGU6ICcyMDI0LTA3LTA1JywKICAgICAgICAgICAgICByZWxhdGVkSXNzdWU6ICdOZWJ1bGEgRHJpdmUnLAogICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRmlybXdhcmUgdXBkYXRlIHRvIGFkZHJlc3MgcGVyZm9ybWFuY2UgaXNzdWVzIGluIE5lYnVsYSBEcml2ZSB1bml0cy4nLAogICAgICAgICAgICAgIHByb2dyZXNzOiAyNSwKICAgICAgICAgICAgICBpc0NyaXRpY2FsOiBjcml0aWNhbEJyZWFrb3V0R3JvdXBzLmluY2x1ZGVzKCdOZWJ1bGEgRHJpdmUnKQogICAgICAgICAgICB9CiAgICAgICAgICBdOwoKICAgICAgICAgIC8vIEZpbHRlciBpdGVtcyB0byBvbmx5IGluY2x1ZGUgdGhvc2UgcmVsYXRlZCB0byBicmVha291dCBncm91cHMgd2l0aCBpc3N1ZXMgaW4gdGhlIGN1cnJlbnQgbW9udGgKICAgICAgICAgIHRoaXMudHJhY2tlZEl0ZW1zID0gYWxsQWN0aW9uSXRlbXMuZmlsdGVyKGl0ZW0gPT4KICAgICAgICAgICAgY3JpdGljYWxCcmVha291dEdyb3Vwcy5zb21lKGdyb3VwTmFtZSA9PgogICAgICAgICAgICAgIGl0ZW0ucmVsYXRlZElzc3VlLmluY2x1ZGVzKGdyb3VwTmFtZSkKICAgICAgICAgICAgKQogICAgICAgICAgKTsKICAgICAgICB9CgogICAgICAgIC8vIFNvcnQgaXRlbXMgYnkgc3RhdHVzIHByaW9yaXR5IGFuZCB0aGVuIGJ5IHdoZXRoZXIgdGhleSdyZSBjcml0aWNhbAogICAgICAgIHRoaXMudHJhY2tlZEl0ZW1zLnNvcnQoKGEsIGIpID0+IHsKICAgICAgICAgIGNvbnN0IHN0YXR1c1ByaW9yaXR5ID0gewogICAgICAgICAgICAnYmxvY2tlZCc6IDAsCiAgICAgICAgICAgICdpbiBwcm9ncmVzcyc6IDEsCiAgICAgICAgICAgICdwZW5kaW5nIHJldmlldyc6IDIsCiAgICAgICAgICAgICdjb21wbGV0ZWQnOiAzCiAgICAgICAgICB9OwoKICAgICAgICAgIC8vIEZpcnN0IHNvcnQgYnkgc3RhdHVzIHByaW9yaXR5CiAgICAgICAgICBjb25zdCBzdGF0dXNEaWZmID0gc3RhdHVzUHJpb3JpdHlbYS5zdGF0dXMudG9Mb3dlckNhc2UoKV0gLSBzdGF0dXNQcmlvcml0eVtiLnN0YXR1cy50b0xvd2VyQ2FzZSgpXTsKCiAgICAgICAgICAvLyBJZiBzdGF0dXMgaXMgdGhlIHNhbWUsIHNvcnQgYnkgY3JpdGljYWwgZmxhZyAoY3JpdGljYWwgaXRlbXMgZmlyc3QpCiAgICAgICAgICBpZiAoc3RhdHVzRGlmZiA9PT0gMCkgewogICAgICAgICAgICByZXR1cm4gYi5pc0NyaXRpY2FsIC0gYS5pc0NyaXRpY2FsOwogICAgICAgICAgfQoKICAgICAgICAgIHJldHVybiBzdGF0dXNEaWZmOwogICAgICAgIH0pOwoKICAgICAgICBjb25zb2xlLmxvZygnVHJhY2tlZCBpdGVtcyBsb2FkZWQgYW5kIHNvcnRlZDonLCB0aGlzLnRyYWNrZWRJdGVtcyk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0cmFja2VkIGl0ZW1zOicsIGVycm9yKTsKICAgICAgICB0aGlzLnRyYWNrZWRJdGVtcyA9IFtdOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaXNMb2FkaW5nVHJhY2tlZEl0ZW1zID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0SXRlbVN0YXR1c0NsYXNzKHN0YXR1cykgewogICAgICBzd2l0Y2ggKHN0YXR1cy50b0xvd2VyQ2FzZSgpKSB7CiAgICAgICAgY2FzZSAnaW4gcHJvZ3Jlc3MnOgogICAgICAgICAgcmV0dXJuICdzdGF0dXMtaW4tcHJvZ3Jlc3MnOwogICAgICAgIGNhc2UgJ3BlbmRpbmcgcmV2aWV3JzoKICAgICAgICAgIHJldHVybiAnc3RhdHVzLXBlbmRpbmcnOwogICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6CiAgICAgICAgICByZXR1cm4gJ3N0YXR1cy1jb21wbGV0ZWQnOwogICAgICAgIGNhc2UgJ2Jsb2NrZWQnOgogICAgICAgICAgcmV0dXJuICdzdGF0dXMtYmxvY2tlZCc7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAnc3RhdHVzLWRlZmF1bHQnOwogICAgICB9CiAgICB9LAoKICAgIC8vIENoZWNrIGlmIGEgZ3JvdXAgaXMgaW4gdGhlIGNyaXRpY2FsIGJyZWFrb3V0IGdyb3VwcyBsaXN0CiAgICBpc0NyaXRpY2FsR3JvdXAoZ3JvdXBOYW1lKSB7CiAgICAgIHJldHVybiB0aGlzLnNvcnRlZENyaXRpY2FsQnJlYWtvdXRHcm91cHMuc29tZShncm91cCA9PiBncm91cC5uYW1lID09PSBncm91cE5hbWUpOwogICAgfSwKCiAgICB2aWV3SXRlbURldGFpbHMoaXRlbSkgewogICAgICBjb25zb2xlLmxvZygnVmlld2luZyBpdGVtIGRldGFpbHM6JywgaXRlbSk7CiAgICAgIC8vIE5hdmlnYXRlIHRvIHRoZSBBY3Rpb24gVHJhY2tlciBwYWdlIHdpdGggdGhlIHNwZWNpZmljIGl0ZW0gSUQKICAgICAgdGhpcy4kcm91dGVyLnB1c2goYC9hY3Rpb24tdHJhY2tlci9pdGVtcy8ke2l0ZW0uaWR9YCk7CiAgICB9LAoKICAgIGdvVG9BY3Rpb25UcmFja2VyKCkgewogICAgICBjb25zb2xlLmxvZygnTmF2aWdhdGluZyB0byBBY3Rpb24gVHJhY2tlcicpOwogICAgICAvLyBOYXZpZ2F0ZSB0byB0aGUgQWN0aW9uIFRyYWNrZXIgcGFnZSB1c2luZyBWdWUgUm91dGVyCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvYWN0aW9uLXRyYWNrZXInKTsKICAgIH0sCgogICAgLy8gUm9vdCBDYXVzZSBDaGFydCBtZXRob2RzCiAgICBhc3luYyBsb2FkUm9vdENhdXNlRGF0YSgpIHsKICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgcm9vdCBjYXVzZSBjaGFydCBkYXRhIGZvciBQUUUgRGFzaGJvYXJkJyk7CiAgICAgIHRoaXMuaXNSb290Q2F1c2VEYXRhTG9hZGluZyA9IHRydWU7CgogICAgICB0cnkgewogICAgICAgIC8vIEdlbmVyYXRlIG1vY2sgZGF0YSBmb3IgdGhlIHJvb3QgY2F1c2UgY2hhcnQKICAgICAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY2FsbCBhbiBBUEkKICAgICAgICBjb25zdCBtb2NrRGF0YSA9IHRoaXMuZ2VuZXJhdGVNb2NrUm9vdENhdXNlRGF0YSgpOwogICAgICAgIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhID0gbW9ja0RhdGE7CgogICAgICAgIGNvbnNvbGUubG9nKCdSb290IGNhdXNlIGNoYXJ0IGRhdGEgbG9hZGVkOicsIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHJvb3QgY2F1c2UgZGF0YTonLCBlcnJvcik7CiAgICAgICAgdGhpcy5yb290Q2F1c2VDaGFydERhdGEgPSBbXTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzUm9vdENhdXNlRGF0YUxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICBnZW5lcmF0ZU1vY2tSb290Q2F1c2VEYXRhKCkgewogICAgICAvLyBHZW5lcmF0ZSBtb2NrIGRhdGEgc2ltaWxhciB0byB3aGF0IE1ldGlzWEZhY3RvcnMgdXNlcwogICAgICBjb25zdCBkYXRhID0gW107CiAgICAgIGNvbnN0IGNhdGVnb3JpZXMgPSBbJ0VsZWN0cmljYWwnLCAnTWVjaGFuaWNhbCcsICdUaGVybWFsJywgJ01hdGVyaWFsJywgJ1Byb2Nlc3MnXTsKICAgICAgY29uc3QgbW9udGhzID0gWydNYXkgMjAyNCcsICdKdW4gMjAyNCcsICdKdWwgMjAyNCddOwoKICAgICAgbW9udGhzLmZvckVhY2gobW9udGggPT4gewogICAgICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7CiAgICAgICAgICAvLyBHZW5lcmF0ZSByYW5kb20gZmFpbCByYXRlcyBiZXR3ZWVuIDAuNSUgYW5kIDQlCiAgICAgICAgICBjb25zdCBiYXNlUmF0ZSA9IE1hdGgucmFuZG9tKCkgKiAzLjUgKyAwLjU7CgogICAgICAgICAgZGF0YS5wdXNoKHsKICAgICAgICAgICAgZ3JvdXA6IGNhdGVnb3J5LAogICAgICAgICAgICBrZXk6IG1vbnRoLAogICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdChiYXNlUmF0ZS50b0ZpeGVkKDIpKSwKICAgICAgICAgICAgZGF0YTogewogICAgICAgICAgICAgIGRlZmVjdHM6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDIwKSArIDUsCiAgICAgICAgICAgICAgdm9sdW1lOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDAwKSArIDUwMCwKICAgICAgICAgICAgICBtb250aDogbW9udGgsCiAgICAgICAgICAgICAgY2F0ZWdvcnk6IGNhdGVnb3J5LAogICAgICAgICAgICAgIGlzQ3JpdGljYWw6IGJhc2VSYXRlID4gMi41LAogICAgICAgICAgICAgIGZvcm1hdHRlZFZhbHVlOiBgJHtiYXNlUmF0ZS50b0ZpeGVkKDIpfSVgCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKCiAgICAgIHJldHVybiBkYXRhOwogICAgfSwKCiAgICBoYW5kbGVSb290Q2F1c2VCYXJDbGljayhldmVudCkgewogICAgICBpZiAoZXZlbnQgJiYgZXZlbnQuZGF0YSkgewogICAgICAgIGNvbnN0IGNsaWNrZWREYXRhID0gZXZlbnQuZGF0YTsKICAgICAgICBjb25zb2xlLmxvZyhgUm9vdCBjYXVzZSBiYXIgY2xpY2tlZCBmb3IgY2F0ZWdvcnk6ICR7Y2xpY2tlZERhdGEuZ3JvdXB9LCBtb250aDogJHtjbGlja2VkRGF0YS5rZXl9YCk7CgogICAgICAgIC8vIFlvdSBjYW4gaW1wbGVtZW50IGFkZGl0aW9uYWwgZnVuY3Rpb25hbGl0eSBoZXJlLCBzdWNoIGFzIHNob3dpbmcgbW9yZSBkZXRhaWxzCiAgICAgICAgLy8gb3IgbmF2aWdhdGluZyB0byB0aGUgTWV0aXNYRmFjdG9ycyBHcm91cCB0YWIgd2l0aCB0aGlzIHNwZWNpZmljIGNhdGVnb3J5CiAgICAgIH0KICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["PQEDashboard.vue"], "names": [], "mappings": ";AAumBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "PQEDashboard.vue", "sourceRoot": "src/components/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>PQE Owner Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Breakout Group Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Breakout Group Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Current Month: {{ currentMonth ? new Date(currentMonth + '-01').toLocaleString('en-US', { month: 'long', year: 'numeric' }) : 'Loading...' }}\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"showAllAlerts = !showAllAlerts\"\n                  v-if=\"sortedCriticalBreakoutGroups.length > 0\"\n                >\n                  {{ showAllAlerts ? 'Show Critical Only' : 'See All Alerts' }}\n                </cv-button>\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"showAllBreakoutGroups = !showAllBreakoutGroups\"\n                  style=\"margin-left: 8px;\"\n                >\n                  {{ showAllBreakoutGroups ? 'Hide Normal Groups' : 'Show All Status' }}\n                </cv-button>\n              </div>\n            </div>\n\n            <!-- Loading State -->\n            <div v-if=\"isLoadingBreakoutGroups\" class=\"loading-container\">\n              <cv-inline-loading\n                status=\"active\"\n                loading-text=\"Loading breakout group alerts...\"\n              ></cv-inline-loading>\n            </div>\n\n            <!-- Critical Alerts (Always Visible) -->\n            <div v-else-if=\"sortedCriticalBreakoutGroups.length > 0\" class=\"breakout-groups-grid\">\n              <div\n                v-for=\"group in sortedHighPriorityGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card highlighted\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Medium Priority Alerts (Shown when \"See All Alerts\" is clicked) -->\n            <div v-if=\"showAllAlerts && sortedMediumPriorityGroups.length > 0\" class=\"breakout-groups-grid medium-priority-groups\">\n              <div\n                v-for=\"group in sortedMediumPriorityGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- All Normal Groups (Shown when \"Show All Status\" is clicked) -->\n            <div v-if=\"showAllBreakoutGroups && normalBreakoutGroups.length > 0\" class=\"breakout-groups-grid normal-groups\">\n              <div\n                v-for=\"group in normalBreakoutGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"sortedCriticalBreakoutGroups.length === 0\" class=\"no-data-message\">\n              No alerts found. All breakout groups are operating within normal parameters.\n            </div>\n          </div>\n\n          <!-- Item Tracking Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Item Tracking</h4>\n                <div class=\"section-subtitle\">\n                  Action items related to current month's critical breakout groups\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"goToActionTracker\"\n                  class=\"action-button\"\n                >\n                  Go to Action Tracker\n                </cv-button>\n              </div>\n            </div>\n\n            <div v-if=\"isLoadingTrackedItems\" class=\"loading-container\">\n              <cv-inline-loading\n                status=\"active\"\n                loading-text=\"Loading tracked items...\"\n              ></cv-inline-loading>\n            </div>\n\n            <div v-else-if=\"trackedItems.length > 0\" class=\"tracked-items-list\">\n              <div\n                v-for=\"item in trackedItems\"\n                :key=\"item.id\"\n                class=\"tracked-item-card\"\n                :class=\"[getItemStatusClass(item.status), {'critical-item': item.isCritical}]\"\n              >\n                <div class=\"tracked-item-header\">\n                  <h5 class=\"tracked-item-name\">{{ item.name }}</h5>\n                  <div class=\"tracked-item-status\">\n                    <span class=\"status-badge\" :class=\"getItemStatusClass(item.status)\">{{ item.status }}</span>\n                    <cv-tag v-if=\"item.isCritical\" kind=\"red\" label=\"Critical\" class=\"critical-tag\" />\n                  </div>\n                </div>\n\n                <div class=\"tracked-item-details\">\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Owner:</span>\n                    <span class=\"detail-value\">{{ item.owner }}</span>\n                  </div>\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Due Date:</span>\n                    <span class=\"detail-value\">{{ item.dueDate }}</span>\n                  </div>\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Related Group:</span>\n                    <span class=\"detail-value related-group\" :class=\"{'critical-group': isCriticalGroup(item.relatedIssue)}\">\n                      {{ item.relatedIssue }}\n                      <cv-tag v-if=\"isCriticalGroup(item.relatedIssue)\" kind=\"blue\" label=\"Current Month\" class=\"month-tag\" />\n                    </span>\n                  </div>\n                  <div class=\"detail-row\" v-if=\"item.progress !== undefined\">\n                    <span class=\"detail-label\">Progress:</span>\n                    <div class=\"progress-container\">\n                      <div class=\"progress-bar\" :style=\"{ width: item.progress + '%' }\"></div>\n                      <span class=\"progress-text\">{{ item.progress }}%</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"tracked-item-footer\">\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    @click.stop=\"viewItemDetails(item)\"\n                  >\n                    View in Action Tracker\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n\n            <div v-else class=\"no-data-message\">\n              No tracked items found for current month's critical breakout groups. Items will appear here when they are linked to the action tracker.\n            </div>\n          </div>\n        </div>\n\n        <!-- Right Column -->\n        <div class=\"dashboard-column\">\n          <!-- Validation Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Validation Status</h4>\n              <div class=\"last-updated-text\">Daily Status</div>\n            </div>\n\n            <!-- Validation Chart -->\n            <div class=\"chart-container\">\n              <div v-if=\"validationChartData.length > 0\">\n                <div class=\"chart-wrapper\">\n                  <div id=\"validationChart\" class=\"carbon-chart-container\"></div>\n                </div>\n              </div>\n              <div v-else class=\"no-data-message\">\n                <cv-inline-loading\n                  status=\"active\"\n                  loading-text=\"Loading validation data...\"\n                ></cv-inline-loading>\n              </div>\n            </div>\n\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Go to the validation page to review and validate new fails.\n                Currently <strong>{{ unvalidatedCount }}</strong> fails need validation.\n              </p>\n              <cv-button\n                kind=\"primary\"\n                @click=\"goToValidationPage\"\n                class=\"action-button\"\n              >\n                Go to Validation Page\n              </cv-button>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Root Cause Analysis</h4>\n              <div class=\"last-updated-text\">Current Month Analysis</div>\n            </div>\n\n            <!-- Root Cause Chart -->\n            <div class=\"chart-container\">\n              <RootCauseChart\n                :data=\"rootCauseChartData\"\n                :loading=\"isRootCauseDataLoading\"\n                :height=\"'400px'\"\n                title=\"Root Cause Categories by Month\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n            </div>\n\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div>\n          </div>\n\n          <!-- Advanced Analysis Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Advanced Analysis</h4>\n            </div>\n            <div class=\"analysis-controls\">\n              <div class=\"control-row\">\n                <div class=\"control-group\">\n                  <label for=\"analysis-type-dropdown\" class=\"control-label\">Analysis Type:</label>\n                  <cv-dropdown\n                    id=\"analysis-type-dropdown\"\n                    v-model=\"selectedAnalysisType\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"Root Cause\">Root Cause Analysis</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n                <cv-button\n                  kind=\"primary\"\n                  @click=\"goToGroupAnalysis\"\n                  class=\"action-button\"\n                >\n                  Go to Analysis\n                </cv-button>\n              </div>\n            </div>\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Select an analysis type and click \"Go to Analysis\" to view detailed breakout group analysis in the Group tab.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Critical Issue Details Modal -->\n    <cv-modal\n      :visible=\"showIssueModal\"\n      @modal-hidden=\"closeIssueModal\"\n      class=\"issue-details-modal\"\n      :size=\"'lg'\"\n    >\n      <template slot=\"title\">Critical Issue Details</template>\n      <template slot=\"content\">\n        <div v-if=\"selectedIssue\" class=\"issue-details-content\">\n          <h4>{{ selectedIssue.category }} - {{ selectedIssue.month }}</h4>\n          <div class=\"issue-chart-container\">\n            <ccv-simple-bar-chart\n              v-if=\"issueChartData.length > 0\"\n              :data=\"issueChartData\"\n              :options=\"issueChartOptions\"\n            ></ccv-simple-bar-chart>\n          </div>\n          <div class=\"issue-ai-description\">\n            <h5>AI Analysis</h5>\n            <p>{{ selectedIssue.aiDescription }}</p>\n          </div>\n          <div class=\"issue-comments\">\n            <cv-text-area\n              v-model=\"selectedIssue.comment\"\n              label=\"Comments\"\n              placeholder=\"Add your comments here...\"\n            ></cv-text-area>\n          </div>\n        </div>\n      </template>\n      <template slot=\"footer\">\n        <cv-button\n          kind=\"secondary\"\n          @click=\"closeIssueModal\"\n        >\n          Cancel\n        </cv-button>\n        <cv-button\n          kind=\"primary\"\n          @click=\"updateIssue\"\n        >\n          Update\n        </cv-button>\n      </template>\n    </cv-modal>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvModal,\n  CvTag,\n  CvTextArea,\n  CvInlineLoading,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\n\n// Import Carbon Charts\nimport { ComboChart } from \"@carbon/charts\";\nimport \"@carbon/charts/styles.css\";\n\n// Import Root Cause Chart component\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart.vue';\n\nexport default {\n  name: 'PQEDashboard',\n  components: {\n    CvButton,\n    CvModal,\n    CvTag,\n    CvTextArea,\n    CvInlineLoading,\n    CvDropdown,\n    CvDropdownItem,\n    RootCauseChart\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Advanced Analysis\n      selectedAnalysisType: 'Root Cause', // Default analysis type\n      selectedBreakoutGroup: null, // Will be set when a breakout group is selected\n\n      // Chart instances\n      categorySummaryChart: null,\n      validationChart: null,\n\n      // Current Month\n      currentMonth: '', // Will be set to current month in YYYY-MM format\n\n      // Loading States\n      isLoadingBreakoutGroups: false,\n      isLoadingTrackedItems: false,\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n      validationChartData: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n      validationChartOptions: {\n        title: 'Daily Status',\n        axes: {\n          left: {\n            title: 'Count',\n            mapsTo: 'value',\n            domain: [0, 150], // Increased range to accommodate volume data\n            scaleType: 'linear'\n          },\n          right: {\n            title: 'Yield (%)',\n            mapsTo: 'value',\n            domain: [0, 100],\n            scaleType: 'linear',\n            correspondingDatasets: [\n              'Daily Yield',\n              'Cumulative Yield',\n              'Target Yield'\n            ]\n          },\n          bottom: {\n            title: 'Day',\n            mapsTo: 'date',\n            scaleType: 'time'\n          }\n        },\n        height: '350px',\n        legend: {\n          alignment: 'center',\n          enabled: true,\n          fontColor: '#f4f4f4' // White text for legend\n        },\n        color: {\n          scale: {\n            'Validated': '#0062ff', // Blue\n            'Unvalidated': '#ff832b', // Orange\n            'Volume': '#24a148', // Green\n            'Daily Yield': '#6929c4', // Purple\n            'Cumulative Yield': '#1192e8', // Light blue\n            'Target Yield': '#fa4d56' // Red\n          }\n        },\n        toolbar: {\n          enabled: false\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100', // Use darkest theme for better contrast\n        grid: {\n          x: {\n            enabled: false\n          },\n          y: {\n            enabled: true\n          }\n        },\n        comboChartTypes: [\n          {\n            type: 'stacked-bar',\n            correspondingDatasets: ['Validated', 'Unvalidated'],\n            options: {\n              fillOpacity: 0.8\n            }\n          },\n          {\n            type: 'line',\n            correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],\n            options: {\n              points: {\n                radius: 3\n              },\n              strokeWidth: 2\n            }\n          }\n        ],\n        thresholds: [\n          {\n            axis: 'right',\n            value: 95, // Target yield threshold\n            label: 'Target Yield (95%)',\n            fillColor: '#fa4d56',\n            opacity: 0.1\n          }\n        ]\n      },\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n      showAllBreakoutGroups: false, // Toggle for showing all breakout groups\n      showAllAlerts: false, // Toggle for showing all alerts (including medium priority)\n\n      // Item Tracking Data\n      trackedItems: [],\n\n      // Modal Data\n      showIssueModal: false,\n      selectedIssue: null,\n      issueChartData: [],\n      issueChartOptions: {\n        title: 'Issue Details',\n        axes: {\n          left: {\n            title: 'X-Factor',\n            mapsTo: 'value',\n            domain: [0, 4]\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'month',\n            scaleType: 'labels'\n          }\n        },\n        height: '300px',\n        legend: {\n          enabled: false\n        },\n        color: {\n          scale: {\n            'X-Factor': '#0062ff'\n          }\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (data) => {\n            return `\n              <div style=\"padding: 10px; background: #f4f4f4; border-radius: 4px;\">\n                <div style=\"font-weight: bold; margin-bottom: 5px;\">${data[0].data.month}</div>\n                <div>X-Factor: ${data[0].value.toFixed(2)}</div>\n              </div>\n            `;\n          }\n        },\n        animations: true,\n        thresholds: [\n          {\n            value: 1.5,\n            label: 'Sustained Problem Threshold',\n            fillColor: '#ff9a00',\n            opacity: 0.1\n          },\n          {\n            value: 3.0,\n            label: 'Critical Spike Threshold',\n            fillColor: '#fa4d56',\n            opacity: 0.1\n          }\n        ]\n      }\n    };\n  },\n  mounted() {\n    this.loadDashboardData();\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Sort breakout groups by xFactor (highest first) and filter critical ones\n    sortedCriticalBreakoutGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 1.5) // Only groups with xFactor >= 1.5 are considered critical\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get normal breakout groups (non-critical)\n    normalBreakoutGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor < 1.5) // Only groups with xFactor < 1.5\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get high priority groups (xFactor >= 3.0)\n    sortedHighPriorityGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 3.0) // Only groups with xFactor >= 3.0 (critical spike)\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get medium priority groups (1.5 <= xFactor < 3.0)\n    sortedMediumPriorityGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 1.5 && group.xFactor < 3.0) // Groups with 1.5 <= xFactor < 3.0 (sustained problem)\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadBreakoutGroups();\n      this.loadTrackedItems();\n      this.loadRootCauseData();\n    },\n\n    loadCriticalIssues() {\n      console.log('Loading critical issues data');\n\n      // Use hardcoded values to match the UI with categories\n      const criticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci3',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '2.5',\n          aiDescription: 'Fan Themis showing higher failure rates for units manufactured in March 2024. Vintage analysis indicates a 2.5x increase in failures for this manufacturing period.',\n          comment: '',\n          analysisType: 'Vintage'\n        },\n        {\n          id: 'ci4',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.6',\n          aiDescription: 'Victoria Crypto units from supplier ABC showing 1.6x higher failure rates compared to other suppliers. Quality audit recommended.',\n          comment: '',\n          analysisType: 'Supplier'\n        }\n      ];\n\n      // Set new critical issues to 0 to match the UI\n      this.newCriticalIssues = [];\n\n      // Set unresolved critical issues\n      this.unresolvedCriticalIssues = criticalIssues;\n\n      // Set critical issues section to collapsed by default\n      this.isCriticalIssuesExpanded = false;\n\n      console.log('Critical issues set:', {\n        new: this.newCriticalIssues,\n        unresolved: this.unresolvedCriticalIssues\n      });\n    },\n\n\n\n    loadValidationCounts() {\n      console.log('Loading validation counts and chart data');\n\n      // Use hardcoded values to match the UI\n      this.validatedCount = 125;\n      this.unvalidatedCount = 37;\n\n      // Generate mock data for the validation chart\n      this.generateValidationChartData();\n\n      console.log('Validation counts set:', {\n        validatedCount: this.validatedCount,\n        unvalidatedCount: this.unvalidatedCount\n      });\n\n      // Initialize the validation chart after data is loaded\n      this.$nextTick(() => {\n        this.initValidationChart();\n      });\n    },\n\n    generateValidationChartData() {\n      // Generate mock data for the validation chart\n      const data = [];\n\n      // Generate data for the last 14 days\n      for (let i = 13; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n\n        // Format date for display in band scale\n        const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\n\n        // Generate random values for validated and unvalidated defects\n        const validated = Math.floor(Math.random() * 12) + 3; // 3-15 validated defects\n        const unvalidated = Math.floor(Math.random() * 8); // 0-8 unvalidated defects\n\n        // Calculate daily yield (assuming 100 units processed per day)\n        const unitsPerDay = 100;\n        const dailyYield = ((unitsPerDay - (validated + unvalidated)) / unitsPerDay) * 100;\n\n        // Generate volume data (units processed per day)\n        const volume = unitsPerDay + Math.floor(Math.random() * 50) - 25; // 75-125 units\n\n        // Create data points with all values\n        // Defect data for grouped bar chart\n        data.push({\n          date: dateStr,\n          group: 'Validated',\n          value: validated\n        });\n\n        data.push({\n          date: dateStr,\n          group: 'Unvalidated',\n          value: unvalidated\n        });\n\n        // Volume data for grouped bar chart\n        data.push({\n          date: dateStr,\n          group: 'Volume',\n          value: volume\n        });\n\n        // Yield data for line chart\n        data.push({\n          date: dateStr,\n          group: 'Daily Yield',\n          value: dailyYield\n        });\n\n        // Calculate cumulative yield (simplified for this example)\n        const cumulativeYield = 90 + (Math.random() * 8); // 90-98% range\n\n        data.push({\n          date: dateStr,\n          group: 'Cumulative Yield',\n          value: cumulativeYield\n        });\n\n        data.push({\n          date: dateStr,\n          group: 'Target Yield',\n          value: 95 // Target yield is 95%\n        });\n      }\n\n      // Set the validation chart data\n      this.validationChartData = data;\n      console.log('Validation chart data generated:', data);\n    },\n\n    initValidationChart() {\n      console.log('Initializing validation chart...');\n      try {\n        // Destroy existing chart if it exists\n        if (this.validationChart) {\n          console.log('Destroying existing chart');\n          this.validationChart.destroy();\n        }\n\n        // Get the chart container element\n        const chartContainer = document.getElementById('validationChart');\n        console.log('Chart container:', chartContainer);\n        if (!chartContainer) {\n          console.error('Validation chart container not found');\n          return;\n        }\n\n        // Log validation chart data\n        console.log('Validation chart data length:', this.validationChartData.length);\n        console.log('Sample data:', this.validationChartData.slice(0, 5));\n\n        // Create a simplified chart configuration\n        const chartOptions = {\n          title: 'Daily Status',\n          axes: {\n            left: {\n              title: 'Count',\n              mapsTo: 'value',\n              domain: [0, 150],\n              scaleType: 'linear'\n            },\n            right: {\n              title: 'Yield (%)',\n              mapsTo: 'value',\n              domain: [0, 100],\n              scaleType: 'linear',\n              correspondingDatasets: [\n                'Daily Yield',\n                'Cumulative Yield',\n                'Target Yield'\n              ]\n            },\n            bottom: {\n              title: 'Day',\n              mapsTo: 'date',\n              scaleType: 'band'\n            }\n          },\n          height: '350px',\n          width: '100%',\n          legend: {\n            alignment: 'center',\n            enabled: true,\n            fontColor: '#f4f4f4'\n          },\n          color: {\n            scale: {\n              'Validated': '#0062ff', // Blue\n              'Unvalidated': '#ff832b', // Orange\n              'Volume': '#24a148', // Green\n              'Daily Yield': '#6929c4', // Purple\n              'Cumulative Yield': '#1192e8', // Light blue\n              'Target Yield': '#fa4d56' // Red\n            }\n          },\n          toolbar: {\n            enabled: false\n          },\n          data: {\n            groupMapsTo: 'group'\n          },\n          theme: 'g100',\n          grid: {\n            x: {\n              enabled: false\n            },\n            y: {\n              enabled: true\n            }\n          },\n          comboChartTypes: [\n            {\n              type: 'grouped-bar',\n              correspondingDatasets: ['Validated', 'Unvalidated', 'Volume']\n            },\n            {\n              type: 'line',\n              correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],\n              options: {\n                points: {\n                  radius: 0\n                },\n                strokeWidth: 2\n              }\n            }\n          ],\n          thresholds: [\n            {\n              axis: 'right',\n              value: 95,\n              label: 'Target Yield (95%)',\n              fillColor: '#fa4d56',\n              opacity: 0.1\n            }\n          ],\n          tooltip: {\n            enabled: true,\n            customHTML: (data) => {\n              let html = `<div style=\"padding: 10px; background: #262626; color: #f4f4f4; border-radius: 4px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\">\n                <div style=\"font-weight: bold; margin-bottom: 5px;\">${data[0].date}</div>`;\n\n              data.forEach(item => {\n                let displayValue;\n                const group = item.group;\n\n                if (group.includes('Yield')) {\n                  // Format yield values as percentages\n                  displayValue = `${item.value.toFixed(1)}%`;\n                } else if (group === 'Volume') {\n                  // Format volume as integer with 'units' label\n                  displayValue = `${Math.round(item.value)} units`;\n                } else {\n                  // Format defect counts as integers\n                  displayValue = Math.round(item.value);\n                }\n\n                html += `<div style=\"margin: 4px 0;\">\n                  <span style=\"display: inline-block; width: 12px; height: 12px; background-color: ${chartOptions.color.scale[group]}; margin-right: 6px; border-radius: 50%;\"></span>\n                  <span style=\"font-weight: 500;\">${group}:</span> ${displayValue}\n                </div>`;\n              });\n\n              html += `</div>`;\n              return html;\n            }\n          }\n        };\n\n        // Create the combo chart with the simplified configuration\n        this.validationChart = new ComboChart(\n          chartContainer,\n          {\n            data: this.validationChartData,\n            options: chartOptions\n          }\n        );\n\n        console.log('Validation chart initialized with ComboChart');\n      } catch (error) {\n        console.error('Error initializing validation chart:', error);\n\n        // Create a fallback display if the chart fails\n        const chartContainer = document.getElementById('validationChart');\n        if (chartContainer) {\n          console.log('Creating fallback display for validation chart');\n          chartContainer.innerHTML = `\n            <div style=\"height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #161616; border-radius: 8px; padding: 16px;\">\n              <div style=\"color: #f4f4f4; margin-bottom: 16px; font-weight: 600; font-size: 1rem;\">Daily Status</div>\n              <div style=\"color: #c6c6c6; margin-bottom: 16px; font-size: 0.875rem;\">Chart could not be displayed. Showing summary data instead.</div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #0062ff; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Validated: <strong>${this.validatedCount}</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #ff832b; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Unvalidated: <strong>${this.unvalidatedCount}</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #24a148; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Volume: <strong>100 units</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #6929c4; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Current Yield: <strong>94.2%</strong></span>\n              </div>\n            </div>\n          `;\n        }\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log('Loading breakout groups from API for current month');\n      try {\n        this.isLoadingBreakoutGroups = true;\n\n        // Get the current month in YYYY-MM format\n        const now = new Date();\n        const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n        this.currentMonth = currentMonth;\n\n        console.log(`Current month: ${this.currentMonth}`);\n\n        // Make API call to get breakout groups data\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            month: currentMonth // Pass the current month to get specific data\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success' && data.breakout_groups) {\n          // Add current month flag to each breakout group\n          this.breakoutGroups = data.breakout_groups.map(group => ({\n            ...group,\n            // If sustainedXFactor is not provided, use a default value\n            sustainedXFactor: group.sustainedXFactor || (group.status === 'Sustained Problem' ? group.xFactor * 0.9 : group.xFactor * 0.5),\n            isCurrentMonth: true, // All groups from the API are for the current month\n            inHeatmap: true // Indicate that these groups are also in the heatmap\n          }));\n\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${currentMonth}`);\n        } else {\n          // Fallback to sample data if API fails\n          console.warn('No breakout groups found in API response, using sample data');\n          this.breakoutGroups = [\n            {\n              name: 'Fan Themis',\n              status: 'Short-Term Spike',\n              xFactor: 3.2,\n              sustainedXFactor: 1.2,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Victoria Crypto',\n              status: 'Sustained Problem',\n              xFactor: 1.8,\n              sustainedXFactor: 1.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Quantum Nexus',\n              status: 'Sustained Problem',\n              xFactor: 1.6,\n              sustainedXFactor: 1.6,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Orion Base',\n              status: 'Normal',\n              xFactor: 0.9,\n              sustainedXFactor: 0.8,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Stellar Core',\n              status: 'Normal',\n              xFactor: 0.8,\n              sustainedXFactor: 0.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Nebula Drive',\n              status: 'Normal',\n              xFactor: 0.7,\n              sustainedXFactor: 0.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Pulsar Matrix',\n              status: 'Normal',\n              xFactor: 0.6,\n              sustainedXFactor: 0.6,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Quasar Link',\n              status: 'Normal',\n              xFactor: 0.5,\n              sustainedXFactor: 0.5,\n              isCurrentMonth: true,\n              inHeatmap: true\n            }\n          ];\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Fallback to empty array\n        this.breakoutGroups = [];\n      } finally {\n        this.isLoadingBreakoutGroups = false;\n      }\n\n      console.log('Breakout groups set:', this.breakoutGroups);\n    },\n\n    getAiDescriptionForIssue(issue) {\n      // This would call WatsonX.ai to get an AI description for the issue\n      // For now, we'll use the existing description\n      console.log(`Getting AI description for issue: ${issue.id}`);\n    },\n\n    getStatusClass(status) {\n      switch (status) {\n        case 'Short-Term Spike':\n          return 'status-spike';\n        case 'Sustained Problem':\n          return 'status-sustained';\n        case 'Critical (Both)':\n          return 'status-critical';\n        default:\n          return 'status-normal';\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log(`Viewing details for issue: ${issue.id}`);\n      this.selectedIssue = { ...issue };\n      this.loadIssueChartData(issue);\n      this.showIssueModal = true;\n    },\n\n    loadIssueChartData(issue) {\n      console.log(`Loading chart data for issue: ${issue.id}`);\n\n      // For now, we'll create some sample data\n      // In a real implementation, this would come from the API\n      const months = [];\n      const values = [];\n\n      // Get the issue month\n      const issueDate = new Date(issue.month + '-01');\n\n      // Create data for 6 months before and after the issue month\n      for (let i = -6; i <= 6; i++) {\n        const date = new Date(issueDate);\n        date.setMonth(date.getMonth() + i);\n        const monthStr = date.toISOString().slice(0, 7); // YYYY-MM format\n\n        // Base value that increases at the issue month\n        let value = 0.5;\n        if (i < 0) {\n          value = 0.5 + (i * 0.05); // Slight variation before issue\n        } else if (i === 0) {\n          // The spike at the issue month\n          value = issue.increaseMultiplier === '(new)' ? 1.0 : parseFloat(issue.increaseMultiplier);\n        } else {\n          // Gradual decrease after the spike\n          value = parseFloat(issue.increaseMultiplier) * Math.pow(0.8, i);\n        }\n\n        months.push(monthStr);\n        values.push({\n          month: monthStr,\n          value: value\n        });\n      }\n\n      // Set the chart data\n      this.issueChartData = values;\n\n      // Update chart options\n      this.issueChartOptions = {\n        title: `${issue.category} - ${issue.month}`,\n        axes: {\n          left: {\n            title: 'X-Factor',\n            mapsTo: 'value',\n            domain: [0, Math.max(...values.map(v => v.value)) * 1.2]\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'month',\n            scaleType: 'labels'\n          }\n        },\n        height: '300px',\n        color: {\n          scale: {\n            'X-Factor': '#0062ff'\n          }\n        }\n      };\n    },\n\n    closeIssueModal() {\n      console.log('Closing issue modal');\n      this.showIssueModal = false;\n      this.selectedIssue = null;\n    },\n\n    updateIssue() {\n      console.log(`Updating issue: ${this.selectedIssue.id}`);\n\n      // In a real implementation, this would call an API to update the issue\n      // For now, we'll just update the local data\n      const index = this.unresolvedCriticalIssues.findIndex(issue => issue.id === this.selectedIssue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues[index].comment = this.selectedIssue.comment;\n        console.log(`Updated issue ${this.selectedIssue.id} with comment: ${this.selectedIssue.comment}`);\n      }\n\n      this.closeIssueModal();\n    },\n\n    goToValidationPage() {\n      console.log('Navigating to validations page');\n      this.$router.push('/validations');\n    },\n\n    goToGroupAnalysis() {\n      console.log(`Navigating to Group tab for ${this.selectedAnalysisType} analysis`);\n\n      // Find the parent MetisXFactors component\n      let parent = this.$parent;\n      while (parent && parent.$options.name !== 'MetisXFactors') {\n        parent = parent.$parent;\n      }\n\n      if (parent) {\n        console.log('Found MetisXFactors parent component, navigating to Group tab');\n\n        // If a breakout group is selected, use it\n        if (this.selectedBreakoutGroup) {\n          // Navigate to the Group tab with the selected breakout group\n          parent.selectBreakoutFromDashboard(this.selectedBreakoutGroup);\n\n          // After navigation, trigger the appropriate analysis based on the selected type\n          this.$nextTick(() => {\n            if (this.selectedAnalysisType === 'Root Cause') {\n              parent.viewRootCauseAnalysis(3); // Show 3 months of data\n            }\n          });\n        } else {\n          // If no breakout group is selected, use the first one from the breakout groups\n          if (this.breakoutGroups.length > 0) {\n            this.selectedBreakoutGroup = this.breakoutGroups[0].name;\n            this.goToGroupAnalysis(); // Call this method again with the selected breakout group\n          } else {\n            console.error('No breakout groups available');\n          }\n        }\n      } else {\n        console.error('Could not find MetisXFactors parent component');\n      }\n    },\n\n    viewBreakoutDetails(group, targetTab = 'group') {\n      console.log(`Viewing details for breakout group: ${group.name} in ${targetTab} tab`);\n\n      // Set the selected breakout group\n      this.selectedBreakoutGroup = group.name;\n\n      // Find the parent MetisXFactors component\n      let parent = this.$parent;\n      while (parent && parent.$options.name !== 'MetisXFactors') {\n        parent = parent.$parent;\n      }\n\n      if (parent) {\n        if (targetTab === 'heatmap') {\n          console.log('Found MetisXFactors parent component, navigating to Heatmap tab');\n          // Set the active tab to Heatmap (index 0) and then select the breakout group\n          parent.activeTab = 0; // Heatmap tab index\n          parent.selectBreakoutFromDashboard(group.name);\n\n          // If there's a specific method to highlight this group in the heatmap, call it\n          if (parent.highlightGroupInHeatmap) {\n            parent.$nextTick(() => {\n              parent.highlightGroupInHeatmap(group.name);\n            });\n          }\n        } else {\n          console.log('Found MetisXFactors parent component, navigating to Group tab');\n          // Default behavior - navigate to Group tab\n          parent.selectBreakoutFromDashboard(group.name);\n        }\n      } else {\n        console.error('Could not find MetisXFactors parent component');\n      }\n    },\n\n    // Navigate to heatmap tab and highlight this group\n    viewBreakoutInHeatmap(group) {\n      this.viewBreakoutDetails(group, 'heatmap');\n    },\n\n    formatMonth(dateStr) {\n      // Format YYYY-MM to MMM format (e.g., 2024-06 to Jun)\n      const date = new Date(dateStr + '-01'); // Add day to make a valid date\n      return date.toLocaleString('en-US', { month: 'short' });\n    },\n\n    // Toggle critical issues section expanded/collapsed\n    toggleCriticalIssuesExpanded() {\n      console.log('Toggling critical issues expanded state');\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    // Handle severity filter dropdown change\n    handleSeverityFilterChange() {\n      console.log(`Severity filter changed to: ${this.severityFilter}`);\n\n      if (this.severityFilter === 'all') {\n        // Clear severity filters\n        this.selectedFilters.severity = [];\n      } else {\n        // Set the selected severity\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    // Handle analysis type filter dropdown change\n    handleAnalysisFilterChange() {\n      console.log(`Analysis filter changed to: ${this.analysisTypeFilter}`);\n\n      if (this.analysisTypeFilter === 'all') {\n        // Clear analysis type filters\n        this.selectedFilters.analysisType = [];\n      } else {\n        // Set the selected analysis type\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    // Clear all filters\n    clearFilters() {\n      console.log('Clearing all filters');\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    initCategorySummaryChart() {\n      try {\n        // Destroy existing chart if it exists\n        if (this.categorySummaryChart) {\n          this.categorySummaryChart.destroy();\n        }\n\n        // Get the chart container element\n        const chartContainer = document.getElementById('categorySummaryChart');\n        if (!chartContainer) {\n          console.error('Category summary chart container not found');\n          return;\n        }\n\n        // Create mock data for the selected category\n        const chartData = this.generateCategorySummaryData();\n\n        // Create chart options\n        const options = {\n          title: `${this.selectedCategory} Analysis Summary`,\n          axes: {\n            left: {\n              title: 'X-Factor',\n              mapsTo: 'value',\n              domain: [0, 4]\n            },\n            bottom: {\n              title: 'Month',\n              mapsTo: 'date',\n              scaleType: 'labels'\n            }\n          },\n          height: '300px',\n          width: '100%',\n          legend: {\n            alignment: 'center',\n            enabled: true\n          },\n          color: {\n            scale: {\n              'X-Factor': '#0062ff',\n              'Threshold': '#ff9a00'\n            }\n          },\n          toolbar: {\n            enabled: false\n          },\n          data: {\n            groupMapsTo: 'group'\n          },\n          theme: 'g90', // Use dark theme to match the UI\n          thresholds: [\n            {\n              value: 1.5,\n              label: 'Sustained Problem Threshold',\n              fillColor: '#ff9a00',\n              opacity: 0.1\n            },\n            {\n              value: 3.0,\n              label: 'Critical Spike Threshold',\n              fillColor: '#fa4d56',\n              opacity: 0.1\n            }\n          ]\n        };\n\n        // Create the chart\n        this.categorySummaryChart = new ComboChart(chartContainer, {\n          data: chartData,\n          options: {\n            ...options,\n            comboChartTypes: [\n              {\n                type: 'line',\n                correspondingDatasets: ['X-Factor', 'Threshold'],\n                options: {\n                  points: {\n                    radius: 3\n                  },\n                  strokeWidth: 2\n                }\n              }\n            ]\n          }\n        });\n\n        console.log('Category summary chart initialized');\n      } catch (error) {\n        console.error('Error initializing category summary chart:', error);\n\n        // Create a simple fallback display for the data\n        const chartContainer = document.getElementById('categorySummaryChart');\n        if (chartContainer) {\n          chartContainer.innerHTML = `\n            <div style=\"height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;\">\n              <div style=\"color: #f4f4f4; margin-bottom: 16px;\">${this.selectedCategory} Analysis Summary</div>\n              <div style=\"color: #0062ff;\">No chart data available</div>\n            </div>\n          `;\n        }\n      }\n    },\n\n    generateCategorySummaryData() {\n      // Generate mock data for the category summary chart\n      const data = [];\n      const currentDate = new Date();\n\n      // Generate data for the last 6 months\n      for (let i = 5; i >= 0; i--) {\n        const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\n        const monthStr = this.formatMonth(monthDate.toISOString().slice(0, 7));\n\n        // Add X-Factor data point\n        const xFactor = i === 0 ?\n          (this.selectedCategory === 'Root Cause' ? 3.2 :\n           this.selectedCategory === 'Vintage' ? 2.5 :\n           this.selectedCategory === 'Supplier' ? 1.6 : 1.0) :\n          Math.random() * 1.5 + 0.5; // Random between 0.5-2.0 for previous months\n\n        data.push({\n          date: monthStr,\n          group: 'X-Factor',\n          value: xFactor\n        });\n\n        // Add threshold reference line\n        data.push({\n          date: monthStr,\n          group: 'Threshold',\n          value: 1.5\n        });\n      }\n\n      return data;\n    },\n\n    // These methods are no longer needed with the new filtering approach\n    // but we'll keep them for backward compatibility\n    getCriticalCountByCategory(category) {\n      return this.unresolvedCriticalIssues.filter(issue => issue.analysisType === category).length;\n    },\n\n    toggleIssueExpanded(issue) {\n      const index = this.expandedIssueIds.indexOf(issue.id);\n      if (index === -1) {\n        // Add to expanded list\n        this.expandedIssueIds.push(issue.id);\n      } else {\n        // Remove from expanded list\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    getXFactorSeverityClass(xFactor) {\n      if (xFactor >= 3.0) {\n        return 'severity-critical';\n      } else if (xFactor >= 1.5) {\n        return 'severity-warning';\n      } else if (xFactor >= 1.0) {\n        return 'severity-caution';\n      } else {\n        return 'severity-normal';\n      }\n    },\n\n    // Item tracking methods\n    async loadTrackedItems() {\n      console.log('Loading tracked items from Action Tracker API');\n      this.isLoadingTrackedItems = true;\n\n      try {\n        // Get the critical breakout groups from the current month\n        const criticalBreakoutGroups = this.sortedCriticalBreakoutGroups.map(group => group.name);\n        console.log('Critical breakout groups for filtering action items:', criticalBreakoutGroups);\n\n        // Make API call to get action tracker data\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            // Filter by breakout groups with issues in the current month\n            breakoutNames: criticalBreakoutGroups\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success' && data.items && data.items.length > 0) {\n          // Format the action tracker items for display\n          this.trackedItems = data.items.map(item => ({\n            id: item.id,\n            name: item.action && item.action.length > 50 ? item.action.substring(0, 50) + '...' : item.action || `Action for ${item.group}`,\n            status: item.status || 'In Progress',\n            owner: item.assignee || 'Unassigned',\n            dueDate: item.deadline || 'No deadline',\n            relatedIssue: item.group || 'Unknown',\n            description: item.notes || 'No description available',\n            progress: item.progress || 0,\n            // Add a flag to indicate if this item is related to a critical breakout group\n            isCritical: criticalBreakoutGroups.includes(item.group)\n          }));\n\n          console.log(`Loaded ${this.trackedItems.length} tracked items from API`);\n        } else {\n          console.warn('No action tracker items found in API response or API call failed, using sample data');\n\n          // Sample data as fallback\n          const allActionItems = [\n            {\n              id: 'item1',\n              name: 'Fan Themis Airflow Investigation',\n              status: 'In Progress',\n              owner: 'John Smith',\n              dueDate: '2024-07-15',\n              relatedIssue: 'Fan Themis',\n              description: 'Investigation into airflow issues in the new Fan Themis design that caused a 3.2x spike in failures.',\n              progress: 65,\n              isCritical: criticalBreakoutGroups.includes('Fan Themis')\n            },\n            {\n              id: 'item2',\n              name: 'Victoria Crypto Power Delivery Fix',\n              status: 'Pending Review',\n              owner: 'Jane Doe',\n              dueDate: '2024-07-10',\n              relatedIssue: 'Victoria Crypto',\n              description: 'Implementation of power delivery improvements to address the sustained problem in Victoria Crypto units.',\n              progress: 85,\n              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')\n            },\n            {\n              id: 'item3',\n              name: 'Supplier ABC Quality Audit',\n              status: 'Completed',\n              owner: 'Robert Johnson',\n              dueDate: '2024-06-30',\n              relatedIssue: 'Victoria Crypto',\n              description: 'Quality audit of Supplier ABC to address higher failure rates in Victoria Crypto units.',\n              progress: 100,\n              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')\n            },\n            {\n              id: 'item4',\n              name: 'Quantum Nexus Thermal Analysis',\n              status: 'In Progress',\n              owner: 'Emily Chen',\n              dueDate: '2024-07-20',\n              relatedIssue: 'Quantum Nexus',\n              description: 'Thermal analysis of Quantum Nexus components to address overheating issues.',\n              progress: 40,\n              isCritical: criticalBreakoutGroups.includes('Quantum Nexus')\n            },\n            {\n              id: 'item5',\n              name: 'Nebula Drive Firmware Update',\n              status: 'Blocked',\n              owner: 'Michael Brown',\n              dueDate: '2024-07-05',\n              relatedIssue: 'Nebula Drive',\n              description: 'Firmware update to address performance issues in Nebula Drive units.',\n              progress: 25,\n              isCritical: criticalBreakoutGroups.includes('Nebula Drive')\n            }\n          ];\n\n          // Filter items to only include those related to breakout groups with issues in the current month\n          this.trackedItems = allActionItems.filter(item =>\n            criticalBreakoutGroups.some(groupName =>\n              item.relatedIssue.includes(groupName)\n            )\n          );\n        }\n\n        // Sort items by status priority and then by whether they're critical\n        this.trackedItems.sort((a, b) => {\n          const statusPriority = {\n            'blocked': 0,\n            'in progress': 1,\n            'pending review': 2,\n            'completed': 3\n          };\n\n          // First sort by status priority\n          const statusDiff = statusPriority[a.status.toLowerCase()] - statusPriority[b.status.toLowerCase()];\n\n          // If status is the same, sort by critical flag (critical items first)\n          if (statusDiff === 0) {\n            return b.isCritical - a.isCritical;\n          }\n\n          return statusDiff;\n        });\n\n        console.log('Tracked items loaded and sorted:', this.trackedItems);\n      } catch (error) {\n        console.error('Error loading tracked items:', error);\n        this.trackedItems = [];\n      } finally {\n        this.isLoadingTrackedItems = false;\n      }\n    },\n\n    getItemStatusClass(status) {\n      switch (status.toLowerCase()) {\n        case 'in progress':\n          return 'status-in-progress';\n        case 'pending review':\n          return 'status-pending';\n        case 'completed':\n          return 'status-completed';\n        case 'blocked':\n          return 'status-blocked';\n        default:\n          return 'status-default';\n      }\n    },\n\n    // Check if a group is in the critical breakout groups list\n    isCriticalGroup(groupName) {\n      return this.sortedCriticalBreakoutGroups.some(group => group.name === groupName);\n    },\n\n    viewItemDetails(item) {\n      console.log('Viewing item details:', item);\n      // Navigate to the Action Tracker page with the specific item ID\n      this.$router.push(`/action-tracker/items/${item.id}`);\n    },\n\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker');\n      // Navigate to the Action Tracker page using Vue Router\n      this.$router.push('/action-tracker');\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Generate mock data for the root cause chart\n        // In a real implementation, this would call an API\n        const mockData = this.generateMockRootCauseData();\n        this.rootCauseChartData = mockData;\n\n        console.log('Root cause chart data loaded:', this.rootCauseChartData);\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        this.rootCauseChartData = [];\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    generateMockRootCauseData() {\n      // Generate mock data similar to what MetisXFactors uses\n      const data = [];\n      const categories = ['Electrical', 'Mechanical', 'Thermal', 'Material', 'Process'];\n      const months = ['May 2024', 'Jun 2024', 'Jul 2024'];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate random fail rates between 0.5% and 4%\n          const baseRate = Math.random() * 3.5 + 0.5;\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(baseRate.toFixed(2)),\n            data: {\n              defects: Math.floor(Math.random() * 20) + 5,\n              volume: Math.floor(Math.random() * 1000) + 500,\n              month: month,\n              category: category,\n              isCritical: baseRate > 2.5,\n              formattedValue: `${baseRate.toFixed(2)}%`\n            }\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.pqe-dashboard-container {\n  width: 100%;\n  color: #f4f4f4; /* Light text for dark mode */\n}\n\n.content-wrapper {\n  padding: 16px;\n  margin-bottom: 16px;\n  background-color: #161616; /* Dark background */\n  border-radius: 4px;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 16px;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray text */\n}\n\n/* Key Metrics Section */\n.key-metrics-section {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 32px;\n}\n\n.metric-card {\n  flex: 1;\n  min-width: 200px;\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  margin-right: 16px;\n  color: #ffffff;\n\n  &.new-issues {\n    background-color: #8a3ffc; /* Purple */\n  }\n\n  &.critical-issues {\n    background-color: #fa4d56; /* Red */\n  }\n\n  &.validated {\n    background-color: #0f62fe; /* Blue */\n  }\n\n  &.unvalidated {\n    background-color: #ff832b; /* Orange */\n  }\n}\n\n.metric-content {\n  flex: 1;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray */\n  margin-bottom: 4px;\n}\n\n.metric-value {\n  font-size: 2.25rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n  line-height: 1.2;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n  margin-top: 4px;\n}\n\n/* Main Content Layout */\n.dashboard-main-content {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24px;\n}\n\n.dashboard-column {\n  flex: 1;\n  min-width: 300px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.section-card {\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  border: 1px solid #333333; /* Dark border */\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 12px;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray */\n  margin-top: 4px;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.section-content {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.section-footer {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.section-description {\n  color: #c6c6c6; /* Light gray */\n  line-height: 1.5;\n  margin-bottom: 16px;\n}\n\n.action-button {\n  align-self: flex-start;\n}\n\n/* Critical Issues Section */\n.critical-issues-header {\n  cursor: pointer;\n  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;\n\n  &:hover {\n    background-color: #353535; /* Darker gray */\n  }\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 32px;\n  height: 32px;\n  padding: 0 12px;\n  border-radius: 16px;\n  background-color: #fa4d56; /* Red */\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #ffffff; /* White text */\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n\n  &.flashing {\n    animation: flash 1.5s infinite;\n  }\n}\n\n@keyframes flash {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.8;\n    transform: scale(1.1);\n  }\n}\n\n.expand-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #393939; /* Dark gray */\n  color: #c6c6c6; /* Light gray */\n  transition: transform 0.3s ease, background-color 0.3s ease;\n\n  &.expanded {\n    transform: rotate(180deg);\n    background-color: #0f62fe; /* Blue */\n    color: #ffffff; /* White */\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Filter Controls */\n.filter-container {\n  margin-bottom: 24px;\n  padding: 16px;\n  background-color: #161616; /* Dark background */\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 12px;\n}\n\n.filter-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n  margin: 0;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.filter-label {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n  margin-right: 8px;\n}\n\n.filter-dropdown {\n  width: 100%;\n}\n\n.clear-filters-button {\n  margin-left: auto;\n  background-color: #393939; /* Dark gray */\n  border: none;\n  transition: all 0.2s ease;\n  color: #f4f4f4; /* White text */\n\n  &:hover {\n    background-color: #4d4d4d; /* Darker gray */\n    color: #ffffff; /* White */\n  }\n}\n\n/* Issues List */\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.no-data-message {\n  margin-top: 16px;\n  padding: 24px;\n  border-radius: 8px;\n  text-align: center;\n  color: #c6c6c6; /* Light gray */\n  background-color: #161616; /* Dark background */\n  font-size: 1rem;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n\n  &::before {\n    content: '🔍';\n    display: block;\n    font-size: 2rem;\n    margin-bottom: 16px;\n  }\n}\n\n.issue-card {\n  padding: 0;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: #161616; /* Dark background */\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);\n    border-color: #4d4d4d; /* Darker border on hover */\n  }\n\n  &.expanded {\n    .expand-indicator {\n      transform: rotate(180deg);\n      background-color: #0f62fe; /* Blue */\n      color: #ffffff; /* White */\n    }\n\n    .issue-header {\n      border-bottom: 1px solid #333333; /* Dark border */\n    }\n  }\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n  padding: 16px;\n  position: relative;\n  background-color: #262626; /* Dark gray */\n  transition: background-color 0.2s ease;\n\n  &:hover {\n    background-color: #353535; /* Darker gray */\n  }\n}\n\n.issue-tags {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.issue-metadata {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  margin-left: auto;\n}\n\n.issue-title {\n  font-weight: 600;\n  margin: 0 16px;\n  color: #f4f4f4; /* White text */\n  flex: 1;\n  font-size: 1rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.issue-multiplier {\n  font-weight: 700;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.875rem;\n\n  &.high-severity {\n    color: #fa4d56; /* Red */\n    background-color: rgba(250, 77, 86, 0.1); /* Transparent red */\n  }\n\n  &.medium-severity {\n    color: #ff832b; /* Orange */\n    background-color: rgba(255, 131, 43, 0.1); /* Transparent orange */\n  }\n}\n\n.analysis-tag {\n  font-size: 0.75rem;\n}\n\n.issue-content {\n  padding: 16px;\n  background-color: #161616; /* Dark background */\n  animation: slideDown 0.3s ease-out;\n  display: flex;\n  flex-direction: column;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    max-height: 500px;\n  }\n}\n\n.ai-description {\n  background-color: #262626; /* Dark gray */\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  position: relative;\n\n  &::before {\n    content: 'AI Analysis';\n    position: absolute;\n    top: -10px;\n    left: 10px;\n    background-color: #6929c4; /* Purple */\n    color: #ffffff; /* White text */\n    padding: 2px 8px;\n    border-radius: 10px;\n    font-size: 0.75rem;\n    font-weight: 600;\n  }\n\n  p {\n    margin: 16px 0 0 0;\n    line-height: 1.5;\n    color: #f4f4f4; /* White text */\n  }\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n/* Breakout Groups Grid */\n.breakout-groups-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.breakout-groups-grid.normal-groups {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-card {\n  padding: 16px;\n  border-radius: 4px;\n  border-left: 4px solid #393939; /* Dark gray */\n  background-color: #262626; /* Dark gray background */\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n\n  &.highlighted {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);\n    position: relative;\n\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      width: 10px;\n      height: 10px;\n      border-radius: 50%;\n      background-color: #fa4d56; /* Red */\n    }\n  }\n\n  &.severity-normal {\n    border-left-color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    border-left-color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    border-left-color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    border-left-color: #fa4d56; /* Red */\n  }\n}\n\n.show-more-footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 8px;\n}\n\n.show-more-button {\n  background-color: transparent;\n  border: 1px dashed #333333; /* Dark border */\n  color: #0f62fe; /* Blue */\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */\n    border-color: #0f62fe; /* Blue */\n  }\n}\n\n.toggle-button {\n  background-color: transparent;\n  color: #0f62fe; /* Blue */\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */\n  }\n}\n\n/* Item Tracking Styles */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  margin: 16px 0;\n}\n\n.tracked-items-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 16px;\n  margin-top: 16px;\n}\n\n.tracked-item-card {\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  border-left: 4px solid #393939; /* Default border */\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n\n  &.status-in-progress {\n    border-left-color: #0f62fe; /* Blue */\n  }\n\n  &.status-pending {\n    border-left-color: #ff832b; /* Orange */\n  }\n\n  &.status-completed {\n    border-left-color: #24a148; /* Green */\n  }\n\n  &.status-blocked {\n    border-left-color: #fa4d56; /* Red */\n  }\n\n  &.critical-item {\n    border: 1px solid #fa4d56; /* Red border */\n    box-shadow: 0 0 0 1px #fa4d56;\n  }\n}\n\n.tracked-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.tracked-item-name {\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0;\n  flex: 1;\n}\n\n.tracked-item-status {\n  margin-left: 8px;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 16px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  background-color: #393939; /* Default background */\n\n  &.status-in-progress {\n    background-color: rgba(15, 98, 254, 0.2); /* Blue background */\n    color: #0f62fe; /* Blue text */\n  }\n\n  &.status-pending {\n    background-color: rgba(255, 131, 43, 0.2); /* Orange background */\n    color: #ff832b; /* Orange text */\n  }\n\n  &.status-completed {\n    background-color: rgba(36, 161, 72, 0.2); /* Green background */\n    color: #24a148; /* Green text */\n  }\n\n  &.status-blocked {\n    background-color: rgba(250, 77, 86, 0.2); /* Red background */\n    color: #fa4d56; /* Red text */\n  }\n}\n\n.tracked-item-details {\n  margin-bottom: 16px;\n}\n\n.detail-row {\n  display: flex;\n  margin-bottom: 8px;\n  font-size: 0.875rem;\n}\n\n.detail-label {\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n  width: 100px;\n}\n\n.detail-value {\n  color: #f4f4f4; /* White */\n\n  &.related-group {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n\n    &.critical-group {\n      color: #fa4d56; /* Red */\n      font-weight: 600;\n    }\n\n    .month-tag {\n      margin-left: 4px;\n    }\n  }\n}\n\n.progress-container {\n  flex: 1;\n  height: 8px;\n  background-color: #393939;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-top: 4px;\n}\n\n.progress-bar {\n  height: 100%;\n  background-color: #0f62fe; /* Blue */\n  border-radius: 4px;\n}\n\n.progress-text {\n  position: absolute;\n  right: 0;\n  top: -16px;\n  font-size: 0.75rem;\n  color: #c6c6c6;\n}\n\n.tracked-item-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.critical-tag {\n  margin-left: 8px;\n}\n\n.medium-priority-groups {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-name {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.breakout-group-xfactor {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.xfactor-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.xfactor-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.xfactor-label {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n  margin-right: 8px;\n}\n\n.xfactor-value {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n\n  &.severity-normal {\n    color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    color: #fa4d56; /* Red */\n  }\n}\n\n.breakout-group-metrics {\n  margin-bottom: 16px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n\n  &.severity-normal {\n    color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    color: #fa4d56; /* Red */\n  }\n}\n\n.status-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.status-label {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n}\n\n.status-value {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.breakout-group-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 16px;\n  padding-top: 8px;\n  border-top: 1px solid #333333; /* Dark border */\n  justify-content: flex-end;\n\n  .action-button {\n    min-width: 120px;\n  }\n}\n\n.current-month-indicator {\n  margin-top: 8px;\n\n  .cv-tag {\n    margin-right: 0;\n  }\n}\n\n.breakout-group-card.current-month {\n  border: 1px solid #0f62fe;\n  box-shadow: 0 0 0 1px #0f62fe;\n}\n\n/* Chart Section */\n.chart-container {\n  height: 350px;\n  width: 100%;\n  margin: 16px 0;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333; /* Dark border */\n  background-color: #161616; /* Dark background */\n  position: relative;\n}\n\n.chart-wrapper {\n  position: relative;\n  height: 350px;\n  width: 100%;\n}\n\n.carbon-chart-container {\n  height: 100%;\n  width: 100%;\n}\n\n.no-data-message {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #c6c6c6; /* Light gray */\n}\n\n/* Carbon Charts styling */\n.carbon-chart-container .bx--chart-holder {\n  position: relative;\n}\n\n.carbon-chart-container .bx--axis.bx--axis--right {\n  display: block !important;\n}\n\n.carbon-chart-container .bx--line {\n  stroke-width: 2px !important;\n}\n\n.carbon-chart-container .bx--axis-title {\n  font-weight: 600;\n  fill: #f4f4f4; /* White text */\n}\n\n.carbon-chart-container .bx--axis-label {\n  fill: #f4f4f4; /* White text */\n}\n\n/* Ensure the lines are properly displayed */\n.carbon-chart-container .bx--cc--line {\n  stroke-width: 2px !important;\n}\n\n/* Ensure the points are visible */\n.carbon-chart-container .bx--cc--line-point {\n  r: 3px;\n  stroke-width: 1px;\n}\n\n/* Ensure the right axis is properly displayed */\n.carbon-chart-container .bx--cc--axes g.right-axis {\n  display: block !important;\n}\n\n/* Ensure the grid lines are properly displayed */\n.carbon-chart-container .bx--cc--grid line {\n  stroke: #333333; /* Dark border */\n  stroke-width: 0.5px;\n}\n\n/* Ensure the threshold line is visible */\n.carbon-chart-container .bx--cc--threshold line {\n  stroke-width: 1.5px !important;\n  stroke-dasharray: 5, 5;\n}\n\n/* Ensure the legend text is white */\n.carbon-chart-container .bx--cc--legend text {\n  fill: #f4f4f4 !important; /* White text */\n}\n\n/* Analysis Controls */\n.analysis-controls {\n  margin-bottom: 16px;\n}\n\n.control-row {\n  display: flex;\n  align-items: flex-end;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.control-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.control-label {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n}\n\n.control-dropdown {\n  width: 100%;\n}\n\n/* Modal Styles */\n.issue-details-modal {\n  background-color: #262626; /* Dark gray background */\n}\n\n.issue-details-content {\n  padding: 16px;\n  color: #f4f4f4; /* White text */\n}\n\n.issue-chart-container {\n  height: 300px;\n  margin-bottom: 16px;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333; /* Dark border */\n  background-color: #161616; /* Dark background */\n}\n</style>\n"]}]}