<template>
  <div ref="chartHolder">
    <!-- The chart will be rendered here -->
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { Combo<PERSON>hart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'YieldChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    eventType: {type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      options: {
        title: "12-Month Yields with Monthly Targets",
        axes: {
          left: {
            mapsTo: 'value',
            scaleType: "linear", // Numeric scale on the left axis
            position: "left",
            title: "Yield",
            ticks: {formatter: (value) => `${(value* 100).toFixed(0)}%`}
          },
          bottom: {
            mapsTo: 'key',
            scaleType: "labels", // Categories displayed on the bottom axis
            position: "bottom",
            title: "Months",
            "domain": [
                            "January",
                            "February",
                            "March",
                            "April",
                            "May",
                            "June",
                            "July",
                            "August",
                            "September",
                            "October",
                            "November",
                            "December"
                        ]
          
          },
          // timeScale: {
          //   timeInterval: 'monthly'
          // }
        },
        height: "400px",
        tooltip:{
          valueFormatter: (value) => {
              if (typeof value === 'number') {
                return `${(value * 100).toFixed(0)}%`; // Only format numbers as percentages
              }
              return value; // Return non-number values as is
            }
        },
        color: {
          scale: {
            'Passing': 'green', // Green color for Passing
            'Failing': 'red', // Red color for Failing
            'Acceptable': 'yellow', // Yellow color for Acceptable
            'Targets': 'blue', // Blue color for Targets
            'Guardband': 'orange' // Gray color for Guardband
          }
        },
// chart1 - line and bar
        comboChartTypes: [
          {
            type: 'simple-bar',
            correspondingDatasets: [
              'Passing',
              'Failing',
              'Acceptable'
            ]
          },
          {
            type: 'line',
            options: {
              points: {
                radius: 5
              }
            },
            correspondingDatasets: [
              'Targets'
            ]
          },
          {
            type: 'line',
            options: {
              points: {
                radius: 5
              }
            },
            correspondingDatasets: [
              'Guardband'
            ]
          }
        ]
      }
    };
  },
  mounted() {
    
    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    this.chart = new ComboChart(chartHolder, {
      data: this.data,
      options: this.options,
    });
    

    // Adding an event listener for the scatter-click event
    // myChart.services.events.addEventListener("scatter-click", (e) => {
    //   alert(`You clicked ${e.detail.datum.group}`);
    // });
    this.chart.services.events.addEventListener("bar-click", (e) => {this.eventType(e)
              ;
            });


    this.updateLegendCheckboxes();
  
},
watch: {
  data(newData) {
    // Update chart data
    this.chart.model.setData(newData);
  },
},
methods: {
  updateLegendCheckboxes() {
    // Wait for the chart to render
    this.$nextTick(() => {
      // Select all the checkboxes in the chart legend
      const checkboxes = document.querySelectorAll('.checkbox[role="checkbox"]');

      checkboxes.forEach(checkbox => {
        // Get the ID of the associated label (legend title)
        const labelId = checkbox.getAttribute('aria-labelledby');
        
        if (labelId) {
          // Find the <p> element with the same ID
          const labelElement = document.getElementById(labelId);

          if (labelElement) {
            const groupName = labelElement.textContent.trim(); // Get the group name

            // Apply circular checkbox only to "Targets" and "Guardband" groups
            if (groupName === 'Targets' || groupName === 'Guardband') {
              // Set the checkbox to be a circle
              checkbox.style.borderRadius = '50%';
              checkbox.style.width = '13px';
              checkbox.style.height = '13px';
              
              // Adjust the background color or other styles as needed
              checkbox.style.backgroundColor = '#5A5A5A'; // Adjust color if necessary

              // Modify the SVG to display a circle
              const svg = checkbox.querySelector('svg');
              if (svg) {
                // svg.innerHTML = ''; // Clear existing SVG path (the checkmark)
                
                // Create a new circle element
                const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                circle.setAttribute('cx', '5.5');  // Center of circle horizontally
                circle.setAttribute('cy', '5.5');  // Center of circle vertically
                circle.setAttribute('r', '5');     // Radius of the circle
                circle.setAttribute('fill', '#ffffff'); // Circle color

                // Append the new circle to the SVG
                svg.appendChild(circle);
              }
            }
          }
        }
      });
    });
  }
}


};
</script>

<style scoped>
/* Add custom styles if needed */
</style>
