<template>
  <div style="position: relative;" class="carbon-chart-container">
    <ccv-line-chart ref="lineChart" :data="data" :options="options"></ccv-line-chart>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';

Vue.use(chartsVue);

export default {
  name: 'LineChartThreshold',
  props: {
    data: Array,
    loading: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: "400px"
    },
    eventType: {
      type: Function,
      default: () => {}
    },
    title: {
      type: String,
      default: 'Line Chart with Thresholds'
    },
    thresholds: {
      type: Array,
      default: () => []
    },
    timeInterval: {
      type: String,
      default: 'monthly'
    }
  },
  data() {
    return {
      options: {
        title: `${this.title}`,
        tooltip: {
          showTotal: false,
          customHTML: (data) => {
            const dateLabel = this.timeInterval === 'weekly' ? 'Week' : 'Date';
            return `
              <div style="padding: 10px; background: #262626; color: #f4f4f4; border-radius: 4px;">
                <div><strong>${dateLabel}:</strong> ${data[0].date}</div>
                <div><strong>Group:</strong> ${data[0].group}</div>
                <div><strong>Value:</strong> ${data[0].value.toFixed(2)}${data[0].group.includes('Rate') ? '%' : ''}</div>
              </div>
            `;
          }
        },
        axes: {
          bottom: {
            title: this.timeInterval === 'weekly' ? 'Week' : 'Date',
            mapsTo: 'date',
            scaleType: this.timeInterval === 'weekly' ? 'labels' : 'time',
            timeScale: this.timeInterval === 'weekly' ? undefined : {
              timeInterval: this.timeInterval,
              addSpaceOnEdges: 0
            },
            ticks: {
              formatter: (date) => {
                if (this.timeInterval === 'weekly') {
                  // For weekly data, format as "Week XX"
                  if (typeof date === 'string' && date.includes('-')) {
                    const [, week] = date.split('-');
                    return `Week ${parseInt(week)}`;
                  }
                }
                return date;
              }
            }
          },
          left: {
            mapsTo: 'value',
            title: 'Value (%)',
            scaleType: 'linear',
            includeZero: true,
            thresholds: this.thresholds || []
          }
        },
        grid: {
          x: {enabled: false},
          y: {enabled: true}
        },
        color: {
          scale: {
            'Actual Failure Rate': '#0f62fe',
            'Expected Rate': '#6929c4',
            'Deviation Score': '#8a3ffc',
            '3-Month Average': '#ee5396'
          }
        },
        curve: 'curveMonotoneX',
        legend: {
          alignment: 'center',
          enabled: true,
          clickable: true,
          truncation: {
            type: 'none'
          },
          color: '#f4f4f4',
          position: 'bottom'
        },
        data: {
          loading: this.loading
        },
        height: this.height,
        resizable: true,
        animations: true
      }
    };
  },
  methods: {
    updateChart() {
      if (this.$refs.lineChart) {
        // Update the thresholds
        const updatedOptions = {
          ...this.options,
          title: this.title,
          data: {
            loading: this.loading
          },
          height: this.height
        };

        // Update axes based on timeInterval
        if (!updatedOptions.axes) updatedOptions.axes = {};
        if (!updatedOptions.axes.bottom) updatedOptions.axes.bottom = {};

        updatedOptions.axes.bottom.title = this.timeInterval === 'weekly' ? 'Week' : 'Date';
        updatedOptions.axes.bottom.scaleType = this.timeInterval === 'weekly' ? 'labels' : 'time';

        if (this.timeInterval === 'weekly') {
          // For weekly data, use labels scale type and remove timeScale
          delete updatedOptions.axes.bottom.timeScale;
        } else {
          // For monthly data, use time scale type
          if (!updatedOptions.axes.bottom.timeScale) updatedOptions.axes.bottom.timeScale = {};
          updatedOptions.axes.bottom.timeScale.timeInterval = this.timeInterval;
          updatedOptions.axes.bottom.timeScale.addSpaceOnEdges = 0;
        }

        // Add tick formatter for weekly data
        if (!updatedOptions.axes.bottom.ticks) updatedOptions.axes.bottom.ticks = {};
        updatedOptions.axes.bottom.ticks.formatter = (date) => {
          if (this.timeInterval === 'weekly') {
            // For weekly data, format as "Week XX"
            if (typeof date === 'string' && date.includes('-')) {
              const [, week] = date.split('-');
              return `Week ${parseInt(week)}`;
            }
          }
          return date;
        };

        // Set thresholds if provided
        if (this.thresholds && this.thresholds.length > 0) {
          console.log('Setting thresholds:', this.thresholds);
          // Make sure axes and left exist
          if (!updatedOptions.axes.left) updatedOptions.axes.left = {};

          // Set the thresholds
          updatedOptions.axes.left.thresholds = this.thresholds;
        }

        this.$refs.lineChart.options = updatedOptions;
      }
    }
  },
  mounted() {
    this.updateChart();
  },
  watch: {
    data: {
      handler() {
        console.log('LineChart data updated:', this.data);
        this.updateChart();
      },
      deep: true
    },
    title() {
      this.updateChart();
    },
    loading() {
      this.updateChart();
    },
    height() {
      this.updateChart();
    },
    thresholds: {
      handler() {
        console.log('LineChart thresholds updated:', this.thresholds);
        this.updateChart();
      },
      deep: true
    },
    timeInterval() {
      console.log('LineChart timeInterval updated:', this.timeInterval);
      this.updateChart();
    }
  }
};
</script>

<style scoped>
.carbon-chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

:deep(.cds--cc--chart) text {
  fill: #f4f4f4 !important;
}

/* Ensure Carbon Charts styles are properly applied */
:deep(.cds--cc--legend-item) {
  display: flex;
  align-items: center;
  color: #f4f4f4 !important;
}

:deep(.cds--cc--legend-circle) {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  margin-right: 8px !important;
}

:deep(.cds--cc--threshold) {
  stroke-width: 2px !important;
}

:deep(.cds--cc--tooltip) {
  background: #262626 !important;
  color: #f4f4f4 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

:deep(.cds--cc--legend-item text) {
  fill: #f4f4f4 !important;
  color: #f4f4f4 !important;
}
</style>
