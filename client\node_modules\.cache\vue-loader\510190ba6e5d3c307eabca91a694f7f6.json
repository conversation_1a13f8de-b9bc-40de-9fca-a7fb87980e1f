{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=style&index=0&id=25dba680&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748530489935}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wcWUtb3duZXItZGFzaGJvYXJkLWNvbnRhaW5lciB7CiAgY29sb3I6ICNmNGY0ZjQ7Cn0KCi5jb250ZW50LXdyYXBwZXIgewogIHBhZGRpbmc6IDFyZW07Cn0KCi5kYXNoYm9hcmQtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDEuNXJlbTsKfQoKLmRhc2hib2FyZC1oZWFkZXIgaDMgewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDEuNXJlbTsKICBmb250LXdlaWdodDogNDAwOwp9CgoubGFzdC11cGRhdGVkLXRleHQgewogIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgY29sb3I6ICM4ZDhkOGQ7Cn0KCi5rZXktbWV0cmljcy1zZWN0aW9uIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpOwogIGdhcDogMXJlbTsKICBtYXJnaW4tYm90dG9tOiAxLjVyZW07Cn0KCi5tZXRyaWMtY2FyZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzI2MjYyNjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgcGFkZGluZzogMS4yNXJlbTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgYm9yZGVyOiAxcHggc29saWQgIzMzMzMzMzsKfQoKLm1ldHJpYy1pY29uIHsKICBtYXJnaW4tcmlnaHQ6IDFyZW07CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIHdpZHRoOiA0OHB4OwogIGhlaWdodDogNDhweDsKICBib3JkZXItcmFkaXVzOiA1MCU7Cn0KCi5tZXRyaWMtaWNvbi5uZXctaXNzdWVzIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE1LCA5OCwgMjU0LCAwLjEpOwogIGNvbG9yOiAjMGY2MmZlOwp9CgoubWV0cmljLWljb24uY3JpdGljYWwtaXNzdWVzIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1MCwgNzcsIDg2LCAwLjEpOwogIGNvbG9yOiAjZmE0ZDU2Owp9CgoubWV0cmljLWljb24udmFsaWRhdGVkIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDM2LCAxNjEsIDcyLCAwLjEpOwogIGNvbG9yOiAjMjRhMTQ4Owp9CgoubWV0cmljLWljb24udW52YWxpZGF0ZWQgewogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxMzEsIDQzLCAwLjEpOwogIGNvbG9yOiAjZmY4MzJiOwp9CgoubWV0cmljLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKfQoKLm1ldHJpYy1sYWJlbCB7CiAgZm9udC1zaXplOiAwLjg3NXJlbTsKICBjb2xvcjogIzhkOGQ4ZDsKICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtOwp9CgoubWV0cmljLXZhbHVlIHsKICBmb250LXNpemU6IDEuNXJlbTsKICBmb250LXdlaWdodDogNjAwOwogIG1hcmdpbi1ib3R0b206IDAuMjVyZW07Cn0KCi5tZXRyaWMtZGVzY3JpcHRpb24gewogIGZvbnQtc2l6ZTogMC43NXJlbTsKICBjb2xvcjogIzhkOGQ4ZDsKfQoKLmNoYXJ0LXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDEuNXJlbTsKfQoKLmNoYXJ0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMXJlbTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjE2OwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBtYXJnaW46IDFyZW07Cn0KCi5zZWN0aW9uLWZvb3RlciB7CiAgcGFkZGluZzogMCAxLjI1cmVtIDEuMjVyZW07Cn0KCi5zZWN0aW9uLWRlc2NyaXB0aW9uIHsKICBtYXJnaW46IDA7CiAgZm9udC1zaXplOiAwLjg3NXJlbTsKICBjb2xvcjogIzhkOGQ4ZDsKICBsaW5lLWhlaWdodDogMS40Owp9CgouZGFzaGJvYXJkLW1haW4tY29udGVudCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsKICBnYXA6IDEuNXJlbTsKfQoKLmRhc2hib2FyZC1jb2x1bW4gewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDEuNXJlbTsKfQoKLnNlY3Rpb24tY2FyZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzI2MjYyNjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3JkZXI6IDFweCBzb2xpZCAjMzMzMzMzOwp9Cgouc2VjdGlvbi1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMS4yNXJlbTsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7Cn0KCi5zZWN0aW9uLWhlYWRlcjpob3ZlciB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzMzMzMzMzsKfQoKLnNlY3Rpb24tdGl0bGUtY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi5zZWN0aW9uLXRpdGxlIHsKICBtYXJnaW46IDA7CiAgZm9udC1zaXplOiAxLjI1cmVtOwogIGZvbnQtd2VpZ2h0OiA0MDA7Cn0KCi5zZWN0aW9uLXN1YnRpdGxlIHsKICBmb250LXNpemU6IDAuODc1cmVtOwogIGNvbG9yOiAjOGQ4ZDhkOwogIG1hcmdpbi10b3A6IDAuMjVyZW07Cn0KCi5zZWN0aW9uLWNvbnRyb2xzIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5zdGF0dXMtaW5kaWNhdG9yIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgd2lkdGg6IDI0cHg7CiAgaGVpZ2h0OiAyNHB4OwogIGJvcmRlci1yYWRpdXM6IDUwJTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmE0ZDU2OwogIGNvbG9yOiAjZmZmZmZmOwogIGZvbnQtc2l6ZTogMC43NXJlbTsKICBmb250LXdlaWdodDogNjAwOwogIG1hcmdpbi1yaWdodDogMC43NXJlbTsKfQoKLnN0YXR1cy1pbmRpY2F0b3IuZmxhc2hpbmcgewogIGFuaW1hdGlvbjogZmxhc2ggMnMgaW5maW5pdGU7Cn0KCi5zdGF0dXMtaW5kaWNhdG9yLnJlc29sdmVkLWluZGljYXRvciB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzI0YTE0ODsKfQoKLnN0YXR1cy1pbmRpY2F0b3Iub3V0c3RhbmRpbmctaW5kaWNhdG9yIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMGY2MmZlOwp9CgpAa2V5ZnJhbWVzIGZsYXNoIHsKICAwJSwgMTAwJSB7IG9wYWNpdHk6IDE7IH0KICA1MCUgeyBvcGFjaXR5OiAwLjU7IH0KfQoKLmV4cGFuZC1pbmRpY2F0b3IgewogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7Cn0KCi5leHBhbmQtaW5kaWNhdG9yLmV4cGFuZGVkIHsKICB0cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpOwp9Cgouc2VjdGlvbi1jb250ZW50IHsKICBwYWRkaW5nOiAwIDEuMjVyZW0gMS4yNXJlbTsKfQoKLmZpbHRlci1jb250YWluZXIgewogIG1hcmdpbi1ib3R0b206IDEuNXJlbTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzMzMzMzOwogIHBhZGRpbmc6IDFyZW07CiAgYm9yZGVyLXJhZGl1czogOHB4Owp9CgouZmlsdGVyLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxcmVtOwp9CgouZmlsdGVyLXRpdGxlIHsKICBtYXJnaW46IDA7CiAgZm9udC1zaXplOiAxcmVtOwogIGZvbnQtd2VpZ2h0OiA0MDA7Cn0KCi5maWx0ZXItY29udHJvbHMgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIGdhcDogMXJlbTsKfQoKLmZpbHRlci1ncm91cCB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMC41cmVtOwp9CgouZmlsdGVyLWxhYmVsIHsKICBjb2xvcjogIzhkOGQ4ZDsKICBmb250LXNpemU6IDAuODc1cmVtOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7Cn0KCi5maWx0ZXItZHJvcGRvd24gewogIHdpZHRoOiAyMDBweDsKfQoKLmlzc3Vlcy1saXN0IHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAxcmVtOwp9CgouaXNzdWUtY2FyZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzMzMzMzMzsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlLCBib3gtc2hhZG93IDAuMnMgZWFzZTsKICBjdXJzb3I6IHBvaW50ZXI7Cn0KCi5pc3N1ZS1jYXJkOmhvdmVyIHsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7CiAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4yKTsKfQoKLmlzc3VlLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDFyZW07CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgouaXNzdWUtdGFncyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDAuNXJlbTsKICBtYXJnaW4tcmlnaHQ6IDFyZW07Cn0KCi5pc3N1ZS10aXRsZSB7CiAgZmxleC1ncm93OiAxOwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5pc3N1ZS1tZXRhZGF0YSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMC41cmVtOwogIG1hcmdpbi1yaWdodDogMXJlbTsKfQoKLmlzc3VlLW11bHRpcGxpZXIgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgcGFkZGluZzogMC4xMjVyZW0gMC4zNzVyZW07CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouaXNzdWUtbXVsdGlwbGllci5oaWdoLXNldmVyaXR5IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1MCwgNzcsIDg2LCAwLjEpOwogIGNvbG9yOiAjZmE0ZDU2Owp9CgouaXNzdWUtbXVsdGlwbGllci5tZWRpdW0tc2V2ZXJpdHkgewogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxMzEsIDQzLCAwLjEpOwogIGNvbG9yOiAjZmY4MzJiOwp9CgouaXNzdWUtbXVsdGlwbGllci5nb29kLXBlcmZvcm1hbmNlIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDM2LCAxNjEsIDcyLCAwLjEpOwogIGNvbG9yOiAjMjRhMTQ4Owp9CgouaXNzdWUtbXVsdGlwbGllci5tZWRpdW0tcGVyZm9ybWFuY2UgewogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxMzEsIDQzLCAwLjEpOwogIGNvbG9yOiAjZmY4MzJiOwp9CgoucmVzb2x2ZWQtaXNzdWUtY2FyZCB7CiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMjRhMTQ4Owp9Cgoub3V0c3RhbmRpbmctaXNzdWUtY2FyZCB7CiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMGY2MmZlOwp9CgoucmVzb2x1dGlvbi1kZXRhaWxzLCAuYWNjZXB0YW5jZS1kZXRhaWxzIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBtYXJnaW4tYm90dG9tOiAxcmVtOwogIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgY29sb3I6ICNjNmM2YzY7Cn0KCi5pc3N1ZS1tdWx0aXBsaWVyLmxvdy1wZXJmb3JtYW5jZSB7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgzNiwgMTYxLCA3MiwgMC4xKTsKICBjb2xvcjogIzI0YTE0ODsKfQoKLnJlc29sdXRpb24tY29tbWVudCwgLmFjY2VwdGFuY2UtY29tbWVudCB7CiAgbWFyZ2luOiAxcmVtIDA7CiAgcGFkZGluZzogMC43NXJlbTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzMzMzMzOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLmNvbW1lbnQtbGFiZWwgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgbWFyZ2luLWJvdHRvbTogMC41cmVtOwogIGNvbG9yOiAjYzZjNmM2Owp9CgouY29tbWVudC10ZXh0IHsKICBmb250LXNpemU6IDAuODc1cmVtOwogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsKfQoKLmlzc3VlLWNvbnRlbnQgewogIHBhZGRpbmc6IDAgMXJlbSAxcmVtOwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjNDQ0NDQ0Owp9CgouYWktZGVzY3JpcHRpb24gewogIG1hcmdpbi1ib3R0b206IDFyZW07CiAgcGFkZGluZzogMXJlbTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjYyNjI2OwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBmb250LXNpemU6IDAuODc1cmVtOwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5hY3Rpb24tY29tbWVudCB7CiAgbWFyZ2luLWJvdHRvbTogMXJlbTsKfQoKLmlzc3VlLWFjdGlvbnMgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICBnYXA6IDAuNXJlbTsKfQoKLm5vLWRhdGEtbWVzc2FnZSB7CiAgY29sb3I6ICM4ZDhkOGQ7CiAgZm9udC1zaXplOiAxcmVtOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAycmVtOwp9CgpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAua2V5LW1ldHJpY3Mtc2VjdGlvbiB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsKICB9CgogIC5kYXNoYm9hcmQtbWFpbi1jb250ZW50IHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOwogIH0KCiAgLmZpbHRlci1jb250cm9scyB7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIH0KCiAgLmZpbHRlci1ncm91cCB7CiAgICB3aWR0aDogMTAwJTsKICB9Cn0K"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAmqDA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Fails Chart Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Fails Analysis</h4>\n            <div class=\"section-subtitle\">Historical data visualization</div>\n          </div>\n        </div>\n\n        <FailsStackedBarChart\n          :pqeOwner=\"pqeOwner\"\n          :breakoutGroups=\"breakoutGroups\"\n        />\n      </div>\n\n      <!-- Root Cause Analysis Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Root Cause Analysis</h4>\n            <div class=\"section-subtitle\">Current Month Analysis</div>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart -->\n        <div class=\"chart-container\">\n          <RootCauseChart\n            :data=\"rootCauseChartData\"\n            :loading=\"isRootCauseDataLoading\"\n            :height=\"'400px'\"\n            title=\"Root Cause Categories by Month\"\n            @bar-click=\"handleRootCauseBarClick\"\n          />\n        </div>\n\n        <div class=\"section-footer\">\n          <p class=\"section-description\">\n            Root cause analysis showing defect categories and their fail rates over time.\n            Click on bars to see detailed information.\n          </p>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header outstanding-issues-header\" @click=\"toggleOutstandingIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Outstanding Issues</h4>\n                <div class=\"section-subtitle\">Accepted issues that are being monitored</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator outstanding-indicator\">\n                  {{ outstandingIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isOutstandingIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Outstanding Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearOutstandingFilters\"\n                    v-if=\"isOutstandingFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"outstanding-category-dropdown\"\n                      v-model=\"outstandingCategoryFilter\"\n                      @change=\"handleOutstandingCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in outstandingCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"outstanding-analysis-dropdown\"\n                      v-model=\"outstandingAnalysisTypeFilter\"\n                      @change=\"handleOutstandingAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issues List -->\n              <div v-if=\"filteredOutstandingIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredOutstandingIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card outstanding-issue-card\"\n                  @click=\"toggleOutstandingIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"blue\"\n                        label=\"Outstanding\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-performance' : 'low-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                    <div class=\"acceptance-details\">\n                      <div class=\"acceptance-date\">\n                        <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                      </div>\n                      <div class=\"accepted-by\">\n                        <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Acceptance Comment -->\n                    <div class=\"acceptance-comment\">\n                      <div class=\"comment-label\">Acceptance Reason:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No outstanding issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header resolved-issues-header\" @click=\"toggleResolvedIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Resolved Issues</h4>\n                <div class=\"section-subtitle\">Track performance of resolved issues</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator resolved-indicator\">\n                  {{ resolvedIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isResolvedIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Resolved Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearResolvedFilters\"\n                    v-if=\"isResolvedFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"category-dropdown\"\n                      v-model=\"resolvedCategoryFilter\"\n                      @change=\"handleResolvedCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in resolvedCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"resolved-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"resolved-analysis-dropdown\"\n                      v-model=\"resolvedAnalysisTypeFilter\"\n                      @change=\"handleResolvedAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issues List -->\n              <div v-if=\"filteredResolvedIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredResolvedIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card resolved-issue-card\"\n                  @click=\"toggleResolvedIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"green\"\n                        label=\"Resolved\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                    <div class=\"resolution-details\">\n                      <div class=\"resolution-date\">\n                        <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                      </div>\n                      <div class=\"original-severity\">\n                        <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                        ({{ issue.increaseMultiplier }}x)\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Resolution Comment -->\n                    <div class=\"resolution-comment\">\n                      <div class=\"comment-label\">Resolution Actions:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No resolved issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\nimport FailsStackedBarChart from '@/components/Charts/FailsStackedBarChart';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    FailsStackedBarChart,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Resolved Issues Data\n      resolvedIssues: [],\n      expandedResolvedIssueIds: [], // Track which resolved issues are expanded\n      isResolvedIssuesExpanded: false, // Track if resolved issues section is expanded\n\n      // Outstanding Issues Data\n      outstandingIssues: [],\n      expandedOutstandingIssueIds: [], // Track which outstanding issues are expanded\n      isOutstandingIssuesExpanded: false, // Track if outstanding issues section is expanded\n\n      // Performance Tracking\n      performanceData: {}, // Track performance metrics for resolved and outstanding issues\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Resolved Issues Filtering\n      resolvedFilters: {\n        category: [],\n        analysisType: []\n      },\n      resolvedCategoryFilter: 'all',\n      resolvedAnalysisTypeFilter: 'all',\n\n      // Outstanding Issues Filtering\n      outstandingFilters: {\n        category: [],\n        analysisType: []\n      },\n      outstandingCategoryFilter: 'all',\n      outstandingAnalysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n\n      // Loading States\n      isLoading: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Filtered resolved issues based on selected filters\n    filteredResolvedIssues() {\n      // If no filters are selected, return all resolved issues\n      if (this.resolvedFilters.category.length === 0 && this.resolvedFilters.analysisType.length === 0) {\n        return this.resolvedIssues;\n      }\n\n      // Apply filters\n      return this.resolvedIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.resolvedFilters.category.length === 0 ||\n                                  this.resolvedFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.resolvedFilters.analysisType.length === 0 ||\n                                  this.resolvedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any resolved issue filters are active\n    isResolvedFiltersActive() {\n      return this.resolvedFilters.category.length > 0 || this.resolvedFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from resolved issues for filtering\n    resolvedCategories() {\n      const categories = new Set();\n      this.resolvedIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    },\n\n    // Filtered outstanding issues based on selected filters\n    filteredOutstandingIssues() {\n      // If no filters are selected, return all outstanding issues\n      if (this.outstandingFilters.category.length === 0 && this.outstandingFilters.analysisType.length === 0) {\n        return this.outstandingIssues;\n      }\n\n      // Apply filters\n      return this.outstandingIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.outstandingFilters.category.length === 0 ||\n                                  this.outstandingFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.outstandingFilters.analysisType.length === 0 ||\n                                  this.outstandingFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any outstanding issue filters are active\n    isOutstandingFiltersActive() {\n      return this.outstandingFilters.category.length > 0 || this.outstandingFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from outstanding issues for filtering\n    outstandingCategories() {\n      const categories = new Set();\n      this.outstandingIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue) {\n        if (newValue) {\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadResolvedIssues();\n      this.loadOutstandingIssues();\n      this.loadRootCauseData();\n    },\n\n    async loadOutstandingIssues() {\n      console.log(`Loading outstanding issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter outstanding issues to only include those related to this PQE's breakout groups\n          const allOutstandingIssues = data.outstanding_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.outstandingIssues = allOutstandingIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.outstandingIssues = allOutstandingIssues;\n          }\n\n          // Update performance data for outstanding issues\n          if (data.performance_data) {\n            // Merge with existing performance data\n            this.performanceData = {\n              ...this.performanceData,\n              ...data.performance_data\n            };\n          }\n\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues();\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleOutstandingIssues() {\n      // Sample data for development\n      this.outstandingIssues = [\n        {\n          id: 'oi1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n          comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-10',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi2',\n          category: 'Victoria Crypto',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known power delivery issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n          comment: 'Power delivery variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-03-22',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Quality Team'\n        },\n        {\n          id: 'oi3',\n          category: 'Quantum Nexus',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.2',\n          aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate. This is a known process variation that has been accepted. Monitoring for any significant changes.',\n          comment: 'Process variation is within accepted tolerance. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-02-15',\n          currentPerformance: '1.1x',\n          acceptedBy: 'Manufacturing Team'\n        },\n        {\n          id: 'oi4',\n          category: 'Stellar Core',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n          comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-05',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi5',\n          category: 'Nebula Drive',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',\n          comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-05-12',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Product Team'\n        }\n      ];\n\n      // Add performance data for outstanding issues\n      const outstandingPerformanceData = {\n        'Fan Themis': [\n          { month: 'Apr 2024', xFactor: 1.5 },\n          { month: 'May 2024', xFactor: 1.4 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.4 },\n          { month: 'Apr 2024', xFactor: 1.3 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Feb 2024', xFactor: 1.3 },\n          { month: 'Mar 2024', xFactor: 1.2 },\n          { month: 'Apr 2024', xFactor: 1.2 },\n          { month: 'May 2024', xFactor: 1.1 },\n          { month: 'Jun 2024', xFactor: 1.1 }\n        ],\n        'Stellar Core': [\n          { month: 'Apr 2024', xFactor: 1.4 },\n          { month: 'May 2024', xFactor: 1.3 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Nebula Drive': [\n          { month: 'May 2024', xFactor: 1.5 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ]\n      };\n\n      // Merge with existing performance data\n      this.performanceData = {\n        ...this.performanceData,\n        ...outstandingPerformanceData\n      };\n    },\n\n    async loadResolvedIssues() {\n      console.log(`Loading resolved issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter resolved issues to only include those related to this PQE's breakout groups\n          const allResolvedIssues = data.resolved_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.resolvedIssues = allResolvedIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.resolvedIssues = allResolvedIssues;\n          }\n\n          // Load performance data for resolved issues\n          if (data.performance_data) {\n            this.performanceData = data.performance_data;\n          }\n\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues();\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleResolvedIssues() {\n      // Sample data for development\n      this.resolvedIssues = [\n        {\n          id: 'ri1',\n          category: 'Fan Themis',\n          month: '2024-05',\n          severity: 'high',\n          increaseMultiplier: '2.8',\n          aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n          comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-05-15',\n          currentPerformance: '0.9x'\n        },\n        {\n          id: 'ri2',\n          category: 'Victoria Crypto',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.7',\n          aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific power delivery problem. Process was corrected.',\n          comment: 'Improved power delivery design and quality control. Monitoring new units closely.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-04-28',\n          currentPerformance: '0.8x'\n        },\n        {\n          id: 'ri3',\n          category: 'Quantum Nexus',\n          month: '2024-03',\n          severity: 'high',\n          increaseMultiplier: '2.2',\n          aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',\n          comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-03-20',\n          currentPerformance: '0.7x'\n        }\n      ];\n\n      // Sample performance data\n      this.performanceData = {\n        'Fan Themis': [\n          { month: 'May 2024', xFactor: 2.8 },\n          { month: 'Jun 2024', xFactor: 0.9 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.6 },\n          { month: 'Apr 2024', xFactor: 1.7 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 0.8 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Mar 2024', xFactor: 2.2 },\n          { month: 'Apr 2024', xFactor: 1.1 },\n          { month: 'May 2024', xFactor: 0.8 },\n          { month: 'Jun 2024', xFactor: 0.7 }\n        ]\n      };\n    },\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range (last 6 months)\n        const endDate = new Date();\n        const startDate = new Date();\n        startDate.setMonth(endDate.getMonth() - 5); // 6 months including current\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', this.rootCauseChartData);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: category,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n      }\n\n      const months = ['2024-03', '2024-04', '2024-05', '2024-06'];\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-container {\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n</style>\n"]}]}