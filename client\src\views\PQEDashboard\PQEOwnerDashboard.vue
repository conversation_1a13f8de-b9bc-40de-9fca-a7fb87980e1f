<template>
  <div class="pqe-owner-dashboard-container">
    <div class="content-wrapper">
      <!-- Header Section with Critical Issues Summary -->
      <div class="dashboard-header">
        <h3>{{ pqeOwner }}'s Dashboard</h3>
        <div class="last-updated-text">
          Last Updated: {{ new Date().toLocaleString() }}
        </div>
      </div>

      <!-- Key Metrics Section -->
      <div class="key-metrics-section">
        <div class="metric-card">
          <div class="metric-icon new-issues">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">New Critical Issues</div>
            <div class="metric-value">{{ newCriticalIssues.length }}</div>
            <div class="metric-description">This Month</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon critical-issues">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Critical Issues</div>
            <div class="metric-value">{{ unresolvedCriticalIssues.length }}</div>
            <div class="metric-description">Need Resolution</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon validated">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Validated Fails</div>
            <div class="metric-value">{{ validatedCount }}</div>
            <div class="metric-description">This Month</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon unvalidated">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z"></path>
              <path d="M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Unvalidated Fails</div>
            <div class="metric-value">{{ unvalidatedCount }}</div>
            <div class="metric-description">Need Validation</div>
          </div>
        </div>
      </div>

      <!-- Root Cause Analysis Section -->
      <div class="section-card chart-section">
        <div class="section-header">
          <div class="section-title-container">
            <h4 class="section-title">Root Cause Analysis</h4>
            <div class="section-subtitle">Current Month Analysis</div>
          </div>
        </div>

        <!-- Root Cause Chart Controls -->
        <div class="chart-controls">
          <div class="control-group">
            <label for="root-cause-view-dropdown" class="control-label">View By:</label>
            <cv-dropdown
              id="root-cause-view-dropdown"
              v-model="rootCauseViewBy"
              class="control-dropdown"
            >
              <cv-dropdown-item value="rootCause">Root Cause</cv-dropdown-item>
            </cv-dropdown>
          </div>
          <div class="control-group">
            <label for="root-cause-time-dropdown" class="control-label">Time Range:</label>
            <cv-dropdown
              id="root-cause-time-dropdown"
              v-model="rootCauseTimeRange"
              @change="handleRootCauseTimeRangeChange"
              class="control-dropdown"
            >
              <cv-dropdown-item value="6month">6 Months</cv-dropdown-item>
              <cv-dropdown-item value="3month">3 Months</cv-dropdown-item>
            </cv-dropdown>
          </div>
          <div class="control-group" v-if="breakoutGroups && breakoutGroups.length > 0">
            <label for="root-cause-group-dropdown" class="control-label">Group:</label>
            <cv-dropdown
              id="root-cause-group-dropdown"
              v-model="rootCauseSelectedGroup"
              @change="handleRootCauseGroupChange"
              class="control-dropdown"
            >
              <cv-dropdown-item value="all">All Groups</cv-dropdown-item>
              <cv-dropdown-item
                v-for="group in breakoutGroups"
                :key="group.name"
                :value="group.name"
              >
                {{ group.name }}
              </cv-dropdown-item>
            </cv-dropdown>
          </div>
        </div>

        <!-- Root Cause Chart -->
        <div class="chart-container">
          <RootCauseChart
            :key="`root-cause-${rootCauseSelectedGroup}-${rootCauseTimeRange}`"
            :data="rootCauseChartData"
            :loading="isRootCauseDataLoading"
            :height="'400px'"
            title="Root Cause Categories by Month"
            @bar-click="handleRootCauseBarClick"
          />
        </div>

        <div class="section-footer">
          <p class="section-description">
            Root cause analysis showing defect categories and their fail rates over time.
            Click on bars to see detailed information.
          </p>
        </div>
      </div>

      <!-- Main Content Layout -->
      <div class="dashboard-main-content">
        <!-- Left Column -->
        <div class="dashboard-column">
          <!-- Critical Issues Section -->
          <div class="section-card">
            <div class="section-header critical-issues-header" @click="toggleCriticalIssuesExpanded">
              <div class="section-title-container">
                <h4 class="section-title">Critical Issues</h4>
                <div class="section-subtitle">Issues requiring immediate attention</div>
              </div>
              <div class="section-controls">
                <div class="status-indicator" :class="{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }">
                  {{ unresolvedCriticalIssues.length }}
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isCriticalIssuesExpanded }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div v-if="isCriticalIssuesExpanded" class="section-content">
              <!-- Filter Controls in a Single Row -->
              <div class="filter-container">
                <div class="filter-header">
                  <h5 class="filter-title">Filter Issues</h5>
                  <cv-button
                    kind="ghost"
                    size="small"
                    class="clear-filters-button"
                    @click="clearFilters"
                    v-if="isFiltersActive"
                  >
                    Clear Filters
                  </cv-button>
                </div>

                <div class="filter-controls">
                  <div class="filter-group">
                    <label for="severity-dropdown" class="filter-label">Severity:</label>
                    <cv-dropdown
                      id="severity-dropdown"
                      v-model="severityFilter"
                      @change="handleSeverityFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Severities</cv-dropdown-item>
                      <cv-dropdown-item value="high">High</cv-dropdown-item>
                      <cv-dropdown-item value="medium">Medium</cv-dropdown-item>
                    </cv-dropdown>
                  </div>

                  <div class="filter-group">
                    <label for="analysis-dropdown" class="filter-label">Analysis Type:</label>
                    <cv-dropdown
                      id="analysis-dropdown"
                      v-model="analysisTypeFilter"
                      @change="handleAnalysisFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Analysis Types</cv-dropdown-item>
                      <cv-dropdown-item value="Root Cause">Root Cause</cv-dropdown-item>
                    </cv-dropdown>
                  </div>
                </div>
              </div>

              <!-- Critical Issues List -->
              <div v-if="filteredCriticalIssues.length > 0" class="issues-list">
                <div
                  v-for="issue in filteredCriticalIssues"
                  :key="issue.id"
                  class="issue-card"
                  @click="toggleIssueExpanded(issue)"
                  :class="{ 'expanded': isIssueExpanded(issue.id) }"
                >
                  <div class="issue-header">
                    <div class="issue-tags">
                      <cv-tag
                        :kind="issue.severity === 'high' ? 'red' : 'magenta'"
                        :label="issue.severity === 'high' ? 'High' : 'Medium'"
                      />
                      <cv-tag
                        kind="purple"
                        class="analysis-tag"
                        :label="issue.analysisType"
                      />
                    </div>
                    <span class="issue-title">{{ issue.category }}</span>
                    <div class="issue-metadata">
                      <cv-tag
                        kind="cool-gray"
                        class="month-tag"
                        :label="issue.month"
                      />
                      <span class="issue-multiplier" :class="issue.severity === 'high' ? 'high-severity' : 'medium-severity'">
                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}
                      </span>
                    </div>
                    <div class="expand-indicator">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                      </svg>
                    </div>
                  </div>

                  <div class="issue-content" v-if="isIssueExpanded(issue.id)">
                    <div class="ai-description">
                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                    </div>

                    <!-- Action Comment Text Box -->
                    <div class="action-comment">
                      <cv-text-area
                        v-model="issue.comment"
                        label="Action Comments"
                        placeholder="Add your comments or action plan here..."
                        :helper-text="issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'"
                      ></cv-text-area>
                    </div>

                    <div class="issue-actions">
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewIssueDetails(issue)"
                      >
                        View Data
                      </cv-button>
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="updateIssue(issue)"
                      >
                        Update
                      </cv-button>
                      <cv-button
                        kind="secondary"
                        size="small"
                        @click.stop="updateIssue(issue, false, true)"
                      >
                        Mark Outstanding
                      </cv-button>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="updateIssue(issue, true, false)"
                      >
                        Mark Resolved
                      </cv-button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-data-message">
                No critical issues found matching the selected filters.
              </div>
            </div>
          </div>

          <!-- Outstanding Issues Section -->
          <div class="section-card">
            <div class="section-header outstanding-issues-header" @click="toggleOutstandingIssuesExpanded">
              <div class="section-title-container">
                <h4 class="section-title">Outstanding Issues</h4>
                <div class="section-subtitle">Accepted issues that are being monitored</div>
              </div>
              <div class="section-controls">
                <div class="status-indicator outstanding-indicator">
                  {{ outstandingIssues.length }}
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isOutstandingIssuesExpanded }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div v-if="isOutstandingIssuesExpanded" class="section-content">
              <!-- Filter Controls in a Single Row -->
              <div class="filter-container">
                <div class="filter-header">
                  <h5 class="filter-title">Filter Outstanding Issues</h5>
                  <cv-button
                    kind="ghost"
                    size="small"
                    class="clear-filters-button"
                    @click="clearOutstandingFilters"
                    v-if="isOutstandingFiltersActive"
                  >
                    Clear Filters
                  </cv-button>
                </div>

                <div class="filter-controls">
                  <div class="filter-group">
                    <label for="outstanding-category-dropdown" class="filter-label">Category:</label>
                    <cv-dropdown
                      id="outstanding-category-dropdown"
                      v-model="outstandingCategoryFilter"
                      @change="handleOutstandingCategoryFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Categories</cv-dropdown-item>
                      <cv-dropdown-item
                        v-for="category in outstandingCategories"
                        :key="category"
                        :value="category"
                      >
                        {{ category }}
                      </cv-dropdown-item>
                    </cv-dropdown>
                  </div>

                  <div class="filter-group">
                    <label for="outstanding-analysis-dropdown" class="filter-label">Analysis Type:</label>
                    <cv-dropdown
                      id="outstanding-analysis-dropdown"
                      v-model="outstandingAnalysisTypeFilter"
                      @change="handleOutstandingAnalysisFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Analysis Types</cv-dropdown-item>
                      <cv-dropdown-item value="Root Cause">Root Cause</cv-dropdown-item>
                    </cv-dropdown>
                  </div>
                </div>
              </div>

              <!-- Outstanding Issues List -->
              <div v-if="filteredOutstandingIssues.length > 0" class="issues-list">
                <div
                  v-for="issue in filteredOutstandingIssues"
                  :key="issue.id"
                  class="issue-card outstanding-issue-card"
                  @click="toggleOutstandingIssueExpanded(issue)"
                  :class="{ 'expanded': isOutstandingIssueExpanded(issue.id) }"
                >
                  <div class="issue-header">
                    <div class="issue-tags">
                      <cv-tag
                        kind="blue"
                        label="Outstanding"
                      />
                      <cv-tag
                        kind="purple"
                        class="analysis-tag"
                        :label="issue.analysisType"
                      />
                    </div>
                    <span class="issue-title">{{ issue.category }}</span>
                    <div class="issue-metadata">
                      <cv-tag
                        kind="cool-gray"
                        class="month-tag"
                        :label="issue.month"
                      />
                      <span class="issue-multiplier" :class="parseFloat(issue.currentPerformance) > 1.3 ? 'medium-performance' : 'low-performance'">
                        Current: {{ issue.currentPerformance }}
                      </span>
                    </div>
                    <div class="expand-indicator">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                      </svg>
                    </div>
                  </div>

                  <div class="issue-content" v-if="isOutstandingIssueExpanded(issue.id)">
                    <div class="acceptance-details">
                      <div class="acceptance-date">
                        <strong>Accepted on:</strong> {{ issue.acceptanceDate }}
                      </div>
                      <div class="accepted-by">
                        <strong>Accepted by:</strong> {{ issue.acceptedBy }}
                      </div>
                    </div>

                    <div class="ai-description">
                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                    </div>

                    <!-- Acceptance Comment -->
                    <div class="acceptance-comment">
                      <div class="comment-label">Acceptance Reason:</div>
                      <div class="comment-text">{{ issue.comment }}</div>
                    </div>

                    <div class="issue-actions">
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewPerformanceData(issue)"
                      >
                        View Performance
                      </cv-button>
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewIssueDetails(issue)"
                      >
                        View Data
                      </cv-button>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="updateIssue(issue, true, false)"
                      >
                        Mark Resolved
                      </cv-button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-data-message">
                No outstanding issues found matching the selected filters.
              </div>
            </div>
          </div>

          <!-- Resolved Issues Section -->
          <div class="section-card">
            <div class="section-header resolved-issues-header" @click="toggleResolvedIssuesExpanded">
              <div class="section-title-container">
                <h4 class="section-title">Resolved Issues</h4>
                <div class="section-subtitle">Track performance of resolved issues</div>
              </div>
              <div class="section-controls">
                <div class="status-indicator resolved-indicator">
                  {{ resolvedIssues.length }}
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isResolvedIssuesExpanded }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div v-if="isResolvedIssuesExpanded" class="section-content">
              <!-- Filter Controls in a Single Row -->
              <div class="filter-container">
                <div class="filter-header">
                  <h5 class="filter-title">Filter Resolved Issues</h5>
                  <cv-button
                    kind="ghost"
                    size="small"
                    class="clear-filters-button"
                    @click="clearResolvedFilters"
                    v-if="isResolvedFiltersActive"
                  >
                    Clear Filters
                  </cv-button>
                </div>

                <div class="filter-controls">
                  <div class="filter-group">
                    <label for="category-dropdown" class="filter-label">Category:</label>
                    <cv-dropdown
                      id="category-dropdown"
                      v-model="resolvedCategoryFilter"
                      @change="handleResolvedCategoryFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Categories</cv-dropdown-item>
                      <cv-dropdown-item
                        v-for="category in resolvedCategories"
                        :key="category"
                        :value="category"
                      >
                        {{ category }}
                      </cv-dropdown-item>
                    </cv-dropdown>
                  </div>

                  <div class="filter-group">
                    <label for="resolved-analysis-dropdown" class="filter-label">Analysis Type:</label>
                    <cv-dropdown
                      id="resolved-analysis-dropdown"
                      v-model="resolvedAnalysisTypeFilter"
                      @change="handleResolvedAnalysisFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Analysis Types</cv-dropdown-item>
                      <cv-dropdown-item value="Root Cause">Root Cause</cv-dropdown-item>
                    </cv-dropdown>
                  </div>
                </div>
              </div>

              <!-- Resolved Issues List -->
              <div v-if="filteredResolvedIssues.length > 0" class="issues-list">
                <div
                  v-for="issue in filteredResolvedIssues"
                  :key="issue.id"
                  class="issue-card resolved-issue-card"
                  @click="toggleResolvedIssueExpanded(issue)"
                  :class="{ 'expanded': isResolvedIssueExpanded(issue.id) }"
                >
                  <div class="issue-header">
                    <div class="issue-tags">
                      <cv-tag
                        kind="green"
                        label="Resolved"
                      />
                      <cv-tag
                        kind="purple"
                        class="analysis-tag"
                        :label="issue.analysisType"
                      />
                    </div>
                    <span class="issue-title">{{ issue.category }}</span>
                    <div class="issue-metadata">
                      <cv-tag
                        kind="cool-gray"
                        class="month-tag"
                        :label="issue.month"
                      />
                      <span class="issue-multiplier" :class="issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-performance'">
                        Current: {{ issue.currentPerformance }}
                      </span>
                    </div>
                    <div class="expand-indicator">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                      </svg>
                    </div>
                  </div>

                  <div class="issue-content" v-if="isResolvedIssueExpanded(issue.id)">
                    <div class="resolution-details">
                      <div class="resolution-date">
                        <strong>Resolved on:</strong> {{ issue.resolutionDate }}
                      </div>
                      <div class="original-severity">
                        <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}
                        ({{ issue.increaseMultiplier }}x)
                      </div>
                    </div>

                    <div class="ai-description">
                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                    </div>

                    <!-- Resolution Comment -->
                    <div class="resolution-comment">
                      <div class="comment-label">Resolution Actions:</div>
                      <div class="comment-text">{{ issue.comment }}</div>
                    </div>

                    <div class="issue-actions">
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewPerformanceData(issue)"
                      >
                        View Performance
                      </cv-button>
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewIssueDetails(issue)"
                      >
                        View Data
                      </cv-button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-data-message">
                No resolved issues found matching the selected filters.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  CvButton,
  CvTag,
  CvTextArea,
  CvDropdown,
  CvDropdownItem
} from '@carbon/vue';
import RootCauseChart from '@/components/RootCauseChart/RootCauseChart';

export default {
  name: 'PQEOwnerDashboard',
  components: {
    CvButton,
    CvTag,
    CvTextArea,
    CvDropdown,
    CvDropdownItem,
    RootCauseChart
  },
  props: {
    pqeOwner: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // Critical Issues Data
      newCriticalIssues: [],
      unresolvedCriticalIssues: [],
      expandedIssueIds: [], // Track which issues are expanded
      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded

      // Resolved Issues Data
      resolvedIssues: [],
      expandedResolvedIssueIds: [], // Track which resolved issues are expanded
      isResolvedIssuesExpanded: false, // Track if resolved issues section is expanded

      // Outstanding Issues Data
      outstandingIssues: [],
      expandedOutstandingIssueIds: [], // Track which outstanding issues are expanded
      isOutstandingIssuesExpanded: false, // Track if outstanding issues section is expanded

      // Performance Tracking
      performanceData: {}, // Track performance metrics for resolved and outstanding issues

      // Filtering
      selectedFilters: {
        severity: [],
        analysisType: []
      },
      severityFilter: 'all',
      analysisTypeFilter: 'all',

      // Resolved Issues Filtering
      resolvedFilters: {
        category: [],
        analysisType: []
      },
      resolvedCategoryFilter: 'all',
      resolvedAnalysisTypeFilter: 'all',

      // Outstanding Issues Filtering
      outstandingFilters: {
        category: [],
        analysisType: []
      },
      outstandingCategoryFilter: 'all',
      outstandingAnalysisTypeFilter: 'all',

      // Validation Data
      validatedCount: 0,
      unvalidatedCount: 0,

      // Breakout Groups Data
      breakoutGroups: [],

      // Root Cause Chart Data
      rootCauseChartData: [],
      isRootCauseDataLoading: false,

      // Root Cause Chart Controls
      rootCauseViewBy: 'rootCause',
      rootCauseTimeRange: '6month',
      rootCauseSelectedGroup: 'all',

      // Loading States
      isLoading: false
    };
  },
  computed: {
    // Filtered critical issues based on selected filters
    filteredCriticalIssues() {
      // If no filters are selected, return all issues
      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {
        return this.unresolvedCriticalIssues;
      }

      // Apply filters
      return this.unresolvedCriticalIssues.filter(issue => {
        // Check if issue passes severity filter
        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||
                                    this.selectedFilters.severity.includes(issue.severity);

        // Check if issue passes analysis type filter
        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||
                                    this.selectedFilters.analysisType.includes(issue.analysisType);

        // Issue must pass both filters
        return passesSeverityFilter && passesAnalysisFilter;
      });
    },

    // Check if any filters are active
    isFiltersActive() {
      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;
    },

    // Filtered resolved issues based on selected filters
    filteredResolvedIssues() {
      // If no filters are selected, return all resolved issues
      if (this.resolvedFilters.category.length === 0 && this.resolvedFilters.analysisType.length === 0) {
        return this.resolvedIssues;
      }

      // Apply filters
      return this.resolvedIssues.filter(issue => {
        // Check if issue passes category filter
        const passesCategoryFilter = this.resolvedFilters.category.length === 0 ||
                                  this.resolvedFilters.category.includes(issue.category);

        // Check if issue passes analysis type filter
        const passesAnalysisFilter = this.resolvedFilters.analysisType.length === 0 ||
                                  this.resolvedFilters.analysisType.includes(issue.analysisType);

        // Issue must pass both filters
        return passesCategoryFilter && passesAnalysisFilter;
      });
    },

    // Check if any resolved issue filters are active
    isResolvedFiltersActive() {
      return this.resolvedFilters.category.length > 0 || this.resolvedFilters.analysisType.length > 0;
    },

    // Get unique categories from resolved issues for filtering
    resolvedCategories() {
      const categories = new Set();
      this.resolvedIssues.forEach(issue => {
        categories.add(issue.category);
      });
      return Array.from(categories);
    },

    // Filtered outstanding issues based on selected filters
    filteredOutstandingIssues() {
      // If no filters are selected, return all outstanding issues
      if (this.outstandingFilters.category.length === 0 && this.outstandingFilters.analysisType.length === 0) {
        return this.outstandingIssues;
      }

      // Apply filters
      return this.outstandingIssues.filter(issue => {
        // Check if issue passes category filter
        const passesCategoryFilter = this.outstandingFilters.category.length === 0 ||
                                  this.outstandingFilters.category.includes(issue.category);

        // Check if issue passes analysis type filter
        const passesAnalysisFilter = this.outstandingFilters.analysisType.length === 0 ||
                                  this.outstandingFilters.analysisType.includes(issue.analysisType);

        // Issue must pass both filters
        return passesCategoryFilter && passesAnalysisFilter;
      });
    },

    // Check if any outstanding issue filters are active
    isOutstandingFiltersActive() {
      return this.outstandingFilters.category.length > 0 || this.outstandingFilters.analysisType.length > 0;
    },

    // Get unique categories from outstanding issues for filtering
    outstandingCategories() {
      const categories = new Set();
      this.outstandingIssues.forEach(issue => {
        categories.add(issue.category);
      });
      return Array.from(categories);
    }
  },
  watch: {
    pqeOwner: {
      immediate: true,
      handler(newValue, oldValue) {
        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);
        if (newValue) {
          // Reset the group selection when PQE owner changes
          this.rootCauseSelectedGroup = 'all';
          this.loadDashboardData();
        }
      }
    }
  },
  methods: {
    loadDashboardData() {
      this.loadCriticalIssues();
      this.loadValidationCounts();
      this.loadResolvedIssues();
      this.loadOutstandingIssues();
      this.loadRootCauseData();
    },

    async loadOutstandingIssues() {
      console.log(`Loading outstanding issues for PQE owner: ${this.pqeOwner}`);
      this.isLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch outstanding issues from the API
        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter outstanding issues to only include those related to this PQE's breakout groups
          const allOutstandingIssues = data.outstanding_issues || [];

          // If we have breakout groups, filter issues to only include those for this PQE's groups
          if (this.breakoutGroups.length > 0) {
            this.outstandingIssues = allOutstandingIssues.filter(issue => {
              // Check if the issue's category matches any of the PQE's breakout groups
              return this.breakoutGroups.some(group =>
                issue.category.includes(group.name) || group.name.includes(issue.category)
              );
            });
          } else {
            // If we don't have breakout groups yet, use all issues
            this.outstandingIssues = allOutstandingIssues;
          }

          // Update performance data for outstanding issues
          if (data.performance_data) {
            // Merge with existing performance data
            this.performanceData = {
              ...this.performanceData,
              ...data.performance_data
            };
          }

          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for ${this.pqeOwner}'s groups`);
        } else {
          console.error('Failed to load outstanding issues:', data.message);
          // Use sample data for development
          this.loadSampleOutstandingIssues();
        }
      } catch (error) {
        console.error('Error loading outstanding issues:', error);
        // Use sample data for development
        this.loadSampleOutstandingIssues();
      } finally {
        this.isLoading = false;
      }
    },

    loadSampleOutstandingIssues() {
      // Sample data for development
      this.outstandingIssues = [
        {
          id: 'oi1',
          category: 'Fan Themis',
          month: '2024-06',
          severity: 'medium',
          increaseMultiplier: '1.4',
          aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',
          comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',
          analysisType: 'Root Cause',
          acceptanceDate: '2024-04-10',
          currentPerformance: '1.3x',
          acceptedBy: 'Engineering Team'
        },
        {
          id: 'oi2',
          category: 'Victoria Crypto',
          month: '2024-05',
          severity: 'medium',
          increaseMultiplier: '1.3',
          aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known power delivery issue that has been accepted as within tolerance. Monitoring for any significant changes.',
          comment: 'Power delivery variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',
          analysisType: 'Root Cause',
          acceptanceDate: '2024-03-22',
          currentPerformance: '1.2x',
          acceptedBy: 'Quality Team'
        },
        {
          id: 'oi3',
          category: 'Quantum Nexus',
          month: '2024-04',
          severity: 'medium',
          increaseMultiplier: '1.2',
          aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate. This is a known process variation that has been accepted. Monitoring for any significant changes.',
          comment: 'Process variation is within accepted tolerance. Monitoring monthly for any significant changes.',
          analysisType: 'Root Cause',
          acceptanceDate: '2024-02-15',
          currentPerformance: '1.1x',
          acceptedBy: 'Manufacturing Team'
        },
        {
          id: 'oi4',
          category: 'Stellar Core',
          month: '2024-05',
          severity: 'medium',
          increaseMultiplier: '1.3',
          aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',
          comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',
          analysisType: 'Root Cause',
          acceptanceDate: '2024-04-05',
          currentPerformance: '1.2x',
          acceptedBy: 'Engineering Team'
        },
        {
          id: 'oi5',
          category: 'Nebula Drive',
          month: '2024-06',
          severity: 'medium',
          increaseMultiplier: '1.4',
          aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',
          comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',
          analysisType: 'Root Cause',
          acceptanceDate: '2024-05-12',
          currentPerformance: '1.3x',
          acceptedBy: 'Product Team'
        }
      ];

      // Add performance data for outstanding issues
      const outstandingPerformanceData = {
        'Fan Themis': [
          { month: 'Apr 2024', xFactor: 1.5 },
          { month: 'May 2024', xFactor: 1.4 },
          { month: 'Jun 2024', xFactor: 1.3 }
        ],
        'Victoria Crypto': [
          { month: 'Mar 2024', xFactor: 1.4 },
          { month: 'Apr 2024', xFactor: 1.3 },
          { month: 'May 2024', xFactor: 1.2 },
          { month: 'Jun 2024', xFactor: 1.2 }
        ],
        'Quantum Nexus': [
          { month: 'Feb 2024', xFactor: 1.3 },
          { month: 'Mar 2024', xFactor: 1.2 },
          { month: 'Apr 2024', xFactor: 1.2 },
          { month: 'May 2024', xFactor: 1.1 },
          { month: 'Jun 2024', xFactor: 1.1 }
        ],
        'Stellar Core': [
          { month: 'Apr 2024', xFactor: 1.4 },
          { month: 'May 2024', xFactor: 1.3 },
          { month: 'Jun 2024', xFactor: 1.2 }
        ],
        'Nebula Drive': [
          { month: 'May 2024', xFactor: 1.5 },
          { month: 'Jun 2024', xFactor: 1.3 }
        ]
      };

      // Merge with existing performance data
      this.performanceData = {
        ...this.performanceData,
        ...outstandingPerformanceData
      };
    },

    async loadResolvedIssues() {
      console.log(`Loading resolved issues for PQE owner: ${this.pqeOwner}`);
      this.isLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch resolved issues from the API
        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter resolved issues to only include those related to this PQE's breakout groups
          const allResolvedIssues = data.resolved_issues || [];

          // If we have breakout groups, filter issues to only include those for this PQE's groups
          if (this.breakoutGroups.length > 0) {
            this.resolvedIssues = allResolvedIssues.filter(issue => {
              // Check if the issue's category matches any of the PQE's breakout groups
              return this.breakoutGroups.some(group =>
                issue.category.includes(group.name) || group.name.includes(issue.category)
              );
            });
          } else {
            // If we don't have breakout groups yet, use all issues
            this.resolvedIssues = allResolvedIssues;
          }

          // Load performance data for resolved issues
          if (data.performance_data) {
            this.performanceData = data.performance_data;
          }

          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for ${this.pqeOwner}'s groups`);
        } else {
          console.error('Failed to load resolved issues:', data.message);
          // Use sample data for development
          this.loadSampleResolvedIssues();
        }
      } catch (error) {
        console.error('Error loading resolved issues:', error);
        // Use sample data for development
        this.loadSampleResolvedIssues();
      } finally {
        this.isLoading = false;
      }
    },

    loadSampleResolvedIssues() {
      // Sample data for development
      this.resolvedIssues = [
        {
          id: 'ri1',
          category: 'Fan Themis',
          month: '2024-05',
          severity: 'high',
          increaseMultiplier: '2.8',
          aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',
          comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',
          analysisType: 'Root Cause',
          resolutionDate: '2024-05-15',
          currentPerformance: '0.9x'
        },
        {
          id: 'ri2',
          category: 'Victoria Crypto',
          month: '2024-04',
          severity: 'medium',
          increaseMultiplier: '1.7',
          aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific power delivery problem. Process was corrected.',
          comment: 'Improved power delivery design and quality control. Monitoring new units closely.',
          analysisType: 'Root Cause',
          resolutionDate: '2024-04-28',
          currentPerformance: '0.8x'
        },
        {
          id: 'ri3',
          category: 'Quantum Nexus',
          month: '2024-03',
          severity: 'high',
          increaseMultiplier: '2.2',
          aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',
          comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',
          analysisType: 'Root Cause',
          resolutionDate: '2024-03-20',
          currentPerformance: '0.7x'
        }
      ];

      // Sample performance data
      this.performanceData = {
        'Fan Themis': [
          { month: 'May 2024', xFactor: 2.8 },
          { month: 'Jun 2024', xFactor: 0.9 }
        ],
        'Victoria Crypto': [
          { month: 'Mar 2024', xFactor: 1.6 },
          { month: 'Apr 2024', xFactor: 1.7 },
          { month: 'May 2024', xFactor: 1.2 },
          { month: 'Jun 2024', xFactor: 0.8 }
        ],
        'Quantum Nexus': [
          { month: 'Mar 2024', xFactor: 2.2 },
          { month: 'Apr 2024', xFactor: 1.1 },
          { month: 'May 2024', xFactor: 0.8 },
          { month: 'Jun 2024', xFactor: 0.7 }
        ]
      };
    },

    async loadCriticalIssues() {
      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);
      this.isLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // First, load the breakout groups for this PQE owner
        await this.loadBreakoutGroups();

        // Fetch critical issues from the API
        const response = await fetch('/api-statit2/get_pqe_critical_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter critical issues to only include those related to this PQE's breakout groups
          const allIssues = data.critical_issues || [];

          // If we have breakout groups, filter issues to only include those for this PQE's groups
          if (this.breakoutGroups.length > 0) {
            this.unresolvedCriticalIssues = allIssues.filter(issue => {
              // Check if the issue's category matches any of the PQE's breakout groups
              return this.breakoutGroups.some(group =>
                issue.category.includes(group.name) || group.name.includes(issue.category)
              );
            });
          } else {
            // If we don't have breakout groups yet, use all issues
            this.unresolvedCriticalIssues = allIssues;
          }

          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);
        } else {
          console.error('Failed to load critical issues:', data.message);
          // Use sample data for development
          this.loadSampleCriticalIssues();
        }
      } catch (error) {
        console.error('Error loading critical issues:', error);
        // Use sample data for development
        this.loadSampleCriticalIssues();
      } finally {
        this.isLoading = false;
      }
    },

    async loadBreakoutGroups() {
      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch breakout groups from the API
        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.breakoutGroups = data.breakout_groups || [];
          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);

          // Reload root cause chart data now that we have the correct breakout groups
          await this.loadRootCauseData();
        } else {
          console.error('Failed to load breakout groups:', data.message);
          // Use sample data for development
          this.loadSampleBreakoutGroups();
          // Reload root cause chart data with sample groups
          await this.loadRootCauseData();
        }
      } catch (error) {
        console.error('Error loading breakout groups:', error);
        // Use sample data for development
        this.loadSampleBreakoutGroups();
        // Reload root cause chart data with sample groups
        await this.loadRootCauseData();
      }
    },

    async loadSampleBreakoutGroups() {
      // Sample data for development
      if (this.pqeOwner === 'Albert G.') {
        this.breakoutGroups = [
          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },
          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },
          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }
        ];
      } else if (this.pqeOwner === 'Sarah L.') {
        this.breakoutGroups = [
          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },
          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }
        ];
      } else {
        // Default sample data
        this.breakoutGroups = [
          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },
          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }
        ];
      }

      // Reload root cause chart data with the sample groups
      await this.loadRootCauseData();
    },

    loadSampleCriticalIssues() {
      // Sample data for development
      this.unresolvedCriticalIssues = [
        {
          id: 'ci1',
          category: 'Fan Themis',
          month: '2024-06',
          severity: 'high',
          increaseMultiplier: '3.2',
          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',
          comment: '',
          analysisType: 'Root Cause'
        },
        {
          id: 'ci2',
          category: 'Victoria Crypto',
          month: '2024-06',
          severity: 'medium',
          increaseMultiplier: '1.8',
          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',
          comment: '',
          analysisType: 'Root Cause'
        },

      ];

      // Set new critical issues to empty array for now
      this.newCriticalIssues = [];
    },

    async loadValidationCounts() {
      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch validation counts from the API
        const response = await fetch('/api-statit2/get_validation_counts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.validatedCount = data.validated_count || 0;
          this.unvalidatedCount = data.unvalidated_count || 0;
          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);
        } else {
          console.error('Failed to load validation counts:', data.message);
          // Use sample data for development
          this.validatedCount = 125;
          this.unvalidatedCount = 37;
        }
      } catch (error) {
        console.error('Error loading validation counts:', error);
        // Use sample data for development
        this.validatedCount = 125;
        this.unvalidatedCount = 37;
      }
    },

    toggleCriticalIssuesExpanded() {
      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;
    },

    toggleResolvedIssuesExpanded() {
      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;
    },

    toggleOutstandingIssuesExpanded() {
      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;
    },

    toggleIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedIssueIds.splice(index, 1);
      }
    },

    toggleResolvedIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedResolvedIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedResolvedIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedResolvedIssueIds.splice(index, 1);
      }
    },

    toggleOutstandingIssueExpanded(issue) {
      const issueId = issue.id;
      const index = this.expandedOutstandingIssueIds.indexOf(issueId);

      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedOutstandingIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedOutstandingIssueIds.splice(index, 1);
      }
    },

    isIssueExpanded(issueId) {
      return this.expandedIssueIds.includes(issueId);
    },

    isResolvedIssueExpanded(issueId) {
      return this.expandedResolvedIssueIds.includes(issueId);
    },

    isOutstandingIssueExpanded(issueId) {
      return this.expandedOutstandingIssueIds.includes(issueId);
    },

    markIssueAsResolved(issue) {
      console.log('Mark issue as resolved:', issue);

      // In a real implementation, this would call an API to update the issue status
      // For now, we'll just move the issue from unresolved to resolved

      // Create a resolved issue object with additional fields
      const resolvedIssue = {
        ...issue,
        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
        currentPerformance: '1.0x' // Initial performance after resolution
      };

      // Add to resolved issues
      this.resolvedIssues.push(resolvedIssue);

      // Remove from unresolved issues
      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);
      if (index !== -1) {
        this.unresolvedCriticalIssues.splice(index, 1);
      }

      // Show success message
      alert(`Issue marked as resolved: ${issue.category}`);
    },

    markIssueAsOutstanding(issue) {
      console.log('Mark issue as outstanding:', issue);

      // In a real implementation, this would call an API to update the issue status
      // For now, we'll just move the issue from unresolved to outstanding

      // Create an outstanding issue object with additional fields
      const outstandingIssue = {
        ...issue,
        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier
        acceptedBy: 'Engineering Team' // Default value
      };

      // Add to outstanding issues
      this.outstandingIssues.push(outstandingIssue);

      // Remove from unresolved issues
      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);
      if (index !== -1) {
        this.unresolvedCriticalIssues.splice(index, 1);
      }

      // Show success message
      alert(`Issue marked as outstanding: ${issue.category}`);
    },

    handleSeverityFilterChange() {
      // Update the selected filters based on the severity dropdown
      if (this.severityFilter === 'all') {
        this.selectedFilters.severity = [];
      } else {
        this.selectedFilters.severity = [this.severityFilter];
      }
    },

    handleAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.analysisTypeFilter === 'all') {
        this.selectedFilters.analysisType = [];
      } else {
        this.selectedFilters.analysisType = [this.analysisTypeFilter];
      }
    },

    clearFilters() {
      this.selectedFilters.severity = [];
      this.selectedFilters.analysisType = [];
      this.severityFilter = 'all';
      this.analysisTypeFilter = 'all';
    },

    handleResolvedCategoryFilterChange() {
      // Update the selected filters based on the category dropdown
      if (this.resolvedCategoryFilter === 'all') {
        this.resolvedFilters.category = [];
      } else {
        this.resolvedFilters.category = [this.resolvedCategoryFilter];
      }
    },

    handleResolvedAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.resolvedAnalysisTypeFilter === 'all') {
        this.resolvedFilters.analysisType = [];
      } else {
        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];
      }
    },

    clearResolvedFilters() {
      this.resolvedFilters.category = [];
      this.resolvedFilters.analysisType = [];
      this.resolvedCategoryFilter = 'all';
      this.resolvedAnalysisTypeFilter = 'all';
    },

    handleOutstandingCategoryFilterChange() {
      // Update the selected filters based on the category dropdown
      if (this.outstandingCategoryFilter === 'all') {
        this.outstandingFilters.category = [];
      } else {
        this.outstandingFilters.category = [this.outstandingCategoryFilter];
      }
    },

    handleOutstandingAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.outstandingAnalysisTypeFilter === 'all') {
        this.outstandingFilters.analysisType = [];
      } else {
        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];
      }
    },

    clearOutstandingFilters() {
      this.outstandingFilters.category = [];
      this.outstandingFilters.analysisType = [];
      this.outstandingCategoryFilter = 'all';
      this.outstandingAnalysisTypeFilter = 'all';
    },

    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {
      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);

      // Emit event to update action tracker
      this.$emit('update-action-tracker', {
        issueId: issue.id,
        category: issue.category,
        comment: issue.comment,
        severity: issue.severity,
        pqeOwner: this.pqeOwner,
        month: issue.month,
        analysisType: issue.analysisType,
        resolved: markAsResolved,
        outstanding: markAsOutstanding
      });

      // If marking as resolved, move the issue to resolved issues
      if (markAsResolved) {
        this.markIssueAsResolved(issue);
      }
      // If marking as outstanding, move the issue to outstanding issues
      else if (markAsOutstanding) {
        this.markIssueAsOutstanding(issue);
      }
      else {
        // Show success message for regular update
        alert(`Action tracker updated for issue: ${issue.category}`);
      }
    },

    viewPerformanceData(issue) {
      console.log('View performance data for:', issue.category);

      // In a real implementation, this would show a modal or chart with performance data
      // For now, we'll just show an alert with the data

      const performanceData = this.performanceData[issue.category];
      if (performanceData && performanceData.length > 0) {
        const performanceText = performanceData
          .map(data => `${data.month}: ${data.xFactor}x`)
          .join('\n');

        alert(`Performance data for ${issue.category}:\n${performanceText}`);
      } else {
        alert(`No performance data available for ${issue.category}`);
      }
    },

    viewIssueDetails(issue) {
      console.log('View issue details for:', issue.category);
      // In a real implementation, this would show a modal or navigate to a detailed view
      alert(`Viewing details for issue: ${issue.category}\nMonth: ${issue.month}\nSeverity: ${issue.severity}\nMultiplier: ${issue.increaseMultiplier}x`);
    },

    // Root Cause Chart methods
    async loadRootCauseData() {
      console.log('Loading root cause chart data for PQE Owner Dashboard');
      this.isRootCauseDataLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Calculate date range based on selected time range
        const endDate = new Date();
        const startDate = new Date();
        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current
        startDate.setMonth(endDate.getMonth() - monthsToFetch);

        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;

        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);

        // Call the new API endpoint
        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner,
            startDate: startDateStr,
            endDate: endDateStr,
            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Transform the API data to chart format
          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);
          console.log('Root cause chart data loaded:', this.rootCauseChartData);
          console.log('Chart data length:', this.rootCauseChartData.length);
          console.log('Sample data point:', this.rootCauseChartData[0]);
          console.log('Breakout groups:', data.breakoutGroups);
          console.log('Part numbers:', data.partNumbers);

          // Force chart update
          this.$nextTick(() => {
            console.log('Chart data after nextTick:', this.rootCauseChartData.length);
          });
        } else {
          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);
          // Fall back to mock data
          this.rootCauseChartData = this.generateMockRootCauseData();
          console.log('Using mock data:', this.rootCauseChartData.length);
        }
      } catch (error) {
        console.error('Error loading root cause data:', error);
        // Fall back to mock data
        this.rootCauseChartData = this.generateMockRootCauseData();
      } finally {
        this.isRootCauseDataLoading = false;
      }
    },

    transformRootCauseData(categoryData) {
      // Transform the API response into chart format
      const chartData = [];

      // categoryData structure: { "category": { "2024-03": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }
      for (const [category, monthData] of Object.entries(categoryData)) {
        // Clean up category name (trim whitespace)
        const cleanCategory = category.trim();

        for (const [month, data] of Object.entries(monthData)) {
          chartData.push({
            group: cleanCategory,
            key: month,
            value: parseFloat(data.failRate.toFixed(2))
          });
        }
      }

      console.log('Transformed root cause data:', chartData);
      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);
      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);
      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));
      return chartData;
    },

    generateMockRootCauseData() {
      // Use the actual breakout groups for this PQE owner instead of generic categories
      let categories = [];

      if (this.breakoutGroups && this.breakoutGroups.length > 0) {
        // Use the actual breakout groups for this PQE owner
        categories = this.breakoutGroups.map(group => group.name);

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      } else {
        // Fallback to sample data if breakout groups aren't loaded yet
        if (this.pqeOwner === 'Albert G.') {
          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];
        } else if (this.pqeOwner === 'Sarah L.') {
          categories = ['Stellar Core', 'Nebula Drive'];
        } else {
          categories = ['Sample Group 1', 'Sample Group 2'];
        }

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      }

      // Generate months based on selected time range
      const now = new Date();
      const months = [];
      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;

      for (let i = monthsToGenerate - 1; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        months.push(`${year}-${month}`);
      }

      const data = [];

      months.forEach(month => {
        categories.forEach(category => {
          // Generate realistic fail rate data (0-5% range)
          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%
          const variation = (Math.random() - 0.5) * 1; // ±0.5%
          const failRate = Math.max(0.1, baseRate + variation);

          data.push({
            group: category,
            key: month,
            value: parseFloat(failRate.toFixed(2))
          });
        });
      });

      return data;
    },

    handleRootCauseBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);

        // You can implement additional functionality here, such as showing more details
        // or navigating to the MetisXFactors Group tab with this specific category
        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\nFail Rate: ${clickedData.value}%`);
      }
    },

    handleRootCauseTimeRangeChange() {
      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);
      this.loadRootCauseData();
    },

    handleRootCauseGroupChange() {
      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);
      console.log('Current chart data length before reload:', this.rootCauseChartData.length);
      this.loadRootCauseData();
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    }
  }
};
</script>

<style scoped>
.pqe-owner-dashboard-container {
  color: #f4f4f4;
}

.content-wrapper {
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 400;
}

.last-updated-text {
  font-size: 0.875rem;
  color: #8d8d8d;
}

.key-metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background-color: #262626;
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  border: 1px solid #333333;
}

.metric-icon {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.metric-icon.new-issues {
  background-color: rgba(15, 98, 254, 0.1);
  color: #0f62fe;
}

.metric-icon.critical-issues {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.metric-icon.validated {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.metric-icon.unvalidated {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.metric-description {
  font-size: 0.75rem;
  color: #8d8d8d;
}

.chart-section {
  margin-bottom: 1.5rem;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background-color: #262626;
  border-bottom: 1px solid #333333;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  white-space: nowrap;
}

.control-dropdown {
  width: 200px;
}

.chart-container {
  padding: 1rem;
  background-color: #161616;
  border-radius: 8px;
  margin: 1rem;
}

.section-footer {
  padding: 0 1.25rem 1.25rem;
}

.section-description {
  margin: 0;
  font-size: 0.875rem;
  color: #8d8d8d;
  line-height: 1.4;
}

.dashboard-main-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.dashboard-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-card {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background-color: #333333;
}

.section-title-container {
  display: flex;
  flex-direction: column;
}

.section-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 400;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-top: 0.25rem;
}

.section-controls {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fa4d56;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 0.75rem;
}

.status-indicator.flashing {
  animation: flash 2s infinite;
}

.status-indicator.resolved-indicator {
  background-color: #24a148;
}

.status-indicator.outstanding-indicator {
  background-color: #0f62fe;
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.expand-indicator {
  transition: transform 0.2s ease;
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding: 0 1.25rem 1.25rem;
}

.filter-container {
  margin-bottom: 1.5rem;
  background-color: #333333;
  padding: 1rem;
  border-radius: 8px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  white-space: nowrap;
}

.filter-dropdown {
  width: 200px;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-card {
  background-color: #333333;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.issue-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.issue-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  position: relative;
}

.issue-tags {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-title {
  flex-grow: 1;
  font-weight: 500;
}

.issue-metadata {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-multiplier {
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.issue-multiplier.high-severity {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.issue-multiplier.medium-severity {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.issue-multiplier.good-performance {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.issue-multiplier.medium-performance {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.resolved-issue-card {
  border-left: 4px solid #24a148;
}

.outstanding-issue-card {
  border-left: 4px solid #0f62fe;
}

.resolution-details, .acceptance-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #c6c6c6;
}

.issue-multiplier.low-performance {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.resolution-comment, .acceptance-comment {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #333333;
  border-radius: 4px;
}

.comment-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #c6c6c6;
}

.comment-text {
  font-size: 0.875rem;
  white-space: pre-wrap;
}

.issue-content {
  padding: 0 1rem 1rem;
  border-top: 1px solid #444444;
}

.ai-description {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #262626;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.5;
}

.action-comment {
  margin-bottom: 1rem;
}

.issue-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.no-data-message {
  color: #8d8d8d;
  font-size: 1rem;
  text-align: center;
  padding: 2rem;
}

@media (max-width: 768px) {
  .key-metrics-section {
    grid-template-columns: 1fr;
  }

  .dashboard-main-content {
    grid-template-columns: 1fr;
  }

  .filter-controls {
    flex-direction: column;
  }

  .filter-group {
    width: 100%;
  }
}
</style>
