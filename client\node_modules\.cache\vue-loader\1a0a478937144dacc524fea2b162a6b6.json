{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue", "mtime": 1748527827772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MetisXFactors.vue"], "names": [], "mappings": ";AAgxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MetisXFactors.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"metis-xfactors-container\">\n    <MainHeader title=\"Metis XFactors Analysis\" />\n\n    <div class=\"bx--grid\">\n      <div class=\"bx--row\">\n        <div class=\"bx--col-lg-16\">\n          <!-- Global Date Controls -->\n          <cv-tile class=\"global-controls-tile\">\n            <div class=\"global-controls\">\n              <div class=\"date-controls-container\">\n                <div class=\"date-controls-layout\">\n                  <div class=\"custom-range-section\">\n                    <div class=\"date-inputs\">\n                      <div class=\"date-section-label\">Start Date:</div>\n                      <div class=\"date-dropdown-container\">\n                        <cv-dropdown\n                          id=\"start-month\"\n                          v-model=\"startMonthStr\"\n                          @change=\"updateStartDate\"\n                          class=\"month-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"(month, index) in months\"\n                            :key=\"index\"\n                            :value=\"String(index+1)\"\n                          >\n                            {{ month }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                        <cv-dropdown\n                          id=\"start-year\"\n                          v-model=\"startYear\"\n                          @change=\"updateStartDate\"\n                          class=\"year-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"year in availableYears\"\n                            :key=\"year\"\n                            :value=\"year\"\n                          >\n                            {{ year }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                      </div>\n\n                      <div class=\"date-section-label\">End Date:</div>\n                      <div class=\"date-dropdown-container\">\n                        <cv-dropdown\n                          id=\"end-month\"\n                          v-model=\"endMonthStr\"\n                          @change=\"updateEndDate\"\n                          class=\"month-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"(month, index) in months\"\n                            :key=\"index\"\n                            :value=\"String(index+1)\"\n                          >\n                            {{ month }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                        <cv-dropdown\n                          id=\"end-year\"\n                          v-model=\"endYear\"\n                          @change=\"updateEndDate\"\n                          class=\"year-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"year in availableYears\"\n                            :key=\"year\"\n                            :value=\"year\"\n                          >\n                            {{ year }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                      </div>\n                    </div>\n\n                    <div class=\"analyze-button-container\">\n                      <cv-button @click=\"analyzeAllData\">Analyze Data</cv-button>\n                    </div>\n                  </div>\n\n                  <div class=\"date-separator\">\n                    <span class=\"or-text\">OR</span>\n                  </div>\n\n                  <div class=\"quick-select-section\">\n                    <div class=\"date-section-label\">Quick Select:</div>\n                    <cv-dropdown\n                      id=\"quick-select\"\n                      v-model=\"selectedTimeRange\"\n                      @change=\"applyTimeRange\"\n                      class=\"quick-select-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"\">Custom Range</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-month\">Last Month</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-3-months\">Last 3 Months</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-6-months\">Last 6 Months</cv-dropdown-item>\n                      <cv-dropdown-item value=\"ytd\">Year to Date</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tile>\n\n          <!-- Tab Navigation -->\n          <cv-tabs v-model=\"activeTab\">\n            <!-- Heatmap Tab (renamed from Dashboard) -->\n            <cv-tab label=\"Heatmap\" :selected=\"activeTab === 0\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"dashboard-controls\">\n                  <div class=\"dashboard-header\">\n                    <h3>XFactor Heatmap</h3>\n                  </div>\n\n                  <div class=\"last-updated-text\">\n                    Last Updated: {{ new Date().toLocaleString() }}\n                  </div>\n                </div>\n\n                <div class=\"dashboard-content\">\n                  <!-- Critical Issues Summary -->\n                  <div class=\"dashboard-critical-issues-summary\" v-if=\"dashboardData.length > 0 && currentMonthCriticalIssues > 0\">\n                    <h4>\n                      <span class=\"critical-count\">{{ currentMonthCriticalIssues }}</span> critical issues detected this month ({{ getCurrentMonthName() }})\n                    </h4>\n                  </div>\n\n                  <div class=\"heatmap-container\" v-if=\"dashboardData.length > 0\">\n                    <div class=\"heatmap-header\">\n                      <h4>XFactor Status Heatmap</h4>\n\n                      <div class=\"heatmap-controls\">\n                        <div class=\"filter-container\">\n                          <div class=\"filter-type\">\n                            <label for=\"filterType\">Filter By:</label>\n                            <cv-dropdown\n                              id=\"filterType\"\n                              v-model=\"selectedFilterType\"\n                              @change=\"onFilterTypeChange\"\n                              class=\"carbon-dropdown filter-type-dropdown\"\n                            >\n                              <cv-dropdown-item\n                                v-for=\"type in filterTypes\"\n                                :key=\"type.value\"\n                                :value=\"type.value\"\n                              >\n                                {{ type.text }}\n                              </cv-dropdown-item>\n                            </cv-dropdown>\n                          </div>\n\n                          <div class=\"owner-filter\">\n                            <label for=\"ownerSelect\">{{ selectedFilterType === 'group' ? 'Commodity' : selectedFilterType === 'dev_owner' ? 'Dev Owner' : 'PQE Owner' }}:</label>\n                            <cv-dropdown\n                              id=\"ownerSelect\"\n                              v-model=\"selectedOwner\"\n                              @change=\"onOwnerChange\"\n                              class=\"carbon-dropdown owner-dropdown\"\n                            >\n                              <cv-dropdown-item\n                                v-for=\"option in ownerOptions[selectedFilterType]\"\n                                :key=\"option\"\n                                :value=\"option\"\n                              >\n                                {{ option }}\n                              </cv-dropdown-item>\n                            </cv-dropdown>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"heatmap-legend\">\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #0062ff;\"></div>\n                        <span>Normal</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #ff9a00;\"></div>\n                        <span>Sustained Problem</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #fa4d56;\"></div>\n                        <span>Short-Term Spike</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #da1e28;\"></div>\n                        <span>Critical (Both)</span>\n                      </div>\n                    </div>\n\n                    <div class=\"heatmap-table-container\">\n                      <table class=\"heatmap-table\">\n                        <thead>\n                          <tr>\n                            <th>Breakout Group</th>\n                            <th v-for=\"(month, index) in dashboardMonths\" :key=\"index\">{{ month }}</th>\n                            <th>Actions</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          <tr v-for=\"(row, rowIndex) in dashboardData\" :key=\"rowIndex\">\n                            <td\n                              class=\"breakout-name\"\n                              @mouseover=\"showAiSummaryTooltip($event)\"\n                              @mouseout=\"hideAiSummaryTooltip()\"\n                              @click=\"getAiSummary(row)\"\n                            >\n                              <div class=\"breakout-name-content\">\n                                {{ row.breakoutName }}\n                                <span class=\"ai-summary-indicator\" v-if=\"row.breakoutName === loadingAiSummaryFor\">\n                                  <span class=\"loading-dots\"></span>\n                                </span>\n                              </div>\n                            </td>\n                            <td\n                              v-for=\"(cell, cellIndex) in row.months\"\n                              :key=\"cellIndex\"\n                              :class=\"getCellClass(cell)\"\n                              @mouseover=\"showCellTooltip(cell, $event)\"\n                              @mouseout=\"hideCellTooltip()\"\n                              @click=\"selectBreakoutFromDashboardWithMonth(row.breakoutName, cell.month)\"\n                              style=\"cursor: pointer;\"\n                            >\n                              <div class=\"cell-content\">\n                                <span v-if=\"cell.xFactor !== null && cell.xFactor !== undefined\">{{ cell.xFactor.toFixed(1) }}</span>\n                                <span v-else>0.0</span>\n                              </div>\n                            </td>\n                            <td class=\"view-data-cell\">\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"selectBreakoutFromDashboard(row.breakoutName)\"\n                              >\n                                View Data\n                              </cv-button>\n                            </td>\n                          </tr>\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n\n                  <div class=\"no-data-message\" v-if=\"dashboardNoDataMessage\">\n                    {{ dashboardNoDataMessage }}\n                  </div>\n                </div>\n\n                <!-- Cell tooltip -->\n                <div class=\"cell-tooltip\" v-if=\"showTooltip\" :style=\"tooltipStyle\">\n                  <div class=\"tooltip-header\">\n                    <strong>{{ tooltipData.breakoutName }}</strong>\n                    <span>{{ tooltipData.month }}</span>\n                  </div>\n                  <div class=\"tooltip-content\">\n                    <p>XFactor: {{ tooltipData.xFactor !== null && tooltipData.xFactor !== undefined ? tooltipData.xFactor.toFixed(2) : '0.00' }}</p>\n                    <p>Target Rate: {{ typeof tooltipData.targetRate === 'number' ? tooltipData.targetRate.toFixed(6) : tooltipData.targetRate }}</p>\n                    <p>Status: {{ tooltipData.status || 'Normal' }}</p>\n                    <p v-if=\"tooltipData.duration\">Duration: {{ tooltipData.duration }} months</p>\n                    <p>Defects: {{ tooltipData.defects !== null && tooltipData.defects !== undefined ? tooltipData.defects : 0 }}</p>\n                    <p>Volume: {{ tooltipData.volume !== null && tooltipData.volume !== undefined ? tooltipData.volume : 0 }}</p>\n                    <p v-if=\"tooltipData.criticalIssues > 0\" class=\"critical-issues-tooltip\">\n                      <strong>{{ tooltipData.criticalIssues }} critical issues detected</strong>\n                    </p>\n                    <p class=\"click-to-view-tooltip\">Click to view detailed analysis</p>\n                  </div>\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n            <!-- XFactor Analysis Tab (renamed to Overall) -->\n            <cv-tab label=\"Overall\" :selected=\"activeTab === 1\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"controls-section\">\n                  <div class=\"selected-date-range\">\n                    <p>Selected Date Range: {{ formatDateRange(startDate, endDate) }}</p>\n                  </div>\n                </div>\n\n                <div class=\"breakout-selection\">\n                  <h4>Select Breakout Group</h4>\n                  <div class=\"breakout-dropdown\">\n                    <cv-dropdown\n                      v-model=\"selectedBreakout\"\n                      @change=\"handleBreakoutChange\"\n                      :disabled=\"isLoading || breakoutNames.length === 0\"\n                    >\n                      <cv-dropdown-item value=\"\">All Breakout Groups ({{ breakoutNames.length }} total)</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"name in breakoutNames\"\n                        :key=\"name\"\n                        :value=\"name\"\n                      >\n                        {{ name }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                    <div v-if=\"isLoading\" class=\"loading-indicator\">Loading...</div>\n                    <div v-else-if=\"breakoutNames.length === 0 && !isLoading\" class=\"no-data-message\">\n                      No breakout groups available\n                    </div>\n                  </div>\n\n                  <!-- Part Numbers Section for Main Tab -->\n                  <div class=\"part-numbers-section\" v-if=\"selectedBreakout && mainTabPartNumbers.length > 0\">\n                    <h4>Part Numbers in {{ selectedBreakout }}</h4>\n                    <p class=\"part-count\">Total: {{ mainTabPartNumbers.length }} part numbers</p>\n                    <div class=\"part-numbers-container\">\n                      <div v-for=\"(pn, index) in mainTabPartNumbers\" :key=\"index\" class=\"part-number-item\">\n                        {{ pn }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"charts-section\">\n                  <div class=\"chart-container\" v-if=\"chartData.length > 0\">\n                    <h4>XFactor Trend Analysis</h4>\n                    <div class=\"chart-wrapper\">\n                      <LineChart\n                        :data=\"chartData\"\n                        :options=\"chartOptions\"\n                        :loading=\"isLoading\"\n                      />\n                    </div>\n\n                    <div class=\"threshold-info\">\n                      <div class=\"threshold-item\">\n                        <div class=\"threshold-color sustained\"></div>\n                        <span>Sustained Problem Threshold (X-Factor > 1.5 for 3+ months)</span>\n                      </div>\n                      <div class=\"threshold-item\">\n                        <div class=\"threshold-color spike\"></div>\n                        <span>Short-Term Spike Threshold (X-Factor > 3.0)</span>\n                      </div>\n                    </div>\n\n                    <div class=\"info-button\" @click=\"showInfoModal = true\">\n                      <span>ⓘ</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"alerts-container\" v-if=\"alerts.length > 0\">\n                  <h4>Alerts</h4>\n                  <table class=\"alerts-table\">\n                    <thead>\n                      <tr>\n                        <th>Breakout Group</th>\n                        <th>Status</th>\n                        <th>Period</th>\n                        <th>X-Factor</th>\n                        <th>Defects</th>\n                        <th>Volume</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      <tr v-for=\"(alert, index) in alerts\" :key=\"index\">\n                        <td>{{ alert.breakoutName }}</td>\n                        <td>\n                          <div class=\"status-indicator\" :class=\"getStatusClass(alert.status)\">\n                            {{ alert.status }}\n                          </div>\n                        </td>\n                        <td>{{ alert.period }}</td>\n                        <td>{{ alert.xFactor.toFixed(2) }}</td>\n                        <td>{{ alert.defects }}</td>\n                        <td>{{ alert.volume }}</td>\n                      </tr>\n                    </tbody>\n                  </table>\n                </div>\n\n                <div class=\"no-data-message\" v-if=\"noDataMessage\">\n                  {{ noDataMessage }}\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n\n\n            <!-- PQE Dashboard Tab -->\n            <cv-tab label=\"PQE Dashboard\" :selected=\"activeTab === 2\">\n              <PQEDashboard />\n            </cv-tab>\n\n            <!-- Group Tab -->\n            <cv-tab label=\"Group\" :selected=\"activeTab === 3\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"group2-tab-container\">\n                  <div class=\"group2-tab-header\">\n                    <h4>Advanced Group Analysis</h4>\n                    <p>Analyze breakout groups with different time periods and analysis types</p>\n                  </div>\n\n                  <div class=\"group2-tab-controls\">\n                    <div class=\"breakout-group-select\">\n                      <label for=\"group2-tab-select\">Select Breakout Group:</label>\n                      <cv-dropdown\n                        id=\"group2-tab-select\"\n                        v-model=\"group2TabSelectedGroup\"\n                        :disabled=\"isGroup2TabLoading || breakoutNames.length === 0\"\n                      >\n                        <cv-dropdown-item\n                          v-for=\"name in breakoutNames\"\n                          :key=\"name\"\n                          :value=\"name\"\n                        >\n                          {{ name }}\n                        </cv-dropdown-item>\n                      </cv-dropdown>\n                    </div>\n                  </div>\n\n                  <div class=\"group2-tab-content\" v-if=\"group2TabSelectedGroup\">\n                    <div class=\"analysis-grid\">\n                      <table class=\"analysis-table\">\n                        <thead>\n                          <tr>\n                            <th>Analysis Type</th>\n                            <th>1 Month</th>\n                            <th>3 Months</th>\n                            <th>6 Months</th>\n                            <th>Custom</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n\n                          <tr>\n                            <td>Root Cause Analysis</td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(1)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(3)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(6)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewCustomRootCauseAnalysis()\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                          </tr>\n\n                        </tbody>\n                      </table>\n                    </div>\n\n\n\n                    <!-- Root Cause Analysis Section -->\n                    <div class=\"root-cause-section\" v-if=\"rootCauseChartData.length > 0 || isRootCauseDataLoading\">\n                      <div class=\"section-header\">\n                        <h4>Root Cause Analysis - {{ group2TabSelectedGroup }} ({{ rootCauseTimeRange }})</h4>\n                      </div>\n\n                      <!-- AI Summary Section - Moved above chart -->\n                      <div class=\"ai-summary-section\" v-if=\"rootCauseAiSummary\">\n                        <div class=\"ai-summary-content\">\n                          <p>{{ rootCauseAiSummary }}</p>\n                        </div>\n                      </div>\n                      <div class=\"ai-summary-loading\" v-else-if=\"isRootCauseAiLoading\">\n                        <div class=\"loading-spinner\"></div>\n                        <span>Generating AI analysis...</span>\n                      </div>\n\n                      <!-- Chart Section -->\n                      <div class=\"root-cause-chart-container\" v-if=\"rootCauseChartData.length > 0\">\n                        <h5>Root Cause Categories</h5>\n                        <div class=\"chart-wrapper\">\n                          <StackedBarChart\n                            :data=\"rootCauseChartData\"\n                            :loading=\"isRootCauseDataLoading\"\n                            :height=\"'400px'\"\n                            :options=\"rootCauseChartOptions\"\n                            @bar-click=\"handleRootCauseBarClick\"\n                          />\n                        </div>\n                      </div>\n                      <div class=\"no-data-message\" v-else-if=\"isRootCauseDataLoading\">\n                        Loading root cause data...\n                      </div>\n                      <div class=\"no-data-message\" v-else>\n                        No root cause data available for this time period\n                      </div>\n\n                      <!-- Critical Issues Section -->\n                      <div class=\"critical-issues-section\" v-if=\"hasCriticalRootCauseIssue\">\n                        <h5>Critical Issues Detected</h5>\n\n                        <!-- Critical Issues List -->\n                        <div v-for=\"issue in criticalIssues\" :key=\"issue.id\" class=\"critical-issue-item\">\n                          <div class=\"critical-issue-header\" @click=\"toggleCriticalIssue(issue.id)\">\n                            <cv-tag\n                              :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                              :label=\"issue.severity === 'high' ? 'High Severity' : 'Medium Severity'\"\n                            />\n                            <span class=\"critical-issue-title\">{{ issue.category }}</span>\n                            <cv-tag\n                              kind=\"cool-gray\"\n                              class=\"month-tag\"\n                              :label=\"issue.month\"\n                            />\n                            <span class=\"critical-issue-multiplier\">\n                              <template v-if=\"issue.increaseMultiplier === '(new)'\">\n                                ({{ issue.increaseMultiplier }})\n                              </template>\n                              <template v-else>\n                                ({{ issue.increaseMultiplier }}x spike)\n                              </template>\n                            </span>\n                            <span class=\"critical-issue-status\" v-if=\"hasCriticalIssueBeenUpdated(issue.id)\">\n                              <cv-tag kind=\"green\" label=\"Updated\" />\n                            </span>\n                            <span class=\"expand-icon\">{{ isIssueExpanded(issue.id) ? '▼' : '▶' }}</span>\n                          </div>\n\n                          <div v-if=\"isIssueExpanded(issue.id)\" class=\"critical-issue-content\">\n                            <!-- AI Description -->\n                            <div class=\"critical-issue-ai-description\">\n                              <p>{{ issue.description }}</p>\n                            </div>\n\n                            <!-- Update Form -->\n                            <div class=\"critical-issue-update-form\">\n                              <h6>Provide an update on this issue:</h6>\n                              <cv-text-area\n                                :value=\"getCriticalIssueUpdateText(issue.id)\"\n                                @input=\"updateCriticalIssueText(issue.id, $event)\"\n                                placeholder=\"Enter your update on this critical issue...\"\n                                :label=\"'Update for ' + issue.category\"\n                                hide-label\n                                rows=\"3\"\n                              />\n                              <cv-button\n                                @click=\"saveCriticalIssueUpdate(issue.id, getCriticalIssueUpdateText(issue.id))\"\n                                :disabled=\"!getCriticalIssueUpdateText(issue.id).trim()\"\n                                class=\"save-update-button\"\n                              >\n                                Save Update\n                              </cv-button>\n                            </div>\n\n                            <!-- Previous Updates -->\n                            <div class=\"previous-updates\" v-if=\"getCriticalIssueHistory(issue.id).length > 0\">\n                              <h6>Previous Updates</h6>\n                              <cv-structured-list condensed>\n                                <template slot=\"headings\">\n                                  <cv-structured-list-heading>Date</cv-structured-list-heading>\n                                  <cv-structured-list-heading>Update</cv-structured-list-heading>\n                                </template>\n                                <template slot=\"items\">\n                                  <cv-structured-list-row\n                                    v-for=\"(update, index) in getCriticalIssueHistory(issue.id)\"\n                                    :key=\"index\"\n                                  >\n                                    <cv-structured-list-cell>{{ formatDate(update.timestamp) }}</cv-structured-list-cell>\n                                    <cv-structured-list-cell>{{ update.content }}</cv-structured-list-cell>\n                                  </cv-structured-list-row>\n                                </template>\n                              </cv-structured-list>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div class=\"no-data-message\" v-if=\"!group2TabSelectedGroup\">\n                    Please select a breakout group to analyze\n                  </div>\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n\n\n\n          </cv-tabs>\n        </div>\n      </div>\n    </div>\n\n    <!-- Info Modal -->\n    <div class=\"info-modal\" v-if=\"showInfoModal\">\n      <div class=\"modal-content\">\n        <span class=\"close-button\" @click=\"showInfoModal = false\">&times;</span>\n        <h3>About XFactor Analysis</h3>\n        <p>\n          The XFactor is a measure that compares the current defect rate to the target defect rate.\n          It helps identify when a part group is experiencing unusual quality issues.\n        </p>\n        <h4>How XFactor is Calculated:</h4>\n        <p>\n          XFactor = Current Period Defect Rate / Target Rate\n        </p>\n        <p>\n          Target rates are defined for each breakout group in the breakout_targets.xlsx file.\n        </p>\n        <h4>Alert Thresholds:</h4>\n        <ul>\n          <li><strong>Short-Term Spike:</strong> XFactor > 3.0 in a single month</li>\n          <li><strong>Sustained Problem:</strong> XFactor > 1.5 for three or more consecutive months</li>\n        </ul>\n        <p>\n          The data is grouped by \"Full Breakout Name\" from the Metis test data, allowing for analysis\n          of quality trends across different part categories.\n        </p>\n      </div>\n    </div>\n\n    <!-- AI Summary Modal -->\n    <div class=\"info-modal\" v-if=\"showAiSummaryModal\">\n      <div class=\"modal-content ai-summary-modal\">\n        <span class=\"close-button\" @click=\"showAiSummaryModal = false\">&times;</span>\n        <h3>AI Summary: {{ aiSummaryBreakoutName }}</h3>\n        <div v-if=\"isLoadingAiSummary\" class=\"ai-summary-loading\">\n          <div class=\"loading-spinner\"></div>\n          <p>Generating AI summary...</p>\n        </div>\n        <div v-else class=\"ai-summary-content\">\n          <p v-html=\"aiSummaryText\"></p>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Summary Tooltip -->\n    <div class=\"ai-tooltip\" v-if=\"showAiTooltip\" :style=\"aiTooltipStyle\">\n      <div class=\"ai-tooltip-content\">\n        <span>AI summary, click to see</span>\n      </div>\n    </div>\n\n    <!-- Failure Modes Modal -->\n    <cv-modal\n      :visible=\"showFailureModesModal\"\n      @modal-hidden=\"showFailureModesModal = false\"\n      class=\"failure-modes-modal\"\n    >\n      <template slot=\"title\">\n        Failure Modes Analysis - {{ selectedCategory }} ({{ selectedMonth }})\n      </template>\n      <template slot=\"content\">\n        <div class=\"failure-modes-content\">\n          <div class=\"failure-modes-chart-container\" v-if=\"failureModesChartData.length > 0\">\n            <HBarChart\n              :data=\"failureModesChartData\"\n              :loading=\"isFailureModesLoading\"\n              :height=\"'400px'\"\n              :title=\"`Failure Modes for ${selectedCategory} (${selectedMonth})`\"\n            />\n          </div>\n          <div class=\"no-data-message\" v-else-if=\"isFailureModesLoading\">\n            Loading failure modes data...\n          </div>\n          <div class=\"no-data-message\" v-else>\n            No failure modes data available for this category and month.\n          </div>\n        </div>\n      </template>\n      <template slot=\"secondary-button\">\n        Close\n      </template>\n    </cv-modal>\n\n    <!-- Custom Date Modal -->\n    <cv-modal\n      :visible=\"showCustomDateModal\"\n      @modal-hidden=\"hideCustomDateModal\"\n      class=\"custom-date-modal\"\n    >\n      <template slot=\"title\">\n        Custom Date Range for {{ customDateAnalysisType }} Analysis\n      </template>\n      <template slot=\"content\">\n        <div class=\"custom-date-content\">\n          <div class=\"date-inputs\">\n            <div class=\"date-section\">\n              <div class=\"date-section-label\">Start Date:</div>\n              <div class=\"date-dropdown-container\">\n                <cv-dropdown\n                  id=\"custom-start-month\"\n                  v-model=\"customStartMonthStr\"\n                  class=\"month-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"(month, index) in months\"\n                    :key=\"index\"\n                    :value=\"String(index+1)\"\n                  >\n                    {{ month }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n                <cv-dropdown\n                  id=\"custom-start-year\"\n                  v-model=\"customStartYear\"\n                  class=\"year-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"year in availableYears\"\n                    :key=\"year\"\n                    :value=\"year\"\n                  >\n                    {{ year }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"date-section\">\n              <div class=\"date-section-label\">End Date:</div>\n              <div class=\"date-dropdown-container\">\n                <cv-dropdown\n                  id=\"custom-end-month\"\n                  v-model=\"customEndMonthStr\"\n                  class=\"month-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"(month, index) in months\"\n                    :key=\"index\"\n                    :value=\"String(index+1)\"\n                  >\n                    {{ month }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n                <cv-dropdown\n                  id=\"custom-end-year\"\n                  v-model=\"customEndYear\"\n                  class=\"year-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"year in availableYears\"\n                    :key=\"year\"\n                    :value=\"year\"\n                  >\n                    {{ year }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"custom-date-buttons\">\n            <cv-button @click=\"applyCustomDateRange\">Apply</cv-button>\n            <cv-button kind=\"secondary\" @click=\"hideCustomDateModal\">Cancel</cv-button>\n          </div>\n        </div>\n      </template>\n    </cv-modal>\n\n\n  </div>\n</template>\n\n<script>\nimport MainHeader from '@/components/MainHeader';\nimport LineChart from '@/components/LineChart/LineChart.vue';\nimport StackedBarChart from '@/components/StackedBarChart.vue';\nimport HBarChart from '@/components/HBarChart';\nimport PQEDashboard from '@/components/PQEDashboard/PQEDashboard.vue';\nimport axios from 'axios';\nimport { formatDashboardSummaryPrompt } from '@/utils/watsonxPrompts';\nimport logger from '@/utils/logger';\nimport {\n  CvDropdown,\n  CvDropdownItem,\n  CvButton,\n  CvModal,\n  CvTile,\n  CvTag,\n  CvTextArea,\n  CvStructuredList,\n  CvStructuredListRow,\n  CvStructuredListCell,\n  CvStructuredListHeading\n} from '@carbon/vue';\n\nexport default {\n  name: 'MetisXFactors',\n  components: {\n    MainHeader,\n    LineChart,\n    StackedBarChart,\n    HBarChart,\n    PQEDashboard,\n    CvDropdown,\n    CvDropdownItem,\n    CvButton,\n    CvModal,\n    CvTile,\n    CvTag,\n    CvTextArea,\n    CvStructuredList,\n    CvStructuredListRow,\n    CvStructuredListCell,\n    CvStructuredListHeading\n  },\n  data() {\n    return {\n      // Modal visibility\n      showCustomDateModal: false,\n\n      // Main tab data\n      startDate: '',\n      endDate: '',\n      minDate: '2024-01', // January 2024\n      maxDate: new Date().toISOString().split('T')[0].substring(0, 7), // YYYY-MM format\n      // Month and year dropdowns\n      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n      availableYears: [],\n      startMonth: 1,\n      startMonthStr: '1',\n      startYear: '2024', // String for Carbon Vue components\n      endMonth: new Date().getMonth() + 1,\n      endMonthStr: String(new Date().getMonth() + 1),\n      endYear: String(new Date().getFullYear()), // String for Carbon Vue components\n      selectedTimeRange: '',\n      breakoutNames: [],\n      selectedBreakout: '',\n      xFactorData: {},\n      chartData: [],\n      alerts: [],\n      mainTabPartNumbers: [],\n      showInfoModal: false,\n      noDataMessage: '',\n      isLoading: false,\n\n      // Breakout tab data\n      breakoutTabSelectedGroup: '',\n      breakoutTabStartDate: '',\n      breakoutTabEndDate: '',\n      breakoutTabTimeRange: 'last-month',\n      // Breakout tab month and year dropdowns\n      breakoutTabStartMonth: 1,\n      breakoutTabStartYear: '2024', // String for Carbon Vue components\n      breakoutTabEndMonth: new Date().getMonth() + 1,\n      breakoutTabEndYear: String(new Date().getFullYear()), // String for Carbon Vue components\n      breakoutTabChartData: [],\n      breakoutTabBarChartData: [],\n      breakoutTabAlerts: [],\n      breakoutTabPartNumbers: [],\n      breakoutTabNoDataMessage: '',\n      isBreakoutTabLoading: false,\n      breakoutTabAllXFactorData: {},\n\n      // Dashboard tab data\n      dashboardData: [],\n      dashboardMonths: [],\n      dashboardNoDataMessage: '',\n      isDashboardLoading: false,\n      showTooltip: false,\n      tooltipStyle: {\n        top: '0px',\n        left: '0px'\n      },\n      tooltipData: {\n        breakoutName: '',\n        month: '',\n        xFactor: null,\n        status: '',\n        duration: null,\n        defects: null,\n        volume: null,\n        criticalIssues: 0\n      },\n      currentMonthCriticalIssues: 0,\n      // Owner filter dropdowns\n      filterTypes: [\n        { value: 'group', text: 'Commodity' },\n        { value: 'dev_owner', text: 'Dev Owner' },\n        { value: 'pqe_owner', text: 'PQE Owner' }\n      ],\n      selectedFilterType: 'group',\n      ownerOptions: {\n        group: [],\n        dev_owner: [],\n        pqe_owner: []\n      },\n      selectedOwner: 'All',\n\n      // AI Summary data\n      showAiTooltip: false,\n      aiTooltipStyle: {\n        top: '0px',\n        left: '0px'\n      },\n      showAiSummaryModal: false,\n      aiSummaryBreakoutName: '',\n      aiSummaryText: '',\n      isLoadingAiSummary: false,\n      loadingAiSummaryFor: '',\n\n      // AI Test tab data\n      selectedAiModel: 'ibm/granite-13b-instruct-v2',\n      aiTemperature: 0.7,\n      aiPrompt: '',\n      aiResponse: '',\n      isAiLoading: false,\n\n      // Category Analysis tab data\n      categoryTabSelectedGroup: '',\n      categoryTabChartData: [],\n      categoryTabCategories: [],\n      categoryTabPartNumbers: [],\n      categoryTabNoDataMessage: '',\n      isCategoryTabLoading: false,\n      categoryChartOptions: {\n        title: 'Defect Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            // Domain will be set dynamically based on data\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        bars: {width: 60},\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        color: {\n          scale: {}\n        },\n        data: {\n          onclick: (data) => {\n            console.log('Chart clicked from options:', data);\n            this.handleCategoryBarClick(data);\n          }\n        }\n      },\n\n      // Group 2 Tab data\n      group2TabSelectedGroup: '',\n      group2TabNoDataMessage: '',\n      isGroup2TabLoading: false,\n\n      // Custom Date Modal data\n      customDateAnalysisType: '',\n      customStartMonthStr: String(new Date().getMonth() + 1),\n      customStartYear: String(new Date().getFullYear()),\n      customEndMonthStr: String(new Date().getMonth() + 1),\n      customEndYear: String(new Date().getFullYear()),\n\n\n\n      // Root Cause Analysis data\n      rootCauseTimeRange: '',\n      rootCauseMonths: 1,\n      rootCauseStartDate: '',\n      rootCauseEndDate: '',\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n      rootCauseAiSummary: '',\n      isRootCauseAiLoading: false,\n      hasCriticalRootCauseIssue: false,\n      criticalIssueDescription: '',\n      criticalIssues: [], // Array of critical issues with details\n      criticalIssueUpdates: {}, // Object mapping issue ID to updates\n      expandedIssues: {}, // Track which issues are expanded\n      rootCauseChartOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true\n            // Domain will be set dynamically based on data\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        bars: {width: 60},\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            if (!dataPoints || dataPoints.length === 0) return '';\n\n            const dataPoint = dataPoints[0];\n            const isCritical = dataPoint.data && dataPoint.data.isCritical;\n            const category = dataPoint.group;\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${category}</strong>${isCritical ? ' <span style=\"color: #fa4d56;\">(Critical)</span>' : ''}</p>\n                <p>Month: ${dataPoint.key}</p>\n                <p>Fail Rate: ${dataPoint.value.toFixed(2)}%</p>\n                <p>Defects: ${dataPoint.data && dataPoint.data.defects ? dataPoint.data.defects : 0}</p>\n                <p>Volume: ${dataPoint.data && dataPoint.data.volume ? dataPoint.data.volume.toLocaleString() : 'N/A'}</p>\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {}  // Will be populated dynamically based on categories\n        },\n        data: {\n          onclick: (data) => {\n            console.log('Root cause chart clicked:', data);\n            this.handleRootCauseBarClick(data);\n          }\n        }\n      },\n\n      // Failure Modes Modal data\n      showFailureModesModal: false,\n      selectedMonth: '',\n      selectedCategory: '',\n\n      // Selected month for dashboard navigation\n      selectedDashboardMonth: '',\n\n      // Active tab index for programmatic tab switching\n      activeTab: 0,\n      failureModesChartData: [],\n      isFailureModesLoading: false,\n      failureModesChartOptions: {\n        title: 'Failure Modes Analysis',\n        axes: {\n          left: {\n            title: 'Count',\n            mapsTo: 'value',\n            scaleType: 'linear'\n          },\n          right: {\n            title: 'Cumulative %',\n            mapsTo: 'percentage',\n            scaleType: 'linear',\n            domain: [0, 100]\n          },\n          bottom: {\n            title: 'Failure Mode',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            // Find the Count data point\n            const countPoint = dataPoints.find(point => point.group === 'Count');\n            // Find the Percentage data point\n            const percentPoint = dataPoints.find(point => point.group === 'Cumulative %');\n\n            if (!countPoint) return '';\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${countPoint.key}</strong></p>\n                <p>Count: ${countPoint.value}</p>\n                ${percentPoint ? `<p>Cumulative %: ${percentPoint.value.toFixed(1)}%</p>` : ''}\n                ${countPoint.data.category ? `<p>Category: ${countPoint.data.category}</p>` : ''}\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {\n            'Count': '#0f62fe',\n            'Cumulative %': '#da1e28'\n          }\n        },\n        comboChartTypes: [\n          {\n            type: 'simple-bar',\n            options: {\n              fillColors: ['#0f62fe']\n            },\n            correspondingDatasets: ['Count']\n          },\n          {\n            type: 'line',\n            options: {\n              points: {\n                enabled: true,\n                radius: 5\n              },\n              strokeWidth: 2\n            },\n            correspondingDatasets: ['Cumulative %']\n          }\n        ]\n      },\n      chartOptions: {\n        title: 'XFactor Trend by Breakout Group',\n        axes: {\n          bottom: {\n            title: 'Period',\n            mapsTo: 'date',\n            scaleType: 'time',\n            domain: [new Date('2024-01-01'), new Date('2025-12-31')], // Set explicit domain to show all dates\n            ticks: {\n              number: 12 // Show more ticks for better readability\n            },\n            formatters: {\n              tick: (date) => {\n                // Format the date to show month and year\n                const d = new Date(date);\n                return d.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n              }\n            }\n          },\n          left: {\n            title: 'XFactor',\n            mapsTo: 'value',\n            scaleType: 'linear',\n            domain: [0, 5],\n            thresholds: [\n              {\n                value: 1.5,\n                label: 'Sustained Problem Threshold',\n                fillColor: 'rgba(255, 204, 0, 0.2)',\n                strokeColor: '#FFCC00'\n              },\n              {\n                value: 3.0,\n                label: 'Short-Term Spike Threshold',\n                fillColor: 'rgba(255, 0, 0, 0.2)',\n                strokeColor: '#FF0000'\n              }\n            ]\n          }\n        },\n        curve: 'curveMonotoneX',\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true,\n          truncation: {\n            type: 'end',\n            threshold: 20\n          }\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            const dataPoint = dataPoints[0];\n            if (!dataPoint) return '';\n\n            // Ensure we're working with a proper date object\n            const date = dataPoint.date instanceof Date ? dataPoint.date : new Date(dataPoint.date);\n            const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });\n\n            // Get target rate if available\n            const targetRate = dataPoint.data && dataPoint.data.targetRate ?\n              dataPoint.data.targetRate :\n              (this.xFactorData[dataPoint.group] && this.xFactorData[dataPoint.group].targetRate ?\n                this.xFactorData[dataPoint.group].targetRate : 'N/A');\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${dataPoint.group}</strong></p>\n                <p>Period: ${formattedDate}</p>\n                <p>XFactor: ${dataPoint.value.toFixed(2)}</p>\n                <p>Target Rate: ${typeof targetRate === 'number' ? targetRate.toFixed(6) : targetRate}</p>\n                ${dataPoint.data.defects ? `<p>Defects: ${dataPoint.data.defects}</p>` : ''}\n                ${dataPoint.data.volume ? `<p>Volume: ${dataPoint.data.volume}</p>` : ''}\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {\n            'Ariel HLA': '#0F62FE',\n            'Parthenon Base': '#6929C4',\n            'Metis': '#1192E8',\n            'Themis': '#005D5D',\n            'Felis HLA': '#9F1853',\n            'Petra HLA': '#FA4D56',\n            'Orion Base': '#570408',\n            'Optic': '#198038',\n            'Victoria Crypto HLA': '#002D9C'\n          }\n        },\n        data: {\n          loading: this.isLoading\n        },\n        zoomBar: {\n          top: {\n            enabled: true\n          }\n        }\n      }\n    };\n  },\n  watch: {\n    activeTab(newVal) {\n      console.log(`Active tab changed to: ${newVal}`);\n\n      // If switching to the Group tab and we have a selected breakout group,\n      // make sure the data is loaded\n      if (newVal === 3 && this.group2TabSelectedGroup) {\n        console.log(`Loading Group tab data for ${this.group2TabSelectedGroup}`);\n\n        // If we have a selected month, use that to determine the time period\n        if (this.selectedDashboardMonth) {\n          console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);\n          this.viewRootCauseAnalysis(1);\n        } else {\n          console.log('No specific month selected, showing 3 months of data');\n          this.viewRootCauseAnalysis(3);\n        }\n      }\n    },\n\n    selectedProcess(newVal) {\n      console.log(`Process changed to: ${newVal}`);\n      this.loadBreakoutGroups();\n    },\n\n    group2TabSelectedGroup(newVal) {\n      console.log(`Group tab selected group changed to: ${newVal}`);\n      if (newVal && this.activeTab === 3) {\n        // If we're on the Group tab and a group is selected, load the data\n        this.analyzeGroup2Data();\n      }\n    }\n  },\n  created() {\n    // Set default date range (last 6 months)\n    const today = new Date();\n    const sixMonthsAgo = new Date();\n    sixMonthsAgo.setMonth(today.getMonth() - 6);\n\n    // Initialize available years (2024 to 2025)\n    // Including 2025 since the API returns data for 2025\n    // Convert years to strings for Carbon Vue components\n    for (let year = 2024; year <= 2025; year++) {\n      this.availableYears.push(String(year));\n    }\n\n    // Set default values for dropdowns\n    this.startMonth = sixMonthsAgo.getMonth() + 1; // 1-based month\n    this.startMonthStr = String(this.startMonth);\n    this.startYear = String(sixMonthsAgo.getFullYear()); // Convert to string for Carbon Vue\n    this.endMonth = today.getMonth() + 1; // 1-based month\n    this.endMonthStr = String(this.endMonth);\n    this.endYear = String(today.getFullYear()); // Convert to string for Carbon Vue\n\n    // Format dates as YYYY-MM for internal use\n    this.startDate = this.formatMonthDate(sixMonthsAgo);\n    this.endDate = this.formatMonthDate(today);\n\n    // Set default date range for breakout tab (last month)\n    const lastMonth = new Date();\n    lastMonth.setMonth(today.getMonth() - 1);\n\n    // Set default values for breakout tab dropdowns\n    this.breakoutTabStartMonth = lastMonth.getMonth() + 1; // 1-based month\n    this.breakoutTabStartYear = String(lastMonth.getFullYear()); // Convert to string for Carbon Vue\n    this.breakoutTabEndMonth = today.getMonth() + 1; // 1-based month\n    this.breakoutTabEndYear = String(today.getFullYear()); // Convert to string for Carbon Vue\n\n    this.breakoutTabStartDate = this.formatMonthDate(lastMonth);\n    this.breakoutTabEndDate = this.formatMonthDate(today);\n\n    // Set min and max dates in the correct format\n    this.minDate = '2024-01'; // January 2024\n    this.maxDate = this.formatMonthDate(today);\n\n    // Load breakout names and owners data\n    this.loadBreakoutNames();\n    this.loadOwners();\n\n    // Automatically analyze data on page load\n    this.$nextTick(() => {\n      this.analyzeData();\n\n      // Load dashboard data with last 6 months\n      this.loadDashboardData();\n    });\n  },\n  mounted() {\n    // Add direct click handler for the chart\n    this.$nextTick(() => {\n      this.setupChartClickHandlers();\n    });\n  },\n  methods: {\n    // Helper method to format date as YYYY-MM for month input\n    formatMonthDate(date) {\n      const year = date.getFullYear();\n      // Month needs to be padded with leading zero if less than 10\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      return `${year}-${month}`;\n    },\n\n    // Helper method to get the first day of a month from YYYY-MM format\n    getFirstDayOfMonth(yearMonth) {\n      if (!yearMonth || yearMonth.length < 7) return null;\n\n      // Extract year and month directly from the string to avoid timezone issues\n      const [year, month] = yearMonth.split('-');\n\n      console.log(`Getting first day of month for ${yearMonth}`);\n      console.log(`Year: ${year}, Month: ${month}`);\n\n      // Return the first day of the month in YYYY-MM-DD format\n      return `${year}-${month}-01`;\n    },\n\n    // Helper method to get the last day of a month from YYYY-MM format\n    getLastDayOfMonth(yearMonth) {\n      if (!yearMonth || yearMonth.length < 7) return null;\n\n      // Extract year and month directly from the string to avoid timezone issues\n      const [year, month] = yearMonth.split('-');\n\n      console.log(`Getting last day of month for ${yearMonth}`);\n      console.log(`Year: ${year}, Month: ${month}`);\n\n      // Calculate the last day of the month\n      // For month 12 (December), we need to handle the year change\n      const nextMonth = parseInt(month) === 12 ? 1 : parseInt(month) + 1;\n      const nextMonthYear = parseInt(month) === 12 ? parseInt(year) + 1 : parseInt(year);\n\n      // Create a date for the first day of the next month, then subtract one day\n      // Use UTC methods to avoid timezone issues\n      const lastDay = new Date(Date.UTC(nextMonthYear, nextMonth - 1, 0));\n      const lastDayOfMonth = lastDay.getUTCDate();\n\n      console.log(`Last day of month: ${lastDayOfMonth}`);\n\n      // Return the last day of the month in YYYY-MM-DD format\n      return `${year}-${month}-${String(lastDayOfMonth).padStart(2, '0')}`;\n    },\n\n    // Helper method to get authentication config\n    getAuthConfig() {\n      const config = {};\n      const token = localStorage.getItem('token');\n\n      // For debugging\n      console.log('Token from localStorage:', token ? 'Token exists' : 'No token found');\n\n      if (token) {\n        config.headers = {\n          'Authorization': `Bearer ${token}`\n        };\n        console.log('Using authentication token for request');\n      } else {\n        // For testing purposes, we'll proceed without authentication\n        // since we've configured the server to skip authentication for Metis routes\n        console.log('No authentication token available, proceeding without authentication');\n      }\n\n      return config;\n    },\n\n    loadOwners() {\n      console.log('Loading owners data...');\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // First, get the Dan OwningGroup values from new_metis_test.xlsx\n      console.log('Requesting owning groups from API...');\n\n      axios.post('/api-statit2/get_metis_owning_groups', {}, config)\n        .then(response => {\n          console.log('Received owning groups response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            // Store the Dan OwningGroup values in the group option\n            this.ownerOptions.group = response.data.owning_groups || [];\n\n            // Add \"All\" option at the beginning\n            this.ownerOptions.group.unshift('All');\n\n            console.log(`Loaded ${this.ownerOptions.group.length} commodities from Dan OwningGroup column`);\n          } else {\n            console.error('Failed to load owning groups:', response.data.error_msg);\n          }\n\n          // Now get the dev and PQE owners\n          return axios.post('/api-statit2/get_metis_owners', {}, config);\n        })\n        .then(response => {\n          console.log('Received owners data response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            const ownersData = response.data.owners_data;\n\n            // Store the dev and PQE owners in the ownerOptions object\n            this.ownerOptions.dev_owner = ownersData.dev_owners || [];\n            this.ownerOptions.pqe_owner = ownersData.pqe_owners || [];\n\n            // Add \"All\" option at the beginning of each list\n            this.ownerOptions.dev_owner.unshift('All');\n            this.ownerOptions.pqe_owner.unshift('All');\n\n            console.log(`Loaded ${this.ownerOptions.dev_owner.length} dev owners and ${this.ownerOptions.pqe_owner.length} PQE owners`);\n\n            // Set default selection\n            this.selectedOwner = 'All';\n          } else {\n            console.error('Failed to load owners data:', response.data.error_msg);\n          }\n        })\n        .catch(error => {\n          console.error('Error loading owners data:', error);\n        });\n    },\n\n    // For backward compatibility - will be removed in future\n    loadOwningGroups() {\n      // This method is kept for backward compatibility\n      // It now calls the new loadOwners method\n      this.loadOwners();\n    },\n\n    loadBreakoutNames() {\n      console.log('Loading breakout names...');\n      this.isLoading = true;\n      this.noDataMessage = 'Loading breakout names...';\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Log the API request\n      console.log('Requesting breakout names from API...');\n\n      axios.post('/api-statit2/get_metis_breakout_names', {}, config)\n        .then(response => {\n          console.log('Received breakout names response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            // Filter out empty or null breakout names\n            const filteredNames = response.data.breakout_names\n              .filter(name => name && name.trim() !== '')\n              .sort((a, b) => a.localeCompare(b)); // Sort alphabetically\n\n            console.log(`Found ${filteredNames.length} valid breakout names out of ${response.data.breakout_names.length} total`);\n\n            // Log some sample breakout names for verification\n            if (filteredNames.length > 0) {\n              console.log('Sample breakout names from client:');\n              filteredNames.slice(0, 5).forEach((name, index) => {\n                console.log(`  ${index + 1}. ${name}`);\n              });\n\n              this.breakoutNames = filteredNames;\n              this.noDataMessage = '';\n            } else {\n              this.breakoutNames = [];\n              this.noDataMessage = 'No valid breakout names found in the data.';\n            }\n          } else {\n            console.error('Error loading breakout names:', response.data);\n            this.breakoutNames = [];\n\n            if (response.data.error_msg) {\n              this.noDataMessage = `Error loading breakout names: ${response.data.error_msg}`;\n            } else {\n              this.noDataMessage = 'Error loading breakout names. Please try again.';\n            }\n          }\n          this.isLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading breakout names:', error);\n          this.breakoutNames = [];\n\n          if (error.response) {\n            console.error('Error response data:', error.response.data);\n\n            if (error.response.status === 401) {\n              this.noDataMessage = 'Authentication error. Please log in again.';\n              // Optionally redirect to login page\n              // this.$router.push('/login');\n            } else {\n              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;\n            }\n          } else if (error.request) {\n            console.error('No response received:', error.request);\n            this.noDataMessage = 'No response received from server. Please check your connection.';\n          } else {\n            console.error('Error message:', error.message);\n            this.noDataMessage = `Error: ${error.message}`;\n          }\n\n          this.isLoading = false;\n        });\n    },\n    analyzeData() {\n      // Validate date range\n      if (!this.startDate || !this.endDate) {\n        this.noDataMessage = 'Please select a valid date range';\n        return;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.startDate + '-01');\n      const end = new Date(this.endDate + '-01');\n\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.noDataMessage = 'Invalid date format';\n        return;\n      }\n\n      if (start > end) {\n        this.noDataMessage = 'Start date must be before end date';\n        return;\n      }\n\n      this.noDataMessage = 'Loading data...';\n      this.isLoading = true;\n      this.chartData = [];\n      this.alerts = [];\n\n      console.log(`Analyzing data from ${this.startDate} to ${this.endDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the YYYY-MM format directly for API requests\n      const requestData = {\n        startDate: this.startDate, // Use the YYYY-MM format directly\n        endDate: this.endDate, // Use the YYYY-MM format directly\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format\n      };\n\n      // Calculate base rate period (1 year back from start date by default)\n      const baseStartDateObj = new Date(this.startDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      // Create a new object with all properties including baseStartDate\n      const apiRequestData = {\n        ...requestData,\n        baseStartDate: baseStartDate\n      };\n\n      console.log(`Date range: ${apiRequestData.startDate} to ${apiRequestData.endDate}`);\n      console.log(`Base rate calculation period starts at: ${apiRequestData.baseStartDate}`);\n\n      // If a specific breakout group is selected, only query for that one\n      if (this.selectedBreakout) {\n        apiRequestData.breakoutName = this.selectedBreakout;\n        console.log(`Requesting XFactors only for breakout group: ${this.selectedBreakout}`);\n      }\n\n      // Log the API request\n      console.log(`Requesting XFactors from API for date range: ${this.startDate} to ${this.endDate}`);\n\n      axios.post('/api-statit2/get_metis_xfactors', apiRequestData, config)\n        .then(response => {\n          console.log('Received response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            this.xFactorData = response.data.xfactors;\n\n            if (Object.keys(this.xFactorData).length === 0) {\n              this.noDataMessage = 'No data found for the selected date range. Please try a different range.';\n              console.warn('No data found in the response');\n            } else {\n              console.log(`Received data for ${Object.keys(this.xFactorData).length} breakout groups`);\n              this.updateChart();\n              this.generateAlerts();\n\n              // If a specific breakout group is selected, get its part numbers\n              if (this.selectedBreakout) {\n                this.getMainTabPartNumbers();\n              }\n\n              this.noDataMessage = '';\n            }\n          } else {\n            console.error('Error analyzing data:', response.data);\n\n            // Check for SQL error message\n            if (response.data.sql_error_msg) {\n              this.noDataMessage = `Database error: ${response.data.sql_error_msg}`;\n            } else {\n              this.noDataMessage = 'Error analyzing data. Please try again.';\n            }\n          }\n          this.isLoading = false;\n        })\n        .catch(error => {\n          console.error('API error:', error);\n\n          if (error.response) {\n            console.error('Error response data:', error.response.data);\n\n            if (error.response.status === 401) {\n              this.noDataMessage = 'Authentication error. Please log in again.';\n              // Optionally redirect to login page\n              // this.$router.push('/login');\n            } else {\n              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;\n            }\n          } else if (error.request) {\n            console.error('No response received:', error.request);\n            this.noDataMessage = 'No response received from server. Please check your connection.';\n          } else {\n            console.error('Error message:', error.message);\n            this.noDataMessage = `Error: ${error.message}`;\n          }\n\n          this.isLoading = false;\n        });\n    },\n    handleBreakoutChange() {\n      console.log(`Breakout group changed to: ${this.selectedBreakout || 'All groups'}`);\n\n      // Update the chart with the selected breakout group\n      this.updateChart();\n\n      // If a specific breakout group is selected, get its part numbers\n      if (this.selectedBreakout) {\n        console.log(`Getting part numbers for selected breakout: ${this.selectedBreakout}`);\n        this.getMainTabPartNumbers();\n\n        // Also update alerts to only show for the selected breakout\n        this.generateAlerts();\n      } else {\n        // Clear part numbers if no breakout group is selected\n        console.log('No breakout group selected, clearing part numbers');\n        this.mainTabPartNumbers = [];\n\n        // Show alerts for all breakout groups\n        this.generateAlerts();\n      }\n    },\n\n    // Get part numbers for the selected breakout group in the main tab\n    getMainTabPartNumbers() {\n      if (!this.selectedBreakout) return;\n\n      console.log(`Getting part numbers for main tab breakout group: ${this.selectedBreakout}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get part numbers for the selected breakout group\n      axios.post('/api-statit2/get_metis_part_numbers', {\n        breakoutName: this.selectedBreakout\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success' && response.data.pns) {\n            this.mainTabPartNumbers = response.data.pns;\n            console.log(`Received ${this.mainTabPartNumbers.length} part numbers for ${this.selectedBreakout}`);\n          } else {\n            console.error('Error getting part numbers for main tab:', response.data);\n            this.mainTabPartNumbers = [];\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting part numbers for main tab:', error);\n          this.mainTabPartNumbers = [];\n        });\n    },\n\n    updateChart() {\n      if (!this.xFactorData || Object.keys(this.xFactorData).length === 0) {\n        this.noDataMessage = 'No data available. Please analyze data first.';\n        console.warn('No xFactorData available for chart');\n        return;\n      }\n\n      this.chartData = [];\n\n      // Get all breakout groups or filter by selected breakout\n      const breakoutsToShow = this.selectedBreakout\n        ? [this.selectedBreakout]\n        : Object.keys(this.xFactorData).slice(0, 10); // Limit to top 10 groups for better performance and readability\n\n      console.log(`Showing ${breakoutsToShow.length} breakout groups in chart`);\n\n      if (breakoutsToShow.length === 0) {\n        this.noDataMessage = 'No breakout groups available to display.';\n        console.warn('No breakout groups to show');\n        return;\n      }\n\n      // Track min and max dates for chart domain\n      let minDate = new Date('2025-12-31');\n      let maxDate = new Date('2024-01-01');\n      let hasValidData = false;\n\n      breakoutsToShow.forEach(breakoutName => {\n        const breakoutData = this.xFactorData[breakoutName];\n        if (!breakoutData || !breakoutData.xFactors) {\n          console.log(`No data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        const dataPoints = [];\n        const xFactorPeriods = Object.keys(breakoutData.xFactors);\n\n        if (xFactorPeriods.length === 0) {\n          console.log(`No periods found for breakout: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing ${xFactorPeriods.length} periods for ${breakoutName}`);\n\n        Object.entries(breakoutData.xFactors).forEach(([period, data]) => {\n          // Add data point for this period\n          // Make sure period is in YYYY-MM format and convert to a proper date\n          const dateParts = period.split('-');\n          if (dateParts.length >= 2) {\n            const year = parseInt(dateParts[0]);\n            const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed\n            const date = new Date(year, month, 1);\n\n            // Validate the date\n            if (isNaN(date.getTime())) {\n              console.error(`Invalid date created from period ${period}`);\n              return;\n            }\n\n            // Update min and max dates\n            if (date < minDate) minDate = new Date(date);\n            if (date > maxDate) maxDate = new Date(date);\n\n            // Ensure xFactor is a valid number\n            const xFactor = typeof data.xFactor === 'number' ? data.xFactor : parseFloat(data.xFactor);\n            if (isNaN(xFactor)) {\n              console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${data.xFactor}`);\n              return;\n            }\n\n            dataPoints.push({\n              date: date,\n              value: xFactor,\n              group: breakoutName,\n              defects: data.defects || 0,\n              volume: data.volume || 0,\n              targetRate: data.targetRate || (breakoutData.targetRate || 0)\n            });\n\n            hasValidData = true;\n          } else {\n            console.error(`Invalid period format: ${period}`);\n          }\n        });\n\n        // Sort by date\n        dataPoints.sort((a, b) => a.date - b.date);\n\n        console.log(`Added ${dataPoints.length} data points for ${breakoutName}`);\n\n        // Add to chart data\n        this.chartData = [...this.chartData, ...dataPoints];\n      });\n\n      console.log(`Chart data has ${this.chartData.length} data points for ${breakoutsToShow.length} breakout groups`);\n\n      if (hasValidData) {\n        console.log(`Date range in data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);\n\n        // Update chart domain based on actual data\n        // Add padding to the date range (1 month before and after)\n        minDate.setMonth(minDate.getMonth() - 1);\n        maxDate.setMonth(maxDate.getMonth() + 1);\n\n        // Update chart options with dynamic domain\n        this.chartOptions.axes.bottom.domain = [minDate, maxDate];\n\n        // Clear any error message\n        this.noDataMessage = '';\n      } else if (this.chartData.length === 0) {\n        this.noDataMessage = 'No data available for the selected criteria.';\n        console.warn('No valid data points found for chart');\n      }\n    },\n\n\n    generateAlerts() {\n      console.log('Generating alerts...');\n      this.alerts = [];\n\n      // Filter breakout groups if a specific one is selected\n      const breakoutsToProcess = this.selectedBreakout\n        ? { [this.selectedBreakout]: this.xFactorData[this.selectedBreakout] }\n        : this.xFactorData;\n\n      Object.entries(breakoutsToProcess).forEach(([breakoutName, data]) => {\n        if (!data || !data.xFactors) {\n          console.log(`No xFactors data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        // Sort periods chronologically\n        const sortedPeriods = Object.keys(data.xFactors).sort();\n\n        if (sortedPeriods.length === 0) {\n          console.log(`No periods found for breakout: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing ${sortedPeriods.length} periods for alerts in ${breakoutName}`);\n\n        // Check for short-term spikes (X-Factor > 3.0)\n        sortedPeriods.forEach(period => {\n          if (!data.xFactors[period]) {\n            console.error(`Missing data for period ${period} in breakout ${breakoutName}`);\n            return;\n          }\n\n          const xFactor = data.xFactors[period].xFactor;\n\n          // Ensure xFactor is a valid number\n          if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {\n            console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${xFactor}`);\n            return;\n          }\n\n          if (xFactor > 3.0) {\n            console.log(`Found short-term spike for ${breakoutName} in ${period}: ${xFactor.toFixed(2)}`);\n            this.alerts.push({\n              breakoutName,\n              status: 'Short-Term Spike',\n              period,\n              xFactor,\n              defects: data.xFactors[period].defects || 0,\n              volume: data.xFactors[period].volume || 0\n            });\n          }\n        });\n\n        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n        for (let i = 0; i < sortedPeriods.length - 2; i++) {\n          const period1 = sortedPeriods[i];\n          const period2 = sortedPeriods[i + 1];\n          const period3 = sortedPeriods[i + 2];\n\n          // Ensure all periods have valid data\n          if (!data.xFactors[period1] || !data.xFactors[period2] || !data.xFactors[period3]) {\n            console.error(`Missing data for one of the periods in sustained problem check: ${period1}, ${period2}, ${period3}`);\n            continue;\n          }\n\n          const xFactor1 = data.xFactors[period1].xFactor;\n          const xFactor2 = data.xFactors[period2].xFactor;\n          const xFactor3 = data.xFactors[period3].xFactor;\n\n          // Ensure all xFactors are valid numbers\n          if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||\n              typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||\n              typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {\n            console.error(`Invalid xFactor values for sustained problem check in ${breakoutName}`);\n            continue;\n          }\n\n          if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {\n            // Count how many consecutive months have X-Factor > 1.5\n            let consecutiveMonths = 3;\n            for (let j = i + 3; j < sortedPeriods.length; j++) {\n              if (data.xFactors[sortedPeriods[j]] && data.xFactors[sortedPeriods[j]].xFactor > 1.5) {\n                consecutiveMonths++;\n              } else {\n                break;\n              }\n            }\n\n            console.log(`Found sustained problem for ${breakoutName} from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);\n\n            // Calculate average xFactor for the sustained problem\n            const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;\n\n            this.alerts.push({\n              breakoutName,\n              status: `Sustained Problem (${consecutiveMonths} months)`,\n              period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,\n              xFactor: avgXFactor,\n              defects: data.xFactors[period3].defects || 0,\n              volume: data.xFactors[period3].volume || 0\n            });\n\n            // Skip the periods we've already included in this sustained problem\n            i += consecutiveMonths - 1;\n          }\n        }\n      });\n\n      // Sort alerts by X-Factor (highest first)\n      this.alerts.sort((a, b) => b.xFactor - a.xFactor);\n\n      console.log(`Generated ${this.alerts.length} alerts`);\n    },\n    // New method to handle quick select button clicks\n    selectTimeRange(range) {\n      this.selectedTimeRange = range;\n      this.applyTimeRange();\n    },\n\n    applyTimeRange() {\n      const today = new Date();\n      const currentMonth = today.getMonth() + 1; // 1-based month\n      const currentYear = today.getFullYear();\n\n      // Variables for calculations\n      let startMonth, startYear, endMonth, endYear;\n\n      switch (this.selectedTimeRange) {\n        case 'last-month': {\n          // Calculate last month directly without using Date object\n          startMonth = currentMonth - 1;\n          startYear = currentYear;\n\n          // Handle January case\n          if (startMonth === 0) {\n            startMonth = 12;\n            startYear = currentYear - 1;\n          }\n\n          // For last month, both start and end should be the previous month\n          endMonth = startMonth;\n          endYear = startYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-3-months': {\n          // Calculate 2 months ago for a total of 3 months including current month\n          startMonth = currentMonth - 2;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-6-months': {\n          // Calculate 5 months ago for a total of 6 months including current month\n          startMonth = currentMonth - 5;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'ytd': {\n          // Update dropdown values\n          this.startMonth = 1; // January\n          this.startMonthStr = '1';\n          this.startYear = currentYear;\n          this.endMonth = currentMonth;\n          this.endMonthStr = String(currentMonth);\n          this.endYear = currentYear;\n\n          // Update date strings directly\n          this.startDate = `${currentYear}-01`;\n          this.endDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;\n          break;\n        }\n      }\n\n      // Synchronize with breakout tab\n      this.breakoutTabStartDate = this.startDate;\n      this.breakoutTabEndDate = this.endDate;\n      this.breakoutTabStartMonth = this.startMonth;\n      this.breakoutTabStartYear = this.startYear;\n      this.breakoutTabEndMonth = this.endMonth;\n      this.breakoutTabEndYear = this.endYear;\n\n      // Also update the breakout tab time range to match\n      this.breakoutTabTimeRange = this.selectedTimeRange;\n\n      // Analyze data for all tabs when time range changes\n      this.analyzeAllData();\n    },\n    getStatusClass(status) {\n      if (status.includes('Short-Term Spike')) {\n        return 'status-spike';\n      } else if (status.includes('Sustained Problem')) {\n        return 'status-sustained';\n      }\n      return 'status-normal';\n    },\n\n    // Breakout tab methods\n    applyBreakoutTabTimeRange() {\n      const today = new Date();\n      const currentMonth = today.getMonth() + 1; // 1-based month\n      const currentYear = today.getFullYear();\n\n      // Variables for calculations\n      let startMonth, startYear, endMonth, endYear;\n\n      switch (this.breakoutTabTimeRange) {\n        case 'last-month': {\n          // Calculate last month directly without using Date object\n          startMonth = currentMonth - 1;\n          startYear = currentYear;\n\n          // Handle January case\n          if (startMonth === 0) {\n            startMonth = 12;\n            startYear = currentYear - 1;\n          }\n\n          // For last month, both start and end should be the previous month\n          endMonth = startMonth;\n          endYear = startYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-3-months': {\n          // Calculate 2 months ago for a total of 3 months including current month\n          startMonth = currentMonth - 2;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-6-months': {\n          // Calculate 5 months ago for a total of 6 months including current month\n          startMonth = currentMonth - 5;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'custom': {\n          // For custom range, don't change the dates automatically\n          // The user will input them manually\n          console.log('Custom date range selected. Using user-provided dates.');\n\n          // If no dates are set, initialize with reasonable defaults\n          if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n            // Calculate 2 months ago for a total of 3 months including current month\n            startMonth = currentMonth - 2;\n            startYear = currentYear;\n\n            // Handle month wrapping\n            if (startMonth <= 0) {\n              startMonth = startMonth + 12;\n              startYear = currentYear - 1;\n            }\n\n            endMonth = currentMonth;\n            endYear = currentYear;\n\n            // Update dropdown values\n            this.breakoutTabStartMonth = startMonth;\n            this.breakoutTabStartYear = startYear;\n            this.breakoutTabEndMonth = endMonth;\n            this.breakoutTabEndYear = endYear;\n\n            // Update date strings directly\n            this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n            this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          }\n\n          return; // Don't automatically analyze for custom range\n        }\n      }\n\n      // Automatically analyze the data when time range changes (except for custom)\n      this.analyzeBreakoutData();\n    },\n\n    analyzeBreakoutData() {\n      // Validate date range\n      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n        this.breakoutTabNoDataMessage = 'Please select a valid date range';\n        return;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.breakoutTabStartDate + '-01');\n      const end = new Date(this.breakoutTabEndDate + '-01');\n\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.breakoutTabNoDataMessage = 'Invalid date format';\n        return;\n      }\n\n      if (start > end) {\n        this.breakoutTabNoDataMessage = 'Start date must be before end date';\n        return;\n      }\n\n      this.breakoutTabNoDataMessage = 'Loading data...';\n      this.isBreakoutTabLoading = true;\n      this.breakoutTabChartData = [];\n      this.breakoutTabBarChartData = [];\n      this.breakoutTabAlerts = [];\n      this.breakoutTabPartNumbers = [];\n      this.breakoutTabAllXFactorData = {};\n\n      console.log(`Analyzing breakout data from ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the YYYY-MM format directly for API requests\n      const startDate = this.breakoutTabStartDate;\n      const endDate = this.breakoutTabEndDate;\n\n      // Calculate base rate period (1 year back from start date by default)\n      const baseStartDateObj = new Date(this.breakoutTabStartDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      console.log(`Breakout tab date range: ${startDate} to ${endDate}`);\n      console.log(`Base rate calculation period starts at: ${baseStartDate}`);\n\n      // First, get data for all breakout groups to populate the bar chart\n      axios.post('/api-statit2/get_metis_xfactors', {\n        startDate: startDate, // Use the YYYY-MM format directly\n        endDate: endDate, // Use the YYYY-MM format directly\n        baseStartDate: baseStartDate,\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            this.breakoutTabAllXFactorData = response.data.xfactors;\n\n            if (Object.keys(this.breakoutTabAllXFactorData).length === 0) {\n              this.breakoutTabNoDataMessage = 'No data found for the selected date range. Please try a different range.';\n              this.isBreakoutTabLoading = false;\n              return;\n            }\n\n            console.log(`Received data for ${Object.keys(this.breakoutTabAllXFactorData).length} breakout groups`);\n\n            // Process bar chart data for all breakout groups\n            this.updateBreakoutBarChart();\n\n            // If a specific breakout group is selected, get its details using the consolidated endpoint\n            if (this.breakoutTabSelectedGroup) {\n              this.getBreakoutAnalysis();\n            } else {\n              this.breakoutTabNoDataMessage = 'Please select a breakout group to see detailed analysis';\n              this.isBreakoutTabLoading = false;\n            }\n          } else {\n            console.error('Error analyzing breakout data:', response.data);\n            this.breakoutTabNoDataMessage = 'Error analyzing data. Please try again.';\n            this.isBreakoutTabLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error:', error);\n          this.breakoutTabNoDataMessage = `Error: ${error.message}`;\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Get detailed analysis for a specific breakout group using the consolidated endpoint\n    getBreakoutAnalysis() {\n      if (!this.breakoutTabSelectedGroup) {\n        console.error('No breakout group selected for detailed analysis');\n        return;\n      }\n\n      console.log(`Getting detailed analysis for breakout group: ${this.breakoutTabSelectedGroup}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the consolidated endpoint to get all data for this breakout group\n      axios.post('/api-statit2/get_metis_breakout_analysis', {\n        breakoutName: this.breakoutTabSelectedGroup,\n        startDate: this.breakoutTabStartDate,\n        endDate: this.breakoutTabEndDate,\n        baseStartDate: this.baseStartDate,\n        exactDateRange: true,\n        useMonthFormat: true\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            console.log('Received detailed analysis for breakout group:', response.data);\n\n            // Update part numbers\n            if (response.data.partNumbers) {\n              this.breakoutTabPartNumbers = response.data.partNumbers;\n              console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);\n            }\n\n            // Update XFactor data for line chart\n            if (response.data.xFactorData && response.data.xFactorData.xFactors) {\n              // Create a structure compatible with the existing updateBreakoutLineChart method\n              const breakoutData = {\n                xFactors: response.data.xFactorData.xFactors\n              };\n\n              // Process the data for the line chart\n              this.processBreakoutLineChartData(breakoutData);\n\n              // Generate alerts for this breakout group\n              this.generateBreakoutAlerts(breakoutData);\n            } else {\n              console.error('No XFactor data found in the response');\n              this.breakoutTabNoDataMessage = 'No XFactor data found for the selected breakout group';\n            }\n\n            this.isBreakoutTabLoading = false;\n          } else {\n            console.error('Error getting detailed analysis:', response.data);\n            this.breakoutTabNoDataMessage = 'Error getting detailed analysis. Please try again.';\n            this.isBreakoutTabLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting detailed analysis:', error);\n          this.breakoutTabNoDataMessage = `Error: ${error.message}`;\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Get part numbers for the selected breakout group\n    getBreakoutPartNumbers() {\n      if (!this.breakoutTabSelectedGroup) return;\n\n      console.log(`Getting part numbers for breakout group: ${this.breakoutTabSelectedGroup}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get part numbers for the selected breakout group\n      axios.post('/api-statit2/get_metis_part_numbers', {\n        breakoutName: this.breakoutTabSelectedGroup\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success' && response.data.pns) {\n            this.breakoutTabPartNumbers = response.data.pns;\n            console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);\n          } else {\n            console.error('Error getting part numbers:', response.data);\n            this.breakoutTabPartNumbers = [];\n          }\n          this.isBreakoutTabLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when getting part numbers:', error);\n          this.breakoutTabPartNumbers = [];\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Process data for the line chart from the consolidated endpoint\n    processBreakoutLineChartData(breakoutData) {\n      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {\n        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;\n        return;\n      }\n\n      // Process chart data\n      this.breakoutTabChartData = [];\n      const dataPoints = [];\n\n      // Track min and max dates for chart domain\n      let minDate = new Date('2025-12-31');\n      let maxDate = new Date('2024-01-01');\n\n      Object.entries(breakoutData.xFactors).forEach(([period, data]) => {\n        // Make sure period is in YYYY-MM format and convert to a proper date\n        const dateParts = period.split('-');\n        if (dateParts.length >= 2) {\n          const year = parseInt(dateParts[0]);\n          const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed\n          const date = new Date(year, month, 1);\n\n          // Update min and max dates\n          if (date < minDate) minDate = new Date(date);\n          if (date > maxDate) maxDate = new Date(date);\n\n          dataPoints.push({\n            date: date,\n            value: data.xFactor,\n            group: this.breakoutTabSelectedGroup,\n            defects: data.defects,\n            volume: data.volume\n          });\n        }\n      });\n\n      // Sort by date\n      dataPoints.sort((a, b) => a.date - b.date);\n      this.breakoutTabChartData = dataPoints;\n\n      // Update chart domain based on actual data\n      if (dataPoints.length > 0) {\n        // Add padding to the date range (1 month before and after)\n        minDate.setMonth(minDate.getMonth() - 1);\n        maxDate.setMonth(maxDate.getMonth() + 1);\n\n        // Update chart options with dynamic domain\n        this.chartOptions.axes.bottom.domain = [minDate, maxDate];\n\n        console.log(`Date range in breakout data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);\n      }\n\n      console.log(`Successfully processed line chart data for ${this.breakoutTabSelectedGroup} with ${dataPoints.length} data points`);\n    },\n\n    // Update the line chart for the selected breakout group (legacy method, kept for compatibility)\n    updateBreakoutLineChart() {\n      if (!this.breakoutTabSelectedGroup || !this.breakoutTabAllXFactorData) {\n        return;\n      }\n\n      const breakoutData = this.breakoutTabAllXFactorData[this.breakoutTabSelectedGroup];\n\n      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {\n        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;\n        return;\n      }\n\n      // Process the data for the line chart\n      this.processBreakoutLineChartData(breakoutData);\n\n      // Generate alerts for this breakout group\n      this.generateBreakoutAlerts(breakoutData);\n    },\n\n    // Update the bar chart with data for all breakout groups\n    updateBreakoutBarChart() {\n      if (!this.breakoutTabAllXFactorData || Object.keys(this.breakoutTabAllXFactorData).length === 0) {\n        return;\n      }\n\n      this.breakoutTabBarChartData = [];\n\n      // Calculate average X-Factor for each breakout group\n      Object.entries(this.breakoutTabAllXFactorData).forEach(([breakoutName, data]) => {\n        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {\n          console.log(`No XFactors data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        let totalXFactor = 0;\n        let totalDefects = 0;\n        let totalVolume = 0;\n        let count = 0;\n        let isCritical = false;\n\n        // Calculate average X-Factor and check if any period is critical\n        Object.values(data.xFactors).forEach(periodData => {\n          totalXFactor += periodData.xFactor;\n          totalDefects += periodData.defects;\n          totalVolume += periodData.volume;\n          count++;\n\n          // Check if this period has a critical X-Factor (> 3.0)\n          if (periodData.xFactor > 3.0) {\n            isCritical = true;\n          }\n        });\n\n        if (count > 0) {\n          const avgXFactor = totalXFactor / count;\n\n          // Create a simpler group name if it's too long\n          const displayName = breakoutName.length > 30\n            ? breakoutName.substring(0, 27) + '...'\n            : breakoutName;\n\n          this.breakoutTabBarChartData.push({\n            group: displayName,\n            value: avgXFactor,\n            defects: totalDefects,\n            volume: totalVolume,\n            // Use the 'Critical' group for breakouts with any critical period\n            groupMapsTo: isCritical ? 'Critical' : 'XFactor'\n          });\n\n          console.log(`Added bar chart data for ${displayName}: XFactor=${avgXFactor.toFixed(2)}, Critical=${isCritical}`);\n        }\n      });\n\n      // Sort by X-Factor value (highest first)\n      this.breakoutTabBarChartData.sort((a, b) => b.value - a.value);\n\n      // Limit to top 15 breakout groups for better visualization\n      this.breakoutTabBarChartData = this.breakoutTabBarChartData.slice(0, 15);\n\n      console.log(`Final bar chart data has ${this.breakoutTabBarChartData.length} items`);\n    },\n\n    // Handle click events on the chart\n    handleBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Bar clicked for breakout group: ${clickedData.group}`);\n\n        // Update the selected breakout group\n        this.breakoutTabSelectedGroup = clickedData.group;\n\n        // Get detailed analysis for this breakout group\n        this.getBreakoutAnalysis();\n      }\n    },\n\n    // Update breakout tab start date from dropdowns\n    updateBreakoutStartDate() {\n      // Format the date as YYYY-MM\n      const month = String(this.breakoutTabStartMonth).padStart(2, '0');\n      this.breakoutTabStartDate = `${this.breakoutTabStartYear}-${month}`;\n\n      console.log(`Breakout tab start date updated to: ${this.breakoutTabStartDate}`);\n\n      // Validate the date range\n      this.validateBreakoutTabDateRange();\n    },\n\n    // Update breakout tab end date from dropdowns\n    updateBreakoutEndDate() {\n      // Format the date as YYYY-MM\n      const month = String(this.breakoutTabEndMonth).padStart(2, '0');\n      this.breakoutTabEndDate = `${this.breakoutTabEndYear}-${month}`;\n\n      console.log(`Breakout tab end date updated to: ${this.breakoutTabEndDate}`);\n\n      // Validate the date range\n      this.validateBreakoutTabDateRange();\n    },\n\n    // Validate breakout tab date range and trigger analysis if valid\n    validateBreakoutTabDateRange() {\n      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n        this.breakoutTabNoDataMessage = 'Please select both start and end dates';\n        console.error('Missing breakout tab start or end date');\n        return false;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.breakoutTabStartDate + '-01');\n      const end = new Date(this.breakoutTabEndDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.breakoutTabNoDataMessage = 'Invalid date format';\n        console.error('Invalid breakout tab date format', {\n          start: this.breakoutTabStartDate,\n          end: this.breakoutTabEndDate\n        });\n        return false;\n      }\n\n      if (start > end) {\n        console.error('Invalid date range: Start date is after end date', {\n          start: this.breakoutTabStartDate,\n          end: this.breakoutTabEndDate\n        });\n        this.breakoutTabNoDataMessage = 'Start date must be before end date';\n        return false;\n      }\n\n      // Set the time range to custom\n      this.breakoutTabTimeRange = 'custom';\n\n      // Clear any error message\n      this.breakoutTabNoDataMessage = '';\n\n      // Analyze the data with the new date range\n      console.log(`Breakout tab date range validated: ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);\n      this.analyzeBreakoutData();\n      return true;\n    },\n\n    // Dashboard methods\n\n    loadDashboardData() {\n      console.log('Loading dashboard data...');\n      this.dashboardNoDataMessage = 'Loading dashboard data...';\n      this.isDashboardLoading = true;\n      this.dashboardData = [];\n\n      // Use the global date range\n      const startDate = this.startDate;\n      const endDate = this.endDate;\n\n      if (!startDate || !endDate) {\n        this.dashboardNoDataMessage = 'Please select a valid date range';\n        this.isDashboardLoading = false;\n        console.error('Invalid date range for dashboard', { startDate, endDate });\n        return;\n      }\n\n      console.log(`Dashboard date range: ${startDate} to ${endDate}`);\n\n      // Calculate a new start date that is 2 months before the selected start date\n      // This allows us to detect critical issues that started before the visible range\n      const extendedStartDate = this.getDateMonthsAgo(startDate, 2);\n      console.log(`Extended start date (2 months before): ${extendedStartDate}`);\n\n      // Convert to full date format for API\n      const apiStartDate = this.getFirstDayOfMonth(extendedStartDate);\n      const apiEndDate = this.getLastDayOfMonth(endDate);\n\n      console.log(`Dashboard API date range: ${apiStartDate} to ${apiEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Calculate base rate period (1 year back from start date)\n      const baseStartDateObj = new Date(startDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      console.log(`Dashboard base rate period starts at: ${baseStartDate}`);\n\n      // Get data for all breakout groups\n      // Use the YYYY-MM format directly for the API to ensure exact month matching\n      const requestData = {\n        startDate: extendedStartDate, // Use the extended start date (2 months before) for the API\n        endDate: endDate, // Use the YYYY-MM format directly\n        baseStartDate: baseStartDate,\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true, // Add flag to indicate we're using YYYY-MM format\n        displayStartDate: startDate // Add the original start date for display purposes\n      };\n\n      // Add filter based on selected type and value\n      if (this.selectedOwner && this.selectedOwner !== 'All') {\n        if (this.selectedFilterType === 'group') {\n          requestData.owningGroup = this.selectedOwner;\n          console.log(`Filtering dashboard by group: ${this.selectedOwner}`);\n        } else if (this.selectedFilterType === 'dev_owner') {\n          requestData.filterType = 'dev_owner';\n          requestData.owner = this.selectedOwner;\n          console.log(`Filtering dashboard by dev owner: ${this.selectedOwner}`);\n        } else if (this.selectedFilterType === 'pqe_owner') {\n          requestData.filterType = 'pqe_owner';\n          requestData.owner = this.selectedOwner;\n          console.log(`Filtering dashboard by PQE owner: ${this.selectedOwner}`);\n        }\n      }\n\n      console.log('Dashboard API request data:', requestData);\n\n      axios.post('/api-statit2/get_metis_xfactors', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const xfactorData = response.data.xfactors;\n            console.log(`Dashboard received data for ${Object.keys(xfactorData).length} breakout groups`);\n\n            if (Object.keys(xfactorData).length === 0) {\n              this.dashboardNoDataMessage = 'No data found for the selected time range';\n              this.isDashboardLoading = false;\n              console.warn('No data found for dashboard');\n              return;\n            }\n\n            // Process data for dashboard\n            this.processDashboardData(xfactorData, startDate, endDate);\n            this.dashboardNoDataMessage = '';\n          } else {\n            console.error('Error loading dashboard data:', response.data);\n            this.dashboardNoDataMessage = 'Error loading dashboard data';\n            if (response.data.sql_error_msg) {\n              console.error('SQL error:', response.data.sql_error_msg);\n              this.dashboardNoDataMessage += `: ${response.data.sql_error_msg}`;\n            }\n          }\n          this.isDashboardLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading dashboard data:', error);\n          this.dashboardNoDataMessage = `Error: ${error.message}`;\n          this.isDashboardLoading = false;\n        });\n    },\n\n    processDashboardData(xfactorData, startDate, endDate) {\n      console.log(`\\n=== PROCESSING DASHBOARD DATA ===`);\n      console.log(`Input Start Date: ${startDate}`);\n      console.log(`Input End Date: ${endDate}`);\n\n      // Extract all unique months from the API response\n      const allApiMonths = new Set();\n\n      // Collect all months from the API data\n      Object.values(xfactorData).forEach(breakoutData => {\n        if (breakoutData.xFactors) {\n          Object.keys(breakoutData.xFactors).forEach(month => {\n            allApiMonths.add(month);\n          });\n        }\n      });\n\n      // Convert to array and sort\n      const apiMonths = Array.from(allApiMonths).sort();\n      console.log('All months from API:', apiMonths);\n\n      // Use the display start date if provided in the API response\n      const displayStartDate = xfactorData.displayStartDate || startDate;\n      console.log(`Using display start date: ${displayStartDate}`);\n\n      // Extract year and month directly from the input strings for comparison\n      let startYear, startMonth, endYear, endMonth;\n\n      // Parse start date (use display start date for filtering)\n      if (displayStartDate.length === 7) {\n        // Format is YYYY-MM\n        [startYear, startMonth] = displayStartDate.split('-');\n      } else if (displayStartDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [startYear, startMonth] = displayStartDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid start date format: ${displayStartDate}`);\n        return;\n      }\n\n      // Parse end date\n      if (endDate.length === 7) {\n        // Format is YYYY-MM\n        [endYear, endMonth] = endDate.split('-');\n      } else if (endDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [endYear, endMonth] = endDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid end date format: ${endDate}`);\n        return;\n      }\n\n      // Convert to numbers for comparison\n      startYear = parseInt(startYear, 10);\n      startMonth = parseInt(startMonth, 10);\n      endYear = parseInt(endYear, 10);\n      endMonth = parseInt(endMonth, 10);\n\n      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);\n      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);\n\n      // Filter API months to only include those within the selected date range\n      // Strictly enforce the date range boundaries\n      const filteredMonths = apiMonths.filter(month => {\n        // Extract year and month for comparison\n        const [monthYear, monthMonth] = month.split('-').map(num => parseInt(num, 10));\n\n        // Check if the month is within the exact range (inclusive)\n        const isAfterOrEqualStart = (monthYear > startYear) ||\n                                   (monthYear === startYear && monthMonth >= startMonth);\n\n        const isBeforeOrEqualEnd = (monthYear < endYear) ||\n                                  (monthYear === endYear && monthMonth <= endMonth);\n\n        return isAfterOrEqualStart && isBeforeOrEqualEnd;\n      });\n\n      console.log('Filtered months based on selected range:', filteredMonths);\n\n      // Use the filtered months from the API data\n      const months = filteredMonths;\n      console.log('Using filtered months from API data:', months);\n      console.log(`===================================\\n`);\n\n      // Format months for display in the table header\n      this.dashboardMonths = months.map(date => {\n        // Parse the YYYY-MM format directly\n        const [year, month] = date.split('-');\n\n        // Create a date object with UTC to avoid timezone issues\n        const d = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, 1));\n\n        // Log the date conversion for debugging\n        console.log(`Converting date ${date} to formatted month: ${d.toUTCString()}`);\n\n        // Format using the numeric month and year\n        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];\n        return `${monthNames[parseInt(month) - 1]} ${year}`;\n      });\n\n      console.log('Formatted dashboard months:', this.dashboardMonths);\n\n      // Process data for each breakout group\n      const dashboardData = [];\n\n      // Log the number of breakout groups\n      console.log(`Processing ${Object.keys(xfactorData).length} breakout groups`);\n\n      Object.entries(xfactorData).forEach(([breakoutName, data]) => {\n        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {\n          console.log(`No data for breakout group: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing breakout group: ${breakoutName} with ${Object.keys(data.xFactors).length} data points`);\n\n        const row = {\n          breakoutName,\n          months: []\n        };\n\n        // Initialize cells for each month\n        months.forEach(month => {\n          row.months.push({\n            month,\n            breakoutName,\n            xFactor: null,\n            status: null,\n            duration: null,\n            defects: null,\n            volume: null\n          });\n        });\n\n        // Log available periods in the data\n        console.log(`Available periods for ${breakoutName}:`, Object.keys(data.xFactors));\n\n        // Fill in data for months that have data\n        Object.entries(data.xFactors).forEach(([period, monthData]) => {\n          // Find the index of this period in our months array\n          const monthIndex = months.findIndex(m => m === period);\n\n          if (monthIndex >= 0) {\n            console.log(`Found data for ${period} at index ${monthIndex}`);\n            row.months[monthIndex].xFactor = monthData.xFactor;\n            row.months[monthIndex].defects = monthData.defects;\n            row.months[monthIndex].volume = monthData.volume;\n\n            // Determine status\n            if (monthData.xFactor > 3.0) {\n              row.months[monthIndex].status = 'Short-Term Spike';\n            }\n          } else {\n            console.warn(`Period ${period} not found in months array for ${breakoutName}`);\n          }\n        });\n\n        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n        for (let i = 0; i < row.months.length - 2; i++) {\n          const cell1 = row.months[i];\n          const cell2 = row.months[i + 1];\n          const cell3 = row.months[i + 2];\n\n          if (cell1.xFactor > 1.5 && cell2.xFactor > 1.5 && cell3.xFactor > 1.5) {\n            // Count consecutive months\n            let consecutiveMonths = 3;\n            for (let j = i + 3; j < row.months.length; j++) {\n              if (row.months[j].xFactor > 1.5) {\n                consecutiveMonths++;\n              } else {\n                break;\n              }\n            }\n\n            // Mark all cells in the sustained problem\n            for (let j = i; j < i + consecutiveMonths; j++) {\n              // If already marked as spike, mark as critical (both)\n              if (row.months[j].status === 'Short-Term Spike') {\n                row.months[j].status = 'Critical';\n              } else {\n                row.months[j].status = 'Sustained Problem';\n              }\n              row.months[j].duration = consecutiveMonths;\n\n              // Mark the last month of the sustained problem to make it blink\n              if (j === i + consecutiveMonths - 1) {\n                row.months[j].isLastMonthOfSustained = true;\n                console.log(`Marking last month of sustained problem for ${row.breakoutName} at index ${j}`);\n              }\n            }\n\n            // Skip the cells we've already processed\n            i += consecutiveMonths - 1;\n          }\n        }\n\n        dashboardData.push(row);\n      });\n\n      // Sort by breakout name\n      dashboardData.sort((a, b) => a.breakoutName.localeCompare(b.breakoutName));\n\n      this.dashboardData = dashboardData;\n      console.log(`Processed dashboard data for ${dashboardData.length} breakout groups`);\n\n      // Count critical issues for the current month\n      this.countCurrentMonthCriticalIssues();\n    },\n\n    // Count critical issues for the current month\n    countCurrentMonthCriticalIssues() {\n      if (!this.dashboardData || this.dashboardData.length === 0 || !this.dashboardMonths || this.dashboardMonths.length === 0) {\n        this.currentMonthCriticalIssues = 0;\n        return;\n      }\n\n      // Get the most recent month (last in the array)\n      const currentMonthIndex = this.dashboardMonths.length - 1;\n\n      // Count critical issues across all breakout groups for the current month\n      let criticalIssueCount = 0;\n\n      this.dashboardData.forEach(row => {\n        if (row.months && row.months.length > currentMonthIndex) {\n          const cell = row.months[currentMonthIndex];\n          if (cell && (cell.status === 'Critical' || cell.status === 'Short-Term Spike')) {\n            criticalIssueCount++;\n          }\n        }\n      });\n\n      this.currentMonthCriticalIssues = criticalIssueCount;\n      console.log(`Found ${criticalIssueCount} critical issues for the current month (${this.getCurrentMonthName()})`);\n    },\n\n    getMonthsBetweenDates(startDate, endDate) {\n      console.log(`\\n=== GETTING MONTHS BETWEEN DATES ===`);\n      console.log(`Input Start Date: ${startDate}`);\n      console.log(`Input End Date: ${endDate}`);\n\n      // Generate months between start and end dates\n      const result = [];\n\n      // Extract year and month directly from the input strings\n      let startYear, startMonth, endYear, endMonth;\n\n      // Parse start date\n      if (startDate.length === 7) {\n        // Format is YYYY-MM\n        [startYear, startMonth] = startDate.split('-');\n      } else if (startDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [startYear, startMonth] = startDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid start date format: ${startDate}`);\n        return result;\n      }\n\n      // Parse end date\n      if (endDate.length === 7) {\n        // Format is YYYY-MM\n        [endYear, endMonth] = endDate.split('-');\n      } else if (endDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [endYear, endMonth] = endDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid end date format: ${endDate}`);\n        return result;\n      }\n\n      // Convert to numbers\n      startYear = parseInt(startYear, 10);\n      startMonth = parseInt(startMonth, 10);\n      endYear = parseInt(endYear, 10);\n      endMonth = parseInt(endMonth, 10);\n\n      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);\n      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);\n\n      // Validate the date range\n      if (startYear > endYear || (startYear === endYear && startMonth > endMonth)) {\n        console.error(`Invalid date range: start date is after end date`);\n        return result;\n      }\n\n      // Generate all months in the range\n      let currentYear = startYear;\n      let currentMonth = startMonth;\n\n      console.log(`Generating monthly entries from ${startYear}-${startMonth} to ${endYear}-${endMonth}`);\n\n      while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {\n        // Format as YYYY-MM\n        const yearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;\n        result.push(yearMonth);\n        console.log(`Added month: ${yearMonth}`);\n\n        // Move to the next month\n        currentMonth++;\n        if (currentMonth > 12) {\n          currentMonth = 1;\n          currentYear++;\n        }\n      }\n\n      console.log(`Generated ${result.length} months between dates`);\n      console.log(`===================================\\n`);\n      return result;\n    },\n\n    getCellClass(cell) {\n      // Only return cell-empty if the cell is completely missing or has no month\n      if (!cell || !cell.month) {\n        return 'cell-empty';\n      }\n\n      if (cell.status === 'Critical') {\n        // Only add the blink class if this is the last month of a sustained problem\n        if (cell.isLastMonthOfSustained) {\n          return 'cell-critical blink';\n        } else {\n          return 'cell-critical';\n        }\n      } else if (cell.status === 'Short-Term Spike') {\n        return 'cell-spike'; // Removed 'blink' class to prevent flashing\n      } else if (cell.status === 'Sustained Problem') {\n        // Only add the blink class if this is the last month of a sustained problem\n        if (cell.isLastMonthOfSustained) {\n          return 'cell-sustained blink';\n        } else {\n          return 'cell-sustained';\n        }\n      } else {\n        // Always return cell-normal for cells with valid month, even if xFactor is 0\n        return 'cell-normal';\n      }\n    },\n\n    showCellTooltip(cell, event) {\n      // Allow tooltips even for cells with 0 defects (xFactor might be 0)\n      if (!cell || !cell.month) return;\n\n      // Parse the YYYY-MM format directly\n      const [year, month] = cell.month.split('-');\n\n      // Get month name using a simple lookup\n      const monthNames = [\n        'January', 'February', 'March', 'April', 'May', 'June',\n        'July', 'August', 'September', 'October', 'November', 'December'\n      ];\n\n      // Get target rate if available\n      const targetRate = this.xFactorData &&\n                        this.xFactorData[cell.breakoutName] &&\n                        this.xFactorData[cell.breakoutName].targetRate ?\n                        this.xFactorData[cell.breakoutName].targetRate : 'N/A';\n\n      // Count critical issues for this cell\n      const criticalIssues = this.countCriticalIssuesForCell(cell.breakoutName, cell.month);\n\n      this.tooltipData = {\n        breakoutName: cell.breakoutName,\n        month: `${monthNames[parseInt(month) - 1]} ${year}`,\n        xFactor: cell.xFactor !== undefined ? cell.xFactor : 0,\n        targetRate: targetRate,\n        status: cell.status || 'Normal',\n        duration: cell.duration,\n        defects: cell.defects !== undefined ? cell.defects : 0,\n        volume: cell.volume !== undefined ? cell.volume : 0,\n        criticalIssues: criticalIssues\n      };\n\n      // Position tooltip near the mouse\n      const offset = 10;\n      this.tooltipStyle = {\n        top: `${event.clientY + offset}px`,\n        left: `${event.clientX + offset}px`\n      };\n\n      this.showTooltip = true;\n    },\n\n    // Count critical issues for a specific breakout group and month\n    countCriticalIssuesForCell(breakoutName, month) {\n      // This is a placeholder implementation - in a real implementation,\n      // you would query the actual critical issues from root cause, vintage, and supplier analyses\n\n      // For now, we'll use a simple heuristic:\n      // If the cell has a status of 'Critical' or 'Short-Term Spike', count it as 1 critical issue\n      // In a real implementation, you would need to query the actual critical issues\n\n      // Find the cell in the dashboard data\n      const row = this.dashboardData.find(row => row.breakoutName === breakoutName);\n      if (!row) return 0;\n\n      const cell = row.months.find(cell => cell.month === month);\n      if (!cell) return 0;\n\n      // Count as a critical issue if the status is Critical or Short-Term Spike\n      if (cell.status === 'Critical' || cell.status === 'Short-Term Spike') {\n        return 1;\n      }\n\n      return 0;\n    },\n\n    // Get the current month name (most recent month in the dashboard)\n    getCurrentMonthName() {\n      if (!this.dashboardMonths || this.dashboardMonths.length === 0) {\n        return '';\n      }\n\n      // Get the most recent month (last in the array)\n      return this.dashboardMonths[this.dashboardMonths.length - 1];\n    },\n\n    hideCellTooltip() {\n      this.showTooltip = false;\n    },\n\n    selectBreakoutFromDashboard(breakoutName) {\n      console.log(`Navigating to Group tab for ${breakoutName}`);\n\n      // Set the selected breakout group first\n      this.group2TabSelectedGroup = breakoutName;\n\n      // Use a more direct approach to select the Group tab\n      this.activeTab = 3; // Set the active tab index to 3 (Group tab)\n\n      // Find all tabs and click the Group tab directly\n      this.$nextTick(() => {\n        // Try different selector approaches\n        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');\n        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);\n\n        if (tabElements && tabElements.length > 3) {\n          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');\n          tabElements[3].click();\n\n          // Trigger root cause analysis for this breakout group\n          this.$nextTick(() => {\n            console.log(`Viewing root cause analysis for ${breakoutName}`);\n            this.viewRootCauseAnalysis(3); // Show 3 months of data\n          });\n        } else {\n          // Try an alternative selector\n          const altTabElements = document.querySelectorAll('.cv-tab');\n          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);\n\n          if (altTabElements && altTabElements.length > 3) {\n            console.log('Clicking Group tab using .cv-tab selector');\n            altTabElements[3].click();\n\n            // Trigger root cause analysis for this breakout group\n            this.$nextTick(() => {\n              console.log(`Viewing root cause analysis for ${breakoutName}`);\n              this.viewRootCauseAnalysis(3); // Show 3 months of data\n            });\n          } else {\n            console.error('Group tab not found with any selector');\n          }\n        }\n      });\n    },\n\n    selectBreakoutFromDashboardWithMonth(breakoutName, month) {\n      console.log(`Navigating to Group tab for ${breakoutName} with month ${month}`);\n\n      // Set the selected breakout group first\n      this.group2TabSelectedGroup = breakoutName;\n\n      // Store the selected month for potential use in analysis\n      this.selectedDashboardMonth = month;\n\n      // Use a more direct approach to select the Group tab\n      this.activeTab = 3; // Set the active tab index to 3 (Group tab)\n\n      // Find all tabs and click the Group tab directly\n      this.$nextTick(() => {\n        // Try different selector approaches\n        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');\n        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);\n\n        if (tabElements && tabElements.length > 3) {\n          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');\n          tabElements[3].click();\n\n          // Trigger root cause analysis for this breakout group and month\n          this.$nextTick(() => {\n            console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);\n            this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month\n          });\n        } else {\n          // Try an alternative selector\n          const altTabElements = document.querySelectorAll('.cv-tab');\n          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);\n\n          if (altTabElements && altTabElements.length > 3) {\n            console.log('Clicking Group tab using .cv-tab selector');\n            altTabElements[3].click();\n\n            // Trigger root cause analysis for this breakout group and month\n            this.$nextTick(() => {\n              console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);\n              this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month\n            });\n          } else {\n            console.error('Group tab not found with any selector');\n          }\n        }\n      });\n    },\n\n    formatDashboardDateRange() {\n      // Format the date range for display in the dashboard info section\n      const today = new Date();\n      let startDate, endDate;\n\n      if (this.dashboardTimeRange === 'custom' && this.dashboardStartDate && this.dashboardEndDate) {\n        // Format custom date range\n        const startDateObj = new Date(this.dashboardStartDate + '-01');\n        const endDateObj = new Date(this.dashboardEndDate + '-01');\n\n        // Format as \"MMM YYYY to MMM YYYY\" (e.g., \"Nov 2024 to May 2025\")\n        startDate = startDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n        endDate = endDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n\n        // For display in the dashboard info section\n        return `${startDate} to ${endDate}`;\n      }\n\n      switch (this.dashboardTimeRange) {\n        case 'last-month': {\n          // For last month, we want the entire previous month\n          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);\n          startDate = lastMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });\n          endDate = startDate; // Same month for start and end\n          return startDate; // Just return the month name for last-month\n        }\n\n        case 'last-3-months': {\n          // For last 3 months, we want the current month and previous 2 months\n          const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 2, 1);\n          startDate = threeMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          break;\n        }\n\n        case 'last-6-months': {\n          // For last 6 months, we want the current month and previous 5 months\n          const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);\n          startDate = sixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          break;\n        }\n\n        default: {\n          // Default to last 6 months\n          const defaultSixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);\n          startDate = defaultSixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n        }\n      }\n\n      return `${startDate} to ${endDate}`;\n    },\n\n    // Format date range for display\n    formatDateRange(startDate, endDate) {\n      if (!startDate || !endDate) return 'No date range selected';\n\n      const startObj = new Date(startDate + '-01');\n      const endObj = new Date(endDate + '-01');\n\n      const startFormatted = startObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n      const endFormatted = endObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n\n      return `${startFormatted} to ${endFormatted}`;\n    },\n\n    // Analyze data for all tabs\n    analyzeAllData() {\n      // Validate date range\n      if (this.validateDateRange()) {\n        console.log('Date range is valid, synchronizing across tabs');\n\n        // Synchronize date ranges across all tabs\n        this.breakoutTabStartDate = this.startDate;\n        this.breakoutTabEndDate = this.endDate;\n        this.breakoutTabStartMonth = this.startMonth;\n        this.breakoutTabStartYear = this.startYear;\n        this.breakoutTabEndMonth = this.endMonth;\n        this.breakoutTabEndYear = this.endYear;\n\n        // Analyze data for main tab\n        this.analyzeData();\n\n        // Analyze data for breakout tab if a group is selected\n        if (this.breakoutTabSelectedGroup) {\n          console.log(`Analyzing data for selected breakout group: ${this.breakoutTabSelectedGroup}`);\n          this.analyzeBreakoutData();\n        }\n\n        // Load dashboard data with the same date range\n        this.loadDashboardData();\n      } else {\n        console.error('Date range validation failed');\n      }\n    },\n\n    // Update start date from dropdowns\n    updateStartDate() {\n      // Convert string to number for internal use\n      this.startMonth = parseInt(this.startMonthStr, 10);\n\n      // Ensure startMonth is a valid number\n      if (isNaN(this.startMonth)) {\n        console.error('Invalid startMonth value:', this.startMonthStr);\n        return;\n      }\n\n      // Format the date as YYYY-MM\n      const month = String(this.startMonth).padStart(2, '0');\n      this.startDate = `${this.startYear}-${month}`;\n\n      // Synchronize with breakout tab\n      this.breakoutTabStartDate = this.startDate;\n      this.breakoutTabStartMonth = this.startMonth;\n      this.breakoutTabStartYear = this.startYear;\n\n      // Set time range to custom since user manually selected dates\n      this.selectedTimeRange = '';\n      this.breakoutTabTimeRange = '';\n\n      // Validate the date range\n      this.validateDateRange();\n    },\n\n    // Update end date from dropdowns\n    updateEndDate() {\n      // Convert string to number for internal use\n      this.endMonth = parseInt(this.endMonthStr, 10);\n\n      // Ensure endMonth is a valid number\n      if (isNaN(this.endMonth)) {\n        console.error('Invalid endMonth value:', this.endMonthStr);\n        return;\n      }\n\n      // Format the date as YYYY-MM\n      const month = String(this.endMonth).padStart(2, '0');\n      this.endDate = `${this.endYear}-${month}`;\n\n      // Synchronize with breakout tab\n      this.breakoutTabEndDate = this.endDate;\n      this.breakoutTabEndMonth = this.endMonth;\n      this.breakoutTabEndYear = this.endYear;\n\n      // Set time range to custom since user manually selected dates\n      this.selectedTimeRange = '';\n      this.breakoutTabTimeRange = '';\n\n      // Validate the date range\n      this.validateDateRange();\n    },\n\n    // Handle filter type change\n    onFilterTypeChange() {\n      console.log(`Filter type changed to: ${this.selectedFilterType}`);\n      // Reset the owner selection when changing filter type\n      this.selectedOwner = 'All';\n      // Don't reload dashboard data until owner is selected\n      console.log('Dashboard data will be reloaded when an owner is selected');\n    },\n\n    // AI Summary tooltip methods\n    showAiSummaryTooltip(event) {\n      // Position the tooltip near the cursor\n      const rect = event.target.getBoundingClientRect();\n      this.aiTooltipStyle = {\n        top: `${rect.bottom + window.scrollY + 5}px`,\n        left: `${rect.left + window.scrollX}px`\n      };\n      this.showAiTooltip = true;\n    },\n\n    hideAiSummaryTooltip() {\n      this.showAiTooltip = false;\n    },\n\n    // Get AI summary for a breakout group using WatsonX.ai\n    async getAiSummary(row) {\n      logger.logInfo(`Getting AI summary for breakout group: ${row.breakoutName}`, 'getAiSummary');\n      this.hideAiSummaryTooltip();\n      this.showAiSummaryModal = true;\n      this.aiSummaryBreakoutName = row.breakoutName;\n      this.isLoadingAiSummary = true;\n      this.loadingAiSummaryFor = row.breakoutName;\n      this.aiSummaryText = '';\n\n      logger.logInfo('Preparing data for WatsonX.ai...', 'getAiSummary');\n\n      try {\n        // Get Action Tracker data for this breakout group\n        const actionTrackerData = await this.getActionTrackerData(row.breakoutName);\n        logger.logInfo('Action Tracker data retrieved', 'getAiSummary');\n\n        // Prepare data for WatsonX.ai\n        const data = this.prepareBreakoutDataForAi(row, actionTrackerData);\n        logger.logInfo(`Data preparation complete. Data length: ${data.length}`, 'getAiSummary');\n\n        // Parse the data to extract actionTrackerInsights if available\n        const parsedData = JSON.parse(data);\n        const actionTrackerInsights = parsedData.actionTrackerInsights;\n        logger.logInfo(`Action tracker insights available: ${!!actionTrackerInsights}`, 'getAiSummary');\n\n        // Format the prompt using the template from watsonxPrompts.js\n        const prompt = formatDashboardSummaryPrompt(\n          data,\n          actionTrackerInsights ? JSON.stringify(actionTrackerInsights) : null\n        );\n\n        logger.logInfo(`Formatted prompt for WatsonX.ai. Length: ${prompt.length}`, 'getAiSummary');\n        logger.logInfo(`Prompt (first 200 chars): ${prompt.substring(0, 200)}...`, 'getAiSummary');\n\n        // Get token from localStorage\n        const token = localStorage.getItem('token');\n        logger.logInfo(`Auth token available: ${!!token}`, 'getAiSummary');\n\n        // Prepare request data for WatsonX.ai\n        const requestData = {\n          model_id: 'ibm/granite-13b-instruct-v2', // Using Granite 13B Chat model\n          prompt: prompt,\n          temperature: 0.3, // Lower temperature for more focused responses\n          api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n          project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek\n        };\n\n        logger.logInfo('Request data prepared', 'getAiSummary');\n\n        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'getAiSummary');\n\n        // Call WatsonX.ai API using fetch\n        const response = await fetch('/api-statit2/watsonx_prompt', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token ? `Bearer ${token}` : ''\n          },\n          body: JSON.stringify(requestData)\n        });\n\n        logger.logInfo(`Received response from API. Status: ${response.status}`, 'getAiSummary');\n\n        // Check if response is ok\n        if (!response.ok) {\n          const errorText = await response.text();\n          logger.logError('Error response text', errorText, 'getAiSummary');\n\n          // Try to parse as JSON if possible\n          try {\n            const errorJson = JSON.parse(errorText);\n            logger.logError('Error response JSON', errorJson, 'getAiSummary');\n            throw new Error(`HTTP error! Status: ${response.status}, ${JSON.stringify(errorJson)}`);\n          } catch (jsonError) {\n            // Not JSON, use as text\n            throw new Error(`HTTP error! Status: ${response.status}, ${errorText}`);\n          }\n        }\n\n        // Parse the JSON response\n        const responseText = await response.text();\n        logger.logInfo('Raw response received', 'getAiSummary');\n\n        let responseData;\n        try {\n          responseData = JSON.parse(responseText);\n          logger.logInfo('Successfully parsed WatsonX.ai response', 'getAiSummary');\n        } catch (jsonError) {\n          logger.logError('Error parsing JSON response', jsonError, 'getAiSummary');\n          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);\n        }\n\n        // Process the response data\n        if (responseData.status === 'success') {\n          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'getAiSummary');\n\n          if (responseData.generated_text) {\n            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'getAiSummary');\n            this.aiSummaryText = responseData.generated_text;\n            logger.logInfo('AI summary set successfully', 'getAiSummary');\n          } else {\n            logger.logError('No generated_text in success response', responseData, 'getAiSummary');\n            this.aiSummaryText = 'No summary generated';\n          }\n        } else {\n          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'getAiSummary');\n          throw new Error(responseData.error || 'Unknown error occurred');\n        }\n      } catch (error) {\n        logger.logError('Error getting AI summary', error, 'getAiSummary');\n\n        // Create a detailed error message for the user\n        let errorMessage = 'Error generating AI summary: ';\n\n        if (error.message) {\n          errorMessage += error.message;\n        }\n\n        // Add HTTP status code if available\n        if (error.response && error.response.status) {\n          errorMessage += ` (Status: ${error.response.status})`;\n        }\n\n        // Add more details if available\n        if (error.response && error.response.data) {\n          errorMessage += `<br><br><strong>Details:</strong><br>${JSON.stringify(error.response.data)}`;\n        }\n\n        // Add troubleshooting suggestions\n        errorMessage += '<br><br><strong>Troubleshooting:</strong><br>' +\n          '• Check your internet connection<br>' +\n          '• Verify that the WatsonX.ai API key is valid<br>' +\n          '• Try again in a few minutes';\n\n        this.aiSummaryText = errorMessage;\n      } finally {\n        this.isLoadingAiSummary = false;\n        this.loadingAiSummaryFor = '';\n      }\n    },\n\n    // Get Action Tracker data for a specific breakout group\n    async getActionTrackerData(breakoutName) {\n      console.log('Getting Action Tracker data' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n      try {\n        // Get authentication config for API call\n        const config = this.getAuthConfig();\n\n        // Make API call to get action tracker data\n        console.log('Fetching action tracker data from API' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n        // Use fetch instead of axios for API call\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': config.headers && config.headers.Authorization ? config.headers.Authorization : ''\n          },\n          body: JSON.stringify({ breakoutName })\n        });\n\n        // Check if response is ok\n        if (!response.ok) {\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\n        }\n\n        // Parse response data\n        const data = await response.json();\n        console.log(`Retrieved ${data.items ? data.items.length : 0} action items` + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n        // If no items property exists, create an empty array\n        if (!data.items) {\n          data.items = [];\n        }\n\n        return data;\n      } catch (error) {\n        console.error('Error getting Action Tracker data:', error);\n        // Return empty data on error\n        return { items: [] };\n      }\n    },\n\n    // Prepare breakout data for AI\n    prepareBreakoutDataForAi(row, actionTrackerData = { items: [] }) {\n      // Create a simplified data structure for the AI\n      const data = {\n        breakoutName: row.breakoutName,\n        months: [],\n        actionTrackerInsights: null\n      };\n\n      // Add month data\n      row.months.forEach((cell, index) => {\n        if (cell && this.dashboardMonths[index]) {\n          data.months.push({\n            month: this.dashboardMonths[index],\n            xFactor: cell.xFactor !== null && cell.xFactor !== undefined ? cell.xFactor : 0,\n            status: cell.status || 'Normal',\n            defects: cell.defects !== null && cell.defects !== undefined ? cell.defects : 0,\n            volume: cell.volume !== null && cell.volume !== undefined ? cell.volume : 0,\n            targetRate: cell.targetRate !== null && cell.targetRate !== undefined ? cell.targetRate : 0\n          });\n        }\n      });\n\n      // Add Action Tracker data if available\n      if (actionTrackerData && actionTrackerData.items && actionTrackerData.items.length > 0) {\n        // Format the Action Tracker data for the AI\n        data.actionTrackerInsights = {\n          totalItems: actionTrackerData.items.length,\n          items: actionTrackerData.items.map(item => ({\n            commodity: item.commodity,\n            group: item.group,\n            partNumber: item.pn,\n            test: item.test,\n            deadline: item.deadline,\n            status: item.status,\n            action: item.action,\n            assignee: item.assignee,\n            notes: item.notes\n          }))\n        };\n\n        console.log('Including Action Tracker data in AI summary:', data.actionTrackerInsights);\n      } else {\n        console.log('No Action Tracker data available for this breakout group');\n      }\n\n      const jsonData = JSON.stringify(data);\n      console.log('Data prepared for AI summary:', data);\n      console.log('JSON data being sent to NeuralSeek:', jsonData.substring(0, 200) + (jsonData.length > 200 ? '...' : ''));\n\n      return jsonData;\n    },\n\n    // This method has been replaced by direct calls to WatsonX.ai API\n\n    // Handle owner selection change\n    onOwnerChange() {\n      console.log(`Owner selection changed to: ${this.selectedOwner} (type: ${this.selectedFilterType})`);\n      // Reload dashboard data with the new owner filter\n      this.loadDashboardData();\n    },\n\n    // Validate date range\n    validateDateRange() {\n      if (!this.startDate || !this.endDate) {\n        this.noDataMessage = 'Please select both start and end dates';\n        console.error('Missing start or end date');\n        return false;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.startDate + '-01');\n      const end = new Date(this.endDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.noDataMessage = 'Invalid date format';\n        console.error('Invalid date format', { start: this.startDate, end: this.endDate });\n        return false;\n      }\n\n      if (start > end) {\n        this.noDataMessage = 'Start date must be before end date';\n        console.error('Start date is after end date', { start: this.startDate, end: this.endDate });\n        return false;\n      }\n\n      // Set the time range to custom when manually validating\n      this.selectedTimeRange = '';\n\n      // Clear any error message\n      this.noDataMessage = '';\n\n      console.log(`Date range validated: ${this.startDate} to ${this.endDate}`);\n      return true;\n    },\n\n    generateBreakoutAlerts(breakoutData) {\n      console.log('Generating breakout tab alerts...');\n      this.breakoutTabAlerts = [];\n\n      if (!breakoutData || !breakoutData.xFactors) {\n        console.log('No xFactors data for breakout tab alerts');\n        return;\n      }\n\n      // Sort periods chronologically\n      const sortedPeriods = Object.keys(breakoutData.xFactors).sort();\n\n      if (sortedPeriods.length === 0) {\n        console.log('No periods found for breakout tab alerts');\n        return;\n      }\n\n      console.log(`Processing ${sortedPeriods.length} periods for breakout tab alerts`);\n\n      // Check for short-term spikes (X-Factor > 3.0)\n      sortedPeriods.forEach(period => {\n        if (!breakoutData.xFactors[period]) {\n          console.error(`Missing data for period ${period} in breakout tab`);\n          return;\n        }\n\n        const xFactor = breakoutData.xFactors[period].xFactor;\n\n        // Ensure xFactor is a valid number\n        if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {\n          console.error(`Invalid xFactor in breakout tab for period ${period}: ${xFactor}`);\n          return;\n        }\n\n        if (xFactor > 3.0) {\n          console.log(`Found short-term spike in breakout tab for ${period}: ${xFactor.toFixed(2)}`);\n          this.breakoutTabAlerts.push({\n            status: 'Short-Term Spike',\n            period,\n            xFactor,\n            defects: breakoutData.xFactors[period].defects || 0,\n            volume: breakoutData.xFactors[period].volume || 0\n          });\n        }\n      });\n\n      // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n      for (let i = 0; i < sortedPeriods.length - 2; i++) {\n        const period1 = sortedPeriods[i];\n        const period2 = sortedPeriods[i + 1];\n        const period3 = sortedPeriods[i + 2];\n\n        // Ensure all periods have valid data\n        if (!breakoutData.xFactors[period1] || !breakoutData.xFactors[period2] || !breakoutData.xFactors[period3]) {\n          console.error(`Missing data for one of the periods in breakout tab sustained problem check: ${period1}, ${period2}, ${period3}`);\n          continue;\n        }\n\n        const xFactor1 = breakoutData.xFactors[period1].xFactor;\n        const xFactor2 = breakoutData.xFactors[period2].xFactor;\n        const xFactor3 = breakoutData.xFactors[period3].xFactor;\n\n        // Ensure all xFactors are valid numbers\n        if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||\n            typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||\n            typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {\n          console.error(`Invalid xFactor values for breakout tab sustained problem check`);\n          continue;\n        }\n\n        if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {\n          // Count how many consecutive months have X-Factor > 1.5\n          let consecutiveMonths = 3;\n          for (let j = i + 3; j < sortedPeriods.length; j++) {\n            if (breakoutData.xFactors[sortedPeriods[j]] && breakoutData.xFactors[sortedPeriods[j]].xFactor > 1.5) {\n              consecutiveMonths++;\n            } else {\n              break;\n            }\n          }\n\n          console.log(`Found sustained problem in breakout tab from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);\n\n          // Calculate average xFactor for the sustained problem\n          const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;\n\n          this.breakoutTabAlerts.push({\n            status: `Sustained Problem (${consecutiveMonths} months)`,\n            period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,\n            xFactor: avgXFactor,\n            defects: breakoutData.xFactors[period3].defects || 0,\n            volume: breakoutData.xFactors[period3].volume || 0\n          });\n\n          // Skip the periods we've already included in this sustained problem\n          i += consecutiveMonths - 1;\n        }\n      }\n\n      // Sort alerts by X-Factor (highest first)\n      this.breakoutTabAlerts.sort((a, b) => b.xFactor - a.xFactor);\n\n      console.log(`Generated ${this.breakoutTabAlerts.length} breakout tab alerts`);\n    },\n\n    // Helper method to get a date X months ago in YYYY-MM format\n    getDateMonthsAgo(yearMonth, monthsAgo) {\n      const [year, month] = yearMonth.split('-').map(num => parseInt(num, 10));\n\n      // Calculate new month and year\n      let newMonth = month - monthsAgo;\n      let newYear = year;\n\n      // Handle month wrapping\n      while (newMonth <= 0) {\n        newMonth += 12;\n        newYear -= 1;\n      }\n\n      // Format as YYYY-MM\n      return `${newYear}-${String(newMonth).padStart(2, '0')}`;\n    },\n\n    // Category Analysis tab methods\n    analyzeCategoryData() {\n      if (!this.categoryTabSelectedGroup) {\n        this.categoryTabNoDataMessage = 'Please select a breakout group to analyze';\n        return;\n      }\n\n      this.categoryTabNoDataMessage = 'Loading category data...';\n      this.isCategoryTabLoading = true;\n      this.categoryTabChartData = [];\n      this.categoryTabCategories = [];\n      this.categoryTabPartNumbers = [];\n\n      console.log(`Analyzing category data for ${this.categoryTabSelectedGroup} from ${this.startDate} to ${this.endDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the global date range\n      const requestData = {\n        breakoutName: this.categoryTabSelectedGroup,\n        startDate: this.startDate,\n        endDate: this.endDate\n      };\n\n      axios.post('/api-statit2/get_metis_category_analysis', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const categoryData = response.data.categoryData;\n\n            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {\n              this.categoryTabNoDataMessage = 'No category data found for the selected breakout group and date range';\n              this.isCategoryTabLoading = false;\n              return;\n            }\n\n            // Store the categories and part numbers\n            this.categoryTabCategories = categoryData.categories || [];\n            this.categoryTabPartNumbers = response.data.partNumbers || [];\n\n            // Set up color scale for categories\n            const colorScale = {};\n            const colors = [\n              '#0f62fe', // blue\n              '#6929c4', // purple\n              '#1192e8', // cyan\n              '#005d5d', // teal\n              '#9f1853', // magenta\n              '#fa4d56', // red\n              '#570408', // maroon\n              '#198038', // green\n              '#002d9c', // navy\n              '#ee538b', // pink\n              '#b28600', // yellow\n              '#8a3800', // orange\n              '#a56eff', // light purple\n              '#08bdba', // light teal\n              '#bae6ff'  // light blue\n            ];\n\n            this.categoryTabCategories.forEach((category, index) => {\n              colorScale[category] = colors[index % colors.length];\n            });\n\n            // Update chart options with color scale\n            this.categoryChartOptions.color.scale = colorScale;\n\n            // Set chart data\n            this.categoryTabChartData = categoryData.chartData;\n            this.categoryTabNoDataMessage = '';\n\n            console.log(`Loaded ${this.categoryTabChartData.length} category data points for ${this.categoryTabSelectedGroup}`);\n            console.log(`Found ${this.categoryTabCategories.length} categories: ${this.categoryTabCategories.join(', ')}`);\n\n            // Calculate the maximum fail rate to set the Y-axis domain\n            this.updateChartYAxisDomain();\n\n            // Setup chart click handlers after chart is updated\n            this.$nextTick(() => {\n              this.setupChartClickHandlers();\n            });\n          } else {\n            console.error('Error analyzing category data:', response.data);\n            this.categoryTabNoDataMessage = 'Error analyzing category data. Please try again.';\n          }\n          this.isCategoryTabLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when analyzing category data:', error);\n          this.categoryTabNoDataMessage = `Error: ${error.message}`;\n          this.isCategoryTabLoading = false;\n        });\n    },\n\n    // Handle click on category bar chart\n    handleCategoryBarClick(event) {\n      console.log('Bar click event received:', event);\n\n      // Check if we have a valid event\n      if (!event) {\n        console.error('Invalid click event');\n        return;\n      }\n\n      let clickedData = null;\n\n      // Try to extract data from different event formats\n      if (event.datum) {\n        // Carbon Charts format\n        clickedData = event.datum;\n      } else if (event.data) {\n        // Alternative format\n        clickedData = event.data;\n      } else if (event.target && event.target.dataset) {\n        // DOM element with dataset\n        clickedData = {\n          group: event.target.dataset.group,\n          key: event.target.dataset.key\n        };\n      } else if (event.element === 'bar') {\n        // Carbon Charts v3 format\n        clickedData = event.data;\n      }\n\n      console.log('Extracted clicked data:', clickedData);\n\n      // If we couldn't extract data, try to find it in the chart data\n      if (!clickedData || (!clickedData.key && !clickedData.group)) {\n        console.log('Trying to find data from chart data...');\n\n        // Try to extract data from the event object\n        if (event.data && typeof event.data === 'object') {\n          // Try different property names that might contain the data\n          const possibleDataProps = ['data', 'datum', 'point', 'bar', 'value'];\n\n          for (const prop of possibleDataProps) {\n            if (event.data[prop] && event.data[prop].group && event.data[prop].key) {\n              clickedData = event.data[prop];\n              console.log(`Found data in event.data.${prop}:`, clickedData);\n              break;\n            }\n          }\n        }\n\n        // If still no data, use the first data point for the clicked month\n        if (!clickedData && event.target) {\n          // Try to determine which month was clicked based on position\n          const barElement = event.target.closest('.bar');\n          if (barElement) {\n            const svgElement = barElement.closest('svg');\n            if (svgElement) {\n              const rect = barElement.getBoundingClientRect();\n              const svgRect = svgElement.getBoundingClientRect();\n              const relativeX = rect.left - svgRect.left;\n              const totalWidth = svgRect.width;\n\n              // Get all unique months\n              const months = [...new Set(this.categoryTabChartData.map(d => d.key))].sort();\n\n              // Calculate which month this might be\n              const monthIndex = Math.floor((relativeX / totalWidth) * months.length);\n\n              if (monthIndex >= 0 && monthIndex < months.length) {\n                const month = months[monthIndex];\n\n                // Find all data points for this month\n                const dataForMonth = this.categoryTabChartData.filter(d => d.key === month);\n\n                if (dataForMonth.length > 0) {\n                  // Try to determine which category was clicked based on the fill color\n                  let categoryIndex = 0;\n                  if (barElement.classList.contains('bar')) {\n                    // Try to extract category index from class name\n                    const fillClass = Array.from(barElement.classList).find(cls => cls.startsWith('fill-'));\n                    if (fillClass) {\n                      const parts = fillClass.split('-');\n                      if (parts.length >= 2) {\n                        categoryIndex = parseInt(parts[1]);\n                      }\n                    }\n                  }\n\n                  // Get all unique categories\n                  const categories = [...new Set(this.categoryTabChartData.map(d => d.group))];\n\n                  // Use the determined category if valid, otherwise use the first one\n                  const category = (categoryIndex >= 0 && categoryIndex < categories.length)\n                    ? categories[categoryIndex]\n                    : dataForMonth[0].group;\n\n                  // Find the specific data point\n                  const dataPoint = this.categoryTabChartData.find(d => d.key === month && d.group === category);\n\n                  if (dataPoint) {\n                    clickedData = dataPoint;\n                    console.log('Found data point from position analysis:', clickedData);\n                  } else {\n                    // Fallback to first data point for this month\n                    clickedData = dataForMonth[0];\n                    console.log('Using first data point for month:', clickedData);\n                  }\n                }\n              }\n            }\n          }\n        }\n\n        // Last resort fallback\n        if (!clickedData && this.categoryTabChartData && this.categoryTabChartData.length > 0) {\n          // For simplicity, just use the first category and month\n          const firstDataPoint = this.categoryTabChartData[0];\n          clickedData = firstDataPoint;\n          console.log('Using fallback data point:', clickedData);\n        } else if (!clickedData) {\n          console.error('No chart data available');\n          return;\n        }\n      }\n\n      // Make sure we have the necessary properties\n      if (!clickedData.key || !clickedData.group) {\n        console.error('Missing key or group in clicked data');\n        return;\n      }\n\n      console.log(`Category bar clicked: ${clickedData.group}, Month: ${clickedData.key}`);\n\n      // Set selected month and category for the modal\n      this.selectedMonth = clickedData.key;\n      this.selectedCategory = clickedData.group;\n\n      // Load failure modes data\n      this.loadFailureModes(this.categoryTabSelectedGroup, clickedData.key, clickedData.group);\n\n      // Show the modal\n      this.showFailureModesModal = true;\n    },\n\n    // Load failure modes data for the selected month and category\n    loadFailureModes(breakoutName, month, category) {\n      this.isFailureModesLoading = true;\n      this.failureModesChartData = [];\n\n      console.log(`Loading failure modes for ${breakoutName}, Month: ${month}, Category: ${category}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Prepare request data\n      const requestData = {\n        breakoutName: breakoutName,\n        month: month,\n        category: category\n      };\n\n      axios.post('/api-statit2/get_metis_failure_modes', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const failureModeData = response.data.failureModeData;\n\n            if (!failureModeData || !failureModeData.chartData || failureModeData.chartData.length === 0) {\n              console.log('No failure modes data found');\n              this.isFailureModesLoading = false;\n              return;\n            }\n\n            // Get the Count data points\n            const countData = failureModeData.chartData.filter(item => item.group === 'Count');\n\n            // Sort by count in descending order\n            countData.sort((a, b) => b.value - a.value);\n\n            // Format data for horizontal bar chart\n            const hBarData = countData.map(item => ({\n              group: \"Count\",\n              key: item.key,\n              value: item.value,\n              category: item.category || category\n            }));\n\n            // Set chart data\n            this.failureModesChartData = hBarData;\n\n            console.log(`Created horizontal bar chart with ${hBarData.length} failure modes`);\n          } else {\n            console.error('Error loading failure modes:', response.data);\n          }\n          this.isFailureModesLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading failure modes:', error);\n          this.isFailureModesLoading = false;\n        });\n    },\n\n    // Get color for category\n    getCategoryColor(category) {\n      const colors = {\n        'Electrical': '#0f62fe',\n        'Mechanical': '#6929c4',\n        'Thermal': '#1192e8',\n        'Material': '#005d5d',\n        'Process': '#9f1853',\n        'Design': '#fa4d56',\n        'Unknown': '#570408',\n        'Other': '#198038'\n      };\n\n      return colors[category] || '#0f62fe';\n    },\n\n    // Group 2 Tab methods\n    analyzeGroup2Data() {\n      if (!this.group2TabSelectedGroup) {\n        this.group2TabNoDataMessage = 'Please select a breakout group to analyze';\n        return;\n      }\n\n      console.log(`Analyzing Group 2 data for ${this.group2TabSelectedGroup}`);\n      this.group2TabNoDataMessage = '';\n\n      // Clear any existing data\n      this.rootCauseChartData = [];\n      this.hasCriticalRootCauseIssue = false;\n\n      // If we have a selected dashboard month, use that to determine the time period\n      // Otherwise, default to 3 months\n      if (this.selectedDashboardMonth) {\n        console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);\n        // View root cause analysis for 1 month centered on the selected month\n        this.viewRootCauseAnalysis(1);\n      } else {\n        // Default to 3 months of data\n        console.log('No specific month selected, showing 3 months of data');\n        this.viewRootCauseAnalysis(3);\n      }\n    },\n\n    // Custom Date Modal methods\n    showCustomDateModal2(analysisType) {\n      this.customDateAnalysisType = analysisType;\n      this.showCustomDateModal = true;\n    },\n\n    hideCustomDateModal() {\n      this.showCustomDateModal = false;\n    },\n\n    // View custom analysis methods\n    viewCustomRootCauseAnalysis() {\n      // Show the custom date modal with root cause analysis type\n      this.showCustomDateModal2('rootcause');\n    },\n\n    applyCustomDateRange() {\n      // Format the dates as YYYY-MM\n      const startMonth = String(this.customStartMonthStr).padStart(2, '0');\n      const endMonth = String(this.customEndMonthStr).padStart(2, '0');\n      const startDate = `${this.customStartYear}-${startMonth}`;\n      const endDate = `${this.customEndYear}-${endMonth}`;\n\n      console.log(`Custom date range: ${startDate} to ${endDate}`);\n\n      // Validate the date range\n      if (this.validateCustomDateRange(startDate, endDate)) {\n        if (this.customDateAnalysisType === 'rootcause') {\n          this.viewRootCauseAnalysis(0, startDate, endDate);\n        }\n\n        this.hideCustomDateModal();\n      }\n    },\n\n    validateCustomDateRange(startDate, endDate) {\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(startDate + '-01');\n      const end = new Date(endDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        console.error('Invalid custom date format', {\n          start: startDate,\n          end: endDate\n        });\n        return false;\n      }\n\n      if (start > end) {\n        console.error('Invalid date range: Start date is after end date', {\n          start: startDate,\n          end: endDate\n        });\n        return false;\n      }\n\n      return true;\n    },\n\n\n\n\n\n\n\n\n\n\n\n    // Root Cause Analysis methods\n    viewRootCauseAnalysis(months, customStartDate, customEndDate) {\n      if (!this.group2TabSelectedGroup) {\n        console.error('No breakout group selected for root cause analysis');\n        return;\n      }\n\n      // Clear any existing chart data\n      this.rootCauseChartData = [];\n      this.hasCriticalRootCauseIssue = false;\n      this.criticalIssues = [];\n      this.criticalIssueDescription = '';\n\n      this.isRootCauseDataLoading = true;\n      this.isRootCauseAiLoading = true;\n      this.rootCauseChartData = [];\n      this.rootCauseAiSummary = '';\n      this.hasCriticalRootCauseIssue = false;\n      this.criticalIssueUpdate = '';\n      this.criticalIssueUpdates = [];\n\n      // Set the time range description\n      if (months === 0 && customStartDate && customEndDate) {\n        this.rootCauseTimeRange = `Custom: ${this.formatDateForDisplay(customStartDate)} to ${this.formatDateForDisplay(customEndDate)}`;\n        this.rootCauseStartDate = customStartDate;\n        this.rootCauseEndDate = customEndDate;\n      } else {\n        this.rootCauseMonths = months;\n\n        // Calculate the date range based on the number of months\n        const endDate = new Date();\n        const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');\n        const endYear = endDate.getFullYear();\n        this.rootCauseEndDate = `${endYear}-${endMonth}`;\n\n        // Calculate start date by going back the specified number of months\n        const startDate = new Date();\n        startDate.setMonth(startDate.getMonth() - (months - 1));\n        const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');\n        const startYear = startDate.getFullYear();\n        this.rootCauseStartDate = `${startYear}-${startMonth}`;\n\n        this.rootCauseTimeRange = months === 1 ? 'Last Month' : `Last ${months} Months`;\n      }\n\n      console.log(`Root cause analysis for ${this.group2TabSelectedGroup} from ${this.rootCauseStartDate} to ${this.rootCauseEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get category data for the selected breakout group\n      axios.post('/api-statit2/get_metis_category_analysis', {\n        breakoutName: this.group2TabSelectedGroup,\n        startDate: this.rootCauseStartDate,\n        endDate: this.rootCauseEndDate\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const categoryData = response.data.categoryData;\n\n            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {\n              console.log('No category data found');\n              this.isRootCauseDataLoading = false;\n              return;\n            }\n\n            // Process the data for the bar chart\n            this.processRootCauseChartData(categoryData);\n\n            // Generate AI summary\n            this.generateRootCauseAiSummary(categoryData);\n          } else {\n            console.error('Error getting category analysis:', response.data);\n            this.isRootCauseDataLoading = false;\n            this.isRootCauseAiLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting category analysis:', error);\n          this.isRootCauseDataLoading = false;\n          this.isRootCauseAiLoading = false;\n        });\n\n      // No need to show a modal as content will be displayed inline\n    },\n\n\n\n    processRootCauseChartData(categoryData) {\n      // Extract all unique categories and months\n      const categories = [...new Set(categoryData.chartData.map(item => item.group))];\n      const months = [...new Set(categoryData.chartData.map(item => item.key))];\n\n      console.log(`Found ${categories.length} categories: ${categories.join(', ')}`);\n      console.log(`Found ${months.length} months: ${months.join(', ')}`);\n\n      // Sort months chronologically\n      months.sort((a, b) => new Date(a + '-01') - new Date(b + '-01'));\n\n      // Create a map to store data by category and month\n      const dataByMonth = {};\n      const previousMonthRates = {};\n      let hasCriticalIssue = false;\n\n      // Initialize data structures\n      categories.forEach(category => {\n        previousMonthRates[category] = [];\n      });\n\n      // Process the data\n      categoryData.chartData.forEach(item => {\n        const { group: category, key: month, value } = item;\n\n        // Store the monthly rates for each category to detect spikes\n        previousMonthRates[category].push({\n          month,\n          value\n        });\n\n        // Initialize month data if it doesn't exist\n        if (!dataByMonth[month]) {\n          dataByMonth[month] = {};\n        }\n\n        // Store the value for this category and month\n        dataByMonth[month][category] = value;\n      });\n\n      // Check for critical issues based on new criteria:\n      // 1) Full month fail rate must be above target rate\n      // 2) Root cause for a month is 3x the fail rate from cumulative fails in past months\n      const criticalIssueDetails = [];\n\n      // Get target rate for this breakout group from the breakout_targets file\n      // This will be used to check if the full month fail rate is above target\n      let targetRate = 0.001; // Default target rate if not found\n\n      // Try to get the target rate from the data if available\n      if (categoryData.targetRate !== undefined) {\n        targetRate = categoryData.targetRate;\n        console.log(`Using target rate from API data: ${targetRate}`);\n      } else {\n        // If not available in the data, we'll use a default or try to fetch it\n        console.log(`No target rate found in API data, using default: ${targetRate}`);\n      }\n\n      // Calculate total fail rate for each month to compare against target\n      const monthlyTotalFailRates = {};\n      months.forEach(month => {\n        let totalFailRate = 0;\n        let totalDefects = 0;\n        let totalVolume = 0;\n\n        // Sum up all categories for this month\n        categories.forEach(category => {\n          const categoryData = previousMonthRates[category].find(data => data.month === month);\n          if (categoryData) {\n            totalFailRate += categoryData.value;\n\n            // If we have actual defect counts, use those\n            if (categoryData.defects !== undefined && categoryData.volume !== undefined) {\n              totalDefects += categoryData.defects;\n              totalVolume = categoryData.volume; // Volume should be the same for all categories in a month\n            }\n          }\n        });\n\n        // Store the total fail rate for this month\n        monthlyTotalFailRates[month] = {\n          failRate: totalFailRate,\n          defects: totalDefects,\n          volume: totalVolume,\n          isAboveTarget: totalFailRate > (targetRate * 100) // Convert target to percentage for comparison\n        };\n\n        console.log(`Month ${month} total fail rate: ${totalFailRate.toFixed(2)}%, target: ${(targetRate * 100).toFixed(2)}%, above target: ${totalFailRate > (targetRate * 100)}`);\n      });\n\n      categories.forEach(category => {\n        // Sort the monthly rates by date\n        previousMonthRates[category].sort((a, b) => {\n          return new Date(a.month + '-01') - new Date(b.month + '-01');\n        });\n\n        const rates = previousMonthRates[category];\n\n        if (rates.length >= 2) {\n          for (let i = 1; i < rates.length; i++) {\n            const currentMonth = rates[i];\n            const previousMonth = rates[i-1];\n\n            // Check if the full month fail rate is above target\n            const isMonthAboveTarget = monthlyTotalFailRates[currentMonth.month] &&\n                                      monthlyTotalFailRates[currentMonth.month].isAboveTarget;\n\n            // Only proceed if the month's total fail rate is above target\n            if (!isMonthAboveTarget) {\n              console.log(`Skipping critical issue check for ${category} in ${currentMonth.month}: month's total fail rate is not above target`);\n              continue;\n            }\n\n            // Check if this root cause is 3x the fail rate from previous month\n            // OR if it's a new root cause that wasn't present before (previousMonth.value is 0)\n            // but only if the current month's value is significant (above 0.1%)\n            if ((currentMonth.value >= previousMonth.value * 3 && previousMonth.value > 0) ||\n                (previousMonth.value === 0 && currentMonth.value > 0.1)) {\n              console.log(`Potential critical issue detected in ${category}: ${previousMonth.value.toFixed(2)}% to ${currentMonth.value.toFixed(2)}%`);\n\n              // Get actual defect counts from the category data if available\n              const previousDefects = (categoryData.defectCounts &&\n                                      categoryData.defectCounts[previousMonth.month] &&\n                                      categoryData.defectCounts[previousMonth.month][category]) || 0;\n              const currentDefects = (categoryData.defectCounts &&\n                                     categoryData.defectCounts[currentMonth.month] &&\n                                     categoryData.defectCounts[currentMonth.month][category]) || 0;\n\n              // If actual counts aren't available, estimate based on percentages\n              const previousVolume = (categoryData.volumeData &&\n                                     categoryData.volumeData[previousMonth.month]) || 10000;\n              const currentVolume = (categoryData.volumeData &&\n                                    categoryData.volumeData[currentMonth.month]) || 10000;\n\n              const previousFails = previousDefects || Math.round(previousVolume * (previousMonth.value / 100));\n              const currentFails = currentDefects || Math.round(currentVolume * (currentMonth.value / 100));\n\n              // This is a critical issue - the month is above target and this root cause has a 3x spike\n              console.log(`Confirmed critical issue in ${category}: month is above target and root cause has 3x spike`);\n              hasCriticalIssue = true;\n\n              // Store details about this critical issue\n              const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n              const [year, monthNum] = currentMonth.month.split('-');\n              const monthName = monthNames[parseInt(monthNum) - 1];\n\n              criticalIssueDetails.push({\n                category,\n                previousValue: previousMonth.value.toFixed(2),\n                currentValue: currentMonth.value.toFixed(2),\n                increaseMultiplier: previousMonth.value === 0 ? \"(new)\" : (currentMonth.value / previousMonth.value).toFixed(1),\n                month: `${monthName} ${year}`,\n                monthShort: currentMonth.month,\n                previousFails,\n                currentFails,\n                failsIncrease: currentFails - previousFails,\n                totalFailRate: monthlyTotalFailRates[currentMonth.month].failRate.toFixed(2),\n                targetRate: (targetRate * 100).toFixed(2),\n                isNew: previousMonth.value === 0\n              });\n\n              // We're no longer changing the category name for critical issues\n              // Just keep track of which ones are critical for coloring\n            }\n          }\n        }\n      });\n\n      // Process critical issues\n      if (criticalIssueDetails.length > 0) {\n        // Sort by increase multiplier (highest first)\n        criticalIssueDetails.sort((a, b) => parseFloat(b.increaseMultiplier) - parseFloat(a.increaseMultiplier));\n\n        // Store all critical issues with unique IDs\n        this.criticalIssues = criticalIssueDetails.map((issue, index) => {\n          const issueId = `${this.group2TabSelectedGroup.replace(/\\s+/g, '-')}-${issue.category.replace(/\\s+/g, '-')}-${issue.month.replace(/\\s+/g, '-')}`;\n\n          // Generate a description that sounds like it was written by an AI\n          let description;\n          if (issue.isNew) {\n            // New root cause that wasn't present before\n            description = `${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant new issue that requires immediate investigation.`;\n          } else {\n            // Existing root cause with a spike\n            description = `${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant deviation from expected performance that requires immediate investigation.`;\n          }\n\n          // Initialize update field if it doesn't exist\n          if (!this.criticalIssueUpdates[issueId]) {\n            this.criticalIssueUpdates[issueId] = {\n              updateText: '',\n              lastUpdated: null,\n              history: []\n            };\n          }\n\n          // Expand the first critical issue by default\n          if (index === 0) {\n            this.$set(this.expandedIssues, issueId, true);\n          }\n\n          return {\n            id: issueId,\n            category: issue.category,\n            previousValue: issue.previousValue,\n            currentValue: issue.currentValue,\n            increaseMultiplier: issue.increaseMultiplier,\n            month: issue.month,\n            description: description,\n            severity: parseFloat(issue.increaseMultiplier) > 5 ? 'high' : 'medium'\n          };\n        });\n\n        // Generate overall description for the most severe issue\n        const issue = criticalIssueDetails[0];\n        if (issue.isNew) {\n          // New root cause that wasn't present before\n          this.criticalIssueDescription = `Critical issue detected: ${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;\n        } else {\n          // Existing root cause with a spike\n          this.criticalIssueDescription = `Critical issue detected: ${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;\n        }\n\n        // If there are multiple critical issues, add a note\n        if (criticalIssueDetails.length > 1) {\n          this.criticalIssueDescription += ` Additionally, ${criticalIssueDetails.length - 1} other root cause categories show critical spikes.`;\n        }\n      } else {\n        this.criticalIssues = [];\n        this.criticalIssueDescription = '';\n      }\n\n      // Format data for stacked bar chart\n      const chartData = [];\n\n      // Create a color scale for the categories\n      const colorScale = {};\n\n      // Define colors for each category\n      const categoryColors = {\n        'Electrical': '#0f62fe',\n        'Mechanical': '#6929c4',\n        'Thermal': '#1192e8',\n        'Material': '#005d5d',\n        'Process': '#9f1853',\n        'Design': '#fa4d56',\n        'Unknown': '#570408',\n        'Other': '#198038',\n        'FLAG': '#8a3ffc',\n        'LINK': '#002d9c',\n        'NONFAIL': '#009d9a',\n        'KRAKEN': '#ee538b',\n        'I2C': '#b28600'\n      };\n\n      // Assign colors to categories\n      categories.forEach(category => {\n        // Use predefined color if available, otherwise use a default color\n        colorScale[category] = categoryColors[category] || `#${Math.floor(Math.random()*16777215).toString(16)}`;\n      });\n\n      // Update the chart options with the color scale\n      this.rootCauseChartOptions.color.scale = colorScale;\n\n      // Create data points for each month and category\n      months.forEach(month => {\n        const monthData = dataByMonth[month] || {};\n\n        // Format the month for display\n        const [year, monthNum] = month.split('-');\n        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formattedMonth = `${monthNames[parseInt(monthNum) - 1]} ${year}`;\n\n        // Add data for each category in this month\n        Object.entries(monthData).forEach(([category, value]) => {\n          // Check if this category/month combination is a critical issue\n          const criticalIssue = criticalIssueDetails.find(issue =>\n            issue.category === category && issue.monthShort === month);\n          const isCritical = !!criticalIssue;\n\n          // Get defect counts from the API data if available\n          const defects = (categoryData.defectCounts &&\n                          categoryData.defectCounts[month] &&\n                          categoryData.defectCounts[month][category]) || 0;\n\n          // Get volume data if available\n          const volume = (categoryData.volumeData &&\n                         categoryData.volumeData[month]) || 10000;\n\n          // Calculate estimated defects if actual count is not available\n          const estimatedDefects = defects || Math.round(volume * (value / 100));\n\n          // Instead of changing the category name for critical issues,\n          // we'll just store the critical status in the data object\n          chartData.push({\n            group: category, // Keep the original category name\n            key: formattedMonth,\n            value: value,\n            data: {\n              defects: estimatedDefects,\n              volume: volume,\n              month: month,\n              originalMonth: month,\n              category: category,\n              isCritical: isCritical\n            }\n          });\n        });\n      });\n\n      // Update the chart data\n      this.rootCauseChartData = chartData;\n      this.hasCriticalRootCauseIssue = hasCriticalIssue;\n\n      // Load previous updates if there are critical issues\n      if (hasCriticalIssue) {\n        this.loadCriticalIssueUpdates();\n      }\n\n      this.isRootCauseDataLoading = false;\n      console.log(`Processed ${chartData.length} data points for root cause analysis`);\n    },\n\n    generateRootCauseAiSummary(categoryData) {\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Wait a moment to ensure critical issues have been processed\n      setTimeout(() => {\n        // Prepare the data for the AI summary\n        const summaryData = {\n          breakoutGroup: this.group2TabSelectedGroup,\n          timeRange: this.rootCauseTimeRange,\n          startDate: this.rootCauseStartDate,\n          endDate: this.rootCauseEndDate,\n          categories: categoryData.chartData,\n          hasCriticalIssues: this.hasCriticalRootCauseIssue,\n          criticalIssuesCount: this.criticalIssues.length,\n          criticalIssues: this.criticalIssues.map(issue => ({\n            category: issue.category,\n            month: issue.month,\n            increaseMultiplier: issue.increaseMultiplier,\n            isNew: issue.increaseMultiplier === '(new)'\n          }))\n        };\n\n        // Force the AI to acknowledge critical issues if they exist\n        if (this.criticalIssues.length > 0) {\n          summaryData.forceCriticalIssueAcknowledgment = true;\n        }\n\n        console.log(`Generating AI summary with critical issues: ${this.hasCriticalRootCauseIssue}, count: ${this.criticalIssues.length}`);\n\n        // Get action tracker data for this breakout group\n        this.getActionTrackerData(this.group2TabSelectedGroup)\n          .then(actionTrackerData => {\n            // Format the prompt with the data and action tracker insights\n            const prompt = this.formatRootCauseAiPrompt(summaryData, actionTrackerData);\n\n            // Call WatsonX.ai API\n            return axios.post('/api-statit2/watsonx_prompt', {\n              model_id: 'ibm/granite-13b-instruct-v2',\n              prompt: prompt,\n              temperature: 0.3,\n              api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n              project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'\n            }, config);\n          })\n          .then(response => {\n            if (response.data.status === 'success') {\n              this.rootCauseAiSummary = response.data.generated_text;\n              console.log('AI summary generated successfully');\n            } else {\n              console.error('Error generating AI summary:', response.data);\n              this.rootCauseAiSummary = 'Unable to generate AI summary at this time.';\n            }\n            this.isRootCauseAiLoading = false;\n          })\n          .catch(error => {\n            console.error('API error when generating AI summary:', error);\n            this.rootCauseAiSummary = 'Error generating AI summary. Please try again later.';\n            this.isRootCauseAiLoading = false;\n          });\n      }, 500); // Wait 500ms to ensure critical issues are processed\n    },\n\n    formatRootCauseAiPrompt(data, actionTrackerData) {\n      // Import the prompt template from watsonxPrompts.js\n      const { formatRootCauseAnalysisPrompt } = require('@/utils/watsonxPrompts');\n\n      // Format the prompt with the data\n      return formatRootCauseAnalysisPrompt(data, actionTrackerData);\n    },\n\n    loadCriticalIssueUpdates() {\n      // In a real implementation, this would load updates from a database\n      // For now, we'll just use a mock implementation\n      console.log('Loading critical issue updates (mock implementation)');\n\n      // We're now using an object instead of an array, so we don't need to reset it here\n      // The updates are initialized for each issue in the processRootCauseChartData method\n    },\n\n    saveCriticalIssueUpdate(issueId, updateText) {\n      // In a real implementation, this would save the update to a database\n      if (updateText && updateText.trim()) {\n        // Get the current date and time\n        const timestamp = new Date();\n\n        // Add the update to the history\n        if (!this.criticalIssueUpdates[issueId]) {\n          this.criticalIssueUpdates[issueId] = {\n            updateText: '',\n            lastUpdated: null,\n            history: []\n          };\n        }\n\n        // Add to history\n        this.criticalIssueUpdates[issueId].history.push({\n          content: updateText,\n          timestamp: timestamp\n        });\n\n        // Update the last updated timestamp\n        this.criticalIssueUpdates[issueId].lastUpdated = timestamp;\n\n        // Clear the update text\n        this.criticalIssueUpdates[issueId].updateText = '';\n\n        console.log(`Critical issue update saved for ${issueId}`);\n\n        // In a real implementation, you would save this to a database\n        // For example:\n        // axios.post('/api-statit2/save_critical_issue_update', {\n        //   issueId,\n        //   updateText,\n        //   timestamp\n        // });\n\n        return true;\n      }\n\n      return false;\n    },\n\n    // Update the text for a critical issue\n    updateCriticalIssueText(issueId, text) {\n      if (!this.criticalIssueUpdates[issueId]) {\n        this.criticalIssueUpdates[issueId] = {\n          updateText: text,\n          lastUpdated: null,\n          history: []\n        };\n      } else {\n        this.criticalIssueUpdates[issueId].updateText = text;\n      }\n    },\n\n    // Get the update text for a critical issue\n    getCriticalIssueUpdateText(issueId) {\n      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].updateText) || '';\n    },\n\n    // Get the history for a critical issue\n    getCriticalIssueHistory(issueId) {\n      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].history) || [];\n    },\n\n    // Check if a critical issue has been updated\n    hasCriticalIssueBeenUpdated(issueId) {\n      return this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].lastUpdated !== null;\n    },\n\n    // Toggle expanded state of a critical issue\n    toggleCriticalIssue(issueId) {\n      this.$set(this.expandedIssues, issueId, !this.isIssueExpanded(issueId));\n    },\n\n    // Check if a critical issue is expanded\n    isIssueExpanded(issueId) {\n      return !!this.expandedIssues[issueId];\n    },\n\n    formatDate(date) {\n      if (!date) return '';\n\n      if (typeof date === 'string') {\n        date = new Date(date);\n      }\n\n      return date.toLocaleString();\n    },\n\n    formatDateForDisplay(dateStr) {\n      if (!dateStr) return '';\n\n      const [year, month] = dateStr.split('-');\n      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n\n      return `${monthNames[parseInt(month) - 1]} ${year}`;\n    },\n\n    // Handle click events on the root cause chart\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.key}, month: ${clickedData.group}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // about the selected category or filtering the data\n      }\n    },\n\n\n\n\n\n    // Setup direct click handlers for the chart\n    setupChartClickHandlers() {\n      console.log('Setting up chart click handlers');\n\n      // We're now handling clicks in the StackedBarChart component\n      // This method is kept for compatibility but doesn't need to do anything\n    },\n\n    // Update the Y-axis domain based on the maximum fail rate\n    updateChartYAxisDomain() {\n      if (!this.categoryTabChartData || this.categoryTabChartData.length === 0) return;\n\n      // Calculate the maximum fail rate across all categories and months\n      let maxFailRate = 0;\n\n      // For stacked bar charts, we need to calculate the total for each month\n      const monthTotals = {};\n\n      // First, calculate the total for each month\n      this.categoryTabChartData.forEach(item => {\n        const month = item.key;\n        if (!monthTotals[month]) {\n          monthTotals[month] = 0;\n        }\n        monthTotals[month] += item.value || 0;\n      });\n\n      // Find the maximum monthly total\n      Object.values(monthTotals).forEach(total => {\n        if (total > maxFailRate) {\n          maxFailRate = total;\n        }\n      });\n\n      console.log(`Maximum fail rate: ${maxFailRate}%`);\n\n      // Set a reasonable Y-axis domain based on the maximum fail rate\n      // Add 10% padding to the top for better visualization\n      const yAxisMax = Math.ceil(maxFailRate * 1.1);\n\n      // Ensure the maximum is at least 0.1% for very small values\n      const finalMax = Math.max(0.1, yAxisMax);\n\n      console.log(`Setting Y-axis domain to [0, ${finalMax}]`);\n\n      // Update the chart options\n      this.categoryChartOptions = {\n        ...this.categoryChartOptions,\n        axes: {\n          ...this.categoryChartOptions.axes,\n          left: {\n            ...this.categoryChartOptions.axes.left,\n            domain: [0, finalMax]\n          }\n        }\n      };\n    },\n\n    // AI Test tab methods\n    async sendAiPrompt() {\n      if (!this.aiPrompt.trim()) {\n        logger.logError('Prompt is empty', null, 'sendAiPrompt');\n        return;\n      }\n\n      this.isAiLoading = true;\n      this.aiResponse = '';\n\n      logger.logInfo(`Sending prompt to WatsonX.ai model: ${this.selectedAiModel}`, 'sendAiPrompt');\n      logger.logInfo(`Prompt length: ${this.aiPrompt.length}`, 'sendAiPrompt');\n      logger.logInfo(`Prompt (first 100 chars): ${this.aiPrompt.substring(0, 100)}...`, 'sendAiPrompt');\n      logger.logInfo(`Temperature: ${this.aiTemperature}`, 'sendAiPrompt');\n\n      // Get token from localStorage\n      const token = localStorage.getItem('token');\n      logger.logInfo(`Auth token available: ${!!token}`, 'sendAiPrompt');\n\n      // Prepare request data\n      const requestData = {\n        model_id: this.selectedAiModel,\n        prompt: this.aiPrompt,\n        temperature: parseFloat(this.aiTemperature),\n        api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n        project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek\n      };\n\n      logger.logInfo('Request data prepared', 'sendAiPrompt');\n\n      try {\n        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'sendAiPrompt');\n\n        // Call WatsonX.ai API using fetch\n        const response = await fetch('/api-statit2/watsonx_prompt', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token ? `Bearer ${token}` : ''\n          },\n          body: JSON.stringify(requestData)\n        });\n\n        logger.logInfo(`Received response from API. Status: ${response.status}`, 'sendAiPrompt');\n\n        // Handle different response statuses\n        if (!response.ok) {\n          let errorMessage = `HTTP error! Status: ${response.status}`;\n\n          if (response.status === 401) {\n            errorMessage = 'Authentication failed. Please check the API key.';\n          } else if (response.status === 403) {\n            errorMessage = 'Access denied. Please check permissions for the project ID.';\n          } else if (response.status === 404) {\n            errorMessage = 'Model not found. Please check the model ID.';\n          }\n\n          // Try to get more error details from the response\n          try {\n            const errorText = await response.text();\n            logger.logError('Error response text', errorText, 'sendAiPrompt');\n\n            // Try to parse as JSON if possible\n            try {\n              const errorJson = JSON.parse(errorText);\n              logger.logError('Error response JSON', errorJson, 'sendAiPrompt');\n              if (errorJson.error) {\n                errorMessage += ` - ${errorJson.error}`;\n              } else {\n                errorMessage += ` - ${errorText}`;\n              }\n            } catch (jsonError) {\n              // Not JSON, use as text\n              if (errorText) {\n                errorMessage += ` - ${errorText}`;\n              }\n            }\n          } catch (textError) {\n            logger.logError('Error parsing error response', textError, 'sendAiPrompt');\n          }\n\n          throw new Error(errorMessage);\n        }\n\n        // Parse the JSON response\n        const responseText = await response.text();\n        logger.logInfo('Raw response received', 'sendAiPrompt');\n\n        let responseData;\n        try {\n          responseData = JSON.parse(responseText);\n          logger.logInfo('Successfully parsed WatsonX.ai response', 'sendAiPrompt');\n        } catch (jsonError) {\n          logger.logError('Error parsing JSON response', jsonError, 'sendAiPrompt');\n          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);\n        }\n\n        if (responseData.status === 'success') {\n          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'sendAiPrompt');\n\n          if (responseData.generated_text) {\n            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'sendAiPrompt');\n            this.aiResponse = responseData.generated_text;\n          } else {\n            logger.logError('No generated_text in success response', responseData, 'sendAiPrompt');\n            this.aiResponse = 'No response generated';\n          }\n        } else {\n          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'sendAiPrompt');\n          this.aiResponse = `Error: ${responseData.error || 'Unknown error occurred'}`;\n        }\n      } catch (error) {\n        logger.logError('API error when calling WatsonX.ai', error, 'sendAiPrompt');\n\n        // Provide detailed error information\n        let errorMessage = error.message || 'Failed to connect to WatsonX.ai';\n\n        if (error.name === 'TypeError' && errorMessage.includes('Failed to fetch')) {\n          errorMessage = 'Network error. Please check your internet connection.';\n        }\n\n        this.aiResponse = `Error: ${errorMessage}`;\n      } finally {\n        this.isAiLoading = false;\n      }\n    },\n\n    clearAiPrompt() {\n      this.aiPrompt = '';\n      this.aiResponse = '';\n    },\n\n    useTestPrompt() {\n      this.aiPrompt = `You are a helpful assistant. Please provide a brief summary of manufacturing quality data.\n\nThe XFactor for part group ABC-123 was 2.5 in January, 1.8 in February, and 1.2 in March, showing improvement.\nThere are 2 open action items for this part group.\n\nPlease summarize this trend in 2-3 sentences.`;\n    },\n\n    async callWatsonXDirectly() {\n      this.isAiLoading = true;\n      this.aiResponse = '';\n\n      try {\n        // First, get an IAM token\n        const iamResponse = await fetch('https://iam.cloud.ibm.com/identity/token', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n            'Accept': 'application/json'\n          },\n          body: new URLSearchParams({\n            'grant_type': 'urn:ibm:params:oauth:grant-type:apikey',\n            'apikey': 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl'\n          })\n        });\n\n        if (!iamResponse.ok) {\n          throw new Error(`IAM authentication failed: ${iamResponse.status}`);\n        }\n\n        const iamData = await iamResponse.json();\n        const accessToken = iamData.access_token;\n\n        if (!accessToken) {\n          throw new Error('No access token received from IAM');\n        }\n\n        // Now call WatsonX.ai directly\n        const watsonxResponse = await fetch('/watsonx-direct/ml/v1/text/generation?version=2023-05-29', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Accept': 'application/json',\n            'Authorization': `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            model_id: 'ibm/granite-13b-instruct-v2',\n            input: this.aiPrompt,\n            parameters: {\n              temperature: 0.7,\n              max_new_tokens: 1024,\n              min_new_tokens: 0,\n              decoding_method: 'greedy'\n            },\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'\n          })\n        });\n\n        if (!watsonxResponse.ok) {\n          const errorText = await watsonxResponse.text();\n          throw new Error(`WatsonX.ai API error: ${watsonxResponse.status} - ${errorText}`);\n        }\n\n        const responseData = await watsonxResponse.json();\n        console.log('Direct WatsonX.ai response:', responseData);\n\n        if (responseData.results && responseData.results.length > 0) {\n          this.aiResponse = responseData.results[0].generated_text || 'No text generated';\n        } else {\n          this.aiResponse = 'No results returned from WatsonX.ai';\n        }\n      } catch (error) {\n        console.error('Error in direct WatsonX.ai call:', error);\n        this.aiResponse = `Error: ${error.message}`;\n      } finally {\n        this.isAiLoading = false;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.metis-xfactors-container {\n  padding: 20px;\n}\n\n/* Tab Styles */\n:deep(.bx--tabs) {\n  margin-bottom: 20px;\n}\n\n:deep(.bx--tabs__nav-item--selected) {\n  box-shadow: inset 0 2px 0 0 #0f62fe;\n}\n\n:deep(.bx--tab-content) {\n  padding: 0;\n}\n\n.content-tile {\n  padding: 20px;\n  margin-bottom: 20px;\n  background-color: #262626;\n  color: #f4f4f4;\n}\n\n/* Common Controls Styles */\n.controls-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.date-controls {\n  display: flex;\n  gap: 15px;\n  align-items: flex-start;\n}\n\n.date-picker {\n  display: flex;\n  flex-direction: column;\n}\n\n.date-picker label {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n.date-picker input {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\n.date-dropdown-container {\n  display: flex;\n  gap: 8px;\n}\n\n.date-dropdown-container select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\n.date-dropdown-container select:first-child {\n  min-width: 120px;\n}\n\n.date-dropdown-container select:last-child {\n  min-width: 80px;\n}\n\n.time-range-dropdown {\n  display: flex;\n  flex-direction: column;\n}\n\n.time-range-dropdown label {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n.time-range-dropdown select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  min-width: 150px;\n}\n\n.analyze-button {\n  padding: 8px 16px;\n  background-color: #0f62fe;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  height: 38px;\n}\n\n.analyze-button:hover {\n  background-color: #0353e9;\n}\n\n/* Main Tab Styles */\n.breakout-selection {\n  margin-bottom: 20px;\n}\n\n.breakout-dropdown {\n  position: relative;\n}\n\n.breakout-dropdown select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  min-width: 300px;\n  width: 100%;\n}\n\n.breakout-dropdown select:disabled {\n  background-color: #f4f4f4;\n  color: #999;\n  cursor: not-allowed;\n}\n\n.breakout-dropdown .loading-indicator {\n  margin-top: 5px;\n  font-size: 14px;\n  color: #0f62fe;\n}\n\n.breakout-dropdown .no-data-message {\n  margin-top: 5px;\n  font-size: 14px;\n  color: #da1e28;\n}\n\n/* Chart Styles */\n.charts-section {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  margin-bottom: 30px;\n}\n\n.chart-container, .breakout-chart-container, .breakout-bar-chart-container {\n  position: relative;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  margin-bottom: 20px;\n}\n\n.chart-container h4, .breakout-chart-container h4, .breakout-bar-chart-container h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n  color: #f4f4f4;\n}\n\n.chart-description {\n  color: #8d8d8d;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 14px;\n}\n\n.chart-wrapper {\n  height: 400px;\n  margin-bottom: 15px;\n}\n\n.threshold-info, .chart-legend {\n  display: flex;\n  gap: 20px;\n  margin-top: 10px;\n  flex-wrap: wrap;\n}\n\n.threshold-item, .legend-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.threshold-color, .legend-color {\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n}\n\n.threshold-color.sustained, .legend-color.normal {\n  background-color: #0F62FE;\n}\n\n.threshold-color.spike, .legend-color.critical {\n  background-color: #DA1E28;\n}\n\n:deep(.custom-tooltip) {\n  background-color: #262626;\n  border: 1px solid #393939;\n  padding: 10px;\n  border-radius: 4px;\n  color: #f4f4f4;\n}\n\n:deep(.custom-tooltip p) {\n  margin: 5px 0;\n}\n\n@media (min-width: 1200px) {\n  .charts-section {\n    flex-direction: row;\n    align-items: stretch;\n  }\n\n  .chart-container {\n    flex: 1;\n    min-width: 0;\n  }\n}\n\n.info-button {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #0f62fe;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n/* Alerts Styles */\n.alerts-container, .breakout-tab-alerts {\n  margin-top: 30px;\n}\n\n.alerts-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 10px;\n}\n\n.alerts-table th, .alerts-table td {\n  padding: 10px;\n  text-align: left;\n  border-bottom: 1px solid #ddd;\n}\n\n.alerts-table th {\n  background-color: #f4f4f4;\n}\n\n.status-indicator {\n  padding: 4px 8px;\n  border-radius: 12px;\n  display: inline-block;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.status-spike {\n  background-color: #ffebeb;\n  color: #da1e28;\n}\n\n.status-sustained {\n  background-color: #fff8e1;\n  color: #b28600;\n}\n\n.status-normal {\n  background-color: #e0f5ea;\n  color: #24a148;\n}\n\n.status-critical {\n  background-color: #990000;\n  color: white;\n}\n\n/* Dashboard styles */\n.dashboard-controls {\n  margin-bottom: 30px;\n}\n\n.dashboard-header {\n  margin-bottom: 20px;\n  border-bottom: 2px solid #0062ff;\n  padding-bottom: 15px;\n}\n\n.dashboard-header h3 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  font-size: 24px;\n  font-weight: 500;\n  color: #161616;\n}\n\n.dashboard-header p {\n  margin: 0;\n  color: #6f6f6f;\n  font-size: 16px;\n}\n\n.dashboard-options {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 20px;\n  background-color: #f4f4f4;\n  border-radius: 8px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-select {\n  padding: 10px 15px;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  font-size: 14px;\n  min-width: 200px;\n  background-color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.dashboard-button {\n  padding: 10px 20px;\n  background-color: #0062ff;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  transition: background-color 0.2s;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-button:hover {\n  background-color: #0353e9;\n}\n\n.button-icon {\n  font-size: 16px;\n}\n\n.heatmap-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.heatmap-header h4 {\n  margin: 0;\n}\n\n.heatmap-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.filter-container {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.filter-type, .owner-filter {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-type label, .owner-filter label {\n  font-weight: 500;\n  color: #f4f4f4;\n  white-space: nowrap;\n}\n\n/* Carbon dropdown styling */\n.carbon-dropdown {\n  background-color: white;\n}\n\n.filter-type-dropdown {\n  min-width: 120px;\n}\n\n.owner-dropdown {\n  min-width: 180px;\n}\n\n.last-updated-text {\n  margin-bottom: 15px;\n  color: #6f6f6f;\n  font-size: 14px;\n  text-align: right;\n  font-style: italic;\n}\n\n.dashboard-date-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-top: 15px;\n  padding: 15px;\n  background-color: #f4f4f4;\n  border-radius: 8px;\n  width: 100%;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.info-label {\n  font-size: 12px;\n  color: #6f6f6f;\n  font-weight: 500;\n}\n\n.info-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #161616;\n}\n\n.heatmap-container {\n  margin-top: 30px;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.heatmap-container h4 {\n  color: #f4f4f4;\n  margin-top: 0;\n  margin-bottom: 20px;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.heatmap-legend {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 20px;\n  background-color: #333333;\n  padding: 15px;\n  border-radius: 6px;\n  justify-content: flex-start;\n}\n\n.heatmap-legend .legend-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.heatmap-legend .status-indicator {\n  width: 16px;\n  height: 16px;\n  border-radius: 4px;\n  display: inline-block;\n}\n\n.heatmap-legend span {\n  color: #f4f4f4;\n  font-size: 14px;\n}\n\n.heatmap-table-container {\n  overflow-x: auto;\n  max-height: 600px;\n  overflow-y: auto;\n  border-radius: 8px;\n  margin-top: 20px;\n  background-color: #262626;\n}\n\n.heatmap-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 2px;\n  border: none;\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.heatmap-table th,\n.heatmap-table td {\n  padding: 12px;\n  text-align: center;\n  border: none;\n  font-size: 14px;\n}\n\n.heatmap-table th {\n  background-color: #333333;\n  color: #f4f4f4;\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 12px;\n}\n\n.heatmap-table .breakout-name {\n  text-align: left;\n  font-weight: bold;\n  position: sticky;\n  left: 0;\n  background-color: #333333;\n  color: #f4f4f4;\n  z-index: 2;\n  padding-left: 15px;\n  min-width: 200px;\n  max-width: 250px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.cell-content {\n  padding: 10px;\n  border-radius: 6px;\n  font-weight: bold;\n  font-size: 16px;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform 0.2s, box-shadow 0.2s;\n  cursor: pointer;\n}\n\n.cell-content:hover {\n  transform: scale(1.05);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  z-index: 5;\n}\n\n.cell-normal {\n  background-color: #0062ff;\n  color: white;\n  opacity: 0.8;\n}\n\n.cell-sustained {\n  background-color: #ff9a00;\n  color: white;\n}\n\n.cell-spike {\n  background-color: #fa4d56;\n  color: white;\n}\n\n.cell-critical {\n  background-color: #da1e28;\n  color: white;\n}\n\n.cell-empty {\n  background-color: #393939;\n  color: #8d8d8d;\n}\n\n.no-data {\n  color: #8d8d8d;\n  font-style: italic;\n  font-size: 14px;\n}\n\n/* Blinking animation for critical cells */\n@keyframes blink {\n  0% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }\n  50% { opacity: 0.8; box-shadow: 0 0 15px rgba(250, 77, 86, 0.7); }\n  100% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n\n.blink {\n  animation: blink 2s infinite, pulse 2s infinite;\n}\n\n/* Tooltip styles */\n.cell-tooltip {\n  position: fixed;\n  background-color: #161616;\n  color: white;\n  padding: 16px;\n  border-radius: 8px;\n  z-index: 1000;\n  min-width: 200px;\n  max-width: 300px;\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\n  border: 1px solid #393939;\n  font-size: 14px;\n  backdrop-filter: blur(5px);\n}\n\n.tooltip-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  padding-bottom: 8px;\n}\n\n.tooltip-header strong {\n  font-size: 16px;\n  color: #f4f4f4;\n}\n\n.tooltip-header span {\n  font-size: 14px;\n  color: #8d8d8d;\n}\n\n.tooltip-content p {\n  margin: 8px 0;\n  font-size: 14px;\n  display: flex;\n  justify-content: space-between;\n}\n\n.tooltip-content p:before {\n  content: \"•\";\n  margin-right: 8px;\n  color: #0062ff;\n}\n\n.tooltip-content p:nth-child(2):before {\n  color: #fa4d56;\n}\n\n.no-data-message {\n  padding: 20px;\n  text-align: center;\n  color: #8d8d8d;\n}\n\n/* Modal styles */\n.info-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #262626;\n  color: #f4f4f4;\n  padding: 20px;\n  border-radius: 4px;\n  max-width: 600px;\n  position: relative;\n}\n\n.ai-summary-modal {\n  width: 90%;\n  max-width: 900px;\n}\n\n.ai-summary-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 30px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.ai-summary-content {\n  line-height: 1.6;\n}\n\n.close-button {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  font-size: 24px;\n  cursor: pointer;\n  color: #aaa;\n}\n\n.close-button:hover {\n  color: #f4f4f4;\n}\n\n.ai-tooltip {\n  position: absolute;\n  z-index: 999;\n  background-color: #0f62fe;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  pointer-events: none;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n.ai-tooltip-content {\n  white-space: nowrap;\n}\n\n.breakout-name {\n  position: relative;\n  cursor: pointer;\n}\n\n.breakout-name:hover {\n  text-decoration: underline;\n  color: #0f62fe;\n}\n\n.breakout-name-content {\n  display: flex;\n  align-items: center;\n}\n\n.ai-summary-indicator {\n  margin-left: 8px;\n}\n\n.loading-dots {\n  display: inline-block;\n  position: relative;\n  width: 40px;\n  height: 10px;\n}\n\n.loading-dots:after {\n  content: '...';\n  position: absolute;\n  animation: dots 1.5s infinite;\n  font-weight: bold;\n  color: #0f62fe;\n}\n\n@keyframes dots {\n  0%, 20% { content: '.'; }\n  40% { content: '..'; }\n  60%, 100% { content: '...'; }\n}\n\n.modal-content h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n}\n\n.modal-content h4 {\n  margin-top: 15px;\n  margin-bottom: 5px;\n}\n\n.modal-content p {\n  margin-bottom: 10px;\n}\n\n.modal-content ul {\n  margin-top: 5px;\n  padding-left: 20px;\n}\n\n/* Breakout Tab Specific Styles */\n.breakout-tab-controls {\n  margin-bottom: 20px;\n}\n\n.breakout-tab-header {\n  margin-bottom: 15px;\n}\n\n.breakout-tab-header h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.breakout-tab-header p {\n  margin: 0;\n  color: #8d8d8d;\n}\n\n.content-tile {\n  background-color: #262626;\n  color: #f4f4f4;\n}\n\n.breakout-tab-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  align-items: flex-end;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.breakout-group-select, .time-range-select {\n  display: flex;\n  flex-direction: column;\n}\n\n.breakout-group-select label, .time-range-select label {\n  margin-bottom: 5px;\n  font-size: 14px;\n}\n\n/* Carbon Dropdown Styling */\n.date-dropdown-container {\n  display: flex;\n  gap: 10px;\n}\n\n.month-dropdown, .year-dropdown {\n  min-width: 150px;\n}\n\n.time-range-dropdown .bx--dropdown {\n  min-width: 200px;\n}\n\n.breakout-dropdown .bx--dropdown {\n  min-width: 250px;\n}\n\n.carbon-dropdown {\n  margin-bottom: 10px;\n}\n\n/* Button styling */\n.bx--btn {\n  margin-top: 10px;\n}\n\n.breakout-tab-date-controls {\n  display: flex;\n  gap: 15px;\n}\n\n.breakout-tab-content {\n  margin-top: 20px;\n}\n\n/* Part Numbers Section Styles */\n.part-numbers-section {\n  margin-top: 30px;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n.part-numbers-section h4 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  color: #f4f4f4;\n}\n\n.part-count {\n  color: #8d8d8d;\n  margin-bottom: 15px;\n  font-size: 14px;\n}\n\n.part-numbers-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n  max-height: 200px;\n  overflow-y: auto;\n  padding-right: 10px;\n}\n\n.part-number-item {\n  background-color: #393939;\n  border-radius: 4px;\n  padding: 6px 12px;\n  font-size: 13px;\n  color: #f4f4f4;\n  font-family: 'IBM Plex Mono', monospace;\n}\n\n/* Date Controls Container Styles */\n.date-controls-container {\n  margin-bottom: 15px;\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.date-controls-layout {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex-wrap: nowrap;\n  gap: 15px;\n}\n\n.custom-range-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.date-inputs {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.date-section-label {\n  color: #f4f4f4;\n  font-size: 14px;\n  white-space: nowrap;\n}\n\n.date-dropdown-container {\n  display: flex;\n  gap: 5px;\n}\n\n.month-dropdown {\n  width: 120px;\n}\n\n.year-dropdown {\n  width: 90px;\n}\n\n.date-section-title {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #f4f4f4;\n}\n\n.date-separator {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  margin: 0 5px;\n}\n\n.date-separator::before, .date-separator::after {\n  content: \"\";\n  width: 1px;\n  height: 20px;\n  background-color: #393939;\n  display: none;\n}\n\n.or-text {\n  padding: 5px 10px;\n  font-weight: 600;\n  color: #f4f4f4;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.quick-select-section {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.quick-select-dropdown {\n  width: 150px;\n}\n\n.selected-range {\n  background-color: #0f62fe !important;\n  border-color: #0f62fe !important;\n  color: #ffffff !important;\n}\n\n.analyze-button-container {\n  display: flex;\n  align-items: center;\n  margin-left: 10px;\n}\n\n.selected-date-range {\n  color: #f4f4f4;\n}\n\n.global-controls-tile {\n  background-color: #333333;\n  color: #f4f4f4;\n}\n\n/* Category Analysis Tab Styles */\n.category-tab-controls {\n  margin-bottom: 20px;\n}\n\n.category-tab-header {\n  margin-bottom: 15px;\n}\n\n.category-tab-header h4 {\n  margin-bottom: 5px;\n  font-size: 18px;\n}\n\n.category-tab-options {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.category-chart-container {\n  margin-bottom: 20px;\n}\n\n.category-tab-part-numbers {\n  margin-top: 20px;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #262626;\n}\n\n.part-numbers-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.part-number-tag {\n  background-color: #393939;\n  border-radius: 4px;\n  padding: 5px 10px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n/* Failure Modes Modal Styles */\n.failure-modes-modal {\n  max-width: 800px;\n}\n\n.failure-modes-content {\n  padding: 20px 0;\n}\n\n.failure-modes-chart-container {\n  height: 400px;\n}\n\n.custom-tooltip {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 10px;\n  font-size: 14px;\n  color: #f4f4f4;\n  max-width: 200px;\n}\n\n/* Group 2 Tab Styles */\n.group2-tab-container {\n  padding: 20px 0;\n}\n\n.group2-tab-header {\n  margin-bottom: 20px;\n}\n\n.group2-tab-header h4 {\n  margin-bottom: 5px;\n  font-size: 18px;\n}\n\n.group2-tab-controls {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.analysis-grid {\n  margin-top: 20px;\n}\n\n.analysis-table {\n  width: 100%;\n  border-collapse: collapse;\n  border: 1px solid #393939;\n}\n\n.analysis-table th,\n.analysis-table td {\n  padding: 12px;\n  text-align: center;\n  border: 1px solid #393939;\n}\n\n.analysis-table th {\n  background-color: #262626;\n  font-weight: 600;\n}\n\n.analysis-table td:first-child {\n  text-align: left;\n  font-weight: 600;\n}\n\n/* Custom Date Modal Styles */\n.custom-date-content {\n  padding: 20px 0;\n}\n\n.date-inputs {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.date-section {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.date-section-label {\n  width: 100px;\n  font-weight: 600;\n}\n\n.custom-date-buttons {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n}\n\n/* Root Cause Section Styles */\n.root-cause-section {\n  margin-top: 30px;\n  padding: 20px;\n  background-color: #161616;\n  border: 1px solid #393939;\n  border-radius: 4px;\n}\n\n.section-header {\n  margin-bottom: 20px;\n  border-bottom: 1px solid #393939;\n  padding-bottom: 10px;\n}\n\n.section-header h4 {\n  font-size: 18px;\n  font-weight: 600;\n  color: #f4f4f4;\n}\n\n.ai-summary-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #161616;\n  border-left: 4px solid #0f62fe;\n  border-radius: 0 4px 4px 0;\n}\n\n.ai-summary-content {\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.ai-summary-content p {\n  margin: 0;\n}\n\n.ai-summary-loading {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n}\n\n.loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.root-cause-chart-container {\n  margin-bottom: 20px;\n}\n\n.root-cause-chart-container h5 {\n  margin-bottom: 10px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.chart-wrapper {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 15px;\n}\n\n/* Critical Issues Section Styles */\n.critical-issues-section {\n  margin-top: 20px;\n  padding: 20px;\n  background-color: #262626;\n  border: 1px solid #fa4d56;\n  border-radius: 4px;\n}\n\n.critical-issues-section h5 {\n  color: #fa4d56;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.critical-issues-summary {\n  margin-bottom: 20px;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n/* Critical Issue Item Styles */\n.critical-issue-item {\n  margin-bottom: 10px;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  background-color: #161616;\n  overflow: hidden;\n}\n\n.critical-issue-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex-wrap: wrap;\n  padding: 12px 16px;\n  cursor: pointer;\n  background-color: #161616;\n  transition: background-color 0.2s;\n}\n\n.critical-issue-header:hover {\n  background-color: #262626;\n}\n\n.critical-issue-title {\n  font-weight: 600;\n  min-width: 120px;\n}\n\n.month-tag {\n  margin-right: 10px;\n}\n\n.critical-issue-multiplier {\n  color: #fa4d56;\n  font-weight: 500;\n}\n\n.critical-issue-status {\n  margin-left: auto;\n}\n\n.expand-icon {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #8d8d8d;\n}\n\n.critical-issue-content {\n  padding: 16px;\n  background-color: #262626;\n  border-top: 1px solid #393939;\n}\n\n.critical-issue-ai-description {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #161616;\n  border-left: 4px solid #0f62fe;\n  border-radius: 0 4px 4px 0;\n}\n\n.critical-issue-ai-description p {\n  line-height: 1.5;\n  font-size: 14px;\n  margin: 0;\n}\n\n/* Update Form Styles */\n.critical-issue-update-form {\n  margin-bottom: 20px;\n}\n\n.critical-issue-update-form h6 {\n  margin-bottom: 10px;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.save-update-button {\n  margin-top: 10px;\n}\n\n/* Previous Updates Styles */\n.previous-updates {\n  margin-top: 20px;\n}\n\n.previous-updates h6 {\n  margin-bottom: 10px;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n/* Carbon Component Overrides */\n\n.cv-tile {\n  background-color: #262626;\n  padding: 16px;\n}\n\n.cv-structured-list {\n  margin-top: 10px;\n  background-color: #262626;\n}\n\n.cv-structured-list-heading {\n  font-size: 12px;\n  color: #8d8d8d;\n}\n\n.cv-structured-list-row {\n  border-bottom: 1px solid #393939;\n}\n\n.cv-structured-list-cell {\n  font-size: 14px;\n  padding: 10px;\n}\n\n/* Custom tooltip styles */\n::v-deep .custom-tooltip {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 14px;\n  color: #f4f4f4;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n::v-deep .custom-tooltip p {\n  margin: 5px 0;\n  line-height: 1.4;\n}\n\n::v-deep .custom-tooltip p:first-child {\n  margin-top: 0;\n  font-weight: 600;\n}\n\n::v-deep .custom-tooltip p:last-child {\n  margin-bottom: 0;\n}\n\n.tooltip-hint {\n  font-style: italic;\n  color: #8d8d8d;\n  margin-top: 8px;\n}\n\n/* AI Test Tab Styles */\n.ai-test-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.ai-test-header {\n  margin-bottom: 10px;\n}\n\n.ai-test-header h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.ai-test-header p {\n  color: #8d8d8d;\n  margin-top: 0;\n}\n\n.ai-test-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.model-selection, .temperature-control {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.model-selection {\n  flex: 1;\n  min-width: 250px;\n}\n\n.temperature-control {\n  flex: 1;\n  min-width: 200px;\n}\n\n.temperature-control input[type=\"range\"] {\n  width: 100%;\n  margin-top: 5px;\n}\n\n.prompt-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.prompt-container label {\n  font-weight: bold;\n}\n\n.prompt-container textarea {\n  width: 100%;\n  padding: 10px;\n  border-radius: 4px;\n  border: 1px solid #393939;\n  background-color: #333333;\n  color: #f4f4f4;\n  font-family: 'IBM Plex Mono', monospace;\n  resize: vertical;\n}\n\n.prompt-buttons {\n  display: flex;\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.response-container {\n  margin-top: 20px;\n  min-height: 200px;\n  border-radius: 4px;\n  border: 1px solid #393939;\n  background-color: #333333;\n  padding: 15px;\n}\n\n.ai-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.ai-response {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.ai-response h5 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.response-content {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 15px;\n  overflow: auto;\n  max-height: 400px;\n}\n\n.response-content pre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  margin: 0;\n  font-family: 'IBM Plex Mono', monospace;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.no-response-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #8d8d8d;\n  font-style: italic;\n}\n\n.view-data-cell {\n  text-align: center;\n  white-space: nowrap;\n}\n\n.dashboard-critical-issues-summary {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 10px 15px;\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.dashboard-critical-issues-summary h4 {\n  margin: 0;\n  color: #f4f4f4;\n  font-size: 16px;\n}\n\n.dashboard-critical-issues-summary .critical-count {\n  color: #fa4d56;\n  font-weight: bold;\n  margin-left: 5px;\n}\n\n.critical-issues-tooltip {\n  color: #fa4d56;\n  font-weight: bold;\n  margin-top: 10px;\n}\n\n.click-to-view-tooltip {\n  color: #0f62fe;\n  font-style: italic;\n  margin-top: 5px;\n}\n</style>\n"]}]}