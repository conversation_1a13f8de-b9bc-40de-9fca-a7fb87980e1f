const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');

// File path for storing action tracker data
const DATA_FILE_PATH = path.join(__dirname, '../data/action_tracker.json');

// Ensure the data directory exists
function ensureDataDirectoryExists() {
  const dataDir = path.join(__dirname, '../data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Load action tracker data from file
function loadActionTrackerData() {
  ensureDataDirectoryExists();

  if (!fs.existsSync(DATA_FILE_PATH)) {
    // Create initial data file with sample data
    const initialData = {
      items: [
        {
          id: 'a',
          commodity: 'Power',
          group: 'Fan Themis',
          pn: '02ED368',
          test: 'FAB',
          deadline: '2024-11-25',
          expectedResolution: '2024-11-20',
          action: "Bringing parts to FA, expected results Thurs. The team is working on validating the new fan design with improved airflow characteristics.",
          expectedImprovements: "30% increase in airflow, 15% reduction in noise levels",
          status: "In-Progress",
          progress: 65,
          assignee: "Stevana C",
          notes: "This is a critical component for the next-gen server cooling system. We need to ensure it meets all thermal requirements under various load conditions.",
          updates: [
            {
              date: "2024-08-15",
              content: "Initial design review completed",
              updatedBy: "Stevana C"
            },
            {
              date: "2024-09-01",
              content: "Prototype testing started",
              updatedBy: "Stevana C"
            }
          ],
          createdAt: "2024-08-01",
          updatedAt: "2024-09-01"
        },
        {
          id: 'b',
          commodity: 'Cable',
          group: 'SMP10',
          pn: '02EA657',
          test: 'FUL',
          deadline: '2024-10-15',
          expectedResolution: '2024-10-10',
          action: "RMA to Amphenol. The cable connectors are showing intermittent connection issues during stress testing.",
          expectedImprovements: "Eliminate intermittent connection failures, improve reliability by 99.9%",
          status: "Blocked",
          progress: 40,
          assignee: "Dave H",
          notes: "This is the third batch with similar issues. We may need to consider alternative suppliers if Amphenol cannot resolve the quality issues.",
          updates: [
            {
              date: "2024-08-10",
              content: "Identified connection issues during stress testing",
              updatedBy: "Dave H"
            },
            {
              date: "2024-08-25",
              content: "Sent samples to Amphenol for analysis",
              updatedBy: "Dave H"
            }
          ],
          createdAt: "2024-08-05",
          updatedAt: "2024-08-25"
        },
        {
          id: 'c',
          commodity: 'Memory',
          group: 'Centaur-D',
          pn: '01KM429',
          test: 'FAB',
          deadline: '2024-09-30',
          expectedResolution: '2024-09-25',
          action: "Completing final validation tests. All performance metrics are within expected ranges.",
          expectedImprovements: "Validation of performance metrics, documentation for production approval",
          status: "Completed",
          progress: 100,
          assignee: "Chon L",
          notes: "This is a standard validation for the new memory modules. No significant issues encountered during testing.",
          updates: [
            {
              date: "2024-07-15",
              content: "Started validation testing",
              updatedBy: "Chon L"
            },
            {
              date: "2024-08-01",
              content: "Performance metrics within expected ranges",
              updatedBy: "Chon L"
            },
            {
              date: "2024-09-15",
              content: "Completed all tests, documentation finalized",
              updatedBy: "Chon L"
            }
          ],
          createdAt: "2024-07-10",
          updatedAt: "2024-09-15"
        },
        {
          id: 'd',
          commodity: 'FAB',
          group: 'Hemlock',
          pn: '02CK123',
          test: 'FAB',
          deadline: '2024-12-15',
          expectedResolution: '2024-12-10',
          action: "Investigating yield issues with the latest batch. Initial analysis suggests a potential issue with the etching process.",
          expectedImprovements: "Increase yield from 75% to 90%, reduce defect rate by 50%",
          status: "In-Progress",
          progress: 35,
          assignee: "Robert T",
          notes: "This is a critical component for upcoming product launches. Need to resolve yield issues quickly to avoid production delays.",
          updates: [
            {
              date: "2024-08-20",
              content: "Identified yield issues in latest batch",
              updatedBy: "Robert T"
            },
            {
              date: "2024-09-05",
              content: "Initial analysis points to etching process issues",
              updatedBy: "Robert T"
            }
          ],
          createdAt: "2024-08-15",
          updatedAt: "2024-09-05"
        },
        {
          id: 'e',
          commodity: 'FUL',
          group: 'Felis HLA',
          pn: '01JT456',
          test: 'FUL',
          deadline: '2024-11-30',
          expectedResolution: '2024-11-25',
          action: "Conducting additional stress tests after recent design changes. Initial results show improved performance under high load conditions.",
          expectedImprovements: "20% better thermal performance, eliminate throttling under sustained load",
          status: "In-Progress",
          progress: 70,
          assignee: "Maria L",
          notes: "Design changes were implemented to address previous thermal issues. Need comprehensive validation before mass production.",
          updates: [
            {
              date: "2024-08-05",
              content: "Implemented design changes to address thermal issues",
              updatedBy: "Maria L"
            },
            {
              date: "2024-08-25",
              content: "Initial stress tests show improved performance",
              updatedBy: "Maria L"
            },
            {
              date: "2024-09-10",
              content: "Started comprehensive validation testing",
              updatedBy: "Maria L"
            }
          ],
          createdAt: "2024-08-01",
          updatedAt: "2024-09-10"
        },
        {
          id: 'f',
          commodity: 'Power',
          group: 'Hardware Asm',
          pn: '03BN789',
          test: 'MST',
          deadline: '2024-12-10',
          expectedResolution: '2024-12-05',
          action: "Finalizing integration tests with new power management firmware. Performance metrics are meeting expectations but need additional endurance testing.",
          expectedImprovements: "10% power efficiency improvement, better thermal management",
          status: "In-Progress",
          progress: 80,
          assignee: "James K",
          notes: "This is part of the power efficiency improvement initiative. Need to ensure compatibility with all system configurations.",
          updates: [
            {
              date: "2024-08-15",
              content: "Initial firmware integration completed",
              updatedBy: "James K"
            },
            {
              date: "2024-09-01",
              content: "Performance testing shows 8% efficiency improvement",
              updatedBy: "James K"
            },
            {
              date: "2024-09-15",
              content: "Started endurance testing, expected to complete in 2 weeks",
              updatedBy: "James K"
            }
          ],
          createdAt: "2024-08-10",
          updatedAt: "2024-09-15"
        }
      ]
    };
    fs.writeFileSync(DATA_FILE_PATH, JSON.stringify(initialData, null, 2));
    return initialData;
  }

  try {
    const data = fs.readFileSync(DATA_FILE_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading action tracker data file:', error);
    return { items: [] };
  }
}

// Save action tracker data to file
function saveActionTrackerDataToFile(data) {
  ensureDataDirectoryExists();
  fs.writeFileSync(DATA_FILE_PATH, JSON.stringify(data, null, 2));
}

// Export controller functions
exports.get_action_tracker_data = (req, res) => {
  getActionTrackerData(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_action_tracker_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(200).json(data);
  });
};

// Export save action tracker data function
exports.save_action_tracker_data = (req, res) => {
  saveActionTrackerData(req.body, function (err, data) {
    if (err) {
      console.error("Error in save_action_tracker_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(200).json(data);
  });
};

// Export update action tracker data function
exports.update_action_tracker_data = (req, res) => {
  updateActionTrackerData(req.body, function (err, data) {
    if (err) {
      console.error("Error in update_action_tracker_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(200).json(data);
  });
};

// Export add update to action function
exports.add_action_update = (req, res) => {
  addActionUpdate(req.body, function (err, data) {
    if (err) {
      console.error("Error in add_action_update:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(200).json(data);
  });
};

// Function to get action tracker data for a specific breakout group
function getActionTrackerData(values, callback) {
  try {
    const { breakoutName, status, commodity, assignee, searchQuery, sortBy, sortOrder } = values;
    console.log(`Getting action tracker data with filters:`, values);

    // Load data from file
    const data = loadActionTrackerData();

    // Create response object
    const responseObj = {
      items: [],
      status_res: "success"
    };

    // Start with all items
    let filteredItems = [...data.items];

    // Apply filters
    if (breakoutName) {
      const breakoutNameLower = breakoutName.toLowerCase();
      filteredItems = filteredItems.filter(item =>
        item.commodity.toLowerCase().includes(breakoutNameLower) ||
        item.group.toLowerCase().includes(breakoutNameLower) ||
        item.pn.toLowerCase().includes(breakoutNameLower)
      );
    }

    if (status && status !== 'All') {
      filteredItems = filteredItems.filter(item => item.status === status);
    }

    if (commodity && commodity !== 'All') {
      filteredItems = filteredItems.filter(item => item.commodity === commodity);
    }

    if (assignee) {
      filteredItems = filteredItems.filter(item =>
        item.assignee.toLowerCase().includes(assignee.toLowerCase())
      );
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredItems = filteredItems.filter(item =>
        item.pn.toLowerCase().includes(query) ||
        item.group.toLowerCase().includes(query) ||
        item.commodity.toLowerCase().includes(query) ||
        item.action.toLowerCase().includes(query) ||
        item.assignee.toLowerCase().includes(query) ||
        item.notes.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    if (sortBy) {
      filteredItems.sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle special cases for date fields
        if (sortBy === 'deadline' || sortBy === 'expectedResolution' || sortBy === 'createdAt' || sortBy === 'updatedAt') {
          aValue = new Date(aValue);
          bValue = new Date(bValue);
        }

        // Handle numeric fields
        if (sortBy === 'progress') {
          aValue = Number(aValue);
          bValue = Number(bValue);
        }

        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
      });
    } else {
      // Default sort by updatedAt (most recent first)
      filteredItems.sort((a, b) => {
        const dateA = new Date(a.updatedAt || a.createdAt);
        const dateB = new Date(b.updatedAt || b.createdAt);
        return dateB - dateA;
      });
    }

    responseObj.items = filteredItems;
    console.log(`Found ${responseObj.items.length} action items after filtering`);

    // Return the response
    return callback(null, responseObj);
  } catch (error) {
    console.error("Error in getActionTrackerData:", error);
    return callback(error, { status_res: "error", message: error.message });
  }
}

// Function to save new action tracker data
function saveActionTrackerData(values, callback) {
  try {
    console.log('Saving new action tracker data:', values);

    // Load existing data
    const data = loadActionTrackerData();

    // Generate a unique ID if one wasn't provided
    const newItem = { ...values };
    if (!newItem.id) {
      newItem.id = 'id_' + Date.now();
    }

    // Add timestamps
    const now = new Date().toISOString().split('T')[0];
    newItem.createdAt = now;
    newItem.updatedAt = now;

    // Initialize updates array if not provided
    if (!newItem.updates) {
      newItem.updates = [];
    }

    // Add initial update
    newItem.updates.push({
      date: now,
      content: "Action item created",
      updatedBy: newItem.assignee || "System"
    });

    // Add progress field if not provided
    if (newItem.progress === undefined) {
      newItem.progress = 0;
    }

    // Add the new item to the data
    data.items.push(newItem);

    // Save the updated data
    saveActionTrackerDataToFile(data);

    // Create response object
    const responseObj = {
      status_res: "success",
      message: "Action tracker data saved successfully",
      item: newItem
    };

    // Return the response
    return callback(null, responseObj);
  } catch (error) {
    console.error("Error in saveActionTrackerData:", error);
    return callback(error, { status_res: "error", message: error.message });
  }
}

// Function to update existing action tracker data
function updateActionTrackerData(values, callback) {
  try {
    const { id, ...updateValues } = values;
    console.log(`Updating action tracker data for ID: ${id}`, updateValues);

    if (!id) {
      return callback(new Error("ID is required for updating action tracker data"), null);
    }

    // Load existing data
    const data = loadActionTrackerData();

    // Find the item to update
    const itemIndex = data.items.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return callback(new Error(`Action item with ID ${id} not found`), null);
    }

    // Get the existing item
    const existingItem = data.items[itemIndex];

    // Update the item with new values
    const updatedItem = { ...existingItem, ...updateValues };

    // Update the timestamp
    updatedItem.updatedAt = new Date().toISOString().split('T')[0];

    // Replace the item in the data
    data.items[itemIndex] = updatedItem;

    // Save the updated data
    saveActionTrackerDataToFile(data);

    // Create response object
    const responseObj = {
      status_res: "success",
      message: "Action tracker data updated successfully",
      item: updatedItem
    };

    // Return the response
    return callback(null, responseObj);
  } catch (error) {
    console.error("Error in updateActionTrackerData:", error);
    return callback(error, { status_res: "error", message: error.message });
  }
}

// Function to add an update to an action item
function addActionUpdate(values, callback) {
  try {
    const { id, update } = values;
    console.log(`Adding update to action item with ID: ${id}`, update);

    if (!id) {
      return callback(new Error("ID is required for adding an update"), null);
    }

    if (!update || !update.content) {
      return callback(new Error("Update content is required"), null);
    }

    // Load existing data
    const data = loadActionTrackerData();

    // Find the item to update
    const itemIndex = data.items.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return callback(new Error(`Action item with ID ${id} not found`), null);
    }

    // Get the existing item
    const existingItem = data.items[itemIndex];

    // Ensure updates array exists
    if (!existingItem.updates) {
      existingItem.updates = [];
    }

    // Create the update object
    const newUpdate = {
      date: new Date().toISOString().split('T')[0],
      content: update.content,
      updatedBy: update.updatedBy || "System"
    };

    // Add the update to the item
    existingItem.updates.push(newUpdate);

    // Update the timestamp
    existingItem.updatedAt = newUpdate.date;

    // Save the updated data
    saveActionTrackerDataToFile(data);

    // Create response object
    const responseObj = {
      status_res: "success",
      message: "Update added successfully",
      item: existingItem,
      update: newUpdate
    };

    // Return the response
    return callback(null, responseObj);
  } catch (error) {
    console.error("Error in addActionUpdate:", error);
    return callback(error, { status_res: "error", message: error.message });
  }
}
