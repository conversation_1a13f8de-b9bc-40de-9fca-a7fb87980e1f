{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue", "mtime": 1748528237445}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IEN2QnV0dG9uLCBDdklubGluZUxvYWRpbmcsIEN2TW9kYWwsIEN2VGFnLCBDdlRleHRBcmVhIH0gZnJvbSAnQGNhcmJvbi92dWUnOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdRRURhc2hib2FyZCcsCiAgY29tcG9uZW50czogewogICAgQ3ZCdXR0b24sCiAgICBDdklubGluZUxvYWRpbmcsCiAgICBDdk1vZGFsLAogICAgQ3ZUYWcsCiAgICBDdlRleHRBcmVhCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNMb2FkaW5nOiB0cnVlLAogICAgICBwcWVPd25lcnM6IFtdLAogICAgICBzaG93Q3JpdGljYWxJc3N1ZXNNb2RhbDogZmFsc2UsCiAgICAgIHNlbGVjdGVkUFFFOiBudWxsLAogICAgICBzZWxlY3RlZEdyb3VwTmFtZTogbnVsbCwKICAgICAgY3JpdGljYWxJc3N1ZXM6IFtdLAogICAgICBvdXRzdGFuZGluZ0lzc3VlczogW10sCiAgICAgIHJlc29sdmVkSXNzdWVzOiBbXSwKICAgICAgZXhwYW5kZWRJc3N1ZUlkczogW10sCiAgICAgIGV4cGFuZGVkT3V0c3RhbmRpbmdJc3N1ZUlkczogW10sCiAgICAgIGV4cGFuZGVkUmVzb2x2ZWRJc3N1ZUlkczogW10sCiAgICAgIGFjdGl2ZUlzc3VlVGFiOiAnY3JpdGljYWwnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIFNvcnQgUFFFIG93bmVycyBieSBjcml0aWNhbCBpc3N1ZXMgY291bnQgKGhpZ2hlc3QgZmlyc3QpCiAgICBzb3J0ZWRQUUVPd25lcnMoKSB7CiAgICAgIHJldHVybiBbLi4udGhpcy5wcWVPd25lcnNdLnNvcnQoKGEsIGIpID0+IGIuY3JpdGljYWxJc3N1ZXMgLSBhLmNyaXRpY2FsSXNzdWVzKTsKICAgIH0sCiAgICAvLyBDYWxjdWxhdGUgdG90YWwgY3JpdGljYWwgaXNzdWVzIGFjcm9zcyBhbGwgUFFFcwogICAgdG90YWxDcml0aWNhbElzc3VlcygpIHsKICAgICAgcmV0dXJuIHRoaXMucHFlT3duZXJzLnJlZHVjZSgodG90YWwsIHBxZSkgPT4gdG90YWwgKyBwcWUuY3JpdGljYWxJc3N1ZXMsIDApOwogICAgfSwKICAgIC8vIENhbGN1bGF0ZSB0b3RhbCB1bnZhbGlkYXRlZCBmYWlscyBhY3Jvc3MgYWxsIFBRRXMKICAgIHRvdGFsVW52YWxpZGF0ZWQoKSB7CiAgICAgIHJldHVybiB0aGlzLnBxZU93bmVycy5yZWR1Y2UoKHRvdGFsLCBwcWUpID0+IHRvdGFsICsgcHFlLnVudmFsaWRhdGVkQ291bnQsIDApOwogICAgfSwKICAgIC8vIENhbGN1bGF0ZSB0b3RhbCBjcml0aWNhbCBicmVha291dCBncm91cHMgYWNyb3NzIGFsbCBQUUVzCiAgICB0b3RhbENyaXRpY2FsR3JvdXBzKCkgewogICAgICByZXR1cm4gdGhpcy5wcWVPd25lcnMucmVkdWNlKCh0b3RhbCwgcHFlKSA9PiB0b3RhbCArIHBxZS5jcml0aWNhbEdyb3Vwcy5sZW5ndGgsIDApOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMubG9hZFBRRU93bmVyc0RhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGxvYWRQUUVPd25lcnNEYXRhKCkgewogICAgICB0aGlzLmlzTG9hZGluZyA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGZXRjaCBQUUUgb3duZXJzIGRhdGEgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9hbGxfcHFlX2RhdGEnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4uY29uZmlnLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7fSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggUFFFIGRhdGE6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIHRoaXMucHFlT3duZXJzID0gZGF0YS5wcWVfb3duZXJzIHx8IFtdOwogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCBkYXRhIGZvciAke3RoaXMucHFlT3duZXJzLmxlbmd0aH0gUFFFIG93bmVyc2ApOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBQUUUgZGF0YTonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgICAgdGhpcy5sb2FkU2FtcGxlRGF0YSgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIFBRRSBkYXRhOicsIGVycm9yKTsKICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgdGhpcy5sb2FkU2FtcGxlRGF0YSgpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaXNMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgbG9hZFNhbXBsZURhdGEoKSB7CiAgICAgIC8vIFNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICB0aGlzLnBxZU93bmVycyA9IFsKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAnQWxiZXJ0IEcuJywKICAgICAgICAgIGNyaXRpY2FsSXNzdWVzOiA0LAogICAgICAgICAgdW52YWxpZGF0ZWRDb3VudDogMTIsCiAgICAgICAgICBjb2xsZWN0aXZlWEZhY3RvcjogMy4yLAogICAgICAgICAgY3JpdGljYWxHcm91cHM6IFsKICAgICAgICAgICAgeyBuYW1lOiAnRmFuIFRoZW1pcycsIHhGYWN0b3I6IDMuMiB9LAogICAgICAgICAgICB7IG5hbWU6ICdWaWN0b3JpYSBDcnlwdG8nLCB4RmFjdG9yOiAxLjggfSwKICAgICAgICAgICAgeyBuYW1lOiAnUXVhbnR1bSBOZXh1cycsIHhGYWN0b3I6IDEuNiB9CiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAnU2FyYWggTC4nLAogICAgICAgICAgY3JpdGljYWxJc3N1ZXM6IDIsCiAgICAgICAgICB1bnZhbGlkYXRlZENvdW50OiA4LAogICAgICAgICAgY29sbGVjdGl2ZVhGYWN0b3I6IDIuNSwKICAgICAgICAgIGNyaXRpY2FsR3JvdXBzOiBbCiAgICAgICAgICAgIHsgbmFtZTogJ1N0ZWxsYXIgQ29yZScsIHhGYWN0b3I6IDIuNSB9LAogICAgICAgICAgICB7IG5hbWU6ICdOZWJ1bGEgRHJpdmUnLCB4RmFjdG9yOiAxLjcgfQogICAgICAgICAgXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogJ01pY2hhZWwgVC4nLAogICAgICAgICAgY3JpdGljYWxJc3N1ZXM6IDEsCiAgICAgICAgICB1bnZhbGlkYXRlZENvdW50OiA1LAogICAgICAgICAgY29sbGVjdGl2ZVhGYWN0b3I6IDEuOSwKICAgICAgICAgIGNyaXRpY2FsR3JvdXBzOiBbCiAgICAgICAgICAgIHsgbmFtZTogJ1B1bHNhciBNYXRyaXgnLCB4RmFjdG9yOiAxLjkgfQogICAgICAgICAgXQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogJ0plbm5pZmVyIEsuJywKICAgICAgICAgIGNyaXRpY2FsSXNzdWVzOiAwLAogICAgICAgICAgdW52YWxpZGF0ZWRDb3VudDogMywKICAgICAgICAgIGNvbGxlY3RpdmVYRmFjdG9yOiAwLjksCiAgICAgICAgICBjcml0aWNhbEdyb3VwczogW10KICAgICAgICB9CiAgICAgIF07CiAgICB9LAoKICAgIHZpZXdQUUVEYXNoYm9hcmQocHFlTmFtZSkgewogICAgICAvLyBFbWl0IGV2ZW50IHRvIHNlbGVjdCB0aGUgUFFFIG93bmVyIGluIHRoZSBwYXJlbnQgY29tcG9uZW50CiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdC1wcWUnLCBwcWVOYW1lKTsKICAgIH0sCgogICAgdmlld0NyaXRpY2FsSXNzdWVzKHBxZSkgewogICAgICB0aGlzLnNlbGVjdGVkUFFFID0gcHFlOwogICAgICB0aGlzLnNlbGVjdGVkR3JvdXBOYW1lID0gbnVsbDsgLy8gUmVzZXQgZ3JvdXAgbmFtZSB3aGVuIHZpZXdpbmcgYWxsIGNyaXRpY2FsIGlzc3VlcwogICAgICB0aGlzLmFjdGl2ZUlzc3VlVGFiID0gJ2NyaXRpY2FsJzsgLy8gU2V0IGFjdGl2ZSB0YWIgdG8gY3JpdGljYWwgaXNzdWVzCiAgICAgIHRoaXMubG9hZEFsbElzc3VlcyhwcWUubmFtZSk7CiAgICAgIHRoaXMuc2hvd0NyaXRpY2FsSXNzdWVzTW9kYWwgPSB0cnVlOwogICAgfSwKCiAgICBjbG9zZUNyaXRpY2FsSXNzdWVzTW9kYWwoKSB7CiAgICAgIHRoaXMuc2hvd0NyaXRpY2FsSXNzdWVzTW9kYWwgPSBmYWxzZTsKICAgICAgdGhpcy5zZWxlY3RlZFBRRSA9IG51bGw7CiAgICAgIHRoaXMuc2VsZWN0ZWRHcm91cE5hbWUgPSBudWxsOwogICAgICB0aGlzLmNyaXRpY2FsSXNzdWVzID0gW107CiAgICAgIHRoaXMub3V0c3RhbmRpbmdJc3N1ZXMgPSBbXTsKICAgICAgdGhpcy5yZXNvbHZlZElzc3VlcyA9IFtdOwogICAgICB0aGlzLmV4cGFuZGVkSXNzdWVJZHMgPSBbXTsKICAgICAgdGhpcy5leHBhbmRlZE91dHN0YW5kaW5nSXNzdWVJZHMgPSBbXTsKICAgICAgdGhpcy5leHBhbmRlZFJlc29sdmVkSXNzdWVJZHMgPSBbXTsKICAgIH0sCgogICAgYXN5bmMgbG9hZEFsbElzc3VlcyhwcWVPd25lcikgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBhbGwgaXNzdWVzIGZvciBQUUUgb3duZXI6ICR7cHFlT3duZXJ9YCk7CgogICAgICAvLyBMb2FkIGFsbCBpc3N1ZSB0eXBlcyBpbiBwYXJhbGxlbAogICAgICBhd2FpdCBQcm9taXNlLmFsbChbCiAgICAgICAgdGhpcy5sb2FkQ3JpdGljYWxJc3N1ZXMocHFlT3duZXIpLAogICAgICAgIHRoaXMubG9hZE91dHN0YW5kaW5nSXNzdWVzKHBxZU93bmVyKSwKICAgICAgICB0aGlzLmxvYWRSZXNvbHZlZElzc3VlcyhwcWVPd25lcikKICAgICAgXSk7CiAgICB9LAoKICAgIGFzeW5jIGxvYWRDcml0aWNhbElzc3VlcyhwcWVPd25lcikgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBjcml0aWNhbCBpc3N1ZXMgZm9yIFBRRSBvd25lcjogJHtwcWVPd25lcn1gKTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGZXRjaCBjcml0aWNhbCBpc3N1ZXMgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfY3JpdGljYWxfaXNzdWVzJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLmNvbmZpZy5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBwcWVPd25lcjogcHFlT3duZXIKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGNyaXRpY2FsIGlzc3VlczogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy5jcml0aWNhbElzc3VlcyA9IGRhdGEuY3JpdGljYWxfaXNzdWVzIHx8IFtdOwogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMuY3JpdGljYWxJc3N1ZXMubGVuZ3RofSBjcml0aWNhbCBpc3N1ZXNgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgY3JpdGljYWwgaXNzdWVzOicsIGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgICB0aGlzLmxvYWRTYW1wbGVDcml0aWNhbElzc3VlcyhwcWVPd25lcik7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgY3JpdGljYWwgaXNzdWVzOicsIGVycm9yKTsKICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgdGhpcy5sb2FkU2FtcGxlQ3JpdGljYWxJc3N1ZXMocHFlT3duZXIpOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVDcml0aWNhbElzc3VlcyhwcWVPd25lcikgewogICAgICAvLyBVc2Ugb3VyIGhlbHBlciBtZXRob2QgdG8gZ2V0IHNhbXBsZSBpc3N1ZXMgZm9yIHRoaXMgUFFFIG93bmVyCiAgICAgIHRoaXMuY3JpdGljYWxJc3N1ZXMgPSB0aGlzLmdldFNhbXBsZUlzc3Vlc0ZvclBRRShwcWVPd25lcik7CiAgICB9LAoKICAgIGFzeW5jIGxvYWRPdXRzdGFuZGluZ0lzc3VlcyhwcWVPd25lcikgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBvdXRzdGFuZGluZyBpc3N1ZXMgZm9yIFBRRSBvd25lcjogJHtwcWVPd25lcn1gKTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGZXRjaCBvdXRzdGFuZGluZyBpc3N1ZXMgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfb3V0c3RhbmRpbmdfaXNzdWVzJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLmNvbmZpZy5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBwcWVPd25lcjogcHFlT3duZXIKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIG91dHN0YW5kaW5nIGlzc3VlczogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy5vdXRzdGFuZGluZ0lzc3VlcyA9IGRhdGEub3V0c3RhbmRpbmdfaXNzdWVzIHx8IFtdOwogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMub3V0c3RhbmRpbmdJc3N1ZXMubGVuZ3RofSBvdXRzdGFuZGluZyBpc3N1ZXNgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgb3V0c3RhbmRpbmcgaXNzdWVzOicsIGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgICB0aGlzLmxvYWRTYW1wbGVPdXRzdGFuZGluZ0lzc3VlcyhwcWVPd25lcik7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgb3V0c3RhbmRpbmcgaXNzdWVzOicsIGVycm9yKTsKICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgdGhpcy5sb2FkU2FtcGxlT3V0c3RhbmRpbmdJc3N1ZXMocHFlT3duZXIpOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVPdXRzdGFuZGluZ0lzc3VlcyhwcWVPd25lcikgewogICAgICAvLyBTYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgaWYgKHBxZU93bmVyID09PSAnQWxiZXJ0IEcuJykgewogICAgICAgIHRoaXMub3V0c3RhbmRpbmdJc3N1ZXMgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAnb2kxJywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdGYW4gVGhlbWlzJywKICAgICAgICAgICAgbW9udGg6ICcyMDI0LTA2JywKICAgICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLAogICAgICAgICAgICBpbmNyZWFzZU11bHRpcGxpZXI6ICcxLjQnLAogICAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnRmFuIFRoZW1pcyBzaG93aW5nIDEuNHggaW5jcmVhc2Ugb3ZlciB0YXJnZXQgcmF0ZS4gVGhpcyBpcyBhIGtub3duIGlzc3VlIHdpdGggdGhlIGNvb2xpbmcgc3lzdGVtIHRoYXQgaGFzIGJlZW4gYWNjZXB0ZWQuIE1vbml0b3JpbmcgZm9yIGFueSBzaWduaWZpY2FudCBjaGFuZ2VzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICdLbm93biBpc3N1ZSB3aXRoIGNvb2xpbmcgc3lzdGVtLiBFbmdpbmVlcmluZyBoYXMgYWNjZXB0ZWQgdGhpcyBhcyBhIGxpbWl0YXRpb24gb2YgdGhlIGN1cnJlbnQgZGVzaWduLiBNb25pdG9yaW5nIGZvciBhbnkgc2lnbmlmaWNhbnQgY2hhbmdlcy4nLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdSb290IENhdXNlJywKICAgICAgICAgICAgYWNjZXB0YW5jZURhdGU6ICcyMDI0LTA0LTEwJywKICAgICAgICAgICAgY3VycmVudFBlcmZvcm1hbmNlOiAnMS4zeCcsCiAgICAgICAgICAgIGFjY2VwdGVkQnk6ICdFbmdpbmVlcmluZyBUZWFtJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdvaTInLAogICAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNScsCiAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS4zJywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ1ZpY3RvcmlhIENyeXB0byBzaG93aW5nIDEuM3ggaW5jcmVhc2UgaW4gZmFpbHVyZSByYXRlLiBUaGlzIGlzIHJlbGF0ZWQgdG8gYSBrbm93biBzdXBwbGllciBpc3N1ZSB0aGF0IGhhcyBiZWVuIGFjY2VwdGVkIGFzIHdpdGhpbiB0b2xlcmFuY2UuIE1vbml0b3JpbmcgZm9yIGFueSBzaWduaWZpY2FudCBjaGFuZ2VzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICdTdXBwbGllciB2YXJpYXRpb24gaXMgd2l0aGluIGFjY2VwdGVkIHRvbGVyYW5jZS4gQ29zdCBvZiBmaXhpbmcgZXhjZWVkcyBiZW5lZml0LiBNb25pdG9yaW5nIG1vbnRobHkgZm9yIGFueSBzaWduaWZpY2FudCBjaGFuZ2VzLicsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1N1cHBsaWVyJywKICAgICAgICAgICAgYWNjZXB0YW5jZURhdGU6ICcyMDI0LTAzLTIyJywKICAgICAgICAgICAgY3VycmVudFBlcmZvcm1hbmNlOiAnMS4yeCcsCiAgICAgICAgICAgIGFjY2VwdGVkQnk6ICdRdWFsaXR5IFRlYW0nCiAgICAgICAgICB9CiAgICAgICAgXTsKICAgICAgfSBlbHNlIGlmIChwcWVPd25lciA9PT0gJ1NhcmFoIEwuJykgewogICAgICAgIHRoaXMub3V0c3RhbmRpbmdJc3N1ZXMgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAnb2kzJywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdTdGVsbGFyIENvcmUnLAogICAgICAgICAgICBtb250aDogJzIwMjQtMDUnLAogICAgICAgICAgICBzZXZlcml0eTogJ21lZGl1bScsCiAgICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzEuMycsCiAgICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdTdGVsbGFyIENvcmUgc2hvd2luZyAxLjN4IGluY3JlYXNlIGluIGZhaWx1cmUgcmF0ZS4gVGhpcyBpc3N1ZSBoYXMgYmVlbiBhY2NlcHRlZCBhcyBhIGtub3duIGxpbWl0YXRpb24uIE1vbml0b3JpbmcgZm9yIGFueSBzaWduaWZpY2FudCBjaGFuZ2VzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICdVbml0cyBoYXZlIGEga25vd24gbGltaXRhdGlvbiBpbiB0aGUgcG93ZXIgbWFuYWdlbWVudCBzeXN0ZW0uIEVuZ2luZWVyaW5nIGhhcyBhY2NlcHRlZCB0aGlzIGlzc3VlLiBNb25pdG9yaW5nIGZvciBhbnkgc2lnbmlmaWNhbnQgY2hhbmdlcy4nLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdSb290IENhdXNlJywKICAgICAgICAgICAgYWNjZXB0YW5jZURhdGU6ICcyMDI0LTA0LTA1JywKICAgICAgICAgICAgY3VycmVudFBlcmZvcm1hbmNlOiAnMS4yeCcsCiAgICAgICAgICAgIGFjY2VwdGVkQnk6ICdFbmdpbmVlcmluZyBUZWFtJwogICAgICAgICAgfQogICAgICAgIF07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5vdXRzdGFuZGluZ0lzc3VlcyA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGxvYWRSZXNvbHZlZElzc3VlcyhwcWVPd25lcikgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyByZXNvbHZlZCBpc3N1ZXMgZm9yIFBRRSBvd25lcjogJHtwcWVPd25lcn1gKTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGZXRjaCByZXNvbHZlZCBpc3N1ZXMgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfcmVzb2x2ZWRfaXNzdWVzJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLmNvbmZpZy5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBwcWVPd25lcjogcHFlT3duZXIKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHJlc29sdmVkIGlzc3VlczogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy5yZXNvbHZlZElzc3VlcyA9IGRhdGEucmVzb2x2ZWRfaXNzdWVzIHx8IFtdOwogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMucmVzb2x2ZWRJc3N1ZXMubGVuZ3RofSByZXNvbHZlZCBpc3N1ZXNgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgcmVzb2x2ZWQgaXNzdWVzOicsIGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgICB0aGlzLmxvYWRTYW1wbGVSZXNvbHZlZElzc3VlcyhwcWVPd25lcik7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgcmVzb2x2ZWQgaXNzdWVzOicsIGVycm9yKTsKICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgdGhpcy5sb2FkU2FtcGxlUmVzb2x2ZWRJc3N1ZXMocHFlT3duZXIpOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVSZXNvbHZlZElzc3VlcyhwcWVPd25lcikgewogICAgICAvLyBTYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgaWYgKHBxZU93bmVyID09PSAnQWxiZXJ0IEcuJykgewogICAgICAgIHRoaXMucmVzb2x2ZWRJc3N1ZXMgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAncmkxJywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdGYW4gVGhlbWlzJywKICAgICAgICAgICAgbW9udGg6ICcyMDI0LTA1JywKICAgICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMi44JywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ0ZhbiBUaGVtaXMgc2hvd2VkIDIuOHggc3Bpa2UgaW4gTWF5IDIwMjQuIFJvb3QgY2F1c2Ugd2FzIGlkZW50aWZpZWQgYXMgYSBtYW51ZmFjdHVyaW5nIGRlZmVjdCBpbiB0aGUgYmVhcmluZyBhc3NlbWJseS4gSXNzdWUgd2FzIHJlc29sdmVkIGJ5IGltcGxlbWVudGluZyBhZGRpdGlvbmFsIHF1YWxpdHkgY2hlY2tzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICdVcGRhdGVkIG1hbnVmYWN0dXJpbmcgcHJvY2VzcyBhbmQgYWRkZWQgaW5zcGVjdGlvbiBzdGVwLiBNb25pdG9yaW5nIHBlcmZvcm1hbmNlLicsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1Jvb3QgQ2F1c2UnLAogICAgICAgICAgICByZXNvbHV0aW9uRGF0ZTogJzIwMjQtMDUtMTUnLAogICAgICAgICAgICBjdXJyZW50UGVyZm9ybWFuY2U6ICcwLjl4JwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdyaTInLAogICAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNCcsCiAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS43JywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ1ZpY3RvcmlhIENyeXB0byBoYWQgc3VzdGFpbmVkIHByb2JsZW0gd2l0aCAxLjd4IHRhcmdldCByYXRlIGZvciAyIGNvbnNlY3V0aXZlIG1vbnRocy4gSXNzdWUgd2FzIHRyYWNlZCB0byBhIHNwZWNpZmljIHN1cHBsaWVyIGJhdGNoLiBTdXBwbGllciBjb3JyZWN0ZWQgdGhlaXIgcHJvY2Vzcy4nLAogICAgICAgICAgICBjb21tZW50OiAnV29ya2VkIHdpdGggc3VwcGxpZXIgdG8gaW1wcm92ZSB0aGVpciBxdWFsaXR5IGNvbnRyb2wuIE1vbml0b3JpbmcgbmV3IGJhdGNoZXMgY2xvc2VseS4nLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdTdXBwbGllcicsCiAgICAgICAgICAgIHJlc29sdXRpb25EYXRlOiAnMjAyNC0wNC0yOCcsCiAgICAgICAgICAgIGN1cnJlbnRQZXJmb3JtYW5jZTogJzAuOHgnCiAgICAgICAgICB9CiAgICAgICAgXTsKICAgICAgfSBlbHNlIGlmIChwcWVPd25lciA9PT0gJ1NhcmFoIEwuJykgewogICAgICAgIHRoaXMucmVzb2x2ZWRJc3N1ZXMgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAncmkzJywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdTdGVsbGFyIENvcmUnLAogICAgICAgICAgICBtb250aDogJzIwMjQtMDUnLAogICAgICAgICAgICBzZXZlcml0eTogJ2hpZ2gnLAogICAgICAgICAgICBpbmNyZWFzZU11bHRpcGxpZXI6ICcyLjUnLAogICAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnU3RlbGxhciBDb3JlIHNob3dlZCAyLjV4IHNwaWtlIGluIE1heSAyMDI0LiBSb290IGNhdXNlIHdhcyBpZGVudGlmaWVkIGFzIGEgdGhlcm1hbCBpc3N1ZSBpbiB0aGUgcG93ZXIgZGVsaXZlcnkgc3Vic3lzdGVtLiBJc3N1ZSB3YXMgcmVzb2x2ZWQgd2l0aCBhIGRlc2lnbiBjaGFuZ2UuJywKICAgICAgICAgICAgY29tbWVudDogJ0ltcGxlbWVudGVkIGRlc2lnbiBjaGFuZ2UgdG8gaW1wcm92ZSB0aGVybWFsIHBlcmZvcm1hbmNlLiBNb25pdG9yaW5nIGZpZWxkIGRhdGEuJywKICAgICAgICAgICAgYW5hbHlzaXNUeXBlOiAnUm9vdCBDYXVzZScsCiAgICAgICAgICAgIHJlc29sdXRpb25EYXRlOiAnMjAyNC0wNS0yMicsCiAgICAgICAgICAgIGN1cnJlbnRQZXJmb3JtYW5jZTogJzAuOHgnCiAgICAgICAgICB9CiAgICAgICAgXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJlc29sdmVkSXNzdWVzID0gW107CiAgICAgIH0KICAgIH0sCgogICAgdG9nZ2xlSXNzdWVFeHBhbmRlZChpc3N1ZSkgewogICAgICBjb25zdCBpc3N1ZUlkID0gaXNzdWUuaWQ7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5leHBhbmRlZElzc3VlSWRzLmluZGV4T2YoaXNzdWVJZCk7CgogICAgICBpZiAoaW5kZXggPT09IC0xKSB7CiAgICAgICAgLy8gSXNzdWUgaXMgbm90IGV4cGFuZGVkLCBzbyBleHBhbmQgaXQKICAgICAgICB0aGlzLmV4cGFuZGVkSXNzdWVJZHMucHVzaChpc3N1ZUlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyBJc3N1ZSBpcyBleHBhbmRlZCwgc28gY29sbGFwc2UgaXQKICAgICAgICB0aGlzLmV4cGFuZGVkSXNzdWVJZHMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKCiAgICB0b2dnbGVPdXRzdGFuZGluZ0lzc3VlRXhwYW5kZWQoaXNzdWUpIHsKICAgICAgY29uc3QgaXNzdWVJZCA9IGlzc3VlLmlkOwogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLmluZGV4T2YoaXNzdWVJZCk7CgogICAgICBpZiAoaW5kZXggPT09IC0xKSB7CiAgICAgICAgLy8gSXNzdWUgaXMgbm90IGV4cGFuZGVkLCBzbyBleHBhbmQgaXQKICAgICAgICB0aGlzLmV4cGFuZGVkT3V0c3RhbmRpbmdJc3N1ZUlkcy5wdXNoKGlzc3VlSWQpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIElzc3VlIGlzIGV4cGFuZGVkLCBzbyBjb2xsYXBzZSBpdAogICAgICAgIHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCgogICAgdG9nZ2xlUmVzb2x2ZWRJc3N1ZUV4cGFuZGVkKGlzc3VlKSB7CiAgICAgIGNvbnN0IGlzc3VlSWQgPSBpc3N1ZS5pZDsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmV4cGFuZGVkUmVzb2x2ZWRJc3N1ZUlkcy5pbmRleE9mKGlzc3VlSWQpOwoKICAgICAgaWYgKGluZGV4ID09PSAtMSkgewogICAgICAgIC8vIElzc3VlIGlzIG5vdCBleHBhbmRlZCwgc28gZXhwYW5kIGl0CiAgICAgICAgdGhpcy5leHBhbmRlZFJlc29sdmVkSXNzdWVJZHMucHVzaChpc3N1ZUlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyBJc3N1ZSBpcyBleHBhbmRlZCwgc28gY29sbGFwc2UgaXQKICAgICAgICB0aGlzLmV4cGFuZGVkUmVzb2x2ZWRJc3N1ZUlkcy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAoKICAgIGlzSXNzdWVFeHBhbmRlZChpc3N1ZUlkKSB7CiAgICAgIHJldHVybiB0aGlzLmV4cGFuZGVkSXNzdWVJZHMuaW5jbHVkZXMoaXNzdWVJZCk7CiAgICB9LAoKICAgIGlzT3V0c3RhbmRpbmdJc3N1ZUV4cGFuZGVkKGlzc3VlSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLmluY2x1ZGVzKGlzc3VlSWQpOwogICAgfSwKCiAgICBpc1Jlc29sdmVkSXNzdWVFeHBhbmRlZChpc3N1ZUlkKSB7CiAgICAgIHJldHVybiB0aGlzLmV4cGFuZGVkUmVzb2x2ZWRJc3N1ZUlkcy5pbmNsdWRlcyhpc3N1ZUlkKTsKICAgIH0sCgogICAgYXN5bmMgdXBkYXRlSXNzdWUoaXNzdWUpIHsKICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZSBpc3N1ZTonLCBpc3N1ZSk7CgogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRQUUUpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdObyBQUUUgb3duZXIgc2VsZWN0ZWQnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBDYWxsIHRoZSBBUEkgdG8gdXBkYXRlIHRoZSBhY3Rpb24gdHJhY2tlcgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi91cGRhdGVfcHFlX2FjdGlvbicsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi5jb25maWcuaGVhZGVycwogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgaXNzdWVJZDogaXNzdWUuaWQsCiAgICAgICAgICAgIGNhdGVnb3J5OiBpc3N1ZS5jYXRlZ29yeSwKICAgICAgICAgICAgY29tbWVudDogaXNzdWUuY29tbWVudCwKICAgICAgICAgICAgc2V2ZXJpdHk6IGlzc3VlLnNldmVyaXR5LAogICAgICAgICAgICBwcWVPd25lcjogdGhpcy5zZWxlY3RlZFBRRS5uYW1lLAogICAgICAgICAgICBtb250aDogaXNzdWUubW9udGgsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogaXNzdWUuYW5hbHlzaXNUeXBlCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgYWN0aW9uIHRyYWNrZXI6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIGFsZXJ0KGBBY3Rpb24gdHJhY2tlciB1cGRhdGVkIGZvciBpc3N1ZTogJHtpc3N1ZS5jYXRlZ29yeX1gKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBhY3Rpb24gdHJhY2tlcjonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgYWxlcnQoJ0ZhaWxlZCB0byB1cGRhdGUgYWN0aW9uIHRyYWNrZXIuIFBsZWFzZSB0cnkgYWdhaW4uJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGFjdGlvbiB0cmFja2VyOicsIGVycm9yKTsKICAgICAgICBhbGVydCgnRXJyb3IgdXBkYXRpbmcgYWN0aW9uIHRyYWNrZXIuIFBsZWFzZSB0cnkgYWdhaW4uJyk7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0UHJpb3JpdHlDbGFzcyhwcWUpIHsKICAgICAgaWYgKHBxZS5jb2xsZWN0aXZlWEZhY3RvciA+PSAzLjApIHJldHVybiAnaGlnaC1wcmlvcml0eSc7CiAgICAgIGlmIChwcWUuY29sbGVjdGl2ZVhGYWN0b3IgPj0gMS41KSByZXR1cm4gJ21lZGl1bS1wcmlvcml0eSc7CiAgICAgIHJldHVybiAnbm9ybWFsLXByaW9yaXR5JzsKICAgIH0sCgogICAgZ2V0Q3JpdGljYWxJc3N1ZXNDbGFzcyhjb3VudCkgewogICAgICBpZiAoY291bnQgPj0gMykgcmV0dXJuICdoaWdoLXZhbHVlJzsKICAgICAgaWYgKGNvdW50ID49IDEpIHJldHVybiAnbWVkaXVtLXZhbHVlJzsKICAgICAgcmV0dXJuICdub3JtYWwtdmFsdWUnOwogICAgfSwKCiAgICBnZXRVbnZhbGlkYXRlZENsYXNzKGNvdW50KSB7CiAgICAgIGlmIChjb3VudCA+PSAxMCkgcmV0dXJuICdoaWdoLXZhbHVlJzsKICAgICAgaWYgKGNvdW50ID49IDUpIHJldHVybiAnbWVkaXVtLXZhbHVlJzsKICAgICAgcmV0dXJuICdub3JtYWwtdmFsdWUnOwogICAgfSwKCiAgICBnZXRPdXRzdGFuZGluZ0NsYXNzKGNvdW50KSB7CiAgICAgIGlmIChjb3VudCA+PSA0KSByZXR1cm4gJ2hpZ2gtdmFsdWUnOwogICAgICBpZiAoY291bnQgPj0gMikgcmV0dXJuICdtZWRpdW0tdmFsdWUnOwogICAgICByZXR1cm4gJ25vcm1hbC12YWx1ZSc7CiAgICB9LAoKICAgIGdldFJlc29sdmVkQ2xhc3MoY291bnQpIHsKICAgICAgaWYgKGNvdW50ID49IDUpIHJldHVybiAnaGlnaC12YWx1ZSc7CiAgICAgIGlmIChjb3VudCA+PSAzKSByZXR1cm4gJ21lZGl1bS12YWx1ZSc7CiAgICAgIHJldHVybiAnbm9ybWFsLXZhbHVlJzsKICAgIH0sCgogICAgZ2V0UGVuZGluZ0NsYXNzKGNvdW50KSB7CiAgICAgIGlmIChjb3VudCA+PSAzKSByZXR1cm4gJ2hpZ2gtdmFsdWUnOwogICAgICBpZiAoY291bnQgPj0gMSkgcmV0dXJuICdtZWRpdW0tdmFsdWUnOwogICAgICByZXR1cm4gJ25vcm1hbC12YWx1ZSc7CiAgICB9LAoKICAgIGdldFhGYWN0b3JDbGFzcyh4RmFjdG9yKSB7CiAgICAgIGlmICh4RmFjdG9yID49IDMuMCkgcmV0dXJuICdoaWdoLXZhbHVlJzsKICAgICAgaWYgKHhGYWN0b3IgPj0gMS41KSByZXR1cm4gJ21lZGl1bS12YWx1ZSc7CiAgICAgIHJldHVybiAnbm9ybWFsLXZhbHVlJzsKICAgIH0sCgogICAgZ2V0R3JvdXBIaWdobGlnaHRDbGFzcyh4RmFjdG9yKSB7CiAgICAgIGlmICh4RmFjdG9yID49IDMuMCkgcmV0dXJuICdoaWdoLWhpZ2hsaWdodCc7CiAgICAgIGlmICh4RmFjdG9yID49IDEuNSkgcmV0dXJuICdtZWRpdW0taGlnaGxpZ2h0JzsKICAgICAgcmV0dXJuICdsb3ctaGlnaGxpZ2h0JzsKICAgIH0sCgogICAgdmlld0dyb3VwQ3JpdGljYWxJc3N1ZXMocHFlLCBncm91cE5hbWUpIHsKICAgICAgdGhpcy5zZWxlY3RlZFBRRSA9IHBxZTsKICAgICAgdGhpcy5zZWxlY3RlZEdyb3VwTmFtZSA9IGdyb3VwTmFtZTsKICAgICAgdGhpcy5hY3RpdmVJc3N1ZVRhYiA9ICdjcml0aWNhbCc7IC8vIFNldCBhY3RpdmUgdGFiIHRvIGNyaXRpY2FsIGlzc3VlcwogICAgICB0aGlzLmxvYWRBbGxJc3N1ZXNGb3JHcm91cChwcWUubmFtZSwgZ3JvdXBOYW1lKTsKICAgICAgdGhpcy5zaG93Q3JpdGljYWxJc3N1ZXNNb2RhbCA9IHRydWU7CiAgICB9LAoKICAgIGFzeW5jIGxvYWRBbGxJc3N1ZXNGb3JHcm91cChwcWVPd25lciwgZ3JvdXBOYW1lKSB7CiAgICAgIGNvbnNvbGUubG9nKGBMb2FkaW5nIGFsbCBpc3N1ZXMgZm9yIFBRRSBvd25lcjogJHtwcWVPd25lcn0sIGdyb3VwOiAke2dyb3VwTmFtZX1gKTsKCiAgICAgIC8vIExvYWQgYWxsIGlzc3VlIHR5cGVzIGluIHBhcmFsbGVsCiAgICAgIGF3YWl0IFByb21pc2UuYWxsKFsKICAgICAgICB0aGlzLmxvYWRDcml0aWNhbElzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpLAogICAgICAgIHRoaXMubG9hZE91dHN0YW5kaW5nSXNzdWVzRm9yR3JvdXAocHFlT3duZXIsIGdyb3VwTmFtZSksCiAgICAgICAgdGhpcy5sb2FkUmVzb2x2ZWRJc3N1ZXNGb3JHcm91cChwcWVPd25lciwgZ3JvdXBOYW1lKQogICAgICBdKTsKICAgIH0sCgogICAgYXN5bmMgbG9hZENyaXRpY2FsSXNzdWVzRm9yR3JvdXAocHFlT3duZXIsIGdyb3VwTmFtZSkgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBjcml0aWNhbCBpc3N1ZXMgZm9yIFBRRSBvd25lcjogJHtwcWVPd25lcn0sIGdyb3VwOiAke2dyb3VwTmFtZX1gKTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGZXRjaCBjcml0aWNhbCBpc3N1ZXMgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfY3JpdGljYWxfaXNzdWVzJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLmNvbmZpZy5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBwcWVPd25lcjogcHFlT3duZXIsCiAgICAgICAgICAgIGdyb3VwTmFtZTogZ3JvdXBOYW1lCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBjcml0aWNhbCBpc3N1ZXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIC8vIElmIHdlIGhhdmUgYSBzcGVjaWZpYyBncm91cCBuYW1lLCBmaWx0ZXIgaXNzdWVzIHRvIG9ubHkgaW5jbHVkZSB0aG9zZSBmb3IgdGhpcyBncm91cAogICAgICAgICAgaWYgKGdyb3VwTmFtZSkgewogICAgICAgICAgICBjb25zdCBhbGxJc3N1ZXMgPSBkYXRhLmNyaXRpY2FsX2lzc3VlcyB8fCBbXTsKICAgICAgICAgICAgdGhpcy5jcml0aWNhbElzc3VlcyA9IGFsbElzc3Vlcy5maWx0ZXIoaXNzdWUgPT4KICAgICAgICAgICAgICBpc3N1ZS5jYXRlZ29yeS5pbmNsdWRlcyhncm91cE5hbWUpIHx8IGdyb3VwTmFtZS5pbmNsdWRlcyhpc3N1ZS5jYXRlZ29yeSkKICAgICAgICAgICAgKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuY3JpdGljYWxJc3N1ZXMgPSBkYXRhLmNyaXRpY2FsX2lzc3VlcyB8fCBbXTsKICAgICAgICAgIH0KICAgICAgICAgIGNvbnNvbGUubG9nKGBMb2FkZWQgJHt0aGlzLmNyaXRpY2FsSXNzdWVzLmxlbmd0aH0gY3JpdGljYWwgaXNzdWVzIGZvciBncm91cCAke2dyb3VwTmFtZX1gKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgY3JpdGljYWwgaXNzdWVzOicsIGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgICB0aGlzLmxvYWRTYW1wbGVDcml0aWNhbElzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGNyaXRpY2FsIGlzc3VlczonLCBlcnJvcik7CiAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgIHRoaXMubG9hZFNhbXBsZUNyaXRpY2FsSXNzdWVzRm9yR3JvdXAocHFlT3duZXIsIGdyb3VwTmFtZSk7CiAgICAgIH0KICAgIH0sCgogICAgbG9hZFNhbXBsZUNyaXRpY2FsSXNzdWVzRm9yR3JvdXAocHFlT3duZXIsIGdyb3VwTmFtZSkgewogICAgICAvLyBHZXQgYWxsIHNhbXBsZSBpc3N1ZXMgZm9yIHRoaXMgUFFFIG93bmVyCiAgICAgIGNvbnN0IGFsbFNhbXBsZUlzc3VlcyA9IHRoaXMuZ2V0U2FtcGxlSXNzdWVzRm9yUFFFKHBxZU93bmVyKTsKCiAgICAgIC8vIEZpbHRlciB0byBvbmx5IGluY2x1ZGUgaXNzdWVzIGZvciB0aGlzIGdyb3VwCiAgICAgIHRoaXMuY3JpdGljYWxJc3N1ZXMgPSBhbGxTYW1wbGVJc3N1ZXMuZmlsdGVyKGlzc3VlID0+CiAgICAgICAgaXNzdWUuY2F0ZWdvcnkuaW5jbHVkZXMoZ3JvdXBOYW1lKSB8fCBncm91cE5hbWUuaW5jbHVkZXMoaXNzdWUuY2F0ZWdvcnkpCiAgICAgICk7CiAgICB9LAoKICAgIGFzeW5jIGxvYWRPdXRzdGFuZGluZ0lzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpIHsKICAgICAgY29uc29sZS5sb2coYExvYWRpbmcgb3V0c3RhbmRpbmcgaXNzdWVzIGZvciBQUUUgb3duZXI6ICR7cHFlT3duZXJ9LCBncm91cDogJHtncm91cE5hbWV9YCk7CgogICAgICB0cnkgewogICAgICAgIC8vIEdldCBhdXRoZW50aWNhdGlvbiBjb25maWcKICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldEF1dGhDb25maWcoKTsKCiAgICAgICAgLy8gRmV0Y2ggb3V0c3RhbmRpbmcgaXNzdWVzIGZyb20gdGhlIEFQSQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfcHFlX291dHN0YW5kaW5nX2lzc3VlcycsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi5jb25maWcuaGVhZGVycwogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgcHFlT3duZXI6IHBxZU93bmVyLAogICAgICAgICAgICBncm91cE5hbWU6IGdyb3VwTmFtZQogICAgICAgICAgfSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggb3V0c3RhbmRpbmcgaXNzdWVzOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsKCiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICAvLyBGaWx0ZXIgaXNzdWVzIHRvIG9ubHkgaW5jbHVkZSB0aG9zZSBmb3IgdGhpcyBncm91cAogICAgICAgICAgY29uc3QgYWxsSXNzdWVzID0gZGF0YS5vdXRzdGFuZGluZ19pc3N1ZXMgfHwgW107CiAgICAgICAgICB0aGlzLm91dHN0YW5kaW5nSXNzdWVzID0gYWxsSXNzdWVzLmZpbHRlcihpc3N1ZSA9PgogICAgICAgICAgICBpc3N1ZS5jYXRlZ29yeS5pbmNsdWRlcyhncm91cE5hbWUpIHx8IGdyb3VwTmFtZS5pbmNsdWRlcyhpc3N1ZS5jYXRlZ29yeSkKICAgICAgICAgICk7CiAgICAgICAgICBjb25zb2xlLmxvZyhgTG9hZGVkICR7dGhpcy5vdXRzdGFuZGluZ0lzc3Vlcy5sZW5ndGh9IG91dHN0YW5kaW5nIGlzc3VlcyBmb3IgZ3JvdXAgJHtncm91cE5hbWV9YCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG91dHN0YW5kaW5nIGlzc3VlczonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgICAgdGhpcy5sb2FkU2FtcGxlT3V0c3RhbmRpbmdJc3N1ZXNGb3JHcm91cChwcWVPd25lciwgZ3JvdXBOYW1lKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBvdXRzdGFuZGluZyBpc3N1ZXM6JywgZXJyb3IpOwogICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICB0aGlzLmxvYWRTYW1wbGVPdXRzdGFuZGluZ0lzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVPdXRzdGFuZGluZ0lzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpIHsKICAgICAgLy8gTG9hZCBhbGwgc2FtcGxlIG91dHN0YW5kaW5nIGlzc3VlcyBmb3IgdGhpcyBQUUUgb3duZXIKICAgICAgdGhpcy5sb2FkU2FtcGxlT3V0c3RhbmRpbmdJc3N1ZXMocHFlT3duZXIpOwoKICAgICAgLy8gRmlsdGVyIHRvIG9ubHkgaW5jbHVkZSBpc3N1ZXMgZm9yIHRoaXMgZ3JvdXAKICAgICAgdGhpcy5vdXRzdGFuZGluZ0lzc3VlcyA9IHRoaXMub3V0c3RhbmRpbmdJc3N1ZXMuZmlsdGVyKGlzc3VlID0+CiAgICAgICAgaXNzdWUuY2F0ZWdvcnkuaW5jbHVkZXMoZ3JvdXBOYW1lKSB8fCBncm91cE5hbWUuaW5jbHVkZXMoaXNzdWUuY2F0ZWdvcnkpCiAgICAgICk7CiAgICB9LAoKICAgIGFzeW5jIGxvYWRSZXNvbHZlZElzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpIHsKICAgICAgY29uc29sZS5sb2coYExvYWRpbmcgcmVzb2x2ZWQgaXNzdWVzIGZvciBQUUUgb3duZXI6ICR7cHFlT3duZXJ9LCBncm91cDogJHtncm91cE5hbWV9YCk7CgogICAgICB0cnkgewogICAgICAgIC8vIEdldCBhdXRoZW50aWNhdGlvbiBjb25maWcKICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldEF1dGhDb25maWcoKTsKCiAgICAgICAgLy8gRmV0Y2ggcmVzb2x2ZWQgaXNzdWVzIGZyb20gdGhlIEFQSQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfcHFlX3Jlc29sdmVkX2lzc3VlcycsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi5jb25maWcuaGVhZGVycwogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgcHFlT3duZXI6IHBxZU93bmVyLAogICAgICAgICAgICBncm91cE5hbWU6IGdyb3VwTmFtZQogICAgICAgICAgfSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggcmVzb2x2ZWQgaXNzdWVzOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsKCiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICAvLyBGaWx0ZXIgaXNzdWVzIHRvIG9ubHkgaW5jbHVkZSB0aG9zZSBmb3IgdGhpcyBncm91cAogICAgICAgICAgY29uc3QgYWxsSXNzdWVzID0gZGF0YS5yZXNvbHZlZF9pc3N1ZXMgfHwgW107CiAgICAgICAgICB0aGlzLnJlc29sdmVkSXNzdWVzID0gYWxsSXNzdWVzLmZpbHRlcihpc3N1ZSA9PgogICAgICAgICAgICBpc3N1ZS5jYXRlZ29yeS5pbmNsdWRlcyhncm91cE5hbWUpIHx8IGdyb3VwTmFtZS5pbmNsdWRlcyhpc3N1ZS5jYXRlZ29yeSkKICAgICAgICAgICk7CiAgICAgICAgICBjb25zb2xlLmxvZyhgTG9hZGVkICR7dGhpcy5yZXNvbHZlZElzc3Vlcy5sZW5ndGh9IHJlc29sdmVkIGlzc3VlcyBmb3IgZ3JvdXAgJHtncm91cE5hbWV9YCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHJlc29sdmVkIGlzc3VlczonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgICAgdGhpcy5sb2FkU2FtcGxlUmVzb2x2ZWRJc3N1ZXNGb3JHcm91cChwcWVPd25lciwgZ3JvdXBOYW1lKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyByZXNvbHZlZCBpc3N1ZXM6JywgZXJyb3IpOwogICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICB0aGlzLmxvYWRTYW1wbGVSZXNvbHZlZElzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpOwogICAgICB9CiAgICB9LAoKICAgIGxvYWRTYW1wbGVSZXNvbHZlZElzc3Vlc0Zvckdyb3VwKHBxZU93bmVyLCBncm91cE5hbWUpIHsKICAgICAgLy8gTG9hZCBhbGwgc2FtcGxlIHJlc29sdmVkIGlzc3VlcyBmb3IgdGhpcyBQUUUgb3duZXIKICAgICAgdGhpcy5sb2FkU2FtcGxlUmVzb2x2ZWRJc3N1ZXMocHFlT3duZXIpOwoKICAgICAgLy8gRmlsdGVyIHRvIG9ubHkgaW5jbHVkZSBpc3N1ZXMgZm9yIHRoaXMgZ3JvdXAKICAgICAgdGhpcy5yZXNvbHZlZElzc3VlcyA9IHRoaXMucmVzb2x2ZWRJc3N1ZXMuZmlsdGVyKGlzc3VlID0+CiAgICAgICAgaXNzdWUuY2F0ZWdvcnkuaW5jbHVkZXMoZ3JvdXBOYW1lKSB8fCBncm91cE5hbWUuaW5jbHVkZXMoaXNzdWUuY2F0ZWdvcnkpCiAgICAgICk7CiAgICB9LAoKICAgIGdldFNhbXBsZUlzc3Vlc0ZvclBRRShwcWVPd25lcikgewogICAgICAvLyBTYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQgYmFzZWQgb24gUFFFIG93bmVyCiAgICAgIGlmIChwcWVPd25lciA9PT0gJ0FsYmVydCBHLicpIHsKICAgICAgICByZXR1cm4gWwogICAgICAgICAgewogICAgICAgICAgICBpZDogJ2NpMScsCiAgICAgICAgICAgIGNhdGVnb3J5OiAnRmFuIFRoZW1pcycsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNicsCiAgICAgICAgICAgIHNldmVyaXR5OiAnaGlnaCcsCiAgICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzMuMicsCiAgICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdGYW4gVGhlbWlzIHNob3dpbmcgMy4yeCBzcGlrZSBpbiBKdW5lIDIwMjQgd2l0aCAxMiBmYWlscyB2cyA0IGV4cGVjdGVkLiBSb290IGNhdXNlIGFwcGVhcnMgdG8gYmUgYWlyZmxvdyBpc3N1ZXMgaW4gdGhlIG5ldyBkZXNpZ24uJywKICAgICAgICAgICAgY29tbWVudDogJycsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1Jvb3QgQ2F1c2UnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBpZDogJ2NpMicsCiAgICAgICAgICAgIGNhdGVnb3J5OiAnVmljdG9yaWEgQ3J5cHRvJywKICAgICAgICAgICAgbW9udGg6ICcyMDI0LTA2JywKICAgICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLAogICAgICAgICAgICBpbmNyZWFzZU11bHRpcGxpZXI6ICcxLjgnLAogICAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnVmljdG9yaWEgQ3J5cHRvIGhhcyBzdXN0YWluZWQgcHJvYmxlbSB3aXRoIDEuOHggdGFyZ2V0IHJhdGUgZm9yIDMgY29uc2VjdXRpdmUgbW9udGhzLiBDb25zaXN0ZW50IHBhdHRlcm4gb2YgcG93ZXIgZGVsaXZlcnkgaXNzdWVzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdSb290IENhdXNlJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdjaTMnLAogICAgICAgICAgICBjYXRlZ29yeTogJ0ZhbiBUaGVtaXMnLAogICAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgICBzZXZlcml0eTogJ2hpZ2gnLAogICAgICAgICAgICBpbmNyZWFzZU11bHRpcGxpZXI6ICcyLjUnLAogICAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnRmFuIFRoZW1pcyBzaG93aW5nIGhpZ2hlciBmYWlsdXJlIHJhdGVzIGZvciB1bml0cyBtYW51ZmFjdHVyZWQgaW4gTWFyY2ggMjAyNC4gVmludGFnZSBhbmFseXNpcyBpbmRpY2F0ZXMgYSAyLjV4IGluY3JlYXNlIGluIGZhaWx1cmVzIGZvciB0aGlzIG1hbnVmYWN0dXJpbmcgcGVyaW9kLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdWaW50YWdlJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdjaTcnLAogICAgICAgICAgICBjYXRlZ29yeTogJ0ZhbiBUaGVtaXMnLAogICAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgICBzZXZlcml0eTogJ21lZGl1bScsCiAgICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzEuNycsCiAgICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdGYW4gVGhlbWlzIHVuaXRzIGZyb20gU3VwcGxpZXIgWFlaIHNob3dpbmcgaGlnaGVyIGZhaWx1cmUgcmF0ZXMuIFF1YWxpdHkgYXVkaXQgcmVjb21tZW5kZWQuJywKICAgICAgICAgICAgY29tbWVudDogJycsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1N1cHBsaWVyJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdjaTgnLAogICAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNicsCiAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS42JywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ1ZpY3RvcmlhIENyeXB0byB1bml0cyBpbiBOb3J0aCBzZWN0b3Igc2hvd2luZyBoaWdoZXIgZmFpbHVyZSByYXRlcy4gU2l0ZS1zcGVjaWZpYyBpc3N1ZSBzdXNwZWN0ZWQuJywKICAgICAgICAgICAgY29tbWVudDogJycsCiAgICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1NlY3RvcicKICAgICAgICAgIH0KICAgICAgICBdOwogICAgICB9CiAgICAgIGVsc2UgaWYgKHBxZU93bmVyID09PSAnU2FyYWggTC4nKSB7CiAgICAgICAgcmV0dXJuIFsKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdjaTQnLAogICAgICAgICAgICBjYXRlZ29yeTogJ1N0ZWxsYXIgQ29yZScsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNicsCiAgICAgICAgICAgIHNldmVyaXR5OiAnaGlnaCcsCiAgICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzIuOCcsCiAgICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdTdGVsbGFyIENvcmUgc2hvd2luZyAyLjh4IHNwaWtlIGluIEp1bmUgMjAyNC4gUm9vdCBjYXVzZSBhcHBlYXJzIHRvIGJlIHJlbGF0ZWQgdG8gcG93ZXIgZGVsaXZlcnkgaXNzdWVzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdSb290IENhdXNlJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdjaTUnLAogICAgICAgICAgICBjYXRlZ29yeTogJ05lYnVsYSBEcml2ZScsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNicsCiAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS43JywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ05lYnVsYSBEcml2ZSBoYXMgc3VzdGFpbmVkIHByb2JsZW0gd2l0aCAxLjd4IHRhcmdldCByYXRlIGZvciAyIGNvbnNlY3V0aXZlIG1vbnRocy4gQ29uc2lzdGVudCBwYXR0ZXJuIG9mIHRoZXJtYWwgaXNzdWVzLicsCiAgICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgICBhbmFseXNpc1R5cGU6ICdTdXBwbGllcicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAnY2k5JywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdTdGVsbGFyIENvcmUnLAogICAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgICBzZXZlcml0eTogJ21lZGl1bScsCiAgICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzEuOScsCiAgICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdTdGVsbGFyIENvcmUgdW5pdHMgbWFudWZhY3R1cmVkIGluIEFwcmlsIDIwMjQgc2hvd2luZyBoaWdoZXIgZmFpbHVyZSByYXRlcy4nLAogICAgICAgICAgICBjb21tZW50OiAnJywKICAgICAgICAgICAgYW5hbHlzaXNUeXBlOiAnVmludGFnZScKICAgICAgICAgIH0KICAgICAgICBdOwogICAgICB9CiAgICAgIGVsc2UgewogICAgICAgIHJldHVybiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAnY2k2JywKICAgICAgICAgICAgY2F0ZWdvcnk6ICdHZW5lcmljIENvbXBvbmVudCcsCiAgICAgICAgICAgIG1vbnRoOiAnMjAyNC0wNicsCiAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJywKICAgICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS42JywKICAgICAgICAgICAgYWlEZXNjcmlwdGlvbjogJ0dlbmVyaWMgY29tcG9uZW50IHNob3dpbmcgMS42eCB0YXJnZXQgcmF0ZS4gRnVydGhlciBpbnZlc3RpZ2F0aW9uIG5lZWRlZC4nLAogICAgICAgICAgICBjb21tZW50OiAnJywKICAgICAgICAgICAgYW5hbHlzaXNUeXBlOiAnU2VjdG9yJwogICAgICAgICAgfQogICAgICAgIF07CiAgICAgIH0KICAgIH0sCgogICAgZ2V0QW5hbHlzaXNUeXBlVGFnS2luZChhbmFseXNpc1R5cGUpIHsKICAgICAgLy8gUmV0dXJuIGRpZmZlcmVudCB0YWcgY29sb3JzIGJhc2VkIG9uIGFuYWx5c2lzIHR5cGUKICAgICAgc3dpdGNoIChhbmFseXNpc1R5cGUpIHsKICAgICAgICBjYXNlICdSb290IENhdXNlJzoKICAgICAgICAgIHJldHVybiAncHVycGxlJzsKICAgICAgICBjYXNlICdWaW50YWdlJzoKICAgICAgICAgIHJldHVybiAndGVhbCc7CiAgICAgICAgY2FzZSAnU2VjdG9yJzoKICAgICAgICAgIHJldHVybiAnYmx1ZSc7CiAgICAgICAgY2FzZSAnU3VwcGxpZXInOgogICAgICAgICAgcmV0dXJuICdncmVlbic7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAnZ3JheSc7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0QXV0aENvbmZpZygpIHsKICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIHRva2VuIGZyb20gbG9jYWxTdG9yYWdlIG9yIFZ1ZXggc3RvcmUKICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fCB0aGlzLiRzdG9yZS5zdGF0ZS50b2tlbjsKCiAgICAgIHJldHVybiB7CiAgICAgICAgaGVhZGVyczogewogICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YAogICAgICAgIH0KICAgICAgfTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["QEDashboard.vue"], "names": [], "mappings": ";AA0d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file": "QEDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"qe-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section -->\n      <div class=\"dashboard-header\">\n        <h3>QE Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- PQE Overview Section -->\n      <div class=\"section-card\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">PQE Owner Overview</h4>\n            <div class=\"section-subtitle\">Prioritized by critical issues count</div>\n          </div>\n        </div>\n\n        <div v-if=\"isLoading\" class=\"loading-container\">\n          <cv-inline-loading\n            status=\"active\"\n            loading-text=\"Loading PQE data...\"\n          ></cv-inline-loading>\n        </div>\n\n        <div v-else-if=\"pqeOwners.length > 0\" class=\"pqe-owners-grid\">\n          <div\n            v-for=\"pqe in sortedPQEOwners\"\n            :key=\"pqe.name\"\n            class=\"pqe-owner-card\"\n            :class=\"getPriorityClass(pqe)\"\n          >\n            <div class=\"pqe-owner-header\">\n              <h5 class=\"pqe-owner-name\">{{ pqe.name }}</h5>\n              <div class=\"pqe-owner-metrics\">\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Critical</div>\n                  <div class=\"metric-value\" :class=\"getCriticalIssuesClass(pqe.criticalIssues)\">\n                    {{ pqe.criticalIssues }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Outstanding</div>\n                  <div class=\"metric-value\" :class=\"getOutstandingClass(pqe.outstandingIssuesCount)\">\n                    {{ pqe.outstandingIssuesCount }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Resolved</div>\n                  <div class=\"metric-value\" :class=\"getResolvedClass(pqe.resolvedIssuesCount)\">\n                    {{ pqe.resolvedIssuesCount }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Pending</div>\n                  <div class=\"metric-value\" :class=\"getPendingClass(pqe.pendingActionTrackerCount)\">\n                    {{ pqe.pendingActionTrackerCount }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"pqe-owner-details\">\n              <div class=\"groups-section\">\n                <div class=\"groups-header\">\n                  <div class=\"groups-title\">Critical Breakout Groups</div>\n                  <div class=\"groups-count\">{{ pqe.criticalGroups.length }}</div>\n                </div>\n                <div class=\"groups-list\">\n                  <div\n                    v-for=\"group in pqe.criticalGroups.slice(0, 3)\"\n                    :key=\"group.name\"\n                    class=\"group-item\"\n                    :class=\"getGroupHighlightClass(group.xFactor)\"\n                    @click.stop=\"viewGroupCriticalIssues(pqe, group.name)\"\n                  >\n                    <div class=\"group-name\">{{ group.name }}</div>\n                    <div class=\"group-metrics\">\n                      <span class=\"group-metric\" title=\"Outstanding Issues\">\n                        <span class=\"metric-icon\">O:</span>{{ group.outstandingIssuesCount }}\n                      </span>\n                      <span class=\"group-metric\" title=\"Resolved Issues\">\n                        <span class=\"metric-icon\">R:</span>{{ group.resolvedIssuesCount }}\n                      </span>\n                      <span class=\"group-xfactor\" :class=\"getXFactorClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}x\n                      </span>\n                    </div>\n                  </div>\n                  <div v-if=\"pqe.criticalGroups.length > 3\" class=\"more-groups\">\n                    + {{ pqe.criticalGroups.length - 3 }} more\n                  </div>\n                </div>\n              </div>\n\n              <!-- Removed collective X-Factor as per requirements -->\n            </div>\n\n            <div class=\"pqe-owner-footer\">\n              <div class=\"button-group\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  class=\"issues-button\"\n                  @click.stop=\"viewCriticalIssues(pqe)\"\n                  :disabled=\"pqe.criticalIssues === 0 && pqe.outstandingIssuesCount === 0 && pqe.resolvedIssuesCount === 0\"\n                >\n                  Show Issues\n                </cv-button>\n                <cv-button\n                  kind=\"ghost\"\n                  size=\"small\"\n                  class=\"view-button\"\n                  @click.stop=\"viewPQEDashboard(pqe.name)\"\n                  :disabled=\"pqe.pendingActionTrackerCount === 0\"\n                >\n                  Pending Action Tracker Updates ({{ pqe.pendingActionTrackerCount }})\n                </cv-button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else class=\"no-data-message\">\n          No PQE owners found. Please check the breakout_targets Excel file.\n        </div>\n      </div>\n\n      <!-- Summary Statistics Section -->\n      <div class=\"summary-section\">\n        <div class=\"summary-card\">\n          <div class=\"summary-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalCriticalIssues }}</div>\n            <div class=\"summary-label\">Total Critical Issues</div>\n          </div>\n        </div>\n\n        <div class=\"summary-card\">\n          <div class=\"summary-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalUnvalidated }}</div>\n            <div class=\"summary-label\">Unvalidated Fails</div>\n          </div>\n        </div>\n\n        <div class=\"summary-card\">\n          <div class=\"summary-icon critical-groups\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalCriticalGroups }}</div>\n            <div class=\"summary-label\">Critical Breakout Groups</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Issues Modal -->\n    <cv-modal\n      :visible=\"showCriticalIssuesModal\"\n      @modal-hidden=\"closeCriticalIssuesModal\"\n      class=\"issues-modal\"\n      :size=\"'lg'\"\n    >\n      <template slot=\"title\">\n        <span v-if=\"selectedPQE && selectedGroupName\">\n          Issues for {{ selectedGroupName }} ({{ selectedPQE.name }})\n        </span>\n        <span v-else-if=\"selectedPQE\">\n          Issues for {{ selectedPQE.name }}\n        </span>\n        <span v-else>\n          Issues\n        </span>\n      </template>\n      <template slot=\"content\">\n        <div v-if=\"selectedPQE\" class=\"modal-content\">\n          <!-- Issue Type Tabs -->\n          <div class=\"issue-type-tabs\">\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'critical' }\"\n              @click=\"activeIssueTab = 'critical'\"\n            >\n              Critical ({{ criticalIssues.length }})\n            </cv-button>\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'outstanding' }\"\n              @click=\"activeIssueTab = 'outstanding'\"\n            >\n              Outstanding ({{ outstandingIssues.length }})\n            </cv-button>\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'resolved' }\"\n              @click=\"activeIssueTab = 'resolved'\"\n            >\n              Resolved ({{ resolvedIssues.length }})\n            </cv-button>\n          </div>\n\n          <!-- Critical Issues -->\n          <div v-if=\"activeIssueTab === 'critical'\" class=\"issues-list\">\n            <div v-if=\"criticalIssues.length === 0\" class=\"no-issues-message\">\n              No critical issues found.\n            </div>\n            <div\n              v-for=\"issue in criticalIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card\"\n              @click=\"toggleIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                    :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                    {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues -->\n          <div v-if=\"activeIssueTab === 'outstanding'\" class=\"issues-list\">\n            <div v-if=\"outstandingIssues.length === 0\" class=\"no-issues-message\">\n              No outstanding issues found.\n            </div>\n            <div\n              v-for=\"issue in outstandingIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card outstanding-issue-card\"\n              @click=\"toggleOutstandingIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    kind=\"blue\"\n                    label=\"Outstanding\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-severity' : 'low-severity'\">\n                    {{ issue.currentPerformance }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                <div class=\"acceptance-details\">\n                  <div class=\"acceptance-date\">\n                    <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                  </div>\n                  <div class=\"accepted-by\">\n                    <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                  </div>\n                </div>\n\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues -->\n          <div v-if=\"activeIssueTab === 'resolved'\" class=\"issues-list\">\n            <div v-if=\"resolvedIssues.length === 0\" class=\"no-issues-message\">\n              No resolved issues found.\n            </div>\n            <div\n              v-for=\"issue in resolvedIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card resolved-issue-card\"\n              @click=\"toggleResolvedIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    kind=\"green\"\n                    label=\"Resolved\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-severity'\">\n                    Current: {{ issue.currentPerformance }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                <div class=\"resolution-details\">\n                  <div class=\"resolution-date\">\n                    <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                  </div>\n                  <div class=\"original-severity\">\n                    <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                    ({{ issue.increaseMultiplier }}x)\n                  </div>\n                </div>\n\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else class=\"loading-container\">\n          <cv-inline-loading\n            status=\"active\"\n            loading-text=\"Loading issues...\"\n          ></cv-inline-loading>\n        </div>\n      </template>\n      <template slot=\"footer\">\n        <cv-button\n          kind=\"secondary\"\n          @click=\"closeCriticalIssuesModal\"\n        >\n          Close\n        </cv-button>\n      </template>\n    </cv-modal>\n  </div>\n</template>\n\n<script>\nimport { CvButton, CvInlineLoading, CvModal, CvTag, CvTextArea } from '@carbon/vue';\n\nexport default {\n  name: 'QEDashboard',\n  components: {\n    CvButton,\n    CvInlineLoading,\n    CvModal,\n    CvTag,\n    CvTextArea\n  },\n  data() {\n    return {\n      isLoading: true,\n      pqeOwners: [],\n      showCriticalIssuesModal: false,\n      selectedPQE: null,\n      selectedGroupName: null,\n      criticalIssues: [],\n      outstandingIssues: [],\n      resolvedIssues: [],\n      expandedIssueIds: [],\n      expandedOutstandingIssueIds: [],\n      expandedResolvedIssueIds: [],\n      activeIssueTab: 'critical'\n    };\n  },\n  computed: {\n    // Sort PQE owners by critical issues count (highest first)\n    sortedPQEOwners() {\n      return [...this.pqeOwners].sort((a, b) => b.criticalIssues - a.criticalIssues);\n    },\n    // Calculate total critical issues across all PQEs\n    totalCriticalIssues() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalIssues, 0);\n    },\n    // Calculate total unvalidated fails across all PQEs\n    totalUnvalidated() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.unvalidatedCount, 0);\n    },\n    // Calculate total critical breakout groups across all PQEs\n    totalCriticalGroups() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalGroups.length, 0);\n    }\n  },\n  mounted() {\n    this.loadPQEOwnersData();\n  },\n  methods: {\n    async loadPQEOwnersData() {\n      this.isLoading = true;\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch PQE owners data from the API\n        const response = await fetch('/api-statit2/get_all_pqe_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n          console.log(`Loaded data for ${this.pqeOwners.length} PQE owners`);\n        } else {\n          console.error('Failed to load PQE data:', data.message);\n          // Use sample data for development\n          this.loadSampleData();\n        }\n      } catch (error) {\n        console.error('Error loading PQE data:', error);\n        // Use sample data for development\n        this.loadSampleData();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleData() {\n      // Sample data for development\n      this.pqeOwners = [\n        {\n          name: 'Albert G.',\n          criticalIssues: 4,\n          unvalidatedCount: 12,\n          collectiveXFactor: 3.2,\n          criticalGroups: [\n            { name: 'Fan Themis', xFactor: 3.2 },\n            { name: 'Victoria Crypto', xFactor: 1.8 },\n            { name: 'Quantum Nexus', xFactor: 1.6 }\n          ]\n        },\n        {\n          name: 'Sarah L.',\n          criticalIssues: 2,\n          unvalidatedCount: 8,\n          collectiveXFactor: 2.5,\n          criticalGroups: [\n            { name: 'Stellar Core', xFactor: 2.5 },\n            { name: 'Nebula Drive', xFactor: 1.7 }\n          ]\n        },\n        {\n          name: 'Michael T.',\n          criticalIssues: 1,\n          unvalidatedCount: 5,\n          collectiveXFactor: 1.9,\n          criticalGroups: [\n            { name: 'Pulsar Matrix', xFactor: 1.9 }\n          ]\n        },\n        {\n          name: 'Jennifer K.',\n          criticalIssues: 0,\n          unvalidatedCount: 3,\n          collectiveXFactor: 0.9,\n          criticalGroups: []\n        }\n      ];\n    },\n\n    viewPQEDashboard(pqeName) {\n      // Emit event to select the PQE owner in the parent component\n      this.$emit('select-pqe', pqeName);\n    },\n\n    viewCriticalIssues(pqe) {\n      this.selectedPQE = pqe;\n      this.selectedGroupName = null; // Reset group name when viewing all critical issues\n      this.activeIssueTab = 'critical'; // Set active tab to critical issues\n      this.loadAllIssues(pqe.name);\n      this.showCriticalIssuesModal = true;\n    },\n\n    closeCriticalIssuesModal() {\n      this.showCriticalIssuesModal = false;\n      this.selectedPQE = null;\n      this.selectedGroupName = null;\n      this.criticalIssues = [];\n      this.outstandingIssues = [];\n      this.resolvedIssues = [];\n      this.expandedIssueIds = [];\n      this.expandedOutstandingIssueIds = [];\n      this.expandedResolvedIssueIds = [];\n    },\n\n    async loadAllIssues(pqeOwner) {\n      console.log(`Loading all issues for PQE owner: ${pqeOwner}`);\n\n      // Load all issue types in parallel\n      await Promise.all([\n        this.loadCriticalIssues(pqeOwner),\n        this.loadOutstandingIssues(pqeOwner),\n        this.loadResolvedIssues(pqeOwner)\n      ]);\n    },\n\n    async loadCriticalIssues(pqeOwner) {\n      console.log(`Loading critical issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.criticalIssues = data.critical_issues || [];\n          console.log(`Loaded ${this.criticalIssues.length} critical issues`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues(pqeOwner);\n      }\n    },\n\n    loadSampleCriticalIssues(pqeOwner) {\n      // Use our helper method to get sample issues for this PQE owner\n      this.criticalIssues = this.getSampleIssuesForPQE(pqeOwner);\n    },\n\n    async loadOutstandingIssues(pqeOwner) {\n      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.outstandingIssues = data.outstanding_issues || [];\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues(pqeOwner);\n      }\n    },\n\n    loadSampleOutstandingIssues(pqeOwner) {\n      // Sample data for development\n      if (pqeOwner === 'Albert G.') {\n        this.outstandingIssues = [\n          {\n            id: 'oi1',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.4',\n            aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n            comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n            analysisType: 'Root Cause',\n            acceptanceDate: '2024-04-10',\n            currentPerformance: '1.3x',\n            acceptedBy: 'Engineering Team'\n          },\n          {\n            id: 'oi2',\n            category: 'Victoria Crypto',\n            month: '2024-05',\n            severity: 'medium',\n            increaseMultiplier: '1.3',\n            aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known supplier issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n            comment: 'Supplier variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n            analysisType: 'Supplier',\n            acceptanceDate: '2024-03-22',\n            currentPerformance: '1.2x',\n            acceptedBy: 'Quality Team'\n          }\n        ];\n      } else if (pqeOwner === 'Sarah L.') {\n        this.outstandingIssues = [\n          {\n            id: 'oi3',\n            category: 'Stellar Core',\n            month: '2024-05',\n            severity: 'medium',\n            increaseMultiplier: '1.3',\n            aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n            comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n            analysisType: 'Root Cause',\n            acceptanceDate: '2024-04-05',\n            currentPerformance: '1.2x',\n            acceptedBy: 'Engineering Team'\n          }\n        ];\n      } else {\n        this.outstandingIssues = [];\n      }\n    },\n\n    async loadResolvedIssues(pqeOwner) {\n      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.resolvedIssues = data.resolved_issues || [];\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues(pqeOwner);\n      }\n    },\n\n    loadSampleResolvedIssues(pqeOwner) {\n      // Sample data for development\n      if (pqeOwner === 'Albert G.') {\n        this.resolvedIssues = [\n          {\n            id: 'ri1',\n            category: 'Fan Themis',\n            month: '2024-05',\n            severity: 'high',\n            increaseMultiplier: '2.8',\n            aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n            comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n            analysisType: 'Root Cause',\n            resolutionDate: '2024-05-15',\n            currentPerformance: '0.9x'\n          },\n          {\n            id: 'ri2',\n            category: 'Victoria Crypto',\n            month: '2024-04',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific supplier batch. Supplier corrected their process.',\n            comment: 'Worked with supplier to improve their quality control. Monitoring new batches closely.',\n            analysisType: 'Supplier',\n            resolutionDate: '2024-04-28',\n            currentPerformance: '0.8x'\n          }\n        ];\n      } else if (pqeOwner === 'Sarah L.') {\n        this.resolvedIssues = [\n          {\n            id: 'ri3',\n            category: 'Stellar Core',\n            month: '2024-05',\n            severity: 'high',\n            increaseMultiplier: '2.5',\n            aiDescription: 'Stellar Core showed 2.5x spike in May 2024. Root cause was identified as a thermal issue in the power delivery subsystem. Issue was resolved with a design change.',\n            comment: 'Implemented design change to improve thermal performance. Monitoring field data.',\n            analysisType: 'Root Cause',\n            resolutionDate: '2024-05-22',\n            currentPerformance: '0.8x'\n          }\n        ];\n      } else {\n        this.resolvedIssues = [];\n      }\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    async updateIssue(issue) {\n      console.log('Update issue:', issue);\n\n      if (!this.selectedPQE) {\n        console.error('No PQE owner selected');\n        return;\n      }\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Call the API to update the action tracker\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment,\n            severity: issue.severity,\n            pqeOwner: this.selectedPQE.name,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          alert(`Action tracker updated for issue: ${issue.category}`);\n        } else {\n          console.error('Failed to update action tracker:', data.message);\n          alert('Failed to update action tracker. Please try again.');\n        }\n      } catch (error) {\n        console.error('Error updating action tracker:', error);\n        alert('Error updating action tracker. Please try again.');\n      }\n    },\n\n    getPriorityClass(pqe) {\n      if (pqe.collectiveXFactor >= 3.0) return 'high-priority';\n      if (pqe.collectiveXFactor >= 1.5) return 'medium-priority';\n      return 'normal-priority';\n    },\n\n    getCriticalIssuesClass(count) {\n      if (count >= 3) return 'high-value';\n      if (count >= 1) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getUnvalidatedClass(count) {\n      if (count >= 10) return 'high-value';\n      if (count >= 5) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getOutstandingClass(count) {\n      if (count >= 4) return 'high-value';\n      if (count >= 2) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getResolvedClass(count) {\n      if (count >= 5) return 'high-value';\n      if (count >= 3) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getPendingClass(count) {\n      if (count >= 3) return 'high-value';\n      if (count >= 1) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getXFactorClass(xFactor) {\n      if (xFactor >= 3.0) return 'high-value';\n      if (xFactor >= 1.5) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getGroupHighlightClass(xFactor) {\n      if (xFactor >= 3.0) return 'high-highlight';\n      if (xFactor >= 1.5) return 'medium-highlight';\n      return 'low-highlight';\n    },\n\n    viewGroupCriticalIssues(pqe, groupName) {\n      this.selectedPQE = pqe;\n      this.selectedGroupName = groupName;\n      this.activeIssueTab = 'critical'; // Set active tab to critical issues\n      this.loadAllIssuesForGroup(pqe.name, groupName);\n      this.showCriticalIssuesModal = true;\n    },\n\n    async loadAllIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading all issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      // Load all issue types in parallel\n      await Promise.all([\n        this.loadCriticalIssuesForGroup(pqeOwner, groupName),\n        this.loadOutstandingIssuesForGroup(pqeOwner, groupName),\n        this.loadResolvedIssuesForGroup(pqeOwner, groupName)\n      ]);\n    },\n\n    async loadCriticalIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading critical issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // If we have a specific group name, filter issues to only include those for this group\n          if (groupName) {\n            const allIssues = data.critical_issues || [];\n            this.criticalIssues = allIssues.filter(issue =>\n              issue.category.includes(groupName) || groupName.includes(issue.category)\n            );\n          } else {\n            this.criticalIssues = data.critical_issues || [];\n          }\n          console.log(`Loaded ${this.criticalIssues.length} critical issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleCriticalIssuesForGroup(pqeOwner, groupName) {\n      // Get all sample issues for this PQE owner\n      const allSampleIssues = this.getSampleIssuesForPQE(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.criticalIssues = allSampleIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    async loadOutstandingIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter issues to only include those for this group\n          const allIssues = data.outstanding_issues || [];\n          this.outstandingIssues = allIssues.filter(issue =>\n            issue.category.includes(groupName) || groupName.includes(issue.category)\n          );\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleOutstandingIssuesForGroup(pqeOwner, groupName) {\n      // Load all sample outstanding issues for this PQE owner\n      this.loadSampleOutstandingIssues(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.outstandingIssues = this.outstandingIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    async loadResolvedIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter issues to only include those for this group\n          const allIssues = data.resolved_issues || [];\n          this.resolvedIssues = allIssues.filter(issue =>\n            issue.category.includes(groupName) || groupName.includes(issue.category)\n          );\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleResolvedIssuesForGroup(pqeOwner, groupName) {\n      // Load all sample resolved issues for this PQE owner\n      this.loadSampleResolvedIssues(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.resolvedIssues = this.resolvedIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    getSampleIssuesForPQE(pqeOwner) {\n      // Sample data for development based on PQE owner\n      if (pqeOwner === 'Albert G.') {\n        return [\n          {\n            id: 'ci1',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '3.2',\n            aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci2',\n            category: 'Victoria Crypto',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.8',\n            aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci3',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '2.5',\n            aiDescription: 'Fan Themis showing higher failure rates for units manufactured in March 2024. Vintage analysis indicates a 2.5x increase in failures for this manufacturing period.',\n            comment: '',\n            analysisType: 'Vintage'\n          },\n          {\n            id: 'ci7',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Fan Themis units from Supplier XYZ showing higher failure rates. Quality audit recommended.',\n            comment: '',\n            analysisType: 'Supplier'\n          },\n          {\n            id: 'ci8',\n            category: 'Victoria Crypto',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.6',\n            aiDescription: 'Victoria Crypto units in North sector showing higher failure rates. Site-specific issue suspected.',\n            comment: '',\n            analysisType: 'Sector'\n          }\n        ];\n      }\n      else if (pqeOwner === 'Sarah L.') {\n        return [\n          {\n            id: 'ci4',\n            category: 'Stellar Core',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '2.8',\n            aiDescription: 'Stellar Core showing 2.8x spike in June 2024. Root cause appears to be related to power delivery issues.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci5',\n            category: 'Nebula Drive',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Nebula Drive has sustained problem with 1.7x target rate for 2 consecutive months. Consistent pattern of thermal issues.',\n            comment: '',\n            analysisType: 'Supplier'\n          },\n          {\n            id: 'ci9',\n            category: 'Stellar Core',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.9',\n            aiDescription: 'Stellar Core units manufactured in April 2024 showing higher failure rates.',\n            comment: '',\n            analysisType: 'Vintage'\n          }\n        ];\n      }\n      else {\n        return [\n          {\n            id: 'ci6',\n            category: 'Generic Component',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.6',\n            aiDescription: 'Generic component showing 1.6x target rate. Further investigation needed.',\n            comment: '',\n            analysisType: 'Sector'\n          }\n        ];\n      }\n    },\n\n    getAnalysisTypeTagKind(analysisType) {\n      // Return different tag colors based on analysis type\n      switch (analysisType) {\n        case 'Root Cause':\n          return 'purple';\n        case 'Vintage':\n          return 'teal';\n        case 'Sector':\n          return 'blue';\n        case 'Supplier':\n          return 'green';\n        default:\n          return 'gray';\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.qe-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n  margin-bottom: 1.5rem;\n  padding: 1.5rem;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.pqe-owners-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.pqe-owner-card {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  flex-direction: column;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n  border-left: 4px solid transparent;\n}\n\n.pqe-owner-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\n}\n\n.pqe-owner-card.high-priority {\n  border-left-color: #fa4d56;\n}\n\n.pqe-owner-card.medium-priority {\n  border-left-color: #ff832b;\n}\n\n.pqe-owner-card.normal-priority {\n  border-left-color: #24a148;\n}\n\n.pqe-owner-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.pqe-owner-name {\n  margin: 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.pqe-owner-metrics {\n  display: flex;\n  gap: 1rem;\n}\n\n.metric-item {\n  text-align: center;\n}\n\n.metric-label {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.metric-value.high-value {\n  color: #fa4d56;\n}\n\n.metric-value.medium-value {\n  color: #ff832b;\n}\n\n.metric-value.normal-value {\n  color: #24a148;\n}\n\n.pqe-owner-details {\n  flex-grow: 1;\n  margin-bottom: 1rem;\n}\n\n.groups-section {\n  margin-bottom: 1rem;\n}\n\n.groups-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.groups-title {\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.groups-count {\n  font-size: 0.75rem;\n  background-color: #393939;\n  padding: 0.125rem 0.375rem;\n  border-radius: 1rem;\n}\n\n.groups-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.group-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: #262626;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  border-left: 3px solid transparent;\n}\n\n.group-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.group-item.high-highlight {\n  border-left-color: #fa4d56;\n  background-color: rgba(250, 77, 86, 0.1);\n}\n\n.group-item.medium-highlight {\n  border-left-color: #ff832b;\n  background-color: rgba(255, 131, 43, 0.1);\n}\n\n.group-item.low-highlight {\n  border-left-color: #24a148;\n  background-color: rgba(36, 161, 72, 0.1);\n}\n\n.group-name {\n  font-size: 0.875rem;\n}\n\n.group-xfactor {\n  font-size: 0.875rem;\n  font-weight: 600;\n}\n\n.more-groups {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n  text-align: center;\n  margin-top: 0.5rem;\n}\n\n.collective-xfactor {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  background-color: #262626;\n  border-radius: 4px;\n  margin-top: 1rem;\n}\n\n.xfactor-label {\n  font-size: 0.875rem;\n}\n\n.xfactor-value {\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.pqe-owner-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.view-button, .issues-button {\n  width: 100%;\n}\n\n.summary-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.summary-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.summary-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.summary-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.summary-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.summary-icon.critical-groups {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.summary-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.summary-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.summary-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.no-data-message, .no-issues-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n/* Critical Issues Modal Styles */\n.issues-modal {\n  max-width: 800px;\n}\n\n.issue-type-tabs {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #333333;\n  padding-bottom: 1rem;\n}\n\n.tab-button {\n  min-width: 120px;\n}\n\n.tab-button.active {\n  background-color: #0f62fe;\n  color: #ffffff;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.acceptance-details, .resolution-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.low-severity {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.group-metrics {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.group-metric {\n  font-size: 0.75rem;\n  color: #c6c6c6;\n  background-color: #333333;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.metric-icon {\n  font-weight: 600;\n  margin-right: 0.125rem;\n}\n\n.modal-content {\n  padding: 1rem 0;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n</style>\n"]}]}