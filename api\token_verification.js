const logger = require('./app/utils/logger');
const jwt = require('jsonwebtoken');

async function verifyToken(req, res, next) {
    let token = req.headers['authorization'];

    if (!token) {
        // Only log the error message without including the request body
        await logger.logError('No Token provided');
        return res.status(401).send({ message: 'No token provided' });
    }

    try {
        // Make sure token is a string before calling replace
        if (typeof token !== 'string') {
            await logger.logError('Token is not a string');
            return res.status(401).send({ message: 'Invalid token format' });
        }

        token = token.replace('Bearer', '').trim();

        // Check if token is empty after trimming
        if (!token) {
            await logger.logError('Token is empty after trimming');
            return res.status(401).send({ message: 'Empty token provided' });
        }

        const decoded = jwt.verify(token, process.env.JWT_KEY);
        req.user = decoded;
        console.log('Authenticated request from username:' + decoded.username)
        await logger.logInfo('Authenticated request from username:' + decoded.username);
        next();
    } catch (error) {
        // Only log the error message without including the request body
        const errorMessage = `Token verification failed. Reason: ${error.name} - ${error.message}`;
        await logger.logError(errorMessage);
        return res.status(401).send({ message: 'Invalid token' });
    }
}

module.exports = verifyToken;


