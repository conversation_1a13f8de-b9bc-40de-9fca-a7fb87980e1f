<template>
  <div ref="chartHolder">
    <!-- The chart will be rendered here -->
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import { ScatterChart } from '@carbon/charts';
import chartsVue from '@carbon/charts-vue';

Vue.use(chartsVue);

export default {
  name: 'ScatterPlot',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    eventType: {type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      options: {
        title: "Try clicking a bubble!",
        axes: {
          bottom: {
            title: "Month",
            mapsTo: "key",
            scaleType: 'labels'
          },
          left: {
            title: "Value",
            mapsTo: "value",
            // includeZero: false, 

          },
        },
        bubble: {
          radiusMapsTo: "group",
          radiusLabel: "Group",
        },
        height: "400px",
      },
    };
  },
  mounted() {
    
      const chartHolder = this.$refs.chartHolder;
      console.log(chartHolder);

      this.chart = new ScatterChart(chartHolder, {
        data: this.data,
        options: this.options,
      });
      

      // Adding an event listener for the scatter-click event
      // myChart.services.events.addEventListener("scatter-click", (e) => {
      //   alert(`You clicked ${e.detail.datum.group}`);
      // });
      this.chart.services.events.addEventListener("scatter-click", (e) => {this.eventType(e)
                ;
              });

    
  },
  watch: {
    data(newData) {
      // Update chart data
      this.chart.model.setData(newData);
    },
  },
};
</script>

<style scoped>
/* Add custom styles if needed */
</style>
