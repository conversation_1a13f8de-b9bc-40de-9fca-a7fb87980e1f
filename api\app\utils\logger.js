/**
 * Logger utility for the API
 * Writes logs to text files in the logs directory
 */

const fs = require('fs');
const path = require('path');

// Log file paths
const INFO_LOG_PATH = path.join(__dirname, '../../logs/info.txt');
const ERROR_LOG_PATH = path.join(__dirname, '../../logs/error.txt');

/**
 * Format a log message with timestamp and source
 * @param {string} message - The message to log
 * @param {string} source - The source of the log (e.g., function name, component)
 * @returns {string} - Formatted log message
 */
function formatLogMessage(message, source) {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${source}] ${message}\n`;
}

/**
 * Write a message to the info log file
 * @param {string} message - The message to log
 * @param {string} source - The source of the log
 */
function logInfo(message, source = 'API') {
  try {
    const logMessage = formatLogMessage(message, source);
    fs.appendFileSync(INFO_LOG_PATH, logMessage);

    // Also log to console in development environment
    if (process.env.NODE_ENV !== 'production') {
      console.log(`INFO: ${source} - ${message}`);
    }
  } catch (error) {
    console.error('Error writing to info log:', error);
  }
}

/**
 * Write a warning message to the info log file
 * @param {string} message - The warning message to log
 * @param {string} source - The source of the warning
 */
function logWarning(message, source = 'API') {
  try {
    const logMessage = formatLogMessage(`WARNING: ${message}`, source);
    fs.appendFileSync(INFO_LOG_PATH, logMessage);

    // Also log to console in development environment
    if (process.env.NODE_ENV !== 'production') {
      console.warn(`WARNING: ${source} - ${message}`);
    }
  } catch (error) {
    console.error('Error writing warning to log:', error);
  }
}

/**
 * Write a message to the error log file
 * @param {string} message - The error message to log
 * @param {Error|string} error - The error object or message
 * @param {string} source - The source of the error
 */
function logError(message, error, source = 'API') {
  try {
    let errorMessage = message;

    // Add error details if available
    if (error) {
      if (error instanceof Error) {
        errorMessage += `\nError: ${error.message}`;
        if (error.stack) {
          errorMessage += `\nStack: ${error.stack}`;
        }
      } else if (typeof error === 'object') {
        errorMessage += `\nError: ${JSON.stringify(error)}`;
      } else {
        errorMessage += `\nError: ${error}`;
      }
    }

    const logMessage = formatLogMessage(errorMessage, source);
    fs.appendFileSync(ERROR_LOG_PATH, logMessage);

    // Also log to console in development environment
    if (process.env.NODE_ENV !== 'production') {
      console.error(`ERROR: ${source} - ${message}`, error);
    }
  } catch (err) {
    console.error('Error writing to error log:', err);
  }
}

module.exports = {
  logInfo,
  logWarning,
  logError
};
