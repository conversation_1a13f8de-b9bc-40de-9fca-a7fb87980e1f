const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');

// Export controller functions
exports.get_daily_fails = (req, res) => {
  getDailyFails(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_daily_fails:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_validation_counts = (req, res) => {
  getValidationCounts(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_validation_counts:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_critical_issues = (req, res) => {
  getPqeCriticalIssues(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_critical_issues:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_breakout_groups = (req, res) => {
  getPqeBreakoutGroups(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_breakout_groups:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_owners = (req, res) => {
  getPqeOwners(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_owners:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_all_pqe_data = (req, res) => {
  getAllPqeData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_all_pqe_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.update_pqe_action = (req, res) => {
  updatePqeAction(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in update_pqe_action:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_chart_data = (req, res) => {
  getPqeChartData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_chart_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_root_cause_analysis = (req, res) => {
  getPqeRootCauseAnalysis(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_root_cause_analysis:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_resolved_issues = (req, res) => {
  getPqeResolvedIssues(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_resolved_issues:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_pqe_outstanding_issues = (req, res) => {
  getPqeOutstandingIssues(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_pqe_outstanding_issues:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Function to get daily fails data
async function getDailyFails(values, callback) {
  let responseObj = {
    daily_fails: [],
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';
    const startDate = values.startDate || '';
    const endDate = values.endDate || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // Load the breakout_targets Excel file to get part numbers for this PQE owner
    const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    if (!fs.existsSync(targetsFilePath)) {
      throw new Error(`Breakout targets file not found at path: ${targetsFilePath}`);
    }

    const workbook = xlsx.readFile(targetsFilePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const targetsData = xlsx.utils.sheet_to_json(worksheet);

    // Filter targets data by PQE owner
    const pqeTargets = targetsData.filter(row => row.PQE_OWNER === pqeOwner);
    if (pqeTargets.length === 0) {
      responseObj.status_res = "success";
      responseObj.daily_fails = [];
      return callback(null, responseObj);
    }

    // Extract part numbers for this PQE owner
    const partNumbers = pqeTargets.map(row => row.PART_NUMBER).filter(pn => pn);
    if (partNumbers.length === 0) {
      responseObj.status_res = "success";
      responseObj.daily_fails = [];
      return callback(null, responseObj);
    }

    // Create placeholders for SQL query
    const pnPlaceholders = partNumbers.map(() => '?').join(',');

    // Connect to R0ADB2 to get defect counts by day
    logger.logInfo("Opening DB2 connection to R0ADB2 for daily fails");
    const configFail = configSwitch.config('R0ADB2');
    let connStringFail = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + configFail.database_user + ";"
      + "PWD=" + configFail.database_password + ";"
      + "HOSTNAME=" + configFail.database_host + ";"
      + "PORT=" + configFail.database_port;

    const connFail = await ibmdb.open(connStringFail);

    // Query to get daily fail counts with validation status
    const failQuery = `
      SELECT
        DATE(INCIDENT_DATE) AS FAIL_DATE,
        STATUS,
        COUNT(*) AS COUNT
      FROM
        QODS.GEM_DEFECT
      WHERE
        PART_NUM IN (${pnPlaceholders})
        ${startDate ? "AND INCIDENT_DATE >= ?" : ""}
        ${endDate ? "AND INCIDENT_DATE <= ?" : ""}
      GROUP BY
        DATE(INCIDENT_DATE),
        STATUS
      ORDER BY
        FAIL_DATE DESC
    `;

    // Prepare query parameters
    const queryParams = [...partNumbers];
    if (startDate) queryParams.push(startDate);
    if (endDate) queryParams.push(endDate);

    // Execute the query
    const failResults = await connFail.query(failQuery, queryParams);
    connFail.close();

    // Process the results
    const dailyFailsMap = {};

    failResults.forEach(row => {
      const date = row.FAIL_DATE.toISOString().split('T')[0];
      const status = row.STATUS || 'unvalidated';
      const count = row.COUNT || 0;

      if (!dailyFailsMap[date]) {
        dailyFailsMap[date] = {
          validated: 0,
          unvalidated: 0
        };
      }

      if (status.toLowerCase() === 'validated') {
        dailyFailsMap[date].validated += count;
      } else {
        dailyFailsMap[date].unvalidated += count;
      }
    });

    // Convert map to array format for chart
    const dailyFails = [];
    Object.entries(dailyFailsMap).forEach(([date, counts]) => {
      dailyFails.push({
        date,
        group: 'Validated',
        value: counts.validated
      });

      dailyFails.push({
        date,
        group: 'Unvalidated',
        value: counts.unvalidated
      });
    });

    // Sort by date
    dailyFails.sort((a, b) => new Date(a.date) - new Date(b.date));

    responseObj.status_res = "success";
    responseObj.daily_fails = dailyFails;
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getDailyFails:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get validation counts
async function getValidationCounts(values, callback) {
  let responseObj = {
    validated_count: 0,
    unvalidated_count: 0,
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // In a real implementation, this would query the database for validation counts
    // For now, return sample data based on the PQE owner

    if (pqeOwner === 'Albert G.') {
      responseObj.validated_count = 125;
      responseObj.unvalidated_count = 37;
    } else if (pqeOwner === 'Sarah L.') {
      responseObj.validated_count = 98;
      responseObj.unvalidated_count = 22;
    } else {
      // Default values for other PQE owners
      responseObj.validated_count = Math.floor(Math.random() * 100) + 50; // Random between 50-150
      responseObj.unvalidated_count = Math.floor(Math.random() * 30) + 10; // Random between 10-40
    }

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getValidationCounts:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get critical issues for a PQE owner
async function getPqeCriticalIssues(values, callback) {
  let responseObj = {
    critical_issues: [],
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // In a real implementation, this would query the database for critical issues
    // For now, return sample data

    // Sample critical issues data
    const allCriticalIssues = [
      {
        id: 'ci1',
        category: 'Fan Themis',
        month: '2024-06',
        severity: 'high',
        increaseMultiplier: '3.2',
        aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',
        comment: '',
        analysisType: 'Root Cause'
      },
      {
        id: 'ci2',
        category: 'Victoria Crypto',
        month: '2024-06',
        severity: 'medium',
        increaseMultiplier: '1.8',
        aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',
        comment: '',
        analysisType: 'Root Cause'
      },
      {
        id: 'ci3',
        category: 'Fan Themis',
        month: '2024-06',
        severity: 'high',
        increaseMultiplier: '2.5',
        aiDescription: 'Fan Themis showing higher failure rates for units manufactured in March 2024. Vintage analysis indicates a 2.5x increase in failures for this manufacturing period.',
        comment: '',
        analysisType: 'Vintage'
      },
      {
        id: 'ci4',
        category: 'Victoria Crypto',
        month: '2024-06',
        severity: 'medium',
        increaseMultiplier: '1.6',
        aiDescription: 'Victoria Crypto units from supplier ABC showing 1.6x higher failure rates compared to other suppliers. Quality audit recommended.',
        comment: '',
        analysisType: 'Supplier'
      },
      {
        id: 'ci5',
        category: 'Stellar Core',
        month: '2024-06',
        severity: 'high',
        increaseMultiplier: '2.8',
        aiDescription: 'Stellar Core showing 2.8x spike in June 2024. Root cause appears to be related to power delivery issues.',
        comment: '',
        analysisType: 'Root Cause'
      },
      {
        id: 'ci6',
        category: 'Nebula Drive',
        month: '2024-06',
        severity: 'medium',
        increaseMultiplier: '1.7',
        aiDescription: 'Nebula Drive has sustained problem with 1.7x target rate for 2 consecutive months. Consistent pattern of thermal issues.',
        comment: '',
        analysisType: 'Root Cause'
      }
    ];

    // For Albert G., return Fan Themis and Victoria Crypto issues
    if (pqeOwner === 'Albert G.') {
      responseObj.critical_issues = allCriticalIssues.filter(issue =>
        issue.category === 'Fan Themis' || issue.category === 'Victoria Crypto' || issue.category === 'Quantum Nexus'
      );
    }
    // For Sarah L., return Stellar Core and Nebula Drive issues
    else if (pqeOwner === 'Sarah L.') {
      responseObj.critical_issues = allCriticalIssues.filter(issue =>
        issue.category === 'Stellar Core' || issue.category === 'Nebula Drive'
      );
    }
    // For other PQE owners, return a subset of issues
    else {
      responseObj.critical_issues = allCriticalIssues.slice(0, 2);
    }

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeCriticalIssues:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get breakout groups for a PQE owner
async function getPqeBreakoutGroups(values, callback) {
  let responseObj = {
    breakout_groups: [],
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // Load the breakout_targets Excel file
    const filePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    if (!fs.existsSync(filePath)) {
      throw new Error(`Breakout targets file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);

    // Get the second sheet which contains the owners data
    if (workbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const ownersSheetName = workbook.SheetNames[1]; // Second sheet
    const ownersSheet = xlsx.utils.sheet_to_json(workbook.Sheets[ownersSheetName]);

    if (ownersSheet.length === 0) {
      throw new Error(`No data found in the owners sheet of breakout_targets.xlsx`);
    }

    // Get the first sheet which contains the target rates
    const targetsSheetName = workbook.SheetNames[0]; // First sheet
    const targetsSheet = xlsx.utils.sheet_to_json(workbook.Sheets[targetsSheetName]);

    // Filter to get breakout groups for this PQE owner
    const pqeBreakoutGroups = ownersSheet
      .filter(row => row['PQE Owner'] === pqeOwner)
      .map(row => row['Breakout Group'])
      .filter(group => group && group.trim() !== '');

    console.log(`Found ${pqeBreakoutGroups.length} breakout groups for PQE owner "${pqeOwner}"`);

    // For each breakout group, get the target rate and calculate xFactor
    const breakoutGroupsData = [];

    for (const groupName of pqeBreakoutGroups) {
      // Find target rate for this breakout group
      const targetRow = targetsSheet.find(t => t['Breakout Group'] === groupName);
      const targetRate = targetRow ? targetRow['Target Rate'] : 0.001; // Default if not found

      // For now, generate random xFactor for demonstration
      // In a real implementation, this would come from actual data
      const xFactor = Math.random() * 4; // Random value between 0 and 4

      // Determine status based on xFactor
      let status = 'Normal';
      if (xFactor >= 3.0) {
        status = 'Short-Term Spike';
      } else if (xFactor >= 1.5) {
        status = 'Sustained Problem';
      }

      breakoutGroupsData.push({
        name: groupName,
        status: status,
        xFactor: xFactor,
        targetRate: targetRate
      });
    }

    // Sort by xFactor (highest first)
    breakoutGroupsData.sort((a, b) => b.xFactor - a.xFactor);

    responseObj.status_res = "success";
    responseObj.breakout_groups = breakoutGroupsData;
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeBreakoutGroups:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get PQE owners from the breakout_targets Excel file
async function getPqeOwners(values, callback) {
  let responseObj = {
    pqe_owners: [],
    status_res: null
  };

  try {
    // Load the breakout_targets Excel file
    const filePath = path.join(__dirname, '../excel/breakout_targets.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Breakout targets Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);

    // Get the second sheet which contains the owners data
    if (workbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const sheetName = workbook.SheetNames[1]; // Second sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    if (sheet.length === 0) {
      throw new Error(`No data found in the owners sheet of breakout_targets.xlsx`);
    }

    // Extract unique PQE owners
    const pqeOwners = [...new Set(sheet.map(item => item['PQE Owner']))];

    // Filter out null/undefined values and sort alphabetically
    responseObj.pqe_owners = pqeOwners
      .filter(owner => owner && owner.trim() !== '')
      .sort();

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeOwners:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get data for all PQE owners for the QE Dashboard
async function getAllPqeData(values, callback) {
  let responseObj = {
    pqe_owners: [],
    status_res: null
  };

  try {
    // Load the breakout_targets Excel file
    const filePath = path.join(__dirname, '../excel/breakout_targets.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Breakout targets Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);

    // Get the second sheet which contains the owners data
    if (workbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const ownersSheetName = workbook.SheetNames[1]; // Second sheet
    const ownersSheet = xlsx.utils.sheet_to_json(workbook.Sheets[ownersSheetName]);

    if (ownersSheet.length === 0) {
      throw new Error(`No data found in the owners sheet of breakout_targets.xlsx`);
    }

    // Get the first sheet which contains the target rates
    const targetsSheetName = workbook.SheetNames[0]; // First sheet
    const targetsSheet = xlsx.utils.sheet_to_json(workbook.Sheets[targetsSheetName]);

    // Group breakout groups by PQE owner
    const pqeOwnersMap = {};

    ownersSheet.forEach(row => {
      const pqeOwner = row['PQE Owner'];
      const breakoutGroup = row['Breakout Group'];

      if (pqeOwner && breakoutGroup) {
        if (!pqeOwnersMap[pqeOwner]) {
          pqeOwnersMap[pqeOwner] = {
            name: pqeOwner,
            breakoutGroups: [],
            criticalGroups: [],
            criticalIssues: 0,
            unvalidatedCount: 0,
            collectiveXFactor: 0
          };
        }

        // Find target rate for this breakout group
        const targetRow = targetsSheet.find(t => t['Breakout Group'] === breakoutGroup);
        const targetRate = targetRow ? targetRow['Target Rate'] : 0.001; // Default if not found

        // For now, generate random xFactor for demonstration
        // In a real implementation, this would come from actual data
        const xFactor = Math.random() * 4; // Random value between 0 and 4

        const breakoutGroupData = {
          name: breakoutGroup,
          xFactor: xFactor,
          targetRate: targetRate
        };

        pqeOwnersMap[pqeOwner].breakoutGroups.push(breakoutGroupData);

        // If xFactor >= 1.5, add to critical groups
        if (xFactor >= 1.5) {
          pqeOwnersMap[pqeOwner].criticalGroups.push(breakoutGroupData);

          // If xFactor >= 3.0, add 2 critical issues, otherwise add 1
          pqeOwnersMap[pqeOwner].criticalIssues += (xFactor >= 3.0) ? 2 : 1;
        }
      }
    });

    // Calculate collective xFactor and generate counts for unvalidated, outstanding, resolved, and pending action tracker updates
    Object.values(pqeOwnersMap).forEach(pqeOwner => {
      // We don't need collective xFactor anymore as per requirements

      // Generate random unvalidated count
      pqeOwner.unvalidatedCount = Math.floor(Math.random() * 20); // Random value between 0 and 20

      // Generate random outstanding issues count
      pqeOwner.outstandingIssuesCount = Math.floor(Math.random() * 5); // Random value between 0 and 5

      // Generate random resolved issues count
      pqeOwner.resolvedIssuesCount = Math.floor(Math.random() * 8); // Random value between 0 and 8

      // Generate random pending action tracker updates count
      pqeOwner.pendingActionTrackerCount = Math.floor(Math.random() * 4); // Random value between 0 and 4

      // Add counts to each breakout group
      pqeOwner.breakoutGroups.forEach(group => {
        group.outstandingIssuesCount = Math.floor(Math.random() * 3); // Random value between 0 and 3
        group.resolvedIssuesCount = Math.floor(Math.random() * 4); // Random value between 0 and 4
      });
    });

    // Convert map to array and sort by critical issues count (highest first)
    responseObj.pqe_owners = Object.values(pqeOwnersMap)
      .sort((a, b) => b.criticalIssues - a.criticalIssues);

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getAllPqeData:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to update action tracker with PQE comments
async function updatePqeAction(values, callback) {
  let responseObj = {
    status_res: null
  };

  try {
    // Extract values from request
    const { issueId, category, comment, severity, pqeOwner, month, analysisType } = values;

    // Validate required parameters
    if (!issueId || !category || !pqeOwner) {
      throw new Error("Missing required parameters: issueId, category, and pqeOwner are required");
    }

    // In a real implementation, this would update the action tracker database
    // For now, just log the action and return success
    logger.logInfo(`Action tracker updated for issue ${issueId} (${category}) by ${pqeOwner}: ${comment}`);
    logger.logInfo(`Additional details - Severity: ${severity}, Month: ${month}, Analysis Type: ${analysisType}`);

    // In a real implementation, we would:
    // 1. Check if an action already exists for this issue
    // 2. If it exists, update it
    // 3. If it doesn't exist, create a new action
    // 4. Store all the details (issueId, category, comment, severity, pqeOwner, month, analysisType)

    responseObj.status_res = "success";
    responseObj.message = "Action tracker updated successfully";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in updatePqeAction:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get resolved issues for a PQE owner
async function getPqeResolvedIssues(values, callback) {
  let responseObj = {
    resolved_issues: [],
    performance_data: {},
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // In a real implementation, this would query the database for resolved issues
    // For now, return sample data

    // Sample resolved issues data
    const allResolvedIssues = [
      {
        id: 'ri1',
        category: 'Fan Themis',
        month: '2024-05',
        severity: 'high',
        increaseMultiplier: '2.8',
        aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',
        comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',
        analysisType: 'Root Cause',
        resolutionDate: '2024-05-15',
        currentPerformance: '0.9x'
      },
      {
        id: 'ri2',
        category: 'Victoria Crypto',
        month: '2024-04',
        severity: 'medium',
        increaseMultiplier: '1.7',
        aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific supplier batch. Supplier corrected their process.',
        comment: 'Worked with supplier to improve their quality control. Monitoring new batches closely.',
        analysisType: 'Supplier',
        resolutionDate: '2024-04-28',
        currentPerformance: '0.8x'
      },
      {
        id: 'ri3',
        category: 'Quantum Nexus',
        month: '2024-03',
        severity: 'high',
        increaseMultiplier: '2.2',
        aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',
        comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',
        analysisType: 'Root Cause',
        resolutionDate: '2024-03-20',
        currentPerformance: '0.7x'
      },
      {
        id: 'ri4',
        category: 'Stellar Core',
        month: '2024-05',
        severity: 'high',
        increaseMultiplier: '2.5',
        aiDescription: 'Stellar Core showed 2.5x spike in May 2024. Root cause was identified as a thermal issue in the power delivery subsystem. Issue was resolved with a design change.',
        comment: 'Implemented design change to improve thermal performance. Monitoring field data.',
        analysisType: 'Root Cause',
        resolutionDate: '2024-05-22',
        currentPerformance: '0.8x'
      },
      {
        id: 'ri5',
        category: 'Nebula Drive',
        month: '2024-04',
        severity: 'medium',
        increaseMultiplier: '1.9',
        aiDescription: 'Nebula Drive had sustained problem with 1.9x target rate for 2 consecutive months. Issue was traced to a specific manufacturing site. Process was corrected.',
        comment: 'Updated manufacturing process at Site B. Monitoring new production closely.',
        analysisType: 'Sector',
        resolutionDate: '2024-04-15',
        currentPerformance: '0.7x'
      }
    ];

    // Sample performance data
    const performanceData = {
      'Fan Themis': [
        { month: 'May 2024', xFactor: 2.8 },
        { month: 'Jun 2024', xFactor: 0.9 }
      ],
      'Victoria Crypto': [
        { month: 'Mar 2024', xFactor: 1.6 },
        { month: 'Apr 2024', xFactor: 1.7 },
        { month: 'May 2024', xFactor: 1.2 },
        { month: 'Jun 2024', xFactor: 0.8 }
      ],
      'Quantum Nexus': [
        { month: 'Mar 2024', xFactor: 2.2 },
        { month: 'Apr 2024', xFactor: 1.1 },
        { month: 'May 2024', xFactor: 0.8 },
        { month: 'Jun 2024', xFactor: 0.7 }
      ],
      'Stellar Core': [
        { month: 'May 2024', xFactor: 2.5 },
        { month: 'Jun 2024', xFactor: 0.8 }
      ],
      'Nebula Drive': [
        { month: 'Apr 2024', xFactor: 1.9 },
        { month: 'May 2024', xFactor: 1.1 },
        { month: 'Jun 2024', xFactor: 0.7 }
      ]
    };

    // For Albert G., return Fan Themis, Victoria Crypto, and Quantum Nexus issues
    if (pqeOwner === 'Albert G.') {
      responseObj.resolved_issues = allResolvedIssues.filter(issue =>
        issue.category === 'Fan Themis' || issue.category === 'Victoria Crypto' || issue.category === 'Quantum Nexus'
      );
    }
    // For Sarah L., return Stellar Core and Nebula Drive issues
    else if (pqeOwner === 'Sarah L.') {
      responseObj.resolved_issues = allResolvedIssues.filter(issue =>
        issue.category === 'Stellar Core' || issue.category === 'Nebula Drive'
      );
    }
    // For other PQE owners, return a subset of issues
    else {
      responseObj.resolved_issues = allResolvedIssues.slice(0, 2);
    }

    // Add performance data
    responseObj.performance_data = performanceData;

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeResolvedIssues:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get outstanding issues for a PQE owner
async function getPqeOutstandingIssues(values, callback) {
  let responseObj = {
    outstanding_issues: [],
    performance_data: {},
    status_res: null
  };

  try {
    // Get the PQE owner from the request
    const pqeOwner = values.pqeOwner || '';

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // In a real implementation, this would query the database for outstanding issues
    // For now, return sample data

    // Sample outstanding issues data
    const allOutstandingIssues = [
      {
        id: 'oi1',
        category: 'Fan Themis',
        month: '2024-06',
        severity: 'medium',
        increaseMultiplier: '1.4',
        aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',
        comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',
        analysisType: 'Root Cause',
        acceptanceDate: '2024-04-10',
        currentPerformance: '1.3x',
        acceptedBy: 'Engineering Team'
      },
      {
        id: 'oi2',
        category: 'Victoria Crypto',
        month: '2024-05',
        severity: 'medium',
        increaseMultiplier: '1.3',
        aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known supplier issue that has been accepted as within tolerance. Monitoring for any significant changes.',
        comment: 'Supplier variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',
        analysisType: 'Supplier',
        acceptanceDate: '2024-03-22',
        currentPerformance: '1.2x',
        acceptedBy: 'Quality Team'
      },
      {
        id: 'oi3',
        category: 'Quantum Nexus',
        month: '2024-04',
        severity: 'medium',
        increaseMultiplier: '1.2',
        aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate for units from manufacturing site C. This is a known process variation that has been accepted. Monitoring for any significant changes.',
        comment: 'Process variation at Site C is within accepted tolerance. Monitoring monthly for any significant changes.',
        analysisType: 'Sector',
        acceptanceDate: '2024-02-15',
        currentPerformance: '1.1x',
        acceptedBy: 'Manufacturing Team'
      },
      {
        id: 'oi4',
        category: 'Stellar Core',
        month: '2024-05',
        severity: 'medium',
        increaseMultiplier: '1.3',
        aiDescription: 'Stellar Core showing 1.3x increase in failure rate for units manufactured in Q1 2024. This vintage issue has been accepted as a known limitation. Monitoring for any significant changes.',
        comment: 'Q1 2024 units have a known limitation in the power management system. Engineering has accepted this for this vintage. Monitoring for any significant changes.',
        analysisType: 'Vintage',
        acceptanceDate: '2024-04-05',
        currentPerformance: '1.2x',
        acceptedBy: 'Engineering Team'
      },
      {
        id: 'oi5',
        category: 'Nebula Drive',
        month: '2024-06',
        severity: 'medium',
        increaseMultiplier: '1.4',
        aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',
        comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',
        analysisType: 'Root Cause',
        acceptanceDate: '2024-05-12',
        currentPerformance: '1.3x',
        acceptedBy: 'Product Team'
      }
    ];

    // Sample performance data
    const performanceData = {
      'Fan Themis': [
        { month: 'Apr 2024', xFactor: 1.5 },
        { month: 'May 2024', xFactor: 1.4 },
        { month: 'Jun 2024', xFactor: 1.3 }
      ],
      'Victoria Crypto': [
        { month: 'Mar 2024', xFactor: 1.4 },
        { month: 'Apr 2024', xFactor: 1.3 },
        { month: 'May 2024', xFactor: 1.2 },
        { month: 'Jun 2024', xFactor: 1.2 }
      ],
      'Quantum Nexus': [
        { month: 'Feb 2024', xFactor: 1.3 },
        { month: 'Mar 2024', xFactor: 1.2 },
        { month: 'Apr 2024', xFactor: 1.2 },
        { month: 'May 2024', xFactor: 1.1 },
        { month: 'Jun 2024', xFactor: 1.1 }
      ],
      'Stellar Core': [
        { month: 'Apr 2024', xFactor: 1.4 },
        { month: 'May 2024', xFactor: 1.3 },
        { month: 'Jun 2024', xFactor: 1.2 }
      ],
      'Nebula Drive': [
        { month: 'May 2024', xFactor: 1.5 },
        { month: 'Jun 2024', xFactor: 1.3 }
      ]
    };

    // For Albert G., return Fan Themis, Victoria Crypto, and Quantum Nexus issues
    if (pqeOwner === 'Albert G.') {
      responseObj.outstanding_issues = allOutstandingIssues.filter(issue =>
        issue.category === 'Fan Themis' || issue.category === 'Victoria Crypto' || issue.category === 'Quantum Nexus'
      );
    }
    // For Sarah L., return Stellar Core and Nebula Drive issues
    else if (pqeOwner === 'Sarah L.') {
      responseObj.outstanding_issues = allOutstandingIssues.filter(issue =>
        issue.category === 'Stellar Core' || issue.category === 'Nebula Drive'
      );
    }
    // For other PQE owners, return a subset of issues
    else {
      responseObj.outstanding_issues = allOutstandingIssues.slice(0, 2);
    }

    // Add performance data
    responseObj.performance_data = performanceData;

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeOutstandingIssues:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get chart data for PQE dashboard
async function getPqeChartData(values, callback) {
  let responseObj = {
    chart_data: [],
    status_res: null
  };

  try {
    // Extract values from request
    const { pqeOwner, chartType, monthsToFetch, groupName } = values;

    // Validate required parameters
    if (!pqeOwner) {
      throw new Error("PQE owner is required");
    }

    // Determine the number of months to fetch
    const numMonths = monthsToFetch || 6;

    // Log the request parameters
    console.log(`Generating chart data for PQE owner: ${pqeOwner}, Chart Type: ${chartType}, Months: ${numMonths}, Group: ${groupName || 'All'}`);


    // Generate sample months (last N months)
    const now = new Date();
    const months = [];

    for (let i = numMonths - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short' });
      const year = date.getFullYear().toString().slice(-2);
      months.push(`${monthName} '${year}`);
    }

    // Sort months chronologically
    months.sort((a, b) => {
      // Extract month and year from date string (e.g., "Jun '24")
      const [aMonth, aYear] = a.split(" ");
      const [bMonth, bYear] = b.split(" ");

      // Compare years first
      if (aYear !== bYear) {
        return aYear.localeCompare(bYear);
      }

      // If years are the same, compare months
      const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return monthOrder.indexOf(aMonth) - monthOrder.indexOf(bMonth);
    });

    // In a real implementation, this would query the database for chart data
    // For now, return sample data based on the chart type

    let chartData = [];

    // Define sample groups for the PQE owner
    let groups = [];
    if (pqeOwner === 'Albert G.') {
      groups = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];
    } else if (pqeOwner === 'Sarah L.') {
      groups = ['Stellar Core', 'Nebula Drive'];
    } else {
      groups = ['Sample Group 1', 'Sample Group 2'];
    }

    // Filter groups if a specific group is requested
    if (groupName) {
      groups = groups.filter(g => g === groupName);
    }

    if (chartType === 'validation') {
      // Generate validated/unvalidated data
      months.forEach(month => {
        // For each group, generate data
        groups.forEach(group => {
          // Generate random values with some variation by group
          const baseValidated = Math.floor(Math.random() * 30) + 15;
          const baseUnvalidated = Math.floor(Math.random() * 20) + 5;

          // Add group name to the data point if filtering by group
          const groupPrefix = groupName ? `${group} - ` : '';

          // Add validated data point
          chartData.push({
            group: `${groupPrefix}Validated`,
            date: month,
            value: baseValidated + (group === 'Fan Themis' ? 10 : 0) // Fan Themis has more validated issues
          });

          // Add unvalidated data point
          chartData.push({
            group: `${groupPrefix}Unvalidated`,
            date: month,
            value: baseUnvalidated + (group === 'Victoria Crypto' ? 8 : 0) // Victoria Crypto has more unvalidated issues
          });
        });
      });
    } else if (chartType === 'rootCause') {
      // Generate root cause data
      const rootCauses = ['Design', 'Manufacturing', 'Material', 'Unknown'];

      months.forEach(month => {
        // For each group, generate data for each root cause
        groups.forEach(group => {
          rootCauses.forEach(cause => {
            // Generate random value with some variation by group and cause
            let value = Math.floor(Math.random() * 15) + 5;

            // Add some specific patterns
            if (group === 'Fan Themis' && cause === 'Design') {
              value += 10; // Fan Themis has more design issues
            } else if (group === 'Victoria Crypto' && cause === 'Manufacturing') {
              value += 8; // Victoria Crypto has more manufacturing issues
            }

            // Add data point
            chartData.push({
              group: `${group} - ${cause}`,
              date: month,
              value: value
            });
          });
        });
      });
    } else if (chartType === 'supplier') {
      // Generate supplier data
      const suppliers = ['Supplier A', 'Supplier B', 'Supplier C'];

      months.forEach(month => {
        // For each group, generate data for each supplier
        groups.forEach(group => {
          suppliers.forEach(supplier => {
            // Generate random value with some variation by group and supplier
            let value = Math.floor(Math.random() * 20) + 5;

            // Add some specific patterns
            if (group === 'Fan Themis' && supplier === 'Supplier A') {
              value += 12; // Fan Themis has more issues with Supplier A
            } else if (group === 'Victoria Crypto' && supplier === 'Supplier C') {
              value += 10; // Victoria Crypto has more issues with Supplier C
            }

            // Add data point
            chartData.push({
              group: `${group} - ${supplier}`,
              date: month,
              value: value
            });
          });
        });
      });
    } else if (chartType === 'sector') {
      // Generate sector data
      const sectors = ['North', 'South', 'East', 'West'];

      months.forEach(month => {
        // For each group, generate data for each sector
        groups.forEach(group => {
          sectors.forEach(sector => {
            // Generate random value with some variation by group and sector
            let value = Math.floor(Math.random() * 12) + 3;

            // Add some specific patterns
            if (group === 'Fan Themis' && sector === 'North') {
              value += 8; // Fan Themis has more issues in North sector
            } else if (group === 'Victoria Crypto' && sector === 'East') {
              value += 6; // Victoria Crypto has more issues in East sector
            }

            // Add data point
            chartData.push({
              group: `${group} - ${sector}`,
              date: month,
              value: value
            });
          });
        });
      });
    } else if (chartType === 'vintage') {
      // Generate vintage data
      const vintages = ['Q1 2024', 'Q4 2023', 'Q3 2023', 'Q2 2023'];

      months.forEach(month => {
        // For each group, generate data for each vintage
        groups.forEach(group => {
          vintages.forEach(vintage => {
            // Generate random value with some variation by group and vintage
            let value = Math.floor(Math.random() * 15) + 3;

            // Add some specific patterns
            if (group === 'Fan Themis' && vintage === 'Q1 2024') {
              value += 10; // Fan Themis has more issues with Q1 2024 vintage
            } else if (group === 'Victoria Crypto' && vintage === 'Q4 2023') {
              value += 8; // Victoria Crypto has more issues with Q4 2023 vintage
            }

            // Add data point
            chartData.push({
              group: `${group} - ${vintage}`,
              date: month,
              value: value
            });
          });
        });
      });
    }

    responseObj.status_res = "success";
    responseObj.chart_data = chartData;
    return callback(null, responseObj);
  } catch (error) {
    logger.logError("Error in getPqeChartData:", error);
    responseObj.status_res = "error";
    responseObj.message = error.message;
    return callback(error, responseObj);
  }
}

// Function to get root cause analysis data for a PQE owner
async function getPqeRootCauseAnalysis(values, callback) {
  let responseObj = {
    status_res: null,
    categoryData: {},
    partNumbers: [],
    sql_error_msg: null
  };

  try {
    // Validate required parameters
    if (!values.pqeOwner) {
      throw new Error("PQE owner is required");
    }

    const pqeOwner = values.pqeOwner;
    const startDate = values.startDate;
    const endDate = values.endDate;
    const selectedGroup = values.selectedGroup; // Optional group filter

    console.log(`Getting root cause analysis for PQE owner: ${pqeOwner}`);
    console.log(`Date range: ${startDate} to ${endDate}`);
    console.log(`Selected group filter: ${selectedGroup || 'All groups'}`);

    // Step 1: Get breakout groups for this PQE owner from breakout_targets.xlsx
    const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    if (!fs.existsSync(targetsFilePath)) {
      throw new Error(`Breakout targets file not found at path: ${targetsFilePath}`);
    }

    const targetsWorkbook = xlsx.readFile(targetsFilePath);
    if (targetsWorkbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const ownersSheetName = targetsWorkbook.SheetNames[1]; // Second sheet
    const ownersSheet = xlsx.utils.sheet_to_json(targetsWorkbook.Sheets[ownersSheetName]);

    // Filter to get breakout groups for this PQE owner
    let pqeBreakoutGroups = ownersSheet
      .filter(row => row['PQE Owner'] === pqeOwner)
      .map(row => row['Breakout Group'])
      .filter(group => group && group.trim() !== '');

    console.log(`Found ${pqeBreakoutGroups.length} breakout groups for PQE owner ${pqeOwner}: ${pqeBreakoutGroups.join(', ')}`);

    // If a specific group is selected, filter to only that group
    if (selectedGroup) {
      pqeBreakoutGroups = pqeBreakoutGroups.filter(group => group === selectedGroup);
      console.log(`Filtered to selected group '${selectedGroup}': ${pqeBreakoutGroups.length} groups`);
    }

    if (pqeBreakoutGroups.length === 0) {
      throw new Error(`No breakout groups found for PQE owner: ${pqeOwner}${selectedGroup ? ` and selected group: ${selectedGroup}` : ''}`);
    }

    // Step 2: Get part numbers for all breakout groups from new_metis_test.xlsx
    const metisFilePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    if (!fs.existsSync(metisFilePath)) {
      throw new Error(`Metis file not found at path: ${metisFilePath}`);
    }

    const metisWorkbook = xlsx.readFile(metisFilePath);
    const metisSheetName = metisWorkbook.SheetNames[0];
    const metisSheet = xlsx.utils.sheet_to_json(metisWorkbook.Sheets[metisSheetName]);

    // Filter rows by the breakout groups for this PQE owner
    const filteredRows = metisSheet.filter(row =>
      pqeBreakoutGroups.includes(row['Full Breakout Name'])
    );
    console.log(`Found ${filteredRows.length} rows for PQE owner breakout groups`);

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for PQE owner: ${pqeOwner}`);

    if (partNumbers.length === 0) {
      throw new Error(`No part numbers found for PQE owner: ${pqeOwner}`);
    }

    // Step 3: Perform root cause analysis using the same logic as getMetisCategoryAnalysis
    // Format part numbers for SQL query (ensure they're strings)
    const formattedPNs = partNumbers.map(pn => String(pn));

    // Connect to the database
    const configSwitch = require("./config.js");
    const ibmdb = require("ibm_db");
    const connStr = configSwitch.getConnStr();
    const conn = await ibmdb.open(connStr);
    console.log("Connected to R0ADB2 for PQE root cause analysis");

    // Get the months between start and end date
    const months = getMonthsBetweenDates(startDate, endDate);
    console.log(`Analyzing ${months.length} months: ${months.join(', ')}`);

    // Initialize category data structure
    const categoryData = {};

    // Use the same query approach as getMetisCategoryAnalysis to get ROOT_CAUSE_1 categories
    const pnPlaceholders = formattedPNs.map(() => '?').join(', ');

    // Query for defect categories by month (same as Group page)
    const categoryQuery = `
      SELECT
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
        b.ROOT_CAUSE_1 AS CATEGORY,
        COUNT(*) AS DEFECT_COUNT
      FROM
        QODS.GEM_DEFECT AS a
      JOIN
        QEVAL.GEM_DEFECT_UPDATES AS b
      ON
        a.DEFECT_ID = b.DEFECT_ID
      WHERE
        a.PART_NUM IN (${pnPlaceholders})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
      GROUP BY
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7), b.ROOT_CAUSE_1
      ORDER BY
        MONTH, DEFECT_COUNT DESC
    `;

    // Prepare query parameters
    const queryParams = [...formattedPNs, startDate, endDate];

    // Execute the query
    const categoryResults = await conn.query(categoryQuery, queryParams);
    console.log(`Retrieved ${categoryResults.length} category records`);

    // Get total defects per month for calculating percentages
    const totalDefectsQuery = `
      SELECT
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
        COUNT(*) AS TOTAL_DEFECTS
      FROM
        QODS.GEM_DEFECT AS a
      WHERE
        a.PART_NUM IN (${pnPlaceholders})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
      GROUP BY
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7)
      ORDER BY
        MONTH
    `;

    const totalDefectsResults = await conn.query(totalDefectsQuery, queryParams);
    console.log(`Retrieved ${totalDefectsResults.length} total defect records`);

    // Close the R0ADB2 connection
    await conn.close();
    console.log("Closed R0ADB2 database connection");

    // Connect to QRYPROD for volume data (same as Group page)
    const configVol = configSwitch.config('QRYPROD');
    let connStringVol = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + configVol.database_user + ";"
      + "PWD=" + configVol.database_password + ";"
      + "HOSTNAME=" + configVol.database_host + ";"
      + "PORT=" + configVol.database_port;

    const connVol = await ibmdb.open(connStringVol);
    console.log("Connected to QRYPROD for volume data");

    // Query for volume data (same as Group page)
    const volumeQuery = `
      SELECT
        Q2QDPN AS PART_NUM,
        COUNT(*) AS COUNT,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS YEAR_MONTH
      FROM (
        SELECT
          Q2QDPN,
          Q2QDSQ,
          MIN(Q2OUTD) AS Q2OUTD,
          MIN(Q2OUTT) AS Q2OUTT
        FROM
          MFS2PCOMN.FCSPQD20
        WHERE
          Q2QDPN IN (${pnPlaceholders})
          AND Q2DEAC != 'Y'
          AND Q2FGID = 'PROCFULT'
        GROUP BY
          Q2QDPN, Q2QDSQ
      ) AS Q1
      WHERE
        Q2OUTD >= ?
        AND Q2OUTD <= ?
      GROUP BY
        Q2QDPN,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM')
      ORDER BY
        YEAR_MONTH
    `;

    // Prepare query parameters for volume query (same as Group page)
    // Convert YYYY-MM dates to YYYY-MM-DD for the database query
    let queryStartDate = `${startDate}-01`;
    let queryEndDate;

    // For end date, set it to the last day of the month
    const [endYear, endMonth] = endDate.split('-');
    const lastDay = new Date(parseInt(endYear), parseInt(endMonth), 0).getDate();
    queryEndDate = `${endDate}-${lastDay}`;

    const volumeQueryParams = [...formattedPNs, queryStartDate, queryEndDate];

    // Execute the volume query
    const volumeResults = await connVol.query(volumeQuery, volumeQueryParams);
    console.log(`Retrieved ${volumeResults.length} volume records`);

    // Close the QRYPROD database connection
    await connVol.close();
    console.log("Closed QRYPROD database connection");

    // Create a map of total defects by month
    const totalDefectsByMonth = {};
    totalDefectsResults.forEach(row => {
      totalDefectsByMonth[row.MONTH] = row.TOTAL_DEFECTS;
    });

    // Create a map of total volume by month (same as Group page)
    const volumeByMonth = {};
    volumeResults.forEach(row => {
      const month = row.YEAR_MONTH;
      if (!volumeByMonth[month]) {
        volumeByMonth[month] = 0;
      }
      volumeByMonth[month] += row.COUNT;
    });

    // Create a map of defect counts by category and month
    const defectCountsByCategory = {};
    categoryResults.forEach(row => {
      const month = row.MONTH;
      const category = row.CATEGORY || 'Unknown';
      const count = row.DEFECT_COUNT;

      if (!defectCountsByCategory[month]) {
        defectCountsByCategory[month] = {};
      }

      defectCountsByCategory[month][category] = count;
    });

    // Process results to create categoryData structure
    const uniqueCategories = [...new Set(categoryResults.map(row => row.CATEGORY || 'Unknown'))];
    console.log(`Found ${uniqueCategories.length} unique categories: ${uniqueCategories.join(', ')}`);

    // Build categoryData structure for each category and month
    uniqueCategories.forEach(category => {
      categoryData[category] = {};

      months.forEach(month => {
        const defectCount = (defectCountsByCategory[month] && defectCountsByCategory[month][category]) || 0;
        const volume = volumeByMonth[month] || 0;
        const failRate = volume > 0 ? (defectCount / volume) * 100 : 0;

        categoryData[category][month] = {
          defects: defectCount,
          volume: volume,
          failRate: parseFloat(failRate.toFixed(4))
        };
      });
    });

    console.log(`Processed ${uniqueCategories.length} categories for ${months.length} months`);

    // Store the results in the response
    responseObj.categoryData = categoryData;
    responseObj.partNumbers = partNumbers;
    responseObj.breakoutGroups = pqeBreakoutGroups;
    responseObj.status_res = "success";

    return callback(null, responseObj);
  } catch (err) {
    console.error("Error in getPqeRootCauseAnalysis:", err);
    responseObj.status_res = "error";
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Helper function to get months between two dates (same as in metis.controller.js)
function getMonthsBetweenDates(startDate, endDate) {
  const months = [];
  const start = new Date(startDate + '-01');
  const end = new Date(endDate + '-01');

  const current = new Date(start);
  while (current <= end) {
    const year = current.getFullYear();
    const month = String(current.getMonth() + 1).padStart(2, '0');
    months.push(`${year}-${month}`);
    current.setMonth(current.getMonth() + 1);
  }

  return months;
}
