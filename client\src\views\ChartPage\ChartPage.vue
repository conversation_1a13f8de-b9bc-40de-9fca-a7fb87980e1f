<template>
  <cv-grid class="chart-page__grid">
    <cv-row class="chart-page__r2">
      <cv-column :sm="2" :md="4" :lg="6">
        <cv-tile :light="lightTile">
          <!-- Include the BarChart component -->
          <YieldChart :data = "bar_chart_data" />
        </cv-tile>
      </cv-column>
    </cv-row>
  </cv-grid>
</template>

<script>
import YieldChart from '../../components/YieldChart'; // Import the BarChart component

export default {
  name: 'ChartPage',
  components: {
    YieldChart, // Register the BarChart component
  },
  data() {
    return {
      lightTile: true,
      bar_chart_data: [], // Initialize bar_chart_data as an empty array
    };
  },
  mounted() {
    this.load_bar_chart();
  },
  methods: {
    async load_bar_chart() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "populate_chart", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.bar_chart_data = data.data.monthly_data;
          console.log("Received data:", data);
          console.log("Jan", this.bar_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },
  }
};
</script>
