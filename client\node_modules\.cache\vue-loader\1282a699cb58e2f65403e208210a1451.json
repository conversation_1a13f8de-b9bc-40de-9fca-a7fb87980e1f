{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue?vue&type=template&id=c59387d2&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\Charts\\FailsStackedBarChart.vue", "mtime": 1748529056975}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}