{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748527899913}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAmBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["<template>\n  <div class=\"root-cause-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart\n    </div>\n    <div v-else>\n      <div class=\"chart-header\" v-if=\"title\">\n        <h5>{{ title }}</h5>\n      </div>\n      <div ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { StackedBarChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      chart: null,\n      defaultOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        color: {\n          scale: {\n            'Electrical': '#0f62fe',\n            'Mechanical': '#6929c4',\n            'Thermal': '#1192e8',\n            'Material': '#005d5d',\n            'Process': '#9f1853',\n            'Design': '#fa4d56',\n            'Unknown': '#570408',\n            'Other': '#198038',\n            'FLAG': '#8a3ffc',\n            'LINK': '#002d9c',\n            'NONFAIL': '#009d9a',\n            'KRAKEN': '#ee538b',\n            'I2C': '#b28600'\n          }\n        },\n        bars: { width: 60 },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100'\n      }\n    };\n  },\n  mounted() {\n    this.initChart();\n  },\n  watch: {\n    data: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      if (!this.data || this.data.length === 0) return;\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) return;\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Initialize the chart\n      this.chart = new StackedBarChart(chartContainer, {\n        data: this.data,\n        options: chartOptions\n      });\n    },\n\n    updateChart() {\n      if (!this.chart) {\n        this.initChart();\n        return;\n      }\n\n      if (!this.data || this.data.length === 0) {\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Update the chart data and options\n      this.chart.model.setData(this.data);\n      this.chart.model.setOptions(chartOptions);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.root-cause-chart-container {\n  width: 100%;\n  position: relative;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #f4f4f4;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #393939;\n  border-top: 3px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #c6c6c6;\n  font-size: 14px;\n}\n\n.chart-header {\n  margin-bottom: 16px;\n}\n\n.chart-header h5 {\n  color: #f4f4f4;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.chart-container {\n  width: 100%;\n  background-color: #161616;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n/* Dark theme styles for Carbon Charts */\n.chart-container :deep(.bx--cc--chart-wrapper) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--chart-svg) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--axis-title) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--axis-label) {\n  fill: #c6c6c6;\n}\n\n.chart-container :deep(.bx--cc--legend-item-text) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--grid-line) {\n  stroke: #393939;\n}\n</style>\n"]}]}