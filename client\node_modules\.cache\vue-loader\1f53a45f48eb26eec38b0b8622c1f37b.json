{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748534148568}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAkBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["<template>\n  <div class=\"root-cause-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart ({{ data ? data.length : 'null' }} items)\n    </div>\n    <div v-if=\"!loading\">\n      <div class=\"chart-header\" v-if=\"title\">\n      </div>\n      <div ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\" v-show=\"data && data.length > 0\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { StackedBarChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      chart: null,\n      initRetryCount: 0,\n      defaultOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            includeZero: true\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        color: {\n          scale: {\n            // Common root cause categories\n            'FUNC': '#0f62fe',\n            'OMI': '#6929c4',\n            'RAIM DEGRADE': '#1192e8',\n            'OTHER': '#005d5d',\n            'LANE DEGRADE': '#9f1853',\n            'IML': '#fa4d56',\n            'PMIC COMM LOST': '#570408',\n            'Unknown': '#198038',\n            'FLAG': '#8a3ffc',\n            'LINK': '#002d9c',\n            'NONFAIL': '#009d9a',\n            'KRAKEN': '#ee538b',\n            'I2C': '#b28600',\n            'CODE': '#ff832b',\n            'DIAG': '#24a148',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba'\n          }\n        },\n        bars: { width: 60 },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100',\n        stacked: true\n      }\n    };\n  },\n  mounted() {\n    console.log('RootCauseChart mounted with data:', this.data ? this.data.length : 'null');\n    if (this.data && this.data.length > 0) {\n      this.initChart();\n    }\n  },\n  watch: {\n    data: {\n      handler(newData) {\n        console.log('RootCauseChart data changed:', newData ? newData.length : 'null', 'items');\n        this.updateChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      console.log('RootCauseChart initChart called with data:', this.data ? this.data.length : 'null');\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart initChart: No data, returning');\n        return;\n      }\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) {\n        if (this.initRetryCount < 3) {\n          this.initRetryCount++;\n          console.log(`RootCauseChart initChart: No chart container, retrying with nextTick (attempt ${this.initRetryCount})`);\n          this.$nextTick(() => {\n            this.initChart();\n          });\n        } else {\n          console.log('RootCauseChart initChart: Container not found after 3 retries, giving up');\n          this.initRetryCount = 0;\n        }\n        return;\n      }\n\n      // Reset retry count on successful container access\n      this.initRetryCount = 0;\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Initialize the chart\n      console.log('RootCauseChart creating chart with data:', this.data.length, 'items');\n      console.log('Sample data:', this.data.slice(0, 3));\n      try {\n        this.chart = new StackedBarChart(chartContainer, {\n          data: this.data,\n          options: chartOptions\n        });\n        console.log('RootCauseChart chart created successfully');\n      } catch (error) {\n        console.error('RootCauseChart error creating chart:', error);\n      }\n    },\n\n    updateChart() {\n      console.log('RootCauseChart updateChart called');\n\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart updateChart: No data, destroying chart');\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      if (!this.chart) {\n        console.log('RootCauseChart updateChart: No chart exists, creating new one');\n        this.initChart();\n        return;\n      }\n\n      // Try to update the existing chart first\n      try {\n        console.log('RootCauseChart updateChart: Updating existing chart with', this.data.length, 'data points');\n        this.chart.model.setData(this.data);\n        console.log('RootCauseChart updateChart: Chart updated successfully');\n      } catch (error) {\n        console.error('RootCauseChart updateChart: Error updating chart, recreating:', error);\n        // If update fails, destroy and recreate\n        this.chart.destroy();\n        this.chart = null;\n        this.initChart();\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.root-cause-chart-container {\n  width: 100%;\n  position: relative;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #f4f4f4;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #393939;\n  border-top: 3px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #c6c6c6;\n  font-size: 14px;\n}\n\n.chart-header {\n  margin-bottom: 16px;\n}\n\n.chart-header h5 {\n  color: #f4f4f4;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.chart-container {\n  width: 100%;\n  background-color: #161616;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n/* Dark theme styles for Carbon Charts */\n.chart-container :deep(.bx--cc--chart-wrapper) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--chart-svg) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--axis-title) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--axis-label) {\n  fill: #c6c6c6;\n}\n\n.chart-container :deep(.bx--cc--legend-item-text) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--grid-line) {\n  stroke: #393939;\n}\n</style>\n"]}]}