{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748533058619}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAmBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["<template>\n  <div class=\"root-cause-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart ({{ data ? data.length : 'null' }} items)\n    </div>\n    <div v-else>\n      <div class=\"chart-header\" v-if=\"title\">\n        <h5>{{ title }}</h5>\n      </div>\n      <div ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { StackedBarChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      chart: null,\n      defaultOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            includeZero: true\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        color: {\n          scale: {\n            // Common root cause categories\n            'FUNC': '#0f62fe',\n            'OMI': '#6929c4',\n            'RAIM DEGRADE': '#1192e8',\n            'OTHER': '#005d5d',\n            'LANE DEGRADE': '#9f1853',\n            'IML': '#fa4d56',\n            'PMIC COMM LOST': '#570408',\n            'Unknown': '#198038',\n            'FLAG': '#8a3ffc',\n            'LINK': '#002d9c',\n            'NONFAIL': '#009d9a',\n            'KRAKEN': '#ee538b',\n            'I2C': '#b28600',\n            'CODE': '#ff832b',\n            'DIAG': '#24a148',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba'\n          }\n        },\n        bars: { width: 60 },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100',\n        stacked: true\n      }\n    };\n  },\n  mounted() {\n    console.log('RootCauseChart mounted with data:', this.data ? this.data.length : 'null');\n    this.$nextTick(() => {\n      this.initChart();\n    });\n  },\n  watch: {\n    data: {\n      handler(newData) {\n        console.log('RootCauseChart data changed:', newData ? newData.length : 'null', 'items');\n        this.$nextTick(() => {\n          this.updateChart();\n        });\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      console.log('RootCauseChart initChart called with data:', this.data ? this.data.length : 'null');\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart initChart: No data, returning');\n        return;\n      }\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) {\n        console.log('RootCauseChart initChart: No chart container, retrying in nextTick');\n        this.$nextTick(() => {\n          this.initChart();\n        });\n        return;\n      }\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Initialize the chart\n      console.log('RootCauseChart creating chart with data:', this.data.length, 'items');\n      console.log('Sample data:', this.data.slice(0, 3));\n      try {\n        this.chart = new StackedBarChart(chartContainer, {\n          data: this.data,\n          options: chartOptions\n        });\n        console.log('RootCauseChart chart created successfully');\n      } catch (error) {\n        console.error('RootCauseChart error creating chart:', error);\n      }\n    },\n\n    updateChart() {\n      console.log('RootCauseChart updateChart called');\n\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart updateChart: No data, destroying chart');\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      // Always destroy and recreate the chart for data changes\n      // This ensures proper rendering when data structure changes\n      if (this.chart) {\n        console.log('RootCauseChart updateChart: Destroying existing chart');\n        this.chart.destroy();\n        this.chart = null;\n      }\n\n      console.log('RootCauseChart updateChart: Recreating chart');\n      this.initChart();\n    },\n\n    // Public method to force chart refresh\n    forceRefresh() {\n      console.log('RootCauseChart forceRefresh called');\n      this.updateChart();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.root-cause-chart-container {\n  width: 100%;\n  position: relative;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #f4f4f4;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #393939;\n  border-top: 3px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #c6c6c6;\n  font-size: 14px;\n}\n\n.chart-header {\n  margin-bottom: 16px;\n}\n\n.chart-header h5 {\n  color: #f4f4f4;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.chart-container {\n  width: 100%;\n  background-color: #161616;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n/* Dark theme styles for Carbon Charts */\n.chart-container :deep(.bx--cc--chart-wrapper) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--chart-svg) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--axis-title) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--axis-label) {\n  fill: #c6c6c6;\n}\n\n.chart-container :deep(.bx--cc--legend-item-text) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--grid-line) {\n  stroke: #393939;\n}\n</style>\n"]}]}