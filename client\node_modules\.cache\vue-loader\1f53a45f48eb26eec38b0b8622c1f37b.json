{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748545182784}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAgBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["<template>\n  <div class=\"root-cause-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart ({{ data ? data.length : 'null' }} items)\n    </div>\n    <div v-if=\"!loading\">\n      <div ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\" v-show=\"data && data.length > 0\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { StackedBarChart, SimpleBarChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      chart: null,\n      defaultOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            includeZero: true\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        color: {\n          scale: {\n            // Common root cause categories\n            'FUNC': '#0f62fe',\n            'OMI': '#6929c4',\n            'RAIM DEGRADE': '#1192e8',\n            'OTHER': '#005d5d',\n            'LANE DEGRADE': '#9f1853',\n            'IML': '#fa4d56',\n            'PMIC COMM LOST': '#570408',\n            'Unknown': '#198038',\n            'FLAG': '#8a3ffc',\n            'LINK': '#002d9c',\n            'NONFAIL': '#009d9a',\n            'KRAKEN': '#ee538b',\n            'I2C': '#b28600',\n            'CODE': '#ff832b',\n            'DIAG': '#24a148',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba'\n          }\n        },\n        bars: { width: 60 },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100',\n        stacked: true\n      }\n    };\n  },\n  mounted() {\n    console.log('RootCauseChart mounted with data:', this.data ? this.data.length : 'null');\n    this.renderChart();\n  },\n  watch: {\n    data: {\n      handler(newData) {\n        console.log('RootCauseChart data changed:', newData ? newData.length : 'null', 'items');\n        this.renderChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.renderChart();\n      },\n      deep: true\n    }\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    renderChart() {\n      console.log('RootCauseChart renderChart called with data:', this.data ? this.data.length : 'null');\n\n      // Always destroy existing chart first\n      if (this.chart) {\n        console.log('RootCauseChart renderChart: Destroying existing chart');\n        this.chart.destroy();\n        this.chart = null;\n      }\n\n      // Don't create chart if no data\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart renderChart: No data, not creating chart');\n        return;\n      }\n\n      // Don't create chart if no container\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) {\n        console.log('RootCauseChart renderChart: No chart container, retrying with nextTick');\n        this.$nextTick(() => {\n          this.renderChart();\n        });\n        return;\n      }\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Create new chart\n      console.log('RootCauseChart renderChart: Creating new chart with', this.data.length, 'items');\n      console.log('Sample data:', this.data.slice(0, 3));\n      console.log('Chart options:', chartOptions);\n\n      try {\n        this.chart = new SimpleBarChart(chartContainer, {\n          data: this.data,\n          options: chartOptions\n        });\n        console.log('RootCauseChart renderChart: Chart created successfully');\n      } catch (error) {\n        console.error('RootCauseChart renderChart: Error creating chart:', error);\n        console.error('Error details:', error.message);\n        console.error('Data that caused error:', this.data);\n        console.error('Options that caused error:', chartOptions);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.root-cause-chart-container {\n  width: 100%;\n  position: relative;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #f4f4f4;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #393939;\n  border-top: 3px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #c6c6c6;\n  font-size: 14px;\n}\n\n.chart-header {\n  margin-bottom: 16px;\n}\n\n.chart-header h5 {\n  color: #f4f4f4;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.chart-container {\n  width: 100%;\n  background-color: #161616;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n/* Dark theme styles for Carbon Charts */\n.chart-container :deep(.bx--cc--chart-wrapper) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--chart-svg) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--axis-title) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--axis-label) {\n  fill: #c6c6c6;\n}\n\n.chart-container :deep(.bx--cc--legend-item-text) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--grid-line) {\n  stroke: #393939;\n}\n</style>\n"]}]}