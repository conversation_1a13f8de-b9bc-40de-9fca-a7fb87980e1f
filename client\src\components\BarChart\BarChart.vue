<template>
  <div ref='chartHolder'>
    <!-- <ccv-simple-bar-chart ref= "barChart" :data="data" :options="options"></ccv-simple-bar-chart>  -->
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { SimpleBarChart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'Bar<PERSON><PERSON>',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: true // Default is loading true
    },
    eventType: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      options: {
        title: 'Vertical simple bar (discrete)',
        axes: {
          left: {
            mapsTo: 'value'
          },
          bottom: {
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        data: {
          loading: this.loading // Bind the loading prop to this value
        },
        height: '400px',
      },
    };
  },
  mounted() {
    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    this.chart = new SimpleBarChart(chartHolder, {
      data: this.data,
      options: this.options,
    });

    this.chart.services.events.addEventListener('bar-click', (e) => {
      this.eventType(e);
    });
  },
  watch: {
    loading(newVal) {
      // Update loading state dynamically when the prop changes
      this.options.data.loading = newVal;
      this.chart.model.setOptions(this.options);
    },
  },
};
</script>

<style>
button[aria-label="Show as table"] {
  display: none;
  pointer-events: none;
}
div.toolbar-control.bx--overflow-menu[aria-label="Show as table"] {
  display: none;
}
</style>
