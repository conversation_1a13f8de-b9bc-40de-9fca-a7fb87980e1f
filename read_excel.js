const fs = require('fs');
const path = require('path');
const xls = require('xlsx');

// Path to the Excel file
const filePath = path.join('api', 'app', 'excel', 'FUL 10-24 to 2-25.xls');

try {
  console.log('Reading Excel file:', filePath);
  
  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.error('File does not exist:', filePath);
    process.exit(1);
  }
  
  // Read the Excel file
  const workbook = xls.readFile(filePath);
  console.log('Excel file read successfully');
  
  // Get sheet names
  console.log('Sheet names:', workbook.SheetNames);
  
  const sheetName = workbook.SheetNames[0];
  console.log('Using sheet:', sheetName);
  
  // Convert sheet to JSON
  const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);
  console.log('Parsed', sheet.length, 'rows from Excel');
  
  // Print the first row to see the structure
  if (sheet.length > 0) {
    console.log('First row structure:', JSON.stringify(sheet[0], null, 2));
    console.log('Column names:', Object.keys(sheet[0]));
  }
  
  // Filter for a specific part group (e.g., "Connector")
  const partGroup = "Connector";
  const partGroupData = sheet.filter(row => row["Code Name"] === partGroup);
  console.log(`Found ${partGroupData.length} rows for part group ${partGroup}`);
  
  // Print all rows for the part group
  partGroupData.forEach((row, index) => {
    console.log(`Row ${index + 1} for ${partGroup}:`, JSON.stringify(row, null, 2));
  });
  
} catch (err) {
  console.error('Error reading Excel file:', err);
}
