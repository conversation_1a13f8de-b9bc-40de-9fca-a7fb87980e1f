const assert = require('assert');
let date = new Date();
const logger = require('../utils/logger');


/**
 * Establishes Ldap connection to IBM BluePages
 * @returns ldap_client - LDAP client if successful
 */
const getLdapClient = async () => {
    const LdapClient = require('promised-ldap');
    let ldap_client;
    try {
        ldap_client = new LdapClient({
            url: 'ldaps://bluepages.ibm.com:636',
            connectionTimeout: 14400
        });
    } catch (err) {
        return err.message;
    }
    return ldap_client;
}

async function authenticateUser(client, user_email, password) {
    let searchQuery = {
        scope: 'sub',
        attrsOnly: false,
        attributes: ['uid', 'mail', 'preferredIdentity', 'cn', 'preferredFirstName', 'givenName', 'ismanager', 'manager', 'dn', 'sn', 'employeecountrycode'],
        sizeLimit: 1,
        filter: '(mail=' + user_email + ')'
    };

    let base = 'ou=bluepages, o=ibm.com';

    // perform LDAP search without password and get BP document
    let res = await client.search(base, searchQuery);

    // user's information document from BluePages
    let user_doc = res.entries[0].object;

    // do LDAP bind to authenticate password
    try {
        await client.bind(user_doc.dn, password);
        console.log('password successfully authenticated');
        return user_doc;
    } catch (error) {
        return error;
    }

}


async function get_newUserInfo(client, user_email) {
    let searchQuery = {
        scope: 'sub',
        attrsOnly: false,
        attributes: ['uid', 'mail', 'preferredIdentity', 'cn', 'preferredFirstName', 'givenName', 'ismanager', 'manager', 'dn', 'sn', 'employeecountrycode'],
        sizeLimit: 1,
        filter: '(mail=' + user_email + ')'
    };

    let base = 'ou=bluepages, o=ibm.com';

    // perform LDAP search without password and get BP document
    let res = await client.search(base, searchQuery);

    // user's information document from BluePages
    let user_doc = res.entries[0].object;

    return user_doc;
    }




/**
 * Perform LDAP search and get BluePages document filtered by searchQuery
 * without authentication
 * @param client LDAP client connection
 * @param filterParam search filter for ldap query
 * @returns entry.object user record from BluePages
 */
const getUserInfo = async (client, filterParam) => {

    let filter;
    if (filterParam.includes('@')) {
        filter = '(mail=' + filterParam + ')';
    } else {
        filter = '(uid=' + filterParam + ')';
    }

    let searchQuery = {
        scope: 'sub',
        attrsOnly: false,
        attributes: ['uid', 'mail', 'cn', 'preferredFirstName', 'ibmloc', 'givenName', 'ismanager', 'manager', 'employeecountrycode'],
        sizeLimit: 1,
        filter: filter
    };

    let base = 'ou=bluepages, o=ibm.com';

    // perform LDAP search, retrieve Bluepages document with the user info
    client.search('ou=bluepages, o=ibm.com', searchQuery, (err, res) => {

        res.on('searchEntry', entry => {
            return entry.object;
        });

        res.on('error', err => {
            console.group('No results found');
            console.log(err);
            console.groupEnd();
        });

    });
}

/**
 * Determine User Type of user
 * @param user_record
 * @returns {Promise<string>} user_type
 */
// const getUserType = async(user_record) => {
//     const ME_list = ['601580897', '4A1236897', '865878897', '572890897',
//         '3A5445897', '632070897', '4A1250897', '5G7556897', '3J6093897', '4A1250897' ];
//     const admin_list= ['3J3442897'];

//     let user_type;
//     // manager if isManager == 'Y'
//     if(user_record.ismanager === 'Y'){
//         user_type = 'manager';
//     }
//     else if (ME_list.includes(user_record.uid) ){
//         user_type = 'engineer';
//     }
//     else if (admin_list.includes(user_record.uid) ){
//         user_type = 'admin';
//     }
//     else{
//         return "employee";
//     }

//     return user_type;
// }


/**
 * Determine user's initial approval status based on user type
 * @param {string} user_type user's type/ user class
 * @param user_record user's JSON record
 * @returns user_record updated user record
 * **/
const updateInitialApproval = async (user_type, user_record) => {
    if(user_type === "manager"){

        // if manager, automatic approval
        user_record['isApproved'] = true;

        // update dateApproved, and approvalComment
        user_record['dateApproved'] = date.toLocaleDateString();
        user_record['approvalComment'] = "Automatically approved as user type is manager."
    }
    else if(user_type === "ME" ||
            user_type ==="employee"||
            user_type === "admin") {

        // initial login, so mark as NOT APPROVED
        user_record['isApproved'] = false;

        // update dateApproved, and approvalComment
        user_record['dateApproved'] = null;
        user_record['approvalComment'] = null;

    }
    else{
        throw new Error("User Type not recognized.")
    }

    return user_record;

}

const updateAutoApproval = async (user_record, approver_name) => {
    user_record['isApproved'] = true;

    // update dateApproved, and approvalComment
    user_record['dateApproved'] = date.toLocaleDateString();
    user_record['approvalComment'] = `User added by manager (${approver_name}).`

    return user_record;
}


module.exports = {getLdapClient, authenticateUser, getUserInfo, updateInitialApproval, updateAutoApproval, get_newUserInfo};


