<template>
  <div class="root-cause-chart-container">
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>Loading chart data...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="no-data-message">
      No data available for the chart ({{ data ? data.length : 'null' }} items)
    </div>
    <div v-if="!loading">
      <div class="chart-header" v-if="title">
      </div>
      <div ref="chartContainer" class="chart-container" :style="{ height }" v-show="data && data.length > 0"></div>
    </div>
  </div>
</template>

<script>
import { StackedBarChart } from '@carbon/charts';
import '@carbon/charts/styles.css';

export default {
  name: 'RootCauseChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '400px'
    },
    title: {
      type: String,
      default: 'Root Cause Categories'
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null,
      initRetryCount: 0,
      defaultOptions: {
        title: 'Root Cause Categories by Month',
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true,
            includeZero: true
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        color: {
          scale: {
            // Common root cause categories
            'FUNC': '#0f62fe',
            'OMI': '#6929c4',
            'RAIM DEGRADE': '#1192e8',
            'OTHER': '#005d5d',
            'LANE DEGRADE': '#9f1853',
            'IML': '#fa4d56',
            'PMIC COMM LOST': '#570408',
            'Unknown': '#198038',
            'FLAG': '#8a3ffc',
            'LINK': '#002d9c',
            'NONFAIL': '#009d9a',
            'KRAKEN': '#ee538b',
            'I2C': '#b28600',
            'CODE': '#ff832b',
            'DIAG': '#24a148',
            'BIOS': '#d12771',
            'HLA': '#08bdba'
          }
        },
        bars: { width: 60 },
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true
        },
        toolbar: {
          enabled: true
        },
        tooltip: {
          enabled: true
        },
        animations: true,
        data: {
          groupMapsTo: 'group'
        },
        theme: 'g100',
        stacked: true
      }
    };
  },
  mounted() {
    console.log('RootCauseChart mounted with data:', this.data ? this.data.length : 'null');
    if (this.data && this.data.length > 0) {
      this.initChart();
    }
  },
  watch: {
    data: {
      handler(newData) {
        console.log('RootCauseChart data changed:', newData ? newData.length : 'null', 'items');
        this.updateChart();
      },
      deep: true
    },
    options: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    initChart() {
      console.log('RootCauseChart initChart called with data:', this.data ? this.data.length : 'null');
      if (!this.data || this.data.length === 0) {
        console.log('RootCauseChart initChart: No data, returning');
        return;
      }

      const chartContainer = this.$refs.chartContainer;
      if (!chartContainer) {
        if (this.initRetryCount < 3) {
          this.initRetryCount++;
          console.log(`RootCauseChart initChart: No chart container, retrying with nextTick (attempt ${this.initRetryCount})`);
          this.$nextTick(() => {
            this.initChart();
          });
        } else {
          console.log('RootCauseChart initChart: Container not found after 3 retries, giving up');
          this.initRetryCount = 0;
        }
        return;
      }

      // Reset retry count on successful container access
      this.initRetryCount = 0;

      // Merge default options with provided options
      const chartOptions = {
        ...this.defaultOptions,
        ...this.options,
        height: this.height,
        data: {
          ...this.defaultOptions.data,
          ...this.options.data,
          onclick: (data) => {
            console.log('Root cause bar clicked:', data);
            this.$emit('bar-click', data);
          }
        }
      };

      // Initialize the chart
      console.log('RootCauseChart creating chart with data:', this.data.length, 'items');
      console.log('Sample data:', this.data.slice(0, 3));
      try {
        this.chart = new StackedBarChart(chartContainer, {
          data: this.data,
          options: chartOptions
        });
        console.log('RootCauseChart chart created successfully');
      } catch (error) {
        console.error('RootCauseChart error creating chart:', error);
      }
    },

    updateChart() {
      console.log('RootCauseChart updateChart called');

      if (!this.data || this.data.length === 0) {
        console.log('RootCauseChart updateChart: No data, destroying chart');
        if (this.chart) {
          this.chart.destroy();
          this.chart = null;
        }
        return;
      }

      if (!this.chart) {
        console.log('RootCauseChart updateChart: No chart exists, creating new one');
        this.initChart();
        return;
      }

      // Try to update the existing chart first
      try {
        console.log('RootCauseChart updateChart: Updating existing chart with', this.data.length, 'data points');
        this.chart.model.setData(this.data);
        console.log('RootCauseChart updateChart: Chart updated successfully');
      } catch (error) {
        console.error('RootCauseChart updateChart: Error updating chart, recreating:', error);
        // If update fails, destroy and recreate
        this.chart.destroy();
        this.chart = null;
        this.initChart();
      }
    }
  }
};
</script>

<style scoped>
.root-cause-chart-container {
  width: 100%;
  position: relative;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #f4f4f4;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #393939;
  border-top: 3px solid #0f62fe;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #c6c6c6;
  font-size: 14px;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h5 {
  color: #f4f4f4;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.chart-container {
  width: 100%;
  background-color: #161616;
  border-radius: 8px;
  padding: 16px;
}

/* Dark theme styles for Carbon Charts */
.chart-container :deep(.bx--cc--chart-wrapper) {
  background-color: #161616;
}

.chart-container :deep(.bx--cc--chart-svg) {
  background-color: #161616;
}

.chart-container :deep(.bx--cc--axis-title) {
  fill: #f4f4f4;
}

.chart-container :deep(.bx--cc--axis-label) {
  fill: #c6c6c6;
}

.chart-container :deep(.bx--cc--legend-item-text) {
  fill: #f4f4f4;
}

.chart-container :deep(.bx--cc--grid-line) {
  stroke: #393939;
}
</style>
