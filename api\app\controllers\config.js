const configFile = require("./config.json");

exports.config = (dbName) => {
    const dbConfig = configFile[dbName];

    if (dbConfig) {
        return {
            database_name: dbName,
            database_user: dbConfig.database_user,
            database_password: dbConfig.database_password,
            database_host: dbConfig.database_host,
            database_port: dbConfig.database_port,
        };
    } else {
        return {
            database_name: process.env.database_name,
            database_user: process.env.database_user,
            database_password: process.env.database_password,
            database_host: process.env.database_host,
            database_port: process.env.database_port,
        };
    }
};

// Function to get the connection string for R0ADB2 database
exports.getConnStr = () => {
    // Use the R0ADB2 database configuration
    const dbConfig = configFile["R0ADB2"];

    if (dbConfig) {
        // For R0ADB2, the database name is "R0ADB2" (hardcoded since it's not in the config)
        return `DATABASE=R0ADB2;HOSTNAME=${dbConfig.database_host};PORT=${dbConfig.database_port};PROTOCOL=TCPIP;UID=${dbConfig.database_user};PWD=${dbConfig.database_password}`;
    } else {
        // Use environment variables as fallback
        return `DATABASE=${process.env.database_name || "R0ADB2"};HOSTNAME=${process.env.database_host};PORT=${process.env.database_port};PROTOCOL=TCPIP;UID=${process.env.database_user};PWD=${process.env.database_password}`;
    }
};
