{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue?vue&type=template&id=acfb3762&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue", "mtime": 1748534884616}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}