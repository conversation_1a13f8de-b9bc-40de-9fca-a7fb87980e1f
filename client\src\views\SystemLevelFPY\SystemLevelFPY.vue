<style scoped lang = "scss">
@import "../../styles/carbon-utils";

.chart-page__grid{
  margin-top: $spacing-08;
}
</style>

<template>
  
   <cv-grid class="chart-page__grid">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />
    <cv-content-switcher aria-label='Choose content'  @selected="onSelected">
   <cv-content-switcher-button content-selector=".content-1" :selected="selectedIndex === 0">Selene</cv-content-switcher-button>
   <cv-content-switcher-button content-selector=".content-2" :selected="selectedIndex === 1">Artemis</cv-content-switcher-button>
  </cv-content-switcher>
  <div class="content-1">
    <cv-row class="chart-page__r2">
      <cv-column :sm="2" :md="4" :lg="6">
        <cv-tile :light="lightTile">
          <!-- Include the BarChart component -->
          <YieldChart v-if="bar_chart_data.length > 0" :data = 'bar_chart_data' :eventType = 'load_bar_chart2' />
        </cv-tile>
      </cv-column>
    </cv-row>
  </div>
  </cv-grid>
</template>

<script>
// import ScatterChart from '@carbon/charts'; // Import the BarChart component
// import ScatterPlot from '../../components/ScatterPlot';
import YieldChart from '../../components/YieldChart';
import MainHeader from '@/components/MainHeader'; // Import the MainHeader component
// import { Events } from '@carbon/charts/services/essentials/events';

export default {
  name: 'SystemLevelFPY',
  components: {
    YieldChart, 
    MainHeader
  },
  data() {
    return {
      lightTile: true,
      bar_chart_data: [], // Initialize bar_chart_data as an empty array
    };
  },
  mounted() {
    this.load_bar_chart();
  },
  methods: {
    async load_bar_chart() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "populate_chart", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.bar_chart_data = data.data.monthly_data;
          console.log("Received data:", data);
          console.log("Jan", this.bar_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    async load_bar_chart2() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "populate_chart2", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.bar_chart_data = data.data.monthly_data;
          console.log("Received data:", data);
          console.log("Jan", this.bar_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    handleBarClick(data) {
      // Update the chart data to only show the clicked bar
      console.log("Bar data received:", data);
      // this.bar_chart_data = [data];
    },
    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },
  }
};
</script>
