{"name": "api-statit2", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "unit-test": "echo 'todo test'", "lint": "echo 'todo test'", "e2e": "echo 'todo test'", "start": "node ./app.js"}, "keywords": ["nodejs", "express", "rest", "api", "mongodb"], "author": "<PERSON>", "license": "ISC", "dependencies": {"@ibm-functions/iam-token-manager": "^1.0.11", "audio-recorder": "^0.8.0", "axios": "^1.7.9", "chai": "^4.3.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^10.0.0", "express": "^4.18.2", "handlebars": "^4.7.7", "helmet": "^7.1.0", "hpp": "^0.2.3", "ibm-watson": "^10.0.0", "ibm_db": "^2.8.2", "jsonwebtoken": "^8.5.1", "ldap-authenticate": "^1.0.0", "ldapjs": "^2.3.1", "mic": "^2.1.2", "mongodb": "^4.17.2", "node-audiorecorder": "^3.0.0", "node-record-lpcm16": "^1.0.1", "node-schedule": "^2.1.1", "nodemailer": "^6.9.3", "path": "^0.12.7", "request": "^2.88.2", "sox": "^0.1.0", "uuid": "^7.0.3", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "xlsjs": "^1.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"ldap-async": "^1.3.2", "promised-ldap": "^0.3.0", "sinon": "^1.17.7"}}