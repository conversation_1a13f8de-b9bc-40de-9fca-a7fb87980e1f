API ERROR LOG
============
Created: ${new Date().toISOString()}

This file contains error logs from the API.
[2025-05-09T21:20:22.112Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.096Z] [getAiSummary] Getting AI summary for breakout group: Pump Themis"}
[2025-05-09T21:20:22.117Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.096Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:20:22.135Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.126Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:20:22.143Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.127Z] [getAiSummary] Data preparation complete. Data length: 774"}
[2025-05-09T21:20:22.176Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.128Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1653"}
[2025-05-09T21:20:22.180Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.127Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T21:20:22.183Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.128Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:20:22.186Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.128Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:20:22.193Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.128Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:20:22.196Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:22.129Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:20:30.655Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.626Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T21:20:30.658Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.627Z] [getAiSummary] Raw response received"}
[2025-05-09T21:20:30.661Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.628Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T21:20:30.664Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.628Z] [getAiSummary] Success response received. Generated text length: 754"}
[2025-05-09T21:20:30.667Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.628Z] [getAiSummary] Generated text (first 100 chars): November 2024: Pump Themis had a high XFactor of 22.727272727272727. The status was short-term spike"}
[2025-05-09T21:20:30.670Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:20:30.629Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T21:22:40.806Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.787Z] [getAiSummary] Getting AI summary for breakout group: SMP10"}
[2025-05-09T21:22:40.812Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.788Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:22:40.837Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.829Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:22:40.839Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Data preparation complete. Data length: 1199"}
[2025-05-09T21:22:40.856Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Action tracker insights available: true"}
[2025-05-09T21:22:40.859Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:22:40.862Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 2567"}
[2025-05-09T21:22:40.865Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:22:40.867Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.830Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:22:40.869Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:40.831Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:22:42.928Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.915Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T21:22:42.930Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.916Z] [getAiSummary] Raw response received"}
[2025-05-09T21:22:42.953Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.916Z] [getAiSummary] Success response received. Generated text length: 317"}
[2025-05-09T21:22:42.956Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.916Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T21:22:42.959Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.916Z] [getAiSummary] Generated text (first 100 chars): We've noticed intermittent connection issues in the cable connectors during stress testing. We've se"}
[2025-05-09T21:22:42.964Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:22:42.917Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T21:24:55.242Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.222Z] [getAiSummary] Getting AI summary for breakout group: Titania "}
[2025-05-09T21:24:55.246Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.222Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:24:55.271Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.264Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:24:55.294Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.265Z] [getAiSummary] Data preparation complete. Data length: 694"}
[2025-05-09T21:24:55.297Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.266Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1573"}
[2025-05-09T21:24:55.299Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.265Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T21:24:55.302Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.266Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:24:55.306Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.266Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:24:55.309Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.266Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:24:55.317Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:24:55.266Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:29:40.480Z] [API] No Token provided
Error: {"type":"error","message":"[2025-05-09T21:29:40.365Z] [getAiSummary] Error getting AI summary\nError: NetworkError when attempting to fetch resource."}
[2025-05-09T21:33:15.959Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.941Z] [getAiSummary] Getting AI summary for breakout group: PSU Zeus"}
[2025-05-09T21:33:15.963Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.942Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:33:15.993Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.971Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:33:15.998Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.972Z] [getAiSummary] Data preparation complete. Data length: 817"}
[2025-05-09T21:33:16.001Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.973Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T21:33:16.004Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.973Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1696"}
[2025-05-09T21:33:16.006Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.974Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:33:16.009Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.974Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:33:16.013Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.974Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:33:16.019Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:15.975Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:33:17.765Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.733Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T21:33:17.770Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.735Z] [getAiSummary] Raw response received"}
[2025-05-09T21:33:17.775Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.736Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T21:33:17.778Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.736Z] [getAiSummary] Success response received. Generated text length: 32"}
[2025-05-09T21:33:17.780Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.737Z] [getAiSummary] Generated text (first 100 chars): Normal volume with some defects."}
[2025-05-09T21:33:17.784Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:33:17.738Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T21:34:00.390Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.373Z] [getAiSummary] Getting AI summary for breakout group: Pump Themis"}
[2025-05-09T21:34:00.395Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.375Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:34:00.411Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.405Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:34:00.432Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.405Z] [getAiSummary] Data preparation complete. Data length: 774"}
[2025-05-09T21:34:00.436Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.406Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1653"}
[2025-05-09T21:34:00.439Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.405Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T21:34:00.441Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.406Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:34:00.444Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.407Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:34:00.448Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.407Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:34:00.451Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:00.407Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:34:05.348Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.321Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T21:34:05.352Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.323Z] [getAiSummary] Raw response received"}
[2025-05-09T21:34:05.354Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.323Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T21:34:05.356Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.324Z] [getAiSummary] Success response received. Generated text length: 754"}
[2025-05-09T21:34:05.358Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.324Z] [getAiSummary] Generated text (first 100 chars): November 2024: Pump Themis had a high XFactor of 22.727272727272727. The status was short-term spike"}
[2025-05-09T21:34:05.362Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:34:05.324Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T21:34:56.060Z] [prompt_watsonx] Error calling WatsonX.ai
Error: Request failed with status code 500
Stack: AxiosError: Request failed with status code 500
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2026:12)
    at IncomingMessage.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3142:11)
    at IncomingMessage.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4252:41)
    at runMicrotasks (<anonymous>)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
[2025-05-09T21:36:23.335Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.317Z] [getAiSummary] Getting AI summary for breakout group: Pisces Base"}
[2025-05-09T21:36:23.340Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.319Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T21:36:23.362Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.355Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T21:36:23.383Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.357Z] [getAiSummary] Data preparation complete. Data length: 704"}
[2025-05-09T21:36:23.385Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.357Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1583"}
[2025-05-09T21:36:23.388Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.357Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T21:36:23.390Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.357Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T21:36:23.393Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.357Z] [getAiSummary] Auth token available: true"}
[2025-05-09T21:36:23.396Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.358Z] [getAiSummary] Request data prepared"}
[2025-05-09T21:36:23.399Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:23.358Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T21:36:24.455Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.447Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T21:36:24.470Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.448Z] [getAiSummary] Raw response received"}
[2025-05-09T21:36:24.484Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.449Z] [getAiSummary] Success response received. Generated text length: 44"}
[2025-05-09T21:36:24.487Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.448Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T21:36:24.490Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.449Z] [getAiSummary] Generated text (first 100 chars): Normal volume with 0 defects in Pisces Base."}
[2025-05-09T21:36:24.492Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T21:36:24.449Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T23:14:02.651Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.469Z] [getAiSummary] Getting AI summary for breakout group: KMM Display Base"}
[2025-05-09T23:14:02.661Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.469Z] [getAiSummary] Preparing data for WatsonX.ai..."}
[2025-05-09T23:14:02.692Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.671Z] [getAiSummary] Action Tracker data retrieved"}
[2025-05-09T23:14:02.695Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.671Z] [getAiSummary] Data preparation complete. Data length: 702"}
[2025-05-09T23:14:02.698Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Action tracker insights available: false"}
[2025-05-09T23:14:02.702Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Formatted prompt for WatsonX.ai. Length: 1581"}
[2025-05-09T23:14:02.706Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Prompt (first 200 chars): \nYou are a helpful assistant that analyzes manufacturing quality data.\n\nTask: Briefly summarize the following XFactor data and relate it to the provided action tracker insights.\n\nInstructions:\n1. XFac..."}
[2025-05-09T23:14:02.708Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Auth token available: true"}
[2025-05-09T23:14:02.712Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Request data prepared"}
[2025-05-09T23:14:02.714Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:02.672Z] [getAiSummary] Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt"}
[2025-05-09T23:14:04.487Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.450Z] [getAiSummary] Received response from API. Status: 200"}
[2025-05-09T23:14:04.490Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.451Z] [getAiSummary] Raw response received"}
[2025-05-09T23:14:04.493Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.451Z] [getAiSummary] Successfully parsed WatsonX.ai response"}
[2025-05-09T23:14:04.496Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.451Z] [getAiSummary] Generated text (first 100 chars): Normal operation with no issues."}
[2025-05-09T23:14:04.499Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.451Z] [getAiSummary] Success response received. Generated text length: 32"}
[2025-05-09T23:14:04.501Z] [API] No Token provided
Error: {"type":"info","message":"[2025-05-09T23:14:04.452Z] [getAiSummary] AI summary set successfully"}
[2025-05-09T23:21:38.171Z] [API] No Token provided
[2025-05-09T23:21:38.179Z] [API] No Token provided
[2025-05-09T23:21:38.201Z] [API] No Token provided
[2025-05-09T23:21:38.205Z] [API] No Token provided
[2025-05-09T23:21:38.207Z] [API] No Token provided
[2025-05-09T23:21:38.211Z] [API] No Token provided
[2025-05-09T23:21:38.215Z] [API] No Token provided
[2025-05-09T23:21:38.218Z] [API] No Token provided
[2025-05-09T23:21:38.242Z] [API] No Token provided
[2025-05-09T23:21:38.244Z] [API] No Token provided
[2025-05-09T23:21:43.106Z] [API] No Token provided
[2025-05-09T23:21:43.120Z] [API] No Token provided
[2025-05-09T23:21:43.122Z] [API] No Token provided
[2025-05-09T23:21:43.125Z] [API] No Token provided
[2025-05-09T23:21:43.129Z] [API] No Token provided
[2025-05-09T23:21:43.131Z] [API] No Token provided
[2025-05-09T23:36:33.187Z] [API] No Token provided
[2025-05-09T23:36:33.195Z] [API] No Token provided
[2025-05-09T23:36:33.217Z] [API] No Token provided
[2025-05-09T23:36:33.219Z] [API] No Token provided
[2025-05-09T23:36:33.229Z] [API] No Token provided
[2025-05-09T23:36:33.232Z] [API] No Token provided
[2025-05-09T23:36:33.233Z] [API] No Token provided
[2025-05-09T23:36:33.236Z] [API] No Token provided
[2025-05-09T23:36:33.241Z] [API] No Token provided
[2025-05-09T23:36:33.246Z] [API] No Token provided
[2025-05-09T23:36:35.089Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:36:34.397Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":546,"stop_reason":"eos_token"}]}
[2025-05-09T23:36:35.110Z] [API] No Token provided
[2025-05-09T23:36:35.113Z] [API] No Token provided
[2025-05-09T23:36:35.117Z] [API] No Token provided
[2025-05-09T23:36:35.121Z] [API] No Token provided
[2025-05-09T23:36:40.618Z] [API] No Token provided
[2025-05-09T23:36:40.620Z] [API] No Token provided
[2025-05-09T23:36:40.639Z] [API] No Token provided
[2025-05-09T23:36:40.641Z] [API] No Token provided
[2025-05-09T23:36:40.644Z] [API] No Token provided
[2025-05-09T23:36:40.658Z] [API] No Token provided
[2025-05-09T23:36:40.660Z] [API] No Token provided
[2025-05-09T23:36:40.663Z] [API] No Token provided
[2025-05-09T23:36:40.674Z] [API] No Token provided
[2025-05-09T23:36:40.676Z] [API] No Token provided
[2025-05-09T23:36:41.483Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:36:40.775Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":504,"stop_reason":"eos_token"}]}
[2025-05-09T23:36:41.495Z] [API] No Token provided
[2025-05-09T23:36:41.501Z] [API] No Token provided
[2025-05-09T23:36:41.503Z] [API] No Token provided
[2025-05-09T23:36:41.506Z] [API] No Token provided
[2025-05-09T23:37:09.642Z] [API] No Token provided
[2025-05-09T23:37:09.644Z] [API] No Token provided
[2025-05-09T23:37:09.665Z] [API] No Token provided
[2025-05-09T23:37:09.673Z] [API] No Token provided
[2025-05-09T23:37:09.676Z] [API] No Token provided
[2025-05-09T23:37:09.678Z] [API] No Token provided
[2025-05-09T23:37:09.681Z] [API] No Token provided
[2025-05-09T23:37:09.684Z] [API] No Token provided
[2025-05-09T23:37:09.686Z] [API] No Token provided
[2025-05-09T23:37:09.688Z] [API] No Token provided
[2025-05-09T23:37:10.621Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:37:09.883Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":524,"stop_reason":"eos_token"}]}
[2025-05-09T23:37:10.636Z] [API] No Token provided
[2025-05-09T23:37:10.638Z] [API] No Token provided
[2025-05-09T23:37:10.641Z] [API] No Token provided
[2025-05-09T23:37:10.647Z] [API] No Token provided
[2025-05-09T23:37:23.477Z] [API] No Token provided
[2025-05-09T23:37:23.479Z] [API] No Token provided
[2025-05-09T23:37:23.502Z] [API] No Token provided
[2025-05-09T23:37:23.504Z] [API] No Token provided
[2025-05-09T23:37:23.510Z] [API] No Token provided
[2025-05-09T23:37:23.512Z] [API] No Token provided
[2025-05-09T23:37:23.517Z] [API] No Token provided
[2025-05-09T23:37:23.519Z] [API] No Token provided
[2025-05-09T23:37:23.522Z] [API] No Token provided
[2025-05-09T23:37:23.526Z] [API] No Token provided
[2025-05-09T23:37:24.326Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:37:23.597Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":505,"stop_reason":"eos_token"}]}
[2025-05-09T23:37:24.340Z] [API] No Token provided
[2025-05-09T23:37:24.343Z] [API] No Token provided
[2025-05-09T23:37:24.346Z] [API] No Token provided
[2025-05-09T23:37:24.349Z] [API] No Token provided
[2025-05-09T23:39:41.133Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.140Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.146Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.149Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.431Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.434Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.436Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.438Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.441Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.443Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.815Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.818Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.846Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.850Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.853Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.955Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.958Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.960Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.963Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:41.966Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:42.841Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:39:42.097Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":505,"stop_reason":"eos_token"}]}
[2025-05-09T23:39:42.861Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:42.871Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:42.875Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:42.878Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:43.091Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:39:42.353Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":504,"stop_reason":"eos_token"}]}
[2025-05-09T23:39:43.105Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:43.108Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:43.115Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:43.118Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:49.987Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:49.989Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.016Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.031Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.034Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.040Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.042Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.045Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.048Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:50.051Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.073Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.080Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.085Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.088Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.091Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:39:51.095Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.690Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.693Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.731Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.734Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.737Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.743Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.747Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.760Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.776Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:13.781Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:14.847Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:40:14.090Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":504,"stop_reason":"eos_token"}]}
[2025-05-09T23:40:14.866Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:14.869Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:14.872Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:14.876Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.562Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.568Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.601Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.617Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.619Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.622Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.628Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.631Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.633Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:52.654Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.733Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.740Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.742Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.747Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.750Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:53.754Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.540Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.548Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.578Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.581Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.585Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.587Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.590Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.601Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.603Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:57.605Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.682Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.685Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.692Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.696Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.702Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:40:58.705Z] [API] Token verification failed Reason:TypeError: Cannot read property 'replace' of undefined
[2025-05-09T23:43:49.450Z] [API] No Token provided
[2025-05-09T23:43:49.456Z] [API] No Token provided
[2025-05-09T23:43:49.482Z] [API] No Token provided
[2025-05-09T23:43:49.487Z] [API] No Token provided
[2025-05-09T23:43:49.489Z] [API] No Token provided
[2025-05-09T23:43:49.495Z] [API] No Token provided
[2025-05-09T23:43:49.498Z] [API] No Token provided
[2025-05-09T23:43:49.501Z] [API] No Token provided
[2025-05-09T23:43:49.504Z] [API] No Token provided
[2025-05-09T23:43:49.507Z] [API] No Token provided
[2025-05-09T23:43:50.568Z] [API] No Token provided
[2025-05-09T23:43:50.571Z] [API] No Token provided
[2025-05-09T23:43:50.574Z] [API] No Token provided
[2025-05-09T23:43:50.582Z] [API] No Token provided
[2025-05-09T23:43:50.585Z] [API] No Token provided
[2025-05-09T23:43:50.589Z] [API] No Token provided
[2025-05-09T23:52:21.267Z] [prompt_watsonx] Empty generated text from WatsonX.ai
Error: {"model_id":"ibm/granite-13b-instruct-v2","created_at":"2025-05-09T23:52:20.514Z","results":[{"generated_text":"","generated_token_count":1,"input_token_count":504,"stop_reason":"eos_token"}]}
[2025-05-10T02:57:32.796Z] [prompt_watsonx] Error calling WatsonX.ai
Error: Request failed with status code 400
Stack: AxiosError: Request failed with status code 400
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2026:12)
    at IncomingMessage.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3142:11)
    at IncomingMessage.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4252:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
[2025-05-11T18:59:12.038Z] [app] Failed to start server: self-signed certificate in certificate chain
Error: self-signed certificate in certificate chain
Stack: MongoServerSelectionError: self-signed certificate in certificate chain
    at Topology.selectServer (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\sdam\topology.js:321:38)
    at async Topology._connect (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\sdam\topology.js:200:28)
    at async Topology.connect (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\sdam\topology.js:152:13)
    at async topologyConnect (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\mongo_client.js:233:17)
    at async MongoClient._connect (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\mongo_client.js:246:13)
    at async MongoClient.connect (C:\Users\<USER>\Documents\statit_2\api\node_modules\mongodb\lib\mongo_client.js:171:13)
    at async Object.connectServer (C:\Users\<USER>\Documents\statit_2\api\mongodbutil.js:48:9)
    at async startServer (C:\Users\<USER>\Documents\statit_2\api\app.js:82:5)
[2025-05-11T19:06:39.727Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:08:29.646Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:09:00.352Z] [app] Failed to start server: Cannot find module 'ibm_db'
Require stack:
- C:\Users\<USER>\Documents\statit_2\api\app\controllers\admin.controller.js
- C:\Users\<USER>\Documents\statit_2\api\app\routes\routes.js
- C:\Users\<USER>\Documents\statit_2\api\app.js
Error: Cannot find module 'ibm_db'
Require stack:
- C:\Users\<USER>\Documents\statit_2\api\app\controllers\admin.controller.js
- C:\Users\<USER>\Documents\statit_2\api\app\routes\routes.js
- C:\Users\<USER>\Documents\statit_2\api\app.js
Stack: Error: Cannot find module 'ibm_db'
Require stack:
- C:\Users\<USER>\Documents\statit_2\api\app\controllers\admin.controller.js
- C:\Users\<USER>\Documents\statit_2\api\app\routes\routes.js
- C:\Users\<USER>\Documents\statit_2\api\app.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Module._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\app\controllers\admin.controller.js:3:15)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:09:30.342Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:09:42.856Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:13:50.149Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:15:55.925Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:17:10.997Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:17:24.135Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:17:41.165Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-11T19:20:58.600Z] [app] Failed to start server: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
Stack: Error: The module '\\?\C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\build\Release\odbc_bindings.node'
was compiled against a different Node.js version using
NODE_MODULE_VERSION 131. This version of Node.js requires
NODE_MODULE_VERSION 137. Please try re-compiling or re-installing
the module (for instance, using `npm rebuild` or `npm install`).
    at Object..node (node:internal/modules/cjs/loader:1925:18)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Module._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at bindings (C:\Users\<USER>\Documents\statit_2\api\node_modules\bindings\bindings.js:112:48)
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\node_modules\ibm_db\lib\odbc.js:57:31)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
[2025-05-12T00:21:09.568Z] [app] Failed to start server: db.collection is not a function
Error: db.collection is not a function
Stack: TypeError: db.collection is not a function
    at Object.<anonymous> (C:\Users\<USER>\Documents\statit_2\api\app\controllers\admin.controller.js:7:18)
    at Module._compile (node:internal/modules/cjs/loader:1469:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Module._load (node:internal/modules/cjs/loader:1104:12)
    at Module.require (node:internal/modules/cjs/loader:1311:19)
    at require (node:internal/modules/helpers:179:18)
    at module.exports (C:\Users\<USER>\Documents\statit_2\api\app\routes\routes.js:9:19)
    at startServer (C:\Users\<USER>\Documents\statit_2\api\app.js:91:35)
[2025-05-15T18:15:53.776Z] [API] No Token provided
[2025-05-15T18:15:53.810Z] [API] No Token provided
[2025-05-15T18:15:53.821Z] [API] No Token provided
[2025-05-15T18:18:49.928Z] [API] No Token provided
[2025-05-15T18:18:49.955Z] [API] No Token provided
[2025-05-15T18:18:49.970Z] [API] No Token provided
[2025-05-15T19:37:57.014Z] [API] Token verification failed. Reason: JsonWebTokenError - jwt malformed
[2025-05-15T20:07:43.869Z] [prompt_watsonx] Error calling WatsonX.ai
Error: Request failed with status code 500
Stack: AxiosError: Request failed with status code 500
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at IncomingMessage.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at IncomingMessage.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
[2025-05-16T03:24:09.063Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T03:42:40.524Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T03:58:58.443Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T04:02:16.038Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T04:02:40.002Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T04:05:26.660Z] [API] Token verification failed. Reason: TokenExpiredError - jwt expired
[2025-05-16T17:14:00.579Z] [API] No Token provided
[2025-05-16T17:14:12.122Z] [API] No Token provided
[2025-05-16T17:14:26.976Z] [API] No Token provided
[2025-05-16T17:15:13.330Z] [API] No Token provided
[2025-05-16T17:15:28.298Z] [API] No Token provided
[2025-05-16T17:16:49.531Z] [API] No Token provided
[2025-05-19T16:18:48.000Z] [validation2.controller] Validation Excel file not found at path: C:\Users\<USER>\Documents\statit_2\api\app\excel\validation_data.xlsx
[2025-05-19T16:18:48.004Z] [validation2.controller] Validation Excel file not found at path: C:\Users\<USER>\Documents\statit_2\api\app\excel\validation_data.xlsx
[2025-05-19T16:19:01.906Z] [validation2.controller] Validation Excel file not found at path: C:\Users\<USER>\Documents\statit_2\api\app\excel\validation_data.xlsx
[2025-05-19T16:19:01.909Z] [validation2.controller] Validation Excel file not found at path: C:\Users\<USER>\Documents\statit_2\api\app\excel\validation_data.xlsx
[2025-05-19T16:19:03.600Z] [validation2.controller] Validation Excel file not found at path: C:\Users\<USER>\Documents\statit_2\api\app\excel\validation_data.xlsx
[2025-05-19T16:35:03.192Z] [classifyDefectWithWatsonX] Invalid classification response: N/A
[2025-05-19T16:58:21.560Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:21.564Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:23.533Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:23.534Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:25.732Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:25.733Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:27.726Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:27.728Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:29.666Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:29.667Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:31.647Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:31.648Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:33.620Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:33.622Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:35.570Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:35.571Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:37.546Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:37.547Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:39.512Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:39.513Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:41.772Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:41.773Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:58:43.759Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:58:43.760Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:04.471Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:04.474Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:06.513Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:06.514Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:08.745Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:08.746Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:10.884Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:10.885Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:12.866Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:12.868Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T16:59:14.860Z] [classifyDefectBatchWithWatsonX] Error parsing batch classification response: Unexpected token . in JSON at position 102. Response: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]
Error: Unexpected token . in JSON at position 102
Stack: SyntaxError: Unexpected token . in JSON at position 102
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:672:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:553:31)
[2025-05-19T16:59:14.861Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:06:12.268Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:12.917Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:13.710Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:14.568Z] [classifyDefectWithWatsonX] Invalid classification format: {"primary": "Other", "subcategory": "Other"}
[2025-05-19T17:06:17.080Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:17.669Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:18.467Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:19.196Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:19.925Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:22.521Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:23.196Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token P in JSON at position 0. Response: Plugging
Error: Unexpected token P in JSON at position 0
Stack: SyntaxError: Unexpected token P in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:23.926Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Plugging
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:24.856Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Plugging
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:25.568Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:28.452Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:29.272Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:29.922Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:30.569Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:31.176Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:33.727Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:34.318Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:34.976Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:35.632Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:36.278Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:38.985Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:39.744Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:40.361Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:41.111Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:41.719Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:44.255Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:44.907Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:45.531Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:46.121Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:46.752Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:49.342Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:50.115Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:50.758Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:51.374Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:52.181Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:54.882Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:55.454Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:56.073Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:56.713Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:06:57.501Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:00.015Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:00.630Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:01.261Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:01.895Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:02.508Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:05.078Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:05.664Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:06.266Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:06.877Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:07.656Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:10.345Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:10.997Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:11.633Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:12.423Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:13.011Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:15.561Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:16.180Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:16.801Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:17.401Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:17.994Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:20.544Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:21.128Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:21.706Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:22.354Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:22.950Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:25.789Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:26.445Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:27.085Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:27.758Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:28.395Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:30.897Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:31.501Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:32.266Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token   in JSON at position 2. Response: 1. Mechanical
2. Other
Error: Unexpected token   in JSON at position 2
Stack: SyntaxError: Unexpected token   in JSON at position 2
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:32.851Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:07:33.449Z] [classifyDefectWithWatsonX] Error parsing classification response: Unexpected token O in JSON at position 0. Response: Other
Error: Unexpected token O in JSON at position 0
Stack: SyntaxError: Unexpected token O in JSON at position 0
    at JSON.parse (<anonymous>)
    at classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:883:37)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:731:36)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:31)
[2025-05-19T17:22:42.901Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:42.905Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:44.683Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:44.684Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:46.039Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:46.041Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:47.386Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:47.388Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:48.792Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:48.793Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:50.146Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:50.147Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:51.507Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:51.508Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:52.889Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:52.891Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T17:22:54.234Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
[2025-05-19T17:22:54.235Z] [processDefectsWithWatsonX] Batch classification returned invalid results. Expected 10, got 0
[2025-05-19T18:03:48.651Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:03:48.827Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:49.420Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:49.893Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:50.373Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:50.881Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:52.371Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:03:52.551Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:53.021Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:53.534Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:54.136Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:54.631Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:56.147Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:03:56.326Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:56.824Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:57.310Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:57.799Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:58.332Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:03:59.901Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:00.081Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:00.575Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:01.064Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:01.553Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:02.053Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:03.573Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:03.922Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:04.428Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:04.920Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:05.410Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:05.895Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:07.601Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:07.799Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:08.276Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:08.774Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:09.253Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:09.747Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:11.258Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:11.418Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:11.886Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:12.524Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:13.019Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:13.504Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:15.170Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:15.328Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:15.802Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:16.289Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:16.763Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:17.240Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:18.744Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:18.914Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:19.407Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:19.912Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:20.399Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:20.868Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:22.369Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:22.531Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:23.160Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:23.648Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:24.292Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:24.773Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:26.432Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:26.611Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:27.079Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:27.534Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:28.017Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:28.493Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:29.991Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:30.165Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:30.668Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:31.150Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:31.621Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:32.280Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:33.796Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:33.988Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:34.483Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:34.980Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:35.459Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:35.942Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:37.513Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:37.693Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:38.169Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:38.689Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:39.161Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:39.650Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:41.162Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:41.343Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:41.833Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:42.323Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:42.954Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:43.438Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:45.112Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:45.282Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:45.758Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:46.253Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:49.045Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:49.527Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:51.091Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:51.267Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:51.747Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:52.234Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:52.718Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:53.348Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:54.844Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:04:55.033Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:55.509Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:56.194Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:56.688Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:04:57.186Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:41.341Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:09:41.515Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:42.176Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:43.184Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:43.663Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:44.320Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:45.804Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:09:45.970Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:46.430Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:46.925Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:47.396Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:47.872Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:49.356Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:09:49.545Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:50.023Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:50.493Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:51.012Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:51.499Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:53.163Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:09:53.355Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:53.842Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:54.335Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:54.804Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:55.305Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:56.774Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:09:57.105Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:57.598Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:58.122Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:58.645Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:09:59.293Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:00.792Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:10:01.146Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:01.618Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:02.238Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:02.698Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:03.167Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:04.655Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:10:04.823Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:05.299Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:05.767Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:06.232Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:10:06.710Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:47.110Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:17:47.293Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:48.136Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:48.620Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:49.123Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:49.617Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:51.110Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:17:51.292Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:51.764Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:52.254Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:52.752Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:53.241Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:54.915Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:17:55.116Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:55.600Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:56.096Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:56.598Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:57.089Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:58.591Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:17:58.789Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:59.288Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:17:59.770Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:00.250Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:00.741Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:02.247Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T18:18:02.603Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:03.093Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:03.608Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:04.255Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T18:18:04.760Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1048:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:51.294Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:34:51.696Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:52.172Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:52.663Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:53.158Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:53.667Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:55.169Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:34:55.336Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:55.834Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:56.316Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:56.803Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:57.284Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:58.965Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:34:59.322Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:34:59.852Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:00.325Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:00.823Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:01.294Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:02.776Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:35:02.946Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:03.428Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:03.900Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:04.380Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:04.864Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:06.346Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:35:06.746Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:07.236Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:07.702Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:08.203Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:08.672Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:35:10.301Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:35:10.463Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:40.237Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:49:40.423Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:41.484Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:41.952Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:42.429Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:42.902Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:44.401Z] [classifyDefectBatchWithWatsonX] Error in classifyDefectBatchWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:745:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:49:44.575Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:45.070Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:49:45.542Z] [classifyDefectWithWatsonX] Error in classifyDefectWithWatsonX: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1046:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:612:34)
[2025-05-19T21:57:48.718Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:48.733Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"0b6e2b97dfc929130530a53f2fd9a8fa","status_code":404}
[2025-05-19T21:57:48.896Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:48.902Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"f99c2a1d4f96c56665fa6b511c53371a","status_code":404}
[2025-05-19T21:57:49.354Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:49.362Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"d1015702526475a9bb326678a4ed2d5c","status_code":404}
[2025-05-19T21:57:50.140Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:50.146Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"0136acbfacfd4a34c1cfedb432a72671","status_code":404}
[2025-05-19T21:57:50.602Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:50.609Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"bda8086828b72db932df64f9a3ba3cbe","status_code":404}
[2025-05-19T21:57:51.076Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:51.082Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"ad476451a6e95f2e3b9fef8203562c53","status_code":404}
[2025-05-19T21:57:52.538Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:52.546Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"f6b227cd677e77bb9993bfbeb7c96eac","status_code":404}
[2025-05-19T21:57:52.872Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:52.880Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"3e8fa1ad77f06dbeb7da83008f70ec21","status_code":404}
[2025-05-19T21:57:53.337Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:53.345Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"adea7e01f2006d12626ad248c2549ffe","status_code":404}
[2025-05-19T21:57:53.819Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:53.829Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"26f619f0727ecfbc353b297fb12c7f78","status_code":404}
[2025-05-19T21:57:54.286Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:54.297Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"7d471619919a6e107e85fdd3da11c68d","status_code":404}
[2025-05-19T21:57:54.757Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:54.767Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"836ba268aa0cc24adf6bd0e14ca09803","status_code":404}
[2025-05-19T21:57:56.236Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:56.240Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"febfdbfa7bca36e243c9f87459bdf4a3","status_code":404}
[2025-05-19T21:57:56.421Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:56.429Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"9c09993b89b46b774c0a015824ed0d7a","status_code":404}
[2025-05-19T21:57:56.894Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:56.898Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"3211f23148467461b2afabc99764febb","status_code":404}
[2025-05-19T21:57:57.350Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:57.354Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"846e3e0c73cc05687a2771f23f43b6a2","status_code":404}
[2025-05-19T21:57:57.849Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:57.853Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"3f262f4451f53f826706a4ec40dfcd22","status_code":404}
[2025-05-19T21:57:58.315Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:58.319Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"5588f24370646a4eb34d97073de947f6","status_code":404}
[2025-05-19T21:57:59.926Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:57:59.930Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"72e95812968983331de1d887d4fcb250","status_code":404}
[2025-05-19T21:58:00.091Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:58:00.095Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"48a65f06ea3f0527d3d8661f79da98a6","status_code":404}
[2025-05-19T21:58:00.556Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:58:00.559Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"3c398f1b50bf78229caa61e85786f9f1","status_code":404}
[2025-05-19T21:58:01.014Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:58:01.017Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"890b599737b62891fa88e25740584698","status_code":404}
[2025-05-19T21:58:01.456Z] [classifyDefectWithWatsonX] WatsonX API error in single defect classification: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1079:22)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:988:34)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T21:58:01.459Z] [classifyDefectWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-chat-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"bd2298d738caba5f3b0b3e4aac7c133a","status_code":404}
[2025-05-19T22:02:06.166Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T22:02:06.181Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-isntruct-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"96c5a9cb915ace1cce3dd204ed5b0175","status_code":404}
[2025-05-19T22:02:12.655Z] [classifyDefectBatchWithWatsonX] WatsonX API error: Request failed with status code 404
Error: Request failed with status code 404
Stack: AxiosError: Request failed with status code 404
    at settle (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:2049:12)
    at Unzip.handleStreamEnd (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:3166:11)
    at Unzip.emit (events.js:412:35)
    at endReadableNT (internal/streams/readable.js:1334:12)
    at processTicksAndRejections (internal/process/task_queues.js:82:21)
    at Axios.request (C:\Users\<USER>\Documents\statit_2\api\node_modules\axios\dist\node\axios.cjs:4276:41)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:748:22)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T22:02:12.660Z] [classifyDefectBatchWithWatsonX] WatsonX API status: 404, data: {"errors":[{"code":"model_not_supported","message":"Model 'ibm/granite-13b-isntruct-v2' is not supported","more_info":"https://cloud.ibm.com/apidocs/watsonx-ai#text-generation"}],"trace":"898e59d716e9c7174d5bd46df6295a0b","status_code":404}
[2025-05-19T22:03:07.029Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token ] in JSON at position 272
Error: Unexpected token ] in JSON at position 272
Stack: SyntaxError: Unexpected token ] in JSON at position 272
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:904:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async processDefectsWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:603:29)
[2025-05-19T22:08:58.357Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:09:05.071Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:09:13.511Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:09:19.909Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:24:01.735Z] [API] No Token provided
[2025-05-19T22:26:15.828Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:26:45.283Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: [{"primary":"Mechanical","subcategory":"Bent"},{"primary":"Functional","subcategory":"Other"}]
[2025-05-19T22:46:26.590Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 266
Error: Unexpected token O in JSON at position 266
Stack: SyntaxError: Unexpected token O in JSON at position 266
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1186:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:657:33)
[2025-05-19T22:46:26.594Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 266. Original prompt: 
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following 5 defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: One of "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - "Scratches": Surface damage, marks, or abrasions
   - "Bent": Components that are deformed, twisted, or not straight
   - "Plugging": Connection issues, improper seating, or insertion problems
   - "Discolor": Discoloration, stains, or color changes
   - "Misalignment": Components not properly aligned or positioned
   - "Need More Info": When the description is too vague to determine a specific subcategory
   - "Other": For issues that don't fit the above subcategories

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON array containing EXACTLY 5 classifications
- You MUST classify ALL 5 defects - this is EXTREMELY IMPORTANT
- Your ENTIRE response must be ONLY the JSON array, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON array
- Do NOT use ellipsis (...) in your response
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- Each item in the array MUST have both "primary" and "subcategory" fields
- The response MUST be a complete, valid JSON array that can be parsed with JSON.parse()
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field MUST be one of: "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

IMPORTANT: Your array MUST contain EXACTLY 5 items - one for each defect description.

The JSON array must be in this exact format with 5 items:
[
  {"primary":"Mechanical","subcategory":"Bent"},
  {"primary":"Functional","subcategory":"Other"},
  {"primary":"Mechanical","subcategory":"Other"},
  {"primary":"Functional","subcategory":"Need More Info"},
  {"primary":"Mechanical","subcategory":"Plugging"}
]

IMPORTANT: Your response should start with '[' and end with ']' with no other characters before or after.
IMPORTANT: Count your items carefully - you MUST provide EXACTLY 5 classifications.

Defect Descriptions:
1. ID: 1929973 - 5/3/25 - The old DCM 11S03NT310YH1938565813 was rejected and replaced with the n
2. ID: 1930080 - 5/6/25....Replaced DCM4(M3CP) location OLD DCM 11S03NT314YH1938544419 with NEW D
3. ID: 1930295 - 5/8/25,,,,,,Replaced DCM4 (M3CP) QUALITY N SEEPROM Screen Failures/Pam OLD DCM 1
4. ID: 1930296 - 5/12/25...Torn down A-Char11S03LX299YH110453L006 ..Sub Assembly tested lever 405
5. ID: 1930349 - 5/13/25...Replaced DCM3(M2CP) location OLD DCM 11S03NT307YH1938374843 with NEW D

Complete JSON Response (5 items):

Error: Unexpected token O in JSON at position 266
Stack: SyntaxError: Unexpected token O in JSON at position 266
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1186:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:657:33)
[2025-05-19T22:48:51.890Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 268
Error: Unexpected token O in JSON at position 268
Stack: SyntaxError: Unexpected token O in JSON at position 268
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1210:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-19T22:48:51.891Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 268. Original prompt: 
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following 5 defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: One of "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - "Scratches": Surface damage, marks, or abrasions
   - "Bent": Components that are deformed, twisted, or not straight
   - "Plugging": Connection issues, improper seating, or insertion problems
   - "Discolor": Discoloration, stains, or color changes
   - "Misalignment": Components not properly aligned or positioned
   - "Need More Info": When the description is too vague to determine a specific subcategory
   - "Other": For issues that don't fit the above subcategories

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON array containing EXACTLY 5 classifications
- You MUST classify ALL 5 defects - this is EXTREMELY IMPORTANT
- Your ENTIRE response must be ONLY the JSON array, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON array
- Do NOT use ellipsis (...) in your response
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- Each item in the array MUST have both "primary" and "subcategory" fields
- The response MUST be a complete, valid JSON array that can be parsed with JSON.parse()
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field MUST be one of: "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

IMPORTANT: Your array MUST contain EXACTLY 5 items - one for each defect description.

The JSON array must be in this exact format with 5 items:
[
  {"primary":"Mechanical","subcategory":"Bent"},
  {"primary":"Functional","subcategory":"Other"},
  {"primary":"Mechanical","subcategory":"Other"},
  {"primary":"Functional","subcategory":"Need More Info"},
  {"primary":"Mechanical","subcategory":"Plugging"}
]

IMPORTANT: Your response should start with '[' and end with ']' with no other characters before or after.
IMPORTANT: Count your items carefully - you MUST provide EXACTLY 5 classifications.

Defect Descriptions:
1. ID: 1930605 - 5/14/25..Torn down A-Char 11S03LX299YH110453T002..parts build back with new A-Ch
2. ID: 1929928 - 5/3/25 - The old DCM 11S03NT316YH1938689989 was rejected and replaced with the n
3. ID: 1929997 - JT 1151727 ound minor damage on the cable. Replaced cable re-ran the OP and it f
4. ID: 1929926 - 5/8/25 - The old DCM 11S03NT313YH1938492346 was rejected and replaced with the n
5. ID: 1929972 - 5/3/25 - The old DCM 11S03NT313YH1938443923 was rejected and replaced with the n

Complete JSON Response (5 items):

Error: Unexpected token O in JSON at position 268
Stack: SyntaxError: Unexpected token O in JSON at position 268
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1210:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-19T22:51:19.196Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 265
Error: Unexpected token O in JSON at position 265
Stack: SyntaxError: Unexpected token O in JSON at position 265
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1238:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-19T22:51:19.197Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token O in JSON at position 265. Original prompt: 
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following 5 defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: One of "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - "Scratches": Surface damage, marks, or abrasions
   - "Bent": Components that are deformed, twisted, or not straight
   - "Plugging": Connection issues, improper seating, or insertion problems
   - "Discolor": Discoloration, stains, or color changes
   - "Misalignment": Components not properly aligned or positioned
   - "Need More Info": When the description is too vague to determine a specific subcategory
   - "Other": For issues that don't fit the above subcategories

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON array containing EXACTLY 5 classifications
- You MUST classify ALL 5 defects - this is EXTREMELY IMPORTANT
- Your ENTIRE response must be ONLY the JSON array, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON array
- Do NOT use ellipsis (...) in your response
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- Each item in the array MUST have both "primary" and "subcategory" fields
- The response MUST be a complete, valid JSON array that can be parsed with JSON.parse()
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field MUST be one of: "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

IMPORTANT: Your array MUST contain EXACTLY 5 items - one for each defect description.

The JSON array must be in this exact format with 5 items:
[
  {"primary":"Mechanical","subcategory":"Bent"},
  {"primary":"Functional","subcategory":"Other"},
  {"primary":"Mechanical","subcategory":"Other"},
  {"primary":"Functional","subcategory":"Need More Info"},
  {"primary":"Mechanical","subcategory":"Plugging"}
]

IMPORTANT: Your response should start with '[' and end with ']' with no other characters before or after.
IMPORTANT: Count your items carefully - you MUST provide EXACTLY 5 classifications.

Defect Descriptions:
1. ID: 1929958 - 5/5/25....Replaced DCM M2CP OLD DCM 11S03NT316YH1938442945 NEW DCM 11S03NT309YH1
2. ID: 1930001 - 5/5/25....Replaced DCM 04 (M3CP) OLD DCM 11S03NT304YH1938711093 NEW DCM 11S03NT3
3. ID: 1929963 - 5/6/25,,,,,Replaced SMPA CABLE SPA2 AND SPA3 AND SPA4 .QUALITY N Suspect Welds/R
4. ID: 1930101 - 5/5/25 - The old DCM 11S03NT303YH1938664066 was replaced with the new DCM 11S03N
5. ID: 1929992 - 5/6/25...Replaced DCM1(M)CP) location OLD DCM 11S03NT304YH1938664335 with NEW DC

Complete JSON Response (5 items):

Error: Unexpected token O in JSON at position 265
Stack: SyntaxError: Unexpected token O in JSON at position 265
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1238:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-20T02:39:20.529Z] [classifyDefectBatchWithWatsonX] Invalid batch classification format: []
[2025-05-20T03:01:35.195Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response: [
  {"primary":"Mechanical","subcategory":"Cable Damage"},
  {"primary":"Functional","subcategory":"DCM Replacement"},
  {"primary":"Mechanical","subcategory":"A-Char Issue"},
  {"primary":"Functional","subcategory":"TestFailure"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":" Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
  {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","subcategory":"Unknown"},
   {"primary":"Mechanical","sub
[2025-05-20T03:01:35.196Z] [classifyDefectBatchWithWatsonX] Could not find valid JSON array in response. Original prompt: 
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following 15 defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: Use a descriptive term that best categorizes the defect

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - Be specific and descriptive (e.g., "Cable Damage", "DCM Replacement", "A-Char Issue", "Threading Problem")
   - Use "Unknown" if you cannot determine a specific subcategory with confidence
   - Choose terms that accurately describe the nature of the defect
   - You are NOT limited to predefined subcategories - use the most appropriate term
   - If the defect involves a component replacement, use the component name in the subcategory

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON array containing EXACTLY 15 classifications
- You MUST classify ALL 15 defects - this is EXTREMELY IMPORTANT
- Your ENTIRE response must be ONLY the JSON array, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON array
- Do NOT use ellipsis (...) in your response
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- Each item in the array MUST have both "primary" and "subcategory" fields (no spaces in field names)
- The response MUST be a complete, valid JSON array that can be parsed with JSON.parse()
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field should be a descriptive term or "Unknown" if uncertain

IMPORTANT: Your array MUST contain EXACTLY 15 items - one for each defect description.

The JSON array must be in this exact format with 15 items:
[
  {"primary":"Mechanical","subcategory":"Cable Damage"},
  {"primary":"Functional","subcategory":"DCM Replacement"},
  {"primary":"Mechanical","subcategory":"A-Char Issue"},
  {"primary":"Functional","subcategory":"Test Failure"},
  {"primary":"Mechanical","subcategory":"Unknown"}
]

IMPORTANT: Your response should start with '[' and end with ']' with no other characters before or after.
IMPORTANT: Count your items carefully - you MUST provide EXACTLY 15 classifications.
IMPORTANT: Do NOT include the phrase "Complete JSON Response:" in your response - just provide the raw JSON array.

Defect Descriptions:
1. ID: 1930128 - 5/8/25 - The old DCM 11S03NT304YH1938650100 was replaced with the new DCM 11S03N
2. ID: 1930082 - 5/6/25...Replaced DCM3(M2CP) location OLD DCM 11S03NT313YH1938448288 with NEW DC
3. ID: 1930161 - 5/7/25.....Replaced DCM2 OLD DCM 11S03NT316YH1938441988 NEW DCM 11S03NT309YH1938
4. ID: 1930174 - 5/7/25 - The old DCM 11S03NT313YH1938087857 was rejected and replaced with the n
5. ID: 1930250 - 5/8/25 - The old DCM 11S03NT303YH1938427452 was rejected and replaced with the n
6. ID: 1930249 - 5/7/25 - The old DCM 11S03NT303YH1938611805 was rejected and replaced with the n
7. ID: 1930225 - 5/8/25 - The old DCM 11S03NT310YH1938464298 was rejected and replaced with the n
8. ID: 1930222 - 5/8/25 - The old DCM 11S03NT313YH1938693108 was rejected and replaced with the n
9. ID: 1930297 - 5/7/25 - The old DCM 11S03NT305YH1938711084 was rejected and replaced with the n
10. ID: 1930294 - 5/9/25 - The old DCM 11S03NT309YH1938410090 was replaced with the new DCM 11S03N
11. ID: 1930299 - 5/15/25......QUALITY N SEEPROM Screen Failures/Pam kh,,,,,,,,,,,, ..............
12. ID: 1930298 - 5/13/25...Torn down A-Char 11S03LX301YH100452M002 failed DCM Tested Sub-Assembly
13. ID: 1930381 - OP 0452 failed with the following SEEPROM MISMATCH, then OP aborted.  Please rep
14. ID: 1930328 - 5/9/25 - The old DCM 11S03NT313YH1938443395 was rejected and replaced with the n
15. ID: 1930368 - please reject book's board or wait for futher instruction per JT: 1152946

Your JSON array (15 items):

[2025-05-20T03:28:55.067Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token } in JSON at position 307
Error: Unexpected token } in JSON at position 307
Stack: SyntaxError: Unexpected token } in JSON at position 307
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1314:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-20T03:30:13.446Z] [classifyDefectBatchWithWatsonX] Failed to extract valid JSON: Unexpected token { in JSON at position 230
Error: Unexpected token { in JSON at position 230
Stack: SyntaxError: Unexpected token { in JSON at position 230
    at JSON.parse (<anonymous>)
    at classifyDefectBatchWithWatsonX (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:1314:38)
    at processTicksAndRejections (internal/process/task_queues.js:95:5)
    at async exports.process_limited_batch (C:\Users\<USER>\Documents\statit_2\api\app\controllers\validation2.controller.js:662:33)
[2025-05-21T14:51:21.379Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:384:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T14:51:21.382Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:384:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T14:52:13.465Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:384:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T14:52:13.467Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:384:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T18:33:47.324Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:394:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T18:33:47.328Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:394:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T18:46:01.526Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:394:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-21T18:46:01.528Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:394:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-28T18:39:44.763Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-28T18:39:44.767Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T13:48:42.590Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T13:48:42.594Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T13:51:09.300Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T13:51:09.302Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T14:25:55.099Z] [API] Error in getPqeBreakoutGroups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T14:25:55.102Z] [API] Error in get_pqe_breakout_groups:
Error: PQE owner is required
Stack: Error: PQE owner is required
    at getPqeBreakoutGroups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:414:13)
    at exports.get_pqe_breakout_groups (C:\Users\<USER>\Documents\statit_2\api\app\controllers\pqe-dashboard.controller.js:40:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Documents\statit_2\api\node_modules\express\lib\router\index.js:175:3)
[2025-05-29T15:01:55.191Z] [API] Error in get_pqe_root_cause_analysis:
Error: [IBM][CLI Driver][DB2] SQL0204N  "MFS2PCOMN.FCSPQD20" is an undefined name.  SQLSTATE=42704

Stack: Error: [IBM][CLI Driver][DB2] SQL0204N  "MFS2PCOMN.FCSPQD20" is an undefined name.  SQLSTATE=42704

[2025-05-29T15:01:55.193Z] [API] Error in get_pqe_root_cause_analysis:
Error: [IBM][CLI Driver][DB2] SQL0204N  "MFS2PCOMN.FCSPQD20" is an undefined name.  SQLSTATE=42704

Stack: Error: [IBM][CLI Driver][DB2] SQL0204N  "MFS2PCOMN.FCSPQD20" is an undefined name.  SQLSTATE=42704

