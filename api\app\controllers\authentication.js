require("dotenv").config();
const auth = require('./authentication_helper_fns.js');
const jwt = require('jsonwebtoken');
const user = require("../pub/user.js");
let date = new Date();
const logger = require('../utils/logger');
const notification = require('../pub/notification.js');
const uuid = require('uuid')
//json object to store authentication info to be returned to the client at login time
let auth_json;
let userProfile;
let user_info;
//Handle post request
exports.authUser = (req, res) => {
    console.log("Accepting POST request")
    main(req, res).then(r => console.log("finish processing POST request"));
}

async function loginLock(req, user_email) {
    //set failed time in user profile

    if (userProfile) {
        let newCount = 0;
        let nowDate = new Date();
        let bLock = '';
        if (userProfile.login_failed_count) {
            if (typeof (userProfile.login_failed_count) === 'string') {
                newCount = parseInt(userProfile.login_failed_count) + 1;
            } else {
                newCount = userProfile.login_failed_count + 1;
            }

            if (newCount === 5) {
                //console.log("locak = true");
                bLock = true;
            }
            if (newCount >= 6) {
                newCount = 5;
            }
            //console.log(newCount);
        } else {
            newCount = 1;
        }

        if (userProfile.login_failed_time && userProfile.login_failed_time !== '' && userProfile.login_failed_time !== null) {
            //determin if within 1 hours
            let lockDate = new Date(userProfile.login_failed_time);
            console.log('Date of failed:' + lockDate.getTime());
            let futureDate = lockDate;
            futureDate.setTime(lockDate.getTime() + 60 * 60 * 1000);
            console.log('Date of futureDate:' + futureDate.getTime());
            console.log('Date of now:' + new Date().getTime());
            if (futureDate.getTime() < new Date().getTime()) {
                // over 1 hour, set new value.
                console.log('over one hour');
                await user.update_user_data(userProfile._id, { $set: { login_failed_time: nowDate, login_failed_count: 1 } });
                auth_json.failed_count = 1;
                logger.logInfo('user has 1 attempts lockout during one hour: ' + user_email + ' ip: ' + req.connection.remoteAddress, 'authentication');
            } else {
                if (bLock) {
                    console.log("login_lock_time = true");
                    await user.update_user_data(userProfile._id, { $set: { login_failed_count: newCount, login_lock_time: nowDate } });
                    logger.logInfo('user has been locked for login 5 attempts lockout during one hour: ' + user_email, 'authentication');

                    //send notification to admin
                    const username = typeof (userProfile.cn) === "string" ? userProfile.cn : userProfile.cn[0]
                    // MongoDB removed - admin notification functionality disabled
                    let user_ids = '';
                    let notificationData = {
                        to_user: user_ids,
                        UserID: userProfile.uid,
                        username
                    }
                    let sentResult = await notification.trigger_notification("both", "Access_Locked_To_Admin", notificationData);
                    if (!sentResult) console.log('Nofification sending error');

                    //send notification to manager and manager's delegate
                    let user_ids2 = userProfile.manager_uid
                    const delegateUids = await user.get_manager_delegate_by_uid(userProfile.uid);
                    if (delegateUids) {
                        user_ids2 += `;${delegateUids}`
                    }
                    let notificationDataToManager = {
                        to_user: user_ids2,
                        UserID: userProfile.uid,
                        username
                    }
                    let sentResult2 = await notification.trigger_notification("both", "Access_Locked_To_Manager", notificationDataToManager);
                    if (!sentResult2) console.log('Nofification sending error');
                } else {
                    await user.update_user_data(userProfile._id, { $set: { login_failed_count: newCount } });
                    logger.logInfo('user has ' + newCount + ' attempts lockout during one hour: ' + user_email + ' ip: ' + req.connection.remoteAddress, 'authentication'); // + ' ' + req.ips ? JSON.stringify(req.ips) : ''
                }
                auth_json.failed_count = newCount;
            }
        } else {
            //set failed time and count
            await user.update_user_data(userProfile._id, { $set: { login_failed_time: nowDate, login_failed_count: 1 } });
            auth_json.failed_count = 1;
        }
        return userProfile;
    }
    return '';

}

async function main(req, res) {
    auth_json = {
        auth: null,
        token: null,
        approved: null,
        archived: null,
        user_type: null,
        user_talent_id: null,
        name: null,
        username: null,
        isDelegate: null,
        ismanager: null,
        manager_uid: null,
        failed_count: null
    };

    //console.log(`JWT KEY ENV VAR: ${process.env}`);
    //TODO: make email input case agnostic

    // get LDAP client
    let client;
    try {
        client = await auth.getLdapClient();
        console.log('successfully got client')
        logger.logInfo(`Login: authUser - successfully got client`, 'authentication');
    } catch (e) {
        console.log(e, 'there was a problem connecting to BluePages');
        logger.logError(`Login: authUser - here was a problem connecting to BluePages: ${e.message}`, e, 'authentication');
    }

    // receive login parameters from Client side
    let user_email = req.body.user;
    let password = req.body.pass;
    auth_json = {
        auth: null,
        token: null,
        approved: null,
        archived: null,
        user_type: null,
        user_talent_id: null,
        name: null,
        username: user_email,
        isDelegate: null,
        ismanager: null,
        manager_uid: null,
        failed_count: null
    };
    user_info = {
        user_email: user_email,
        login_attempt_datetime: new Date().toLocaleString(),
        session_w3_login_result: 'Successful',
        session_token_login_result: 'Successful',
        qualify_authorization: 'Successful',
        session_id: uuid.v4(),
        session_key: '',
        session_address: req.connection.remoteAddress,
        session_useragent: req.headers['user-agent'],
        session_ended: null
    }

    // authenticate user using email and password and get their BP record
    let user_doc_bp;
    try {
        userProfile = await user.get_user_info_by_email(user_email);
        if (userProfile && userProfile.login_lock_time && userProfile.login_lock_time !== '' && userProfile.login_lock_time !== null) {
            auth_json.auth = 1;
            auth_json.failed_count = 5;

            user_info.session_w3_login_result = 'Unsuccessful'
            user_info.session_token_login_result = 'Unsuccessful'
            user_info.qualify_authorization = 'Not Authorized'
            await insert_session_monitor(user_info, user_doc_bp)
            res.status(501).json(auth_json); // status code 501 = authentication error
            return;
        }
        user_doc_bp = await auth.authenticateUser(client, user_email, password);
        // console.log('user_doc_bp: ', user_doc_bp);

        if (user_doc_bp.lde_message && user_doc_bp.lde_message === 'Invalid Credentials') {
            auth_json.auth = 1;
            console.log('there was a problem authenticating - Invalid Credentials')
            logger.logError(`Login: authUser - there was a problem authenticating - Invalid Credentials`, null, 'authentication');
            await loginLock(req, user_email);

            user_info.session_w3_login_result = 'Incorrect Password'
            user_info.session_token_login_result = 'Incorrect Password'
            user_info.qualify_authorization = 'Not Authorized'
            await insert_session_monitor(user_info, user_doc_bp)
            res.status(501).json(auth_json); // status code 501 = authentication error
            return;
        }
        //set failed login in mongo as empty
        if (userProfile && userProfile.login_failed_count && userProfile.login_failed_count !== 0) {
            console.log('clear lock info');
            await user.update_user_data(userProfile._id, { $set: { login_failed_count: 0, login_lock_time: null } });
        }
        auth_json.auth = 0;

    } catch (error) {
        auth_json.auth = 1;
        console.log('there was a problem authenticating ');
        logger.logError(`Login: authUser - there was a problem authenticating: ${error.message}`, null, 'authentication');
        await loginLock(req, user_email);

        user_info.session_w3_login_result = 'Email Not Found'
        user_info.session_token_login_result = 'Email Not Found'
        user_info.qualify_authorization = 'Not Authorized'
        await insert_session_monitor(user_info, user_doc_bp)
        res.status(501).json(auth_json); // status code 501 = authentication error
        return;
    }
    console.log('user_doc_bp.uid=' + user_doc_bp.uid);
    //when user have to reset w3id password as too many time input wrong password, will pass auth but uid is null.
    if (!user_doc_bp.uid) {
        auth_json.auth = 1;
        console.log('there was a problem authenticating ');
        logger.logError('there was a problem authenticating when user login: ' + user_email, null, 'authentication');
        await loginLock(req, user_email);

        user_info.session_w3_login_result = 'Unsuccessful'
        user_info.session_token_login_result = 'Unsuccessful'
        user_info.qualify_authorization = 'Not Authorized'
        await insert_session_monitor(user_info, user_doc_bp)
        res.status(501).json(auth_json); // status code 501 = authentication error
        return;
    }

    /* Generate userBasicInfo
    object with a subset of userInfo (Employee SN, Preferred name and mail)
    it is used to generate jwt token along with secret key in .env file
    this will be returned to loginPage in client along with the token in the responseObj Object
    */
    const userBasicInfo = {
        id: user_doc_bp.uid,
        name: user_doc_bp.preferredFirstName,
        username: user_doc_bp.mail,
    };

    console.log(`USER BASIC INFO: ${userBasicInfo}`);
    auth_json.user_talent_id = user_doc_bp.uid;
    // store JWT token
    try {
        // Generate token with user basic info to return to the client
        // Set token valid period to 8 hours
        const expirationTime = '8h';
        const token = jwt.sign(userBasicInfo, process.env.JWT_KEY, { expiresIn: expirationTime });
        console.log("token: " + token);
        auth_json.token = token;
        user_info.session_key = token;

    } catch (e) {
        console.log(e);
        console.log('problem generating web token, contact support');
        logger.logError(`Login: authUser - problem generating web token, contact support`, null, 'authentication');

        user_info.session_token_login_result = 'Unsucessful'
        user_info.qualify_authorization = 'Not Authorized'
        await insert_session_monitor(user_info, user_doc_bp)
        res.status(201).json(auth_json);
        return;
    }


    // check if user exists in database
    let user_record;
    let user_type;
    try {
        // MongoDB removed - user lookup disabled
        // Use the BP record as the user record
        user_record = null;
    } catch (e) {
        logger.logError(`Login: authUser - user lookup error: ${e.message}`, e, 'authentication')
    }

    // user record is found in database
    if (user_record != null) {
        // console.log(user_record);
        console.log("user record FOUND")
        logger.logInfo('Login: authUser - user record FOUND:' + user_email, 'authentication');
        // check if user is approved for access to qualify
        let approved = user_record.isApproved;
        let archived = user_record.isArchived;
        // if not approved
        if (approved !== true && archived !== true) {
            console.log("user not approved");
            logger.logInfo('Login: authUser - user not approved', 'authentication');
            // set auth_json to 1 (false) and return
            auth_json.approved = 1;
            auth_json.archived = 1;
        } else if (archived === true && approved === true) {
            // set auth_json to 0(true) and return
            console.log("User is archived")
            logger.logInfo('Login: authUser - user is archived', 'authentication');
            auth_json.archived = 0;
            auth_json.approved = 0;
        }
        else if (archived === false && approved === true) {
            console.log("user approved to access qualify");
            logger.logInfo('Login: authUser - user approved to access qualify', 'authentication');
            // if approved, re-determine user type and update record
            //user_type = await auth.getUserType(user_record);
            console.log(user_record.userType);
            logger.logInfo('user login :' + user_email, 'authentication');
            //user_record.userType = user_type;
            // return to client
            auth_json.approved = 0;
            auth_json.archived = 1;
        } else {
            console.log("ERROR with approval & archive user status.")
            logger.logError('Login: authUser - ERROR with approval & archive user status.', null, 'authentication');
        }
    }

    // user record is not found in database
    else {
        console.log("user not in database");
        logger.logInfo('Login: authUser - user not in database', 'authentication');
        user_record = user_doc_bp;

        // add manager_uid field to record
        let manager_uid = user_record.manager;
        manager_uid = manager_uid.split(',')[0].split('=')[1];
        user_record['manager_uid'] = manager_uid;
        user_record['access_req_date'] = date.toLocaleDateString();;
        //determine user's type and update record
        //user_type = await auth.getUserType(user_record);

        // initializing archive status & date
        user_record['isArchived'] = false;
        user_record['dateArchived'] = '';

        if (user_record.ismanager === 'Y') {
            user_type = 'manager';
            user_record['userType'] = "manager";
        } else {
            user_type = "employee";
            user_record['userType'] = "employee";
        }


        //add delegate_uid field if user is a manager
        if (user_type == 'manager') {
            user_record['delegate_uid'] = '';
        }

        // initialize isApproved
        user_record['isApproved'] = false;

        // determine initial approval to access Qualify
        try {
            user_record = await auth.updateInitialApproval(user_type, user_record);

            // update auth_json and return to client
            if (user_record.isApproved === true) {
                auth_json.approved = 0;

            } else {
                auth_json.approved = 1;
            }
            console.log("BLEH", auth_json.approved, auth_json.archived)


        } catch (e) {
            console.log(e);
        }

        // MongoDB removed - user record storage disabled
        console.log('user record processing completed');
        logger.logInfo('Login: authUser - user record processing completed', 'authentication');


        // return to client and include user's name, username and manager_uid

    }

    auth_json.name = `${user_record.preferredFirstName} ${user_record.sn}`;
    auth_json.username = user_record.mail;
    auth_json.manager_uid = user_record.manager_uid;
    auth_json.user_type = user_record.userType;
    auth_json.isDelegate = user_record.isDelegate;

    if (user_record.ismanager == 'Y') { auth_json.ismanager = true } else { auth_json.ismanager = false }

    console.log(`AUTH JSON ${auth_json}`);
    await insert_session_monitor(user_info, user_doc_bp)
    res.status(201).json(auth_json);

}

async function insert_session_monitor(user, user_doc_bp) {
    // MongoDB removed - session monitoring disabled
    logger.logInfo(`Session_Monitor - user login information - user_email: ${auth_json.username}`, 'authentication');
}

exports.update_session_monitor = async (req, res) => {
    try {
        const user_email = req.body.user;
        const session_key = req.body.token;
        const date = new Date().toLocaleString()

        console.log('update session expires start')
        console.log('user_email: ', user_email)
        console.log('date: ', date)
        console.log('session_key: ', session_key)
        logger.logInfo(`Session_Monitor - update session expires - user_email: ${user_email}, date: ${date}, session_key: ${session_key}`, 'authentication');
        // MongoDB removed - session monitoring disabled
        logger.logInfo(`Session_Monitor - session expired for user: ${user_email}`, 'authentication')
        console.log('update session expires end')
        res.status(201).json();
    } catch (error) {
        logger.logError(`Session_Monitor - update session expires error: ${error.message}`, null, 'authentication');
        res.status(501).json();
    }

}

