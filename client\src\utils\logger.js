/**
 * Logger utility for the client
 * Writes logs to local storage and console
 */

// Maximum number of log entries to keep in local storage
const MAX_LOG_ENTRIES = 1000;

// Local storage keys
const INFO_LOG_KEY = 'client_info_logs';
const ERROR_LOG_KEY = 'client_error_logs';

/**
 * Format a log message with timestamp and source
 * @param {string} message - The message to log
 * @param {string} source - The source of the log (e.g., function name, component)
 * @returns {string} - Formatted log message
 */
function formatLogMessage(message, source) {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${source}] ${message}`;
}

/**
 * Get logs from local storage
 * @param {string} key - The local storage key
 * @returns {Array} - Array of log entries
 */
function getLogsFromStorage(key) {
  try {
    const logs = localStorage.getItem(key);
    return logs ? JSON.parse(logs) : [];
  } catch (error) {
    console.error(`Error reading logs from local storage: ${error.message}`);
    return [];
  }
}

/**
 * Save logs to local storage
 * @param {string} key - The local storage key
 * @param {Array} logs - Array of log entries
 */
function saveLogsToStorage(key, logs) {
  try {
    // Trim logs if they exceed the maximum number
    if (logs.length > MAX_LOG_ENTRIES) {
      logs = logs.slice(-MAX_LOG_ENTRIES);
    }
    localStorage.setItem(key, JSON.stringify(logs));
  } catch (error) {
    console.error(`Error saving logs to local storage: ${error.message}`);
  }
}

/**
 * Write a message to the info log
 * @param {string} message - The message to log
 * @param {string} source - The source of the log
 */
function logInfo(message, source = 'Client') {
  try {
    const formattedMessage = formatLogMessage(message, source);

    // Log to console in development environment
    if (process.env.NODE_ENV !== 'production') {
      console.log(`INFO: ${source} - ${message}`);
    }

    // Save to local storage
    const logs = getLogsFromStorage(INFO_LOG_KEY);
    logs.push(formattedMessage);
    saveLogsToStorage(INFO_LOG_KEY, logs);
  } catch (error) {
    // If logging fails, log to console as fallback
    console.log(`Failed to save info log: ${message}`, error);
  }
}

/**
 * Write an error message to the error log
 * @param {string} message - The error message to log
 * @param {Error|string} error - The error object or message
 * @param {string} source - The source of the error
 */
function logError(message, error, source = 'Client') {
  try {
    let errorMessage = message;

    // Add error details if available
    if (error) {
      if (error instanceof Error) {
        errorMessage += `\nError: ${error.message}`;
        if (error.stack) {
          errorMessage += `\nStack: ${error.stack}`;
        }
      } else if (typeof error === 'object') {
        errorMessage += `\nError: ${JSON.stringify(error)}`;
      } else {
        errorMessage += `\nError: ${error}`;
      }
    }

    const formattedMessage = formatLogMessage(errorMessage, source);

    // Log to console in development environment
    if (process.env.NODE_ENV !== 'production') {
      console.error(`ERROR: ${source} - ${message}`, error);
    }

    // Save to local storage
    const logs = getLogsFromStorage(ERROR_LOG_KEY);
    logs.push(formattedMessage);
    saveLogsToStorage(ERROR_LOG_KEY, logs);
  } catch (err) {
    // If logging fails, log to console as fallback
    console.error(`Failed to save error log: ${message}`, error);
  }
}

/**
 * Get all info logs
 * @returns {Array} - Array of info log entries
 */
function getInfoLogs() {
  return getLogsFromStorage(INFO_LOG_KEY);
}

/**
 * Get all error logs
 * @returns {Array} - Array of error log entries
 */
function getErrorLogs() {
  return getLogsFromStorage(ERROR_LOG_KEY);
}

/**
 * Clear all info logs
 */
function clearInfoLogs() {
  saveLogsToStorage(INFO_LOG_KEY, []);
}

/**
 * Clear all error logs
 */
function clearErrorLogs() {
  saveLogsToStorage(ERROR_LOG_KEY, []);
}

/**
 * Export logs to a downloadable text file
 * @param {string} type - The type of logs to export ('info' or 'error')
 */
function exportLogs(type) {
  try {
    // Get logs based on type
    const logs = type === 'info' ? getInfoLogs() : getErrorLogs();

    if (logs.length === 0) {
      console.log(`No ${type} logs to export`);
      return;
    }

    // Create a text file with the logs
    const content = logs.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    // Create a link to download the file
    const a = document.createElement('a');
    a.href = url;
    a.download = `${type}_logs_${new Date().toISOString().replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
  } catch (error) {
    console.error(`Error exporting ${type} logs:`, error);
  }
}

export default {
  logInfo,
  logError,
  getInfoLogs,
  getErrorLogs,
  clearInfoLogs,
  clearErrorLogs,
  exportLogs
};
