const configSwitch = require("./config.js");
const config = configSwitch.config();
const ibmdb = require("ibm_db");
const ldap = require("ldapjs");
const logger = require('../utils/logger');
const path = require('path');
const xls = require('xlsjs');



//each exoorts, calls a different function. Pass values from client and the callback
exports.check_db = (req, res) => {

  check_db(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.get_pns_from_excel = (req, res) => {

  get_pns_from_excel(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.check_db2 = (req, res) => {

  check_db2(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.get_pn_fpy_volumes = (req, res) => {

  get_pn_fpy_volumes(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.get_fail_count_fpy = (req, res) => {

  get_fail_count_fpy(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.get_defect_percentages_fpy = (req, res) => {

  get_defect_percentages_fpy(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.get_xfactor_base = (req, res) => {

  get_xfactor_base(req.body, function (err, data) {

    res.status(201).json(data);
  });

};

exports.createPnPercentageArray = (req, res) => {

  createPnPercentageArray(req.body, function (err, data) {

    res.status(201).json(data);
  });

};



async function check_db(values, callback) {

  let responseObj = {
    status_res: null,
    sql_state: null,
    sql_message: null,
    sql_error_msg: null,
    output: null,
    sql_code: null,
    data: []
  };

  let user_type = values["user_type"];
  let action = values["action"];

  // Permissions check simulation (replace with actual function)
  let reply = true;
  let err = false;

  if (reply) {
    console.log("Opening DB2 connection to R0ADB2");

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, async function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      } else {
        console.log("Connected to R0ADB2");

        // Run the query
        conn.query('select a.DEFECT_ID, a.PART_NUM,a.PART_SER, a.INCIDENT_DATE, a.PROCESS, b.ROOT_CAUSE_1, b.ROOT_CAUSE_2 from QODS.GEM_DEFECT as a join QEVAL.GEM_DEFECT_UPDATES as b on a.DEFECT_ID = b.DEFECT_ID where a.PART_NUM = ?', ['0000003HD554'], async function (err, rows) {
          if (err) {
            // Log and handle query error
            console.error("Query Error:", err);
            responseObj.sql_error_msg = err.message;
            return callback(err, responseObj);
          } else {
            // Success
            responseObj.status_res = "success";
            responseObj.output = rows;
            responseObj.sql_code = 0;

            console.log("Query result:", rows);

            // Log success
            logger.logInfo(`Query executed successfully for user_type: ${user_type} and action: ${action}`, 'com.controller');
            return callback(null, responseObj);
          }
        });

        // Close the connection after the query is completed
        conn.close(function () {
          console.log("Connection to R0ADB2 closed");
        });
      }
    });

  } else {
    // If permission is denied, return a permission error
    responseObj.status_res = `Permissions DENIED for the following values: ${user_type} ${action}`;
    logger.logError(`Permissions DENIED for user_type: ${user_type} and action: ${action}`, null, 'com.controller');
    return callback(new Error("Permissions denied"), responseObj);
  }
}


async function get_pns_from_excel(values, callback) {
  let responseObj = {
    pns: [],
    status_res: null
  };

  try {
    // Load the Excel file
    const filePath = path.join(__dirname, `../excel/CommodityGroupings.xlsx`);
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Filter rows where "Group" matches values["group"]
    const matchingRows = sheet.filter(row => row.Commodity === values["commodity"]);
    console.log("DT1", matchingRows)
    // Extract "PN" values from matching rows
    responseObj.pns = [...new Set(matchingRows.map(item => item.PN))];

    responseObj.status_res = "success"
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error:", err);
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

function get_fail_count_fpy(values, callback) {


  let responseObj = {
    status_res: null,
    counts_by_period: []
  };
  console.log("Opening DB2 connection to R0ADB2");

  // Get the DB2 connection configuration for R0ADB2
  const config = configSwitch.config('R0ADB2');
  let connString = "DRIVER={DB2};"
    + "DATABASE=" + 'R0ADB2' + ";"
    + "UID=" + config.database_user + ";"
    + "PWD=" + config.database_password + ";"
    + "HOSTNAME=" + config.database_host + ";"
    + "PORT=" + config.database_port;

  // Open the connection
  ibmdb.open(connString, function (err, conn) {
    if (err) {
      // Log and handle error
      console.error("Error connecting to DB:", err);
      responseObj.sql_error_msg = err.message;
      return callback(err, responseObj);
    }

    console.log("Connected to R0ADB2");
    const pnPlaceholders = values['PN'].map(() => '?').join(', ');
    // Run the query
    conn.query(`SELECT PART_NUM,
        COUNT(*) AS count FROM QODS.GEM_DEFECT WHERE PART_NUM IN (${pnPlaceholders}) AND INCIDENT_DATE > ? AND INCIDENT_DATE < ? GROUP BY PART_NUM;` , [...values["PN"], values["startDate"], values["endDate"]], function (err, rows) {
      if (err) {
        // Log and handle query error
        console.error("Query Error:", err);
        responseObj.sql_error_msg = err.message;
        conn.close(function() {
          console.log("Connection to R0ADB2 closed");
          return callback(err, responseObj);
        });
      } else {

        responseObj.fail_count = rows.length;
        responseObj.output = rows;
        responseObj.status_res = "success";
        responseObj.sql_code = 0;

        console.log("Query result:", rows);

        // Close the connection and call the callback
        conn.close(function() {
          console.log("Connection to R0ADB2 closed");
          return callback(null, responseObj);
        });
      }
    });
  });

}
async function get_pn_fpy_volumes(values, callback) {

  let responseObj = {
    status_res: null,
    sql_state: null,
    sql_message: null,
    sql_error_msg: null,
    output: null,
    sql_code: null,
    data: []
  };

  let user_type = values["user_type"];
  let action = values["action"];

  // Permissions check simulation (replace with actual function)
  let reply = true;
  let err = false;

  if (reply) {
    console.log("Opening DB2 connection to QRYPROD");

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('QRYPROD');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, async function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      } else {
        console.log("Connected to QRYPROD");
        const pnPlaceholders = values['PN'].map(() => '?').join(', ');
        // Run the query
        conn.query(`SELECT
        Q2QDPN,
        COUNT(*) AS count
    FROM (
        SELECT
            Q2QDPN,
            Q2QDSQ,
            MIN(Q2OUTD) AS Q2OUTD,
            MIN(Q2OUTT) AS Q2OUTT
        FROM MFS2PCOMN.FCSPQD20
        WHERE Q2QDPN IN (${pnPlaceholders})
          AND Q2DEAC != 'Y'
          AND Q2FGID = 'PROCFULT'
        GROUP BY
            Q2QDPN,
            Q2QDSQ
    ) AS Q1
    WHERE Q2OUTD > ? AND Q2OUTD < ?
    GROUP BY Q2QDPN;`, [...values["PN"],values["startDate"], values["endDate"]], async function (err, rows) {
          if (err) {
            // Log and handle query error
            console.error("Query Error:", err);
            console.log("DAN", values["PN"] )
            responseObj.sql_error_msg = err.message;
            return callback(err, responseObj);
          } else {
            responseObj.vol_count = rows.length
            responseObj.status_res = "success";
            responseObj.output = rows;
            responseObj.sql_code = 0;

            console.log("Query result:", rows);

            // Log success
            logger.logInfo(`Query executed successfully for user_type: ${user_type} and action: ${action}`, 'com.controller');
            return callback(null, responseObj);
          }
        });

        // Close the connection after the query is completed
        conn.close(function () {
          console.log("Connection to QRYPROD closed");
        });
      }
    });

  } else {
    // If permission is denied, return a permission error
    responseObj.status_res = `Permissions DENIED for the following values: ${user_type} ${action}`;
    logger.logError(`Permissions DENIED for user_type: ${user_type} and action: ${action}`, null, 'com.controller');
    return callback(new Error("Permissions denied"), responseObj);
  }
}

async function get_defect_percentages_fpy(values, callback) {
  let responseObj = {
    status_res: null,
    fail_count: 0,
    vol_count: 0,
    percentages: {},
    sql_error_msg: null
  };

  const pnPlaceholders = values['PN'].map(() => '?').join(', ');
  //   // Sample end date string
  // const endDateString = values["endDate"];

  // // Convert the string to a Date object
  // const endDate = new Date(endDateString);

  // // Create a new date object for six months ago
  // const sixMonthsAgo = new Date(endDate);
  // sixMonthsAgo.setMonth(endDate.getMonth() - 6);

  // // Format the date to the desired format (e.g., 'YYYY-MM-DD')
  // const year = sixMonthsAgo.getFullYear();
  // const month = String(sixMonthsAgo.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  // const formattedDate = `${year}-${month}-01`; // Setting the day to the first of the month

  try {
    // Connect to R0ADB2 to get defect counts
    console.log("Opening DB2 connection to R0ADB2");
    const configFail = configSwitch.config('R0ADB2');
    let connStringFail = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + configFail.database_user + ";"
      + "PWD=" + configFail.database_password + ";"
      + "HOSTNAME=" + configFail.database_host + ";"
      + "PORT=" + configFail.database_port;

    const connFail = await ibmdb.open(connStringFail);

    const failQuery = `SELECT
    PART_NUM,
    VARCHAR_FORMAT(INCIDENT_DATE, 'YYYY-MM') AS YEAR_MONTH,
    COUNT(*) AS count
FROM
    QODS.GEM_DEFECT
WHERE
    PART_NUM IN (${pnPlaceholders})
    AND INCIDENT_DATE >= ?
    AND INCIDENT_DATE < ?
GROUP BY
    PART_NUM,
    VARCHAR_FORMAT(INCIDENT_DATE, 'YYYY-MM')
ORDER BY
    YEAR_MONTH DESC;`;


// Ensure you pass the PN values first, followed by the dates
const failRows = await new Promise((resolve, reject) => {
  connFail.query(failQuery, [...values["PN"], values["startDate"], values["endDate"]], function (err, rows) {
    if (err) return reject(err);
    resolve(rows);
  });
});

    responseObj.fail_count = failRows.length;
    responseObj.fail_output = failRows;
    console.log("Defect count query result:", failRows);

    connFail.close();
    // return callback(null, responseObj);

    // Connect to QRYPROD to get volume counts
    console.log("Opening DB2 connection to QRYPROD");
    const configVol = configSwitch.config('QRYPROD');
    let connStringVol = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + configVol.database_user + ";"
      + "PWD=" + configVol.database_password + ";"
      + "HOSTNAME=" + configVol.database_host + ";"
      + "PORT=" + configVol.database_port;

    const connVol = await ibmdb.open(connStringVol);
    const volQuery = `SELECT
    Q2QDPN AS PART_NUM,
    COUNT(*) AS count,
    VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS year_month
FROM (
    SELECT
        Q2QDPN,
        Q2QDSQ,
        MIN(Q2OUTD) AS Q2OUTD,
        MIN(Q2OUTT) AS Q2OUTT
    FROM
        MFS2PCOMN.FCSPQD20
    WHERE
        Q2QDPN IN (${pnPlaceholders})
        AND Q2DEAC != 'Y'
        AND Q2FGID = 'PROCFULT'
    GROUP BY
        Q2QDPN, Q2QDSQ
) AS Q1
WHERE
    Q2OUTD > ?
    AND Q2OUTD < ?
GROUP BY
    Q2QDPN,
    VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM')
ORDER BY
    year_month
`;

    const volRows = await new Promise((resolve, reject) => {
      connVol.query(volQuery, [...values["PN"], values["startDate"], values["endDate"]], function (err, rows) {
        if (err) return reject(err);
        resolve(rows);
      });
    });

    responseObj.vol_count = volRows.length;
    responseObj.vol_output = volRows;
    console.log("Volume count query result:", volRows);

      // grouping based on CommodityGroupings
      const filePath = path.join(__dirname, `../excel/CommodityGroupings.xlsx`);
      const workbook = xls.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);


      responseObj.fail_output.forEach(failRow => {
        const part = failRow.PART_NUM;
        const yearMonth = failRow.YEAR_MONTH;

        const groupMatch = sheet.find(sheetRow => sheetRow.PN === failRow.PART_NUM);

        // If a match is found, add the Group to the volRow
        if (groupMatch) {
        failRow.GROUP = groupMatch.Group; // Add Group dynamically to volRow
        } else {
        failRow.GROUP = "UNKNOWN"; // Optionally handle cases where no match is found
        }


        // Find the corresponding volume row for the same part and year_month
        const volumeRow = responseObj.vol_output.find(v => v.PART_NUM === part && v.YEAR_MONTH === yearMonth);

        if (volumeRow) {
          // Initialize part entry if not already present
          if (!responseObj.percentages[part]) {
            responseObj.percentages[part] = {};
          }

          const groupMatch2 = sheet.find(sheetRow => sheetRow.PN === volumeRow.PART_NUM);
            // If a match is found, add the Group to the volRow
          if (groupMatch2) {
            volumeRow.GROUP = groupMatch2.Group; // Add Group dynamically to volRow
            } else {
            volumeRow.GROUP = "UNKNOWN"; // Optionally handle cases where no match is found
            }

          // Calculate the percentage for this part and year_month
          responseObj.percentages[part][yearMonth] = ((failRow.COUNT / volumeRow.COUNT)).toFixed(2);
        }
      });
    responseObj.status_res = "success";

    // Close the volume connection
    connVol.close();
    return callback(null, responseObj);

  } catch (err) {
    console.error("Error:", err);
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

async function get_xfactor_base(values, callback) {
  let responseObj = {
    status_res: null,
    percentages: {},
    sql_error_msg: null
  };

  // step 1: get YTD (from end date) FPY for each PN

  const pnPlaceholders = values['PN'].map(() => '?').join(', ');

  // Convert the string to a Date object
  const endDate = new Date(values["endDate"]);

  // Format the date to the desired format (e.g., 'YYYY-MM-DD')
  const year = endDate.getFullYear();
  const startDate = `${year}-01-01`; // Setting the day to the first of the month
  try {
    // Connect to R0ADB2 to get defect counts
    console.log("Opening DB2 connection to R0ADB2");
    const configFail = configSwitch.config('R0ADB2');
    let connStringFail = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + configFail.database_user + ";"
      + "PWD=" + configFail.database_password + ";"
      + "HOSTNAME=" + configFail.database_host + ";"
      + "PORT=" + configFail.database_port;
    const connFail = await ibmdb.open(connStringFail);

    const failQuery = `SELECT
    PART_NUM,
    COUNT(*) AS count
FROM
    QODS.GEM_DEFECT
WHERE
    PART_NUM IN (${pnPlaceholders})
    AND INCIDENT_DATE >= ?
    AND INCIDENT_DATE < ?
GROUP BY
    PART_NUM`;


// Ensure you pass the PN values first, followed by the dates
const failRows = await new Promise((resolve, reject) => {
  connFail.query(failQuery, [...values["PN"], startDate, values["endDate"]], function (err, rows) {
    if (err) return reject(err);
    resolve(rows);
  });
});

    responseObj.fail_count = failRows.length;
    responseObj.fail_output = failRows;
    console.log("Defect count query result:", failRows);

    connFail.close();
    // return callback(null, responseObj);

    // Connect to QRYPROD to get volume counts
    console.log("Opening DB2 connection to QRYPROD");
    const configVol = configSwitch.config('QRYPROD');
    let connStringVol = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + configVol.database_user + ";"
      + "PWD=" + configVol.database_password + ";"
      + "HOSTNAME=" + configVol.database_host + ";"
      + "PORT=" + configVol.database_port;
    const connVol = await ibmdb.open(connStringVol);
    const volQuery = `SELECT
    Q2QDPN AS PART_NUM,
    COUNT(*) AS count
FROM (
    SELECT
        Q2QDPN,
        Q2QDSQ,
        MIN(Q2OUTD) AS Q2OUTD,
        MIN(Q2OUTT) AS Q2OUTT
    FROM
        MFS2PCOMN.FCSPQD20
    WHERE
        Q2QDPN IN (${pnPlaceholders})
        AND Q2DEAC != 'Y'
        AND Q2FGID = 'PROCFULT'
    GROUP BY
        Q2QDPN, Q2QDSQ
) AS Q1
WHERE
    Q2OUTD > ?
    AND Q2OUTD < ?
GROUP BY
    Q2QDPN
ORDER BY
    PART_NUM;
`;

    const volRows = await new Promise((resolve, reject) => {
      connVol.query(volQuery, [...values["PN"], startDate, values["endDate"]], function (err, rows) {
        if (err) return reject(err);
        resolve(rows);
      });
    });

    responseObj.vol_count = volRows.length;
    responseObj.vol_output = volRows;
    console.log("Volume count query result:", volRows);

    // grouping based on CommodityGroupings
    const filePath = path.join(__dirname, `../excel/CommodityGroupings.xlsx`);
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

      responseObj.fail_output.forEach(failRow => {
        const part = failRow.PART_NUM;
        const volumeRow = responseObj.vol_output.find(v => v.PART_NUM === part);

        const groupMatch = sheet.find(sheetRow => sheetRow.PN === failRow.PART_NUM);

        // If a match is found, add the Group to the volRow
        if (groupMatch) {
        failRow.GROUP = groupMatch.Group; // Add Group dynamically to volRow
        } else {
        failRow.GROUP = "UNKNOWN"; // Optionally handle cases where no match is found
        }

        if (volumeRow) {
          // Initialize part entry if not already present
          if (!responseObj.percentages[part]) {
            responseObj.percentages[part] = {};
          }
          const groupMatch2 = sheet.find(sheetRow => sheetRow.PN === volumeRow.PART_NUM);
          // If a match is found, add the Group to the volRow
        if (groupMatch2) {
          volumeRow.GROUP = groupMatch2.Group; // Add Group dynamically to volRow
          } else {
          volumeRow.GROUP = "UNKNOWN"; // Optionally handle cases where no match is found
          }

          // Calculate the percentage for this part and year_month
          responseObj.percentages[part] = ((failRow.COUNT / volumeRow.COUNT)).toFixed(2);
        }
      });
    responseObj.status_res = "success";

    // Close the volume connection
    connVol.close();
    return callback(null, responseObj);

  } catch (err) {
    console.error("Error:", err);
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

async function createPnPercentageArray(values, callback) {
  responseObj = {
    data : [],
  }
  try {
    // Call get_defect_percentages_fpy API to get monthly percentages
    const monthlyResponse = await new Promise((resolve, reject) => {
      get_defect_percentages_fpy(values, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    // Call get_xfactor_base API to get yearly base defect rates
    const yearlyResponse = await new Promise((resolve, reject) => {
      get_xfactor_base(values, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    // Check if both responses have success status
    if (monthlyResponse.status_res !== "success" || yearlyResponse.status_res !== "success") {
      throw new Error("Error fetching data from APIs");
    }

    // Initialize result array
    const resultArray = [];

    // Extract monthly percentages and yearly base rates
    const monthlyPercentages = monthlyResponse.percentages;
    const yearlyRates = yearlyResponse.percentages;

    const monthlyArray = aggregatePNs(monthlyResponse.fail_output, monthlyResponse.vol_output)
    const yearlyArray = aggregatePNs(yearlyResponse.fail_output, yearlyResponse.vol_output)
    const com_percentages = calcPerc(monthlyArray.def_totals,monthlyArray.vol_totals )
    const base_percentages = calcPerc(yearlyArray.def_totals,yearlyArray.vol_totals )
    const XFactors = calcXFactor(com_percentages, base_percentages)

  //FOR GROUPS included
    const result = [];
    const resultHeat = [];
// `${Period}-01T00:00:00.000Z`

    for (const [group, periods] of Object.entries(XFactors)) {
      for (const [Period, value] of Object.entries(periods)) {
        result.push({ group, date: new Date(Period.slice(0,4), Period.slice(5, 7) - 1,1 ), value});
        resultHeat.push({ group, Period, value});
      }
    }

          // Sort the result array based on the date field
      result.sort((a, b) => a.date - b.date);

      // Sort the resultHeat array based on the Period field
      resultHeat.sort((a, b) => {
          // Parse Period strings into comparable Date objects
          const dateA = new Date(a.Period.slice(0, 4), a.Period.slice(5, 7) - 1, 1);
          const dateB = new Date(b.Period.slice(0, 4), b.Period.slice(5, 7) - 1, 1);
          return dateA - dateB;
      });
    //FOR INDIVIDUAL PNS included
    // Loop through each part number in monthlyPercentages
    for (const partNum in monthlyPercentages) {
      const monthlyData = monthlyPercentages[partNum];
      const baseDefectRate = parseFloat(yearlyRates[partNum] || 0); // Default to 0 if no base rate

      // For each month, create an object with required fields
      for (const month in monthlyData) {
        const percentageForMonth = parseFloat(monthlyData[month]);
        const x_factor = (percentageForMonth/baseDefectRate).toFixed(2)
        // Push the object into resultArray
        resultArray.push({
          group: partNum,
          defRate: percentageForMonth,
          Period: `${month}-01T04:00:00.000Z`,
          Target: baseDefectRate,
          value: x_factor
        });
      }
    }
    console.log("PERCENTAGES: ", result)
    responseObj.status_res = "success"
    responseObj.data = result
    responseObj.data2 = resultHeat
    // Call the callback with the final formatted data
    return callback(null, responseObj);

  } catch (error) {
    console.error("Error creating PN percentage array:", error);
    responseObj.status_res = "error"
    return callback(error, null);
  }
}

function calcXFactor(def_rates, base_rates){
  const result = {};

  for (const key in def_rates) {
    result[key] = {};

    // Get the base rate for this key
    const baseRate = parseFloat(base_rates[key]?.undefined) || 1; // Default to 1 if undefined

    // Iterate over each month's rate
    for (const month in def_rates[key]) {
      const monthlyRate = parseFloat(def_rates[key][month]);
      result[key][month] = (monthlyRate / baseRate).toFixed(2); // Perform division and format to 2 decimal places
    }
  }

  return result
}

function calcPerc(def_totals, vol_totals){
  // Create the new object for A/B
const result = {};
const A = def_totals
const B = vol_totals

// Iterate over keys (e.g., SMP9, UNKNOWN)
for (const key in A) {
  result[key] = {};

  // Iterate over months in A[key]
  for (const month in A[key]) {
    if (B[key] && B[key][month] !== 0) {
      // Compute the ratio A/B for each month
      result[key][month] = (A[key][month] / B[key][month]).toFixed(2);
    } else {
      // Handle cases where B[key][month] is 0 or missing
      result[key][month] = null; // or some error value like NaN
    }
  }
}



  return result
}


function aggregatePNs(defect_array, vol_array) {
  const def_totals = {};
  const vol_totals = {};

  // Loop through each item in the defect array
  for (const item of defect_array) {
    const group = item.GROUP;
    const year_month = item.YEAR_MONTH;

    // Initialize the group if it doesn't exist
    if (!(group in def_totals)) {
      def_totals[group] = {};
    }

    // Initialize the year_month within the group if it doesn't exist
    if (!(year_month in def_totals[group])) {
      def_totals[group][year_month] = 0;
    }

    // Add the count to the specific year_month in the group
    def_totals[group][year_month] += item.COUNT;
  }

  // Loop through each item in the volume array (if needed)
  for (const item of vol_array) {
    const group = item.GROUP;
    const year_month = item.YEAR_MONTH;

    // Initialize the group if it doesn't exist
    if (!(group in vol_totals)) {
      vol_totals[group] = {};
    }

    // Initialize the year_month within the group if it doesn't exist
    if (!(year_month in vol_totals[group])) {
      vol_totals[group][year_month] = 0;
    }

    // Add the count to the specific year_month in the group
    vol_totals[group][year_month] += item.COUNT;
  }

  return { def_totals, vol_totals };
}
