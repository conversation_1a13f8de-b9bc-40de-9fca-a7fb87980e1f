<template>
  <div class="combo-chart-wrapper" :style="{ height: height }">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>
    <div v-else-if="!data || data.length === 0" class="no-data-message">
      No data available
    </div>
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import { ComboChart } from '@carbon/charts';
import '@carbon/charts/styles-g90.css';

export default {
  name: 'LineBarComboChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null,
      error: null,
      defaultOptions: {
        title: 'Failure Modes Comparison',
        axes: {
          left: {
            title: 'Count',
            mapsTo: 'value',
            scaleType: 'linear'
          },
          right: {
            title: '% of Total',
            mapsTo: 'percentage',
            scaleType: 'linear',
            domain: [0, 100]
          },
          bottom: {
            title: 'Failure Mode',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        height: this.height,
        width: '100%',
        legend: {
          alignment: 'center',
          enabled: true
        },
        tooltip: {
          enabled: true
        },
        comboChartTypes: [
          {
            type: 'simple-bar',
            options: {
              fillColors: ['#0f62fe']
            },
            correspondingDatasets: ['Count']
          },
          {
            type: 'line',
            options: {
              points: {
                enabled: true,
                radius: 5
              }
            },
            correspondingDatasets: ['Percentage']
          }
        ],
        data: {
          loading: this.loading
        }
      }
    };
  },
  computed: {
    chartOptions() {
      return {
        ...this.defaultOptions,
        ...this.options,
        height: this.height,
        data: {
          ...this.defaultOptions.data,
          ...this.options.data,
          loading: this.loading
        }
      };
    }
  },
  watch: {
    data: {
      handler() {
        this.renderChart();
      },
      deep: true
    },
    options: {
      handler() {
        this.updateOptions();
      },
      deep: true
    },
    height(newHeight) {
      if (this.chart) {
        this.chartOptions.height = newHeight;
        this.updateOptions();
      }
    },
    loading(newLoading) {
      if (this.chart) {
        this.chartOptions.data.loading = newLoading;
        this.updateOptions();
      }
    }
  },
  mounted() {
    this.renderChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    renderChart() {
      if (!this.data || this.data.length === 0 || !this.$refs.chartContainer) {
        return;
      }

      try {
        if (this.chart) {
          this.chart.destroy();
        }

        this.chart = new ComboChart(this.$refs.chartContainer, {
          data: this.data,
          options: this.chartOptions
        });

        this.error = null;
      } catch (err) {
        console.error('Error rendering chart:', err);
        this.error = 'Error rendering chart: ' + err.message;
      }
    },
    updateOptions() {
      if (this.chart) {
        this.chart.model.setOptions(this.chartOptions);
      }
    }
  }
};
</script>

<style scoped>
.combo-chart-wrapper {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message, .no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #da1e28;
  font-weight: bold;
}

.no-data-message {
  color: #8d8d8d;
}
</style>
