<template>
  <div ref="chartHolder">
    <!-- <ccv-simple-bar-chart ref="barChart" :data="data" :options="options"></ccv-simple-bar-chart> -->
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { SimpleBarChart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'HBarChart2',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    eventType: {
      type: Function,
      default: () => {}
    },
    period: {
      type: String,
      default: 'period'
    },
    process: {
      type: String,
      default: 'process'
    },
    commodity: {
      type: String,
      default: 'commodity'
    },
    highlightedItem: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      options: {
        title:  `${this.process} X-Factor Chart for ${this.period}`,
        tooltip: {
          customHTML: (data) => {
            let color_val = '';
            if (data[0].group === "Needs Attention"){
              color_val = "red"}
            else if(data[0].group === "Good"){
              color_val = "#00FF00"}
            else{color_val = "yellow"}
            return `
              <div>
        <ul class="multi-tooltip">
          <li>
            <div class="datapoint-tooltip">
              <div class="label">
                <p ">${data[0].codeName}</p>
              </div>
              <p style="color: ${color_val}; class="value">${data[0].xFactor.toFixed(2)}X</p>
            </div>
          </li>

        </ul>
      </div>
            `;
          }
        },
        axes: {
          left: {
            title: "Name",
            mapsTo: 'codeName',
            scaleType: 'labels',
          },
          bottom: {
            title: "xFactor",
            mapsTo: 'xFactor_scaled',
            scaleType: "linear",
            // domain: [0,3],
            ticks: {
              values: [0,1,2,3],
              formatter: (value) => `${value}X`
            },

            addSpaceOnEdges: 0,

            thresholds: [
              {
                value: 1,
                fillColor: 'blue',
                label: "Target"
              },
              {
                value: 1.2,
                fillColor: 'red',
                label: "Threshold"
              },
            ]
          }
        },
        grid: {x:{alignWithAxisTicks: true},
        y:{alignWithAxisTicks: true}},

        color: {
          scale: {
            'Good': '#00FF00',
            'Acceptable': 'yellow',
            'Needs Attention': 'red',
          }
        },
        height: '400px',

      }
    };
  },
  mounted() {
    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    this.chart = new SimpleBarChart(chartHolder, {
      data: this.data,
      options: this.options,
    });

    this.chart.services.events.addEventListener("bar-click", (e) => {
      this.eventType(e);
    });
  },
  watch: {
    data(newData) {
      // Update chart data
      this.chart.model.setData(newData);

    },
    period(newPeriod) {
      this.options.title = `${this.process} X-Factor Chart for ${newPeriod}`
      this.chart.model.setOptions(this.options);
    },
    process(newProcess) {
      this.options.title = `${newProcess} X-Factor Chart for ${this.period}`
      this.chart.model.setOptions(this.options);
    },
    highlightedItem(newItem) {
      if (newItem) {
        this.highlightBar(newItem);
      }
    }
  },
  methods: {
    // Method to highlight a specific bar by codeName
    highlightBar(codeName) {
      if (!this.chart || !codeName) return;

      console.log(`Highlighting bar for ${codeName}`);

      // Find the DOM elements in the chart
      const chartHolder = this.$refs.chartHolder;
      if (!chartHolder) return;

      // First try to find the text element with the codeName
      const textElements = chartHolder.querySelectorAll('text');
      let targetText = null;

      for (let i = 0; i < textElements.length; i++) {
        if (textElements[i].textContent === codeName) {
          targetText = textElements[i];
          break;
        }
      }

      if (targetText) {
        // Find the closest rect element (the bar)
        const parentGroup = targetText.closest('g');
        if (parentGroup) {
          // Look for the rect within this group or nearby groups
          let targetBar = parentGroup.querySelector('rect');

          // If not found in the immediate group, try to find it in a sibling group
          if (!targetBar) {
            const siblingGroups = parentGroup.parentElement.querySelectorAll('g');
            for (let i = 0; i < siblingGroups.length; i++) {
              const rect = siblingGroups[i].querySelector('rect');
              if (rect) {
                targetBar = rect;
                break;
              }
            }
          }

          // If we found a bar, highlight it
          if (targetBar) {
            this.applyHighlight(targetBar);
            return;
          }
        }
      }

      // If we couldn't find the bar through text, try looking at all bars
      const allBars = chartHolder.querySelectorAll('rect');

      // Find the bar that might correspond to our codeName
      if (allBars.length > 0) {
        // Find the index of our codeName in the data array
        const index = this.data.findIndex(item => item.codeName === codeName);

        if (index >= 0 && index < allBars.length) {
          this.applyHighlight(allBars[index]);
        } else {
          // Last resort: try to find a bar with a data attribute or similar that matches
          let found = false;
          allBars.forEach(bar => {
            // Check if this bar has any attribute that might identify it
            const dataValue = bar.getAttribute('data-value');
            const ariaLabel = bar.getAttribute('aria-label');

            if ((dataValue && dataValue.includes(codeName)) ||
                (ariaLabel && ariaLabel.includes(codeName))) {
              this.applyHighlight(bar);
              found = true;
            }
          });

          // If still not found, just highlight the first problematic bar as a fallback
          if (!found && allBars.length > 0) {
            // Find bars that might be red (problematic)
            const redBars = Array.from(allBars).filter(bar => {
              const fill = bar.getAttribute('fill');
              return fill === 'red' || fill === '#FF0000';
            });

            if (redBars.length > 0) {
              this.applyHighlight(redBars[0]);
            } else {
              // Last resort: just highlight the first bar
              this.applyHighlight(allBars[0]);
            }
          }
        }
      }
    },

    // Apply highlight styling to a bar element
    applyHighlight(barElement) {
      if (!barElement) return;

      // Remove any existing highlights
      const existingHighlights = this.$refs.chartHolder.querySelectorAll('.highlighted-bar');
      existingHighlights.forEach(el => {
        el.classList.remove('highlighted-bar');
        el.style.stroke = null;
        el.style.strokeWidth = null;
        el.style.fillOpacity = null;
      });

      // Store original values to restore later
      const originalFill = barElement.getAttribute('fill');
      const originalStroke = barElement.getAttribute('stroke');
      const originalStrokeWidth = barElement.getAttribute('stroke-width');

      // Add a class for styling
      barElement.classList.add('highlighted-bar');

      // Add a pulsing border effect
      barElement.style.stroke = '#FF5733';
      barElement.style.strokeWidth = '3px';

      // Optional: Temporarily change the fill opacity to make it more noticeable
      if (originalFill !== '#FF0000' && originalFill !== 'red') {
        barElement.style.fillOpacity = '0.8';
      }

      // Scroll to ensure the bar is visible
      barElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Create a temporary overlay indicator
      const rect = barElement.getBoundingClientRect();
      const indicator = document.createElement('div');
      indicator.style.position = 'absolute';
      indicator.style.left = `${rect.left}px`;
      indicator.style.top = `${rect.top - 30}px`; // Position above the bar
      indicator.style.background = '#FF5733';
      indicator.style.color = 'white';
      indicator.style.padding = '5px 10px';
      indicator.style.borderRadius = '4px';
      indicator.style.fontWeight = 'bold';
      indicator.style.zIndex = '1000';
      indicator.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
      indicator.textContent = 'Issue Here';
      indicator.style.animation = 'fadeIn 0.3s';
      document.body.appendChild(indicator);

      // Remove highlight and indicator after a delay
      setTimeout(() => {
        barElement.classList.remove('highlighted-bar');
        barElement.style.stroke = originalStroke || null;
        barElement.style.strokeWidth = originalStrokeWidth || null;
        barElement.style.fillOpacity = null;

        // Remove the indicator with a fade out effect
        indicator.style.animation = 'fadeOut 0.3s';
        setTimeout(() => {
          if (document.body.contains(indicator)) {
            document.body.removeChild(indicator);
          }
        }, 300);
      }, 5000); // Keep highlighted for 5 seconds
    }
  }
};
</script>

<style scoped>
.highlighted-bar {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { stroke-width: 3px; stroke-opacity: 1; }
  50% { stroke-width: 5px; stroke-opacity: 0.7; }
  100% { stroke-width: 3px; stroke-opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
}
</style>

