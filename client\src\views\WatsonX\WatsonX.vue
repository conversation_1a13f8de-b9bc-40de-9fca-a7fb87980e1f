<style scoped lang="scss">
@import "../../styles/carbon-utils";

.chart-page__grid {
  margin-top: $spacing-08;
}
</style>

<template>
  <cv-grid class="chart-page__grid">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />
    <div class="content-1">
          <LineChart
            v-if="line_chart_data.length > 0"
            :data="line_chart_data"
            :loading="loading"
          />
    </div>
    <div id="liveTranscript" style="border: 1px solid #ddd; padding: 10px; min-height: 50px;">
</div>
  </cv-grid>
</template>

<script>
import MainHeader from "@/components/MainHeader";
import LineChart from "../../components/LineChart/LineChart.vue";
import WatsonSpeech from 'watson-speech'

export default {
  name: "WatsonX",
  components: {
    MainHeader,
    LineChart,
  },
  data() {
    return {
      lightTile: true,
      watsonXChartData: [],
      expandedSideNav: false,
      useFixed: false,
      lastResponse: "",
      line_chart_data: [],
      comp1: "",
      comp2: "",
      time_qty: 0,
      time_var: "",
      formattedDate: new Date().toISOString().split("T")[0],
      loading: true,
      watsonAssistantInstance: null, // Store Watson Assistant instance
      watsonAssistantScript: null,  // Store reference to the script
      isRecording: false, 
      currentStream: null,

    };
  },
  mounted() {
    this.initWatsonAssistant();
    
  },
  beforeDestroy() { // For Vue 2
    this.cleanUpWatsonAssistant();
  },
  unmounted() { // For Vue 3
    this.cleanUpWatsonAssistant();
  },
  methods: {
    
    async load_line_chart() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        const startDate = new Date(new Date().setMonth(new Date().getMonth() - this.time_qty)).toISOString().split('T')[0]
        // Fetch data from the API
        fetch(process.env.VUE_APP_API_PATH + "populate_watsonx_line", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify({comp1: this.comp1, comp2: this.comp2, startDate: startDate, endDate: this.formattedDate}),
      })
        .then((response) => this.handleResponse(response))
        .then((data) => {
          if (data.status_res === "success") {
            this.line_chart_data = data.data;
            console.log("Received line data:", this.line_chart_data);
            this.loading = false;
          } 
          console.log(data);
        });
      }catch (error) {
        console.error("Error loading data:", error);
      }
    },

    async onStartRecord() {
  const stream = WatsonSpeech.SpeechToText.recognizeMicrophone({
    accessToken: "eyJraWQiOiIyMDI1MDEzMDA4NDQiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BJxg70W_tUFPw1b9Xtt8vVaOPfCna-np5-TekM9zaxS0ZGO4VVZqPekpvbrcsMN54zkfiRUwefnlNDfvGCCGkIwopuEzrDX8JTL6Tcwk0b4Bpuf3XkmP3fbMKRL1IqNtGpKC1tPKVWMC46D04JC8lQP-pj7vZlKFbmOXi1NgtoydPHqokFK6yfVWeXyggw-DN1A_UwDy09-Vj0XAOiUBJ6WQcemBNkz-0fqmeuPq78NN2dhBp2mgB6jTgOnUaJYccnjlAfeM4xTPFP5Y2fF01ZjqzTWoLixucnCOABUQPI4XjXY6VSeFDLVbr_LRIKuRWJqK1PPYY_IKcKtXy-zxJQ",
    model: 'en-US_Telephony',
    objectMode: true,
    interim_results: true,
  });
  
  stream.on('data', (data) => {
    if (data.results[0]) {
      const transcript = data.results[0].alternatives.map(message => message.transcript).join(' ');
      console.log("STREAM",data.results)
      if (data.results[0].final) {
        console.log("Final transcript:", transcript);
        this.sendTextToAssistant(transcript); // Send the final result
      } else {
        console.log("Interim transcript:", transcript);
        console.log("DAN", transcript)
        this.updateLiveTranscript(transcript); // Update UI with live text
      }
    }
  });

  stream.on('error', (err) => {
    console.error("Speech recognition error:", err);
  });

  this.currentStream = stream;
  this.setButtonState(true); // Indicate recording is active
},


// Function to update the UI with live transcription
updateLiveTranscript(text) {
  document.getElementById('liveTranscript').innerText = text;
}
,

onStopRecord() {
  if (this.currentStream) {
    console.log("Stopping now")
    this.currentStream.stop();
  }
  this.currentStream = null;
},

setButtonState(localIsRecording) {
  this.isRecording = localIsRecording;
  const label = this.isRecording ? 'Stop' : 'Start';
  document.querySelector('.RecordButton').innerHTML = `${label} recording`;
},

async onButtonClick() {
  this.setButtonState(!this.isRecording);
  if (this.isRecording) {
    this.onStartRecord();
  } else {
    this.onStopRecord();
  }
},
async sendTextToAssistant(text) {
  const sendObject = { input: { text } };
  this.watsonAssistantInstance.send(sendObject)

}, 



  async addRecordButton(instance) {
  const button = document.createElement('button');
  button.classList.add('RecordButton');
  button.innerHTML = 'Start recording';
  button.addEventListener('click', this.onButtonClick);
  instance.writeableElements.beforeInputElement.appendChild(button);
},



    initWatsonAssistant() {
      window.watsonAssistantChatOptions = {
        integrationID: "f5d2d722-39fe-4fcb-95e4-b4d417e94c19",
        headerConfig: {
          showRestartButton: true,
        },
        themeConfig: {
          useAITheme: true,
        },
        showLauncher: true,
        region: "us-south",
        serviceInstanceID: "cf4a41d8-0730-40fc-809b-a71900083fa5",
        onLoad: async (instance) => {
          this.watsonAssistantInstance = instance; // Store the instance for cleanup
          const handler = (event) => {
            const output_vars = event.data.context.skills["actions skill"].skill_variables;
            console.log("VARS2,",event.data.context)
            try {
              const cleanedText = output_vars.compareResult
                .replace(/^```json/, "")
                .replace(/```$/, "")
                .trim();
              console.log("RESPONSE JSON", JSON.parse(cleanedText));
              this.lastResponse = JSON.parse(cleanedText);
              this.comp1 = this.lastResponse.comp1;
              this.comp2 = this.lastResponse.comp2;
              this.time_qty = this.lastResponse.time_qty;
              this.time_var = this.lastResponse.var;
              this.load_line_chart();
            } catch (error) {
              console.error("Invalid JSON string:", output_vars.compareResult, error);
              this.lastResponse = null; // or handle the error as needed
            }
          };
          this.addRecordButton(instance)
          instance.on({ type: "receive", handler: handler });
          // Render and open Watson Assistant
          await instance.render();
          await instance.openWindow();
        },
      };

      setTimeout(() => {
        const script = document.createElement("script");
        script.src =
          "https://web-chat.global.assistant.watson.appdomain.cloud/versions/" +
          (window.watsonAssistantChatOptions.clientVersion || "latest") +
          "/WatsonAssistantChatEntry.js";

        // Add script to the DOM and store reference
        this.watsonAssistantScript = script;
        document.head.appendChild(script);
      });
    },
    cleanUpWatsonAssistant() {
      // Close the Watson Assistant window
      if (this.watsonAssistantInstance) {
        this.watsonAssistantInstance.destroy(); // Close widget and clean up listeners
        this.watsonAssistantInstance = null;
      }

      // Remove the script from the DOM
      if (this.watsonAssistantScript) {
        document.head.removeChild(this.watsonAssistantScript);
        this.watsonAssistantScript = null;
      }

      // Remove global variables
      if (window.watsonAssistantChatOptions) {
        delete window.watsonAssistantChatOptions;
      }
    },

    async handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },

  },
};


</script>
