<style scoped lang="scss">
@import "../../styles/carbon-utils";

.dashboard-container {
  min-height: 100vh;
  background-color: #161616;
}

.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333333;
}

.page-title {
  color: #f4f4f4;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0;
}

.action-controls {
  display: flex;
  gap: 1rem;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: #262626;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #333333;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-label {
  color: #8d8d8d;
  font-size: 0.875rem;
}

.search-box {
  flex-grow: 1;
  max-width: 300px;
}

/* Table styling */
.action-table {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table-header {
  background-color: #333333;
  padding: 1rem;
  border-bottom: 1px solid #444444;
}

.table-title {
  color: #f4f4f4;
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
}

/* Cell styling */
.edit-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #0f62fe;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.edit-icon:hover {
  opacity: 1;
}

.editable-field {
  display: flex;
  align-items: center;
}

.editable-field input {
  background-color: #333333;
  border: 1px solid #0f62fe;
  color: #f4f4f4;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.action-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-text {
  flex-grow: 1;
}

.see-more-button {
  background-color: #0f62fe;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.see-more-button:hover {
  background-color: #0353e9;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-in-progress {
  background-color: #0f62fe;
}

.status-completed {
  background-color: #42be65;
}

.status-blocked {
  background-color: #fa4d56;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.in-progress {
  background-color: rgba(15, 98, 254, 0.2);
  color: #78a9ff;
}

.status-badge.completed {
  background-color: rgba(66, 190, 101, 0.2);
  color: #6fdc8c;
}

.status-badge.blocked {
  background-color: rgba(250, 77, 86, 0.2);
  color: #ff8389;
}

/* Status banner */
.status-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.status-banner-in-progress {
  background-color: rgba(15, 98, 254, 0.1);
  border: 1px solid rgba(15, 98, 254, 0.3);
}

.status-banner-completed {
  background-color: rgba(66, 190, 101, 0.1);
  border: 1px solid rgba(66, 190, 101, 0.3);
}

.status-banner-blocked {
  background-color: rgba(250, 77, 86, 0.1);
  border: 1px solid rgba(250, 77, 86, 0.3);
}

.assignee-info {
  color: #f4f4f4;
  font-size: 0.875rem;
}

/* Modal actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Form styling */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.5rem;
}

/* Modal styles */
.action-modal {
  max-width: 800px;
}

.modal-content {
  padding: 1.5rem 0;
  color: #f4f4f4;
}

.modal-section {
  margin-bottom: 2rem;
}

.section-title {
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  color: #f4f4f4;
  border-bottom: 1px solid #333333;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.375rem;
}

.info-value {
  font-size: 1rem;
  color: #f4f4f4;
}

.action-details {
  background-color: #333333;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #444444;
}

.action-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #f4f4f4;
  font-size: 1rem;
}

.action-description {
  white-space: pre-line;
  margin-bottom: 1.5rem;
  color: #f4f4f4;
  line-height: 1.5;
}

/* Required field indicator */
.required-field {
  color: #fa4d56;
  margin-left: 4px;
  font-weight: bold;
}

/* Form validation styles */
.form-error {
  color: #fa4d56;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Form note */
.form-note {
  color: #8d8d8d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .search-box {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1.5rem 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .action-controls {
    width: 100%;
  }
}

/* Loading and error states */
.loading-indicator, .loading-message {
  color: #0f62fe;
  font-size: 14px;
  margin-top: 10px;
}

.error-message {
  color: #da1e28;
  font-size: 14px;
  margin-top: 10px;
}

.empty-message {
  color: #8d8d8d;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Progress bar styling */
.progress-container {
  width: 100%;
  padding: 0.5rem 0;
}

.detail-progress-bar {
  margin-bottom: 1rem;
}

.progress-update-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  background-color: #333333;
  padding: 1rem;
  border-radius: 4px;
}

/* Updates styling */
.updates-list {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.update-item {
  background-color: #333333;
  padding: 1rem;
  border-radius: 4px;
  border-left: 3px solid #0f62fe;
}

.update-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #8d8d8d;
}

.update-content {
  color: #f4f4f4;
  line-height: 1.5;
}

.update-date {
  font-weight: 600;
}

.update-by {
  font-style: italic;
}

.add-update-form {
  margin-top: 1rem;
}

.update-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.expected-improvements {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Progress indicator in table */
.cv-progress {
  width: 100%;
}

/* Modal form styling */
.modal-dropdown {
  width: 100%;
}

.info-item .cv-text-input,
.info-item .cv-dropdown {
  margin-top: 0.25rem;
}

.action-textarea {
  margin-bottom: 1.5rem;
}

.info-grid {
  row-gap: 1.5rem;
}
</style>


<template>
  <div class="dashboard-container">
    <!-- Inherit the MainHeader component -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <main class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Action Tracker</h1>
        <div class="action-controls">
          <cv-button kind="primary" @click="openNewActionModal">Add New Action</cv-button>
          <cv-button kind="secondary">Export</cv-button>
        </div>
      </div>

      <!-- Filter Bar -->
      <div class="filter-bar">
        <div class="filter-group">
          <div class="filter-label">Status:</div>
          <cv-dropdown
            v-model="statusFilter"
            label="Filter by status"
            :items="statusOptions"
          ></cv-dropdown>
        </div>

        <div class="filter-group">
          <div class="filter-label">Commodity:</div>
          <cv-dropdown
            v-model="commodityFilter"
            label="Filter by commodity"
            :items="commodityOptions"
          ></cv-dropdown>
        </div>

        <div class="search-box">
          <cv-search
            v-model="searchQuery"
            label="Search"
            placeholder="Search actions..."
          ></cv-search>
        </div>
      </div>

      <!-- Action Table -->
      <div class="action-table">
        <div class="table-header">
          <h2 class="table-title">Action Items</h2>
          <div v-if="isLoading" class="loading-indicator">Loading action items...</div>
          <div v-if="loadingError" class="error-message">Error: {{ loadingError }}</div>
        </div>

        <cv-data-table :columns="columns">
          <template slot="data">
            <cv-data-table-row
              v-if="isLoading && rows.length === 0"
              :key="'loading'"
            >
              <cv-data-table-cell colspan="10">
                <div class="loading-message">Loading action items...</div>
              </cv-data-table-cell>
            </cv-data-table-row>
            <cv-data-table-row
              v-else-if="loadingError"
              :key="'error'"
            >
              <cv-data-table-cell colspan="10">
                <div class="error-message">{{ loadingError }}</div>
              </cv-data-table-cell>
            </cv-data-table-row>
            <cv-data-table-row
              v-else-if="filteredRows.length === 0"
              :key="'empty'"
            >
              <cv-data-table-cell colspan="10">
                <div class="empty-message">No action items found.</div>
              </cv-data-table-cell>
            </cv-data-table-row>
            <cv-data-table-row
              v-for="row in filteredRows"
              v-else
              :key="row.id"
              :class="'status-row-' + row.status.toLowerCase()"
            >
              <!-- Status indicator -->
              <cv-data-table-cell>
                <div class="status-badge" :class="row.status.toLowerCase()">
                  <span class="status-indicator" :class="'status-' + row.status.toLowerCase()"></span>
                  {{ row.status }}
                </div>
              </cv-data-table-cell>

              <cv-data-table-cell>{{ row.commodity }}</cv-data-table-cell>
              <cv-data-table-cell>{{ row.group }}</cv-data-table-cell>
              <cv-data-table-cell>{{ row.pn }}</cv-data-table-cell>

              <!-- Editable Test Name -->
              <cv-data-table-cell>
                <div class="editable-field">
                  <span v-if="!row.isEditingTest">{{ row.editableTest }}</span>
                  <input
                    v-if="row.isEditingTest"
                    v-model="row.editableTest"
                    @blur="stopEditingTest(row)"
                    @keyup.enter="stopEditingTest(row)"
                  />
                  <span class="edit-icon" @click="editTest(row)">
                    <Edit20 />
                  </span>
                </div>
              </cv-data-table-cell>

              <!-- Actions -->
              <cv-data-table-cell>
                <div class="action-cell">
                  <span class="action-text">{{ truncateText(row.action, 40) }}</span>
                  <button class="see-more-button" @click="handleSeeMore(row)">Details</button>
                </div>
              </cv-data-table-cell>

              <!-- Progress -->
              <cv-data-table-cell>
                <div class="progress-container">
                  <cv-progress :value="row.progress || 0" :label-text="`${row.progress || 0}%`" />
                </div>
              </cv-data-table-cell>

              <!-- Editable Deadline -->
              <cv-data-table-cell>
                <div class="editable-field">
                  <span v-if="!row.isEditingDL">{{ formatDate(row.deadline) }}</span>
                  <input
                    v-if="row.isEditingDL"
                    v-model="row.deadline"
                    @blur="stopEditingDL(row)"
                    @keyup.enter="stopEditingDL(row)"
                    type="date"
                  />
                  <span class="edit-icon" @click="editDL(row)">
                    <Edit20 />
                  </span>
                </div>
              </cv-data-table-cell>

              <!-- Expected Resolution -->
              <cv-data-table-cell>
                <div class="editable-field">
                  <span v-if="!row.isEditingER">{{ formatDate(row.expectedResolution) }}</span>
                  <input
                    v-if="row.isEditingER"
                    v-model="row.expectedResolution"
                    @blur="stopEditingER(row)"
                    @keyup.enter="stopEditingER(row)"
                    type="date"
                  />
                  <span class="edit-icon" @click="editER(row)">
                    <Edit20 />
                  </span>
                </div>
              </cv-data-table-cell>

              <!-- Assignee -->
              <cv-data-table-cell>{{ row.assignee }}</cv-data-table-cell>
            </cv-data-table-row>
          </template>
        </cv-data-table>
      </div>
    </main>

    <!-- Action Details Modal -->
    <cv-modal
      
      :visible="modalVisible"
      @modal-hidden="modalVisible = false"
     
    >
      <template slot="title">
        <div>Action Details - {{ selectedRow ? selectedRow.pn : '' }}</div>
      </template>
      <template slot="content">
        <div class="modal-content" v-if="selectedRow">
          <!-- Status Banner -->
          <div class="status-banner" :class="'status-banner-' + selectedRow.status.toLowerCase()">
            <div class="status-badge" :class="selectedRow.status.toLowerCase()">
              <span class="status-indicator" :class="'status-' + selectedRow.status.toLowerCase()"></span>
              {{ selectedRow.status }}
            </div>
            <div class="assignee-info">Assigned to: <strong>{{ selectedRow.assignee }}</strong></div>
          </div>

          <!-- Basic Information Section -->
          <div class="modal-section">
            <div class="section-title">Basic Information</div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Commodity</span>
                <cv-dropdown
                  v-model="selectedRow.commodity"
                  label="Commodity"
                  :items="commodityOptions.filter(item => item !== 'All')"
                  class="modal-dropdown"
                ></cv-dropdown>
              </div>
              <div class="info-item">
                <span class="info-label">Group</span>
                <cv-text-input
                  v-model="selectedRow.group"
                  label="Group"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Part Number</span>
                <cv-text-input
                  v-model="selectedRow.pn"
                  label="Part Number"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Test</span>
                <cv-text-input
                  v-model="selectedRow.editableTest"
                  label="Test"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Deadline</span>
                <cv-text-input
                  type="date"
                  v-model="selectedRow.deadline"
                  label="Deadline"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Expected Resolution</span>
                <cv-text-input
                  type="date"
                  v-model="selectedRow.expectedResolution"
                  label="Expected Resolution"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Status</span>
                <cv-dropdown
                  v-model="selectedRow.status"
                  label="Status"
                  :items="statusOptions.filter(item => item !== 'All')"
                  class="modal-dropdown"
                ></cv-dropdown>
              </div>
              <div class="info-item">
                <span class="info-label">Assignee</span>
                <cv-text-input
                  v-model="selectedRow.assignee"
                  label="Assignee"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Created</span>
                <span class="info-value">{{ formatDate(selectedRow.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Updated</span>
                <span class="info-value">{{ formatDate(selectedRow.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Action Details Section -->
          <div class="modal-section">
            <div class="section-title">Action Details</div>
            <div class="action-details">
              <div class="action-title">Current Action</div>
              <cv-text-area
                v-model="selectedRow.action"
                label="Action Description"
                class="action-textarea"
              ></cv-text-area>

              <div class="expected-improvements">
                <div class="action-title">Expected Improvements</div>
                <cv-text-area
                  v-model="selectedRow.expectedImprovements"
                  label="Expected Improvements"
                  placeholder="Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)"
                  class="action-textarea"
                ></cv-text-area>
              </div>

              <div class="progress-section">
                <div class="action-title">Progress</div>
                <cv-progress
                  :value="selectedRow.progress || 0"
                  :label-text="`${selectedRow.progress || 0}%`"
                  class="detail-progress-bar"
                />
                <div class="progress-update-controls">
                  <cv-slider
                    v-model="selectedRow.progress"
                    :min="0"
                    :max="100"
                    :step="5"
                    :label="'Update Progress'"
                  ></cv-slider>
                  <cv-button
                    kind="primary"
                    size="small"
                    @click="updateProgress(selectedRow)"
                  >Update Progress</cv-button>
                </div>
              </div>

              <div v-if="selectedRow.updates && selectedRow.updates.length > 0">
                <div class="action-title">Updates History</div>
                <div class="updates-list">
                  <div v-for="(update, index) in selectedRow.updates" :key="index" class="update-item">
                    <div class="update-header">
                      <span class="update-date">{{ formatDate(update.date) }}</span>
                      <span class="update-by">by {{ update.updatedBy }}</span>
                    </div>
                    <div class="update-content">{{ update.content }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Add Update Section -->
          <div class="modal-section">
            <div class="section-title">Add Update</div>
            <div class="add-update-form">
              <cv-text-area
                v-model="newUpdate"
                label="Update Content"
                placeholder="Enter update details..."
              ></cv-text-area>
              <div class="update-form-actions">
                <cv-button
                  kind="primary"
                  @click="addUpdate(selectedRow)"
                >Add Update</cv-button>
              </div>
            </div>
          </div>

          <!-- Issues Section -->
          <div class="modal-section" v-if="selectedRow.issues && selectedRow.issues.length > 0">
            <div class="section-title">Related Issues</div>
            <cv-data-table
              :columns="issueColumns"
              :data="selectedRow.issues"
              :title="''"
            ></cv-data-table>
          </div>

          <!-- Notes Section -->
          <div class="modal-section">
            <div class="section-title">Notes</div>
            <cv-text-area
              v-model="selectedRow.notes"
              label="Additional Notes"
              placeholder="Enter any additional notes"
            ></cv-text-area>
          </div>

          <!-- Action Buttons -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="modalVisible = false">Cancel</cv-button>
            <cv-button kind="primary" @click="updateEntireAction(selectedRow)">Update Action</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>

    <!-- New Action Modal -->
    <cv-modal
      class="action-modal"
      :visible="newActionModalVisible"
      @modal-hidden="newActionModalVisible = false"
      :size="'lg'"
    >
      <template slot="title">
        <div>Create New Action</div>
      </template>
      <template slot="content">
        <div class="modal-content">
          <!-- Basic Information Section -->
          <div class="modal-section">
            <div class="section-title">Basic Information</div>
            <p class="form-note">Fields marked with <span class="required-field">*</span> are required</p>
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">Process/Commodity <span class="required-field">*</span></label>
                <cv-dropdown
                  v-model="newAction.commodity"
                  label="Select Process/Commodity"
                  :items="commodityOptions.filter(item => item !== 'All')"
                ></cv-dropdown>
              </div>

              <div class="form-group">
                <label class="form-label">Part Group <span class="required-field">*</span></label>
                <cv-text-input
                  v-model="newAction.group"
                  label="Part Group"
                  placeholder="Enter part group name"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Part Number</label>
                <cv-text-input
                  v-model="newAction.pn"
                  label="Part Number"
                  placeholder="Enter part number"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Test</label>
                <cv-text-input
                  v-model="newAction.test"
                  label="Test"
                  placeholder="Enter test name"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Deadline</label>
                <cv-text-input
                  type="date"
                  v-model="newAction.deadline"
                  label="Deadline"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Expected Resolution Date</label>
                <cv-text-input
                  type="date"
                  v-model="newAction.expectedResolution"
                  label="Expected Resolution"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Status</label>
                <cv-dropdown
                  v-model="newAction.status"
                  label="Select Status"
                  :items="statusOptions.filter(item => item !== 'All')"
                ></cv-dropdown>
              </div>

              <div class="form-group">
                <label class="form-label">Progress (%)</label>
                <cv-slider
                  v-model="newAction.progress"
                  :min="0"
                  :max="100"
                  :step="5"
                  :label="'Progress'"
                ></cv-slider>
              </div>

              <div class="form-group">
                <label class="form-label">Assignee</label>
                <cv-text-input
                  v-model="newAction.assignee"
                  label="Assignee"
                  placeholder="Enter assignee name"
                ></cv-text-input>
              </div>
            </div>
          </div>

          <!-- Action Details Section -->
          <div class="modal-section">
            <div class="section-title">Action Details</div>
            <div class="form-group full-width">
              <label class="form-label">Action Description <span class="required-field">*</span></label>
              <cv-text-area
                v-model="newAction.action"
                label="Action Description"
                placeholder="Describe the action to be taken"
              ></cv-text-area>
            </div>

            <div class="form-group full-width">
              <label class="form-label">Expected Improvements</label>
              <cv-text-area
                v-model="newAction.expectedImprovements"
                label="Expected Improvements"
                placeholder="Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)"
              ></cv-text-area>
            </div>
          </div>

          <!-- Notes Section -->
          <div class="modal-section">
            <div class="section-title">Notes</div>
            <div class="form-group full-width">
              <label class="form-label">Additional Notes</label>
              <cv-text-area
                v-model="newAction.notes"
                label="Notes"
                placeholder="Enter any additional notes"
              ></cv-text-area>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="newActionModalVisible = false">Cancel</cv-button>
            <cv-button kind="primary" @click="createNewAction">Create Action</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>
  </div>
</template>



<script>
import { Edit20 } from '@carbon/icons-vue';
import MainHeader from '@/components/MainHeader'; // Import the MainHeader component
export default {
  name: 'ActionTracker',
  components: {
    Edit20,
    MainHeader
  },
  data() {
    return {
      columns: ['Status', 'Commodity', 'Group', 'PN', 'Test', 'Actions', 'Progress', 'Deadline', 'Expected Resolution', 'Assignee'],
      issueColumns: ['Issue ID', 'Description', 'Severity', 'Date Reported'],
      historyColumns: ['Date', 'Action', 'Updated By'],
      modalVisible: false,
      newActionModalVisible: false,
      selectedRow: null,
      searchQuery: '',
      statusFilter: 'All',
      commodityFilter: 'All',
      statusOptions: ['All', 'In-Progress', 'Completed', 'Blocked'],
      commodityOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],
      expandedSideNav: false,
      useFixed: true,
      isLoading: false,
      loadingError: null,
      newAction: {
        commodity: '',
        group: '',
        pn: '',
        test: '',
        deadline: '',
        expectedResolution: '',
        expectedImprovements: '',
        progress: 0,
        status: 'In-Progress',
        assignee: '',
        action: '',
        notes: ''
      },
      newUpdate: '',
      rows: [],
    };
  },
  computed: {
    filteredRows() {
      return this.rows.filter(row => {
        // Filter by status
        if (this.statusFilter !== 'All' && row.status !== this.statusFilter) {
          return false;
        }

        // Filter by commodity
        if (this.commodityFilter !== 'All' && row.commodity !== this.commodityFilter) {
          return false;
        }

        // Filter by search query
        if (this.searchQuery) {
          const query = this.searchQuery.toLowerCase();
          return (
            row.commodity.toLowerCase().includes(query) ||
            row.group.toLowerCase().includes(query) ||
            row.pn.toLowerCase().includes(query) ||
            row.action.toLowerCase().includes(query) ||
            row.assignee.toLowerCase().includes(query)
          );
        }

        return true;
      });
    }
  },
  mounted() {
    // Load action tracker data from API
    this.loadActionTrackerData();

    // Check if we have query parameters to create a new action
    const query = this.$route.query;
    if (query.createAction === 'true') {
      // Populate the new action form with data from query parameters
      this.newAction = {
        commodity: query.commodity || this.commodityOptions.filter(item => item !== 'All')[0] || '',
        group: query.group || '',
        pn: query.pn || '',
        test: query.test || '',
        deadline: query.deadline || new Date().toISOString().split('T')[0],
        status: 'In-Progress',
        assignee: query.assignee || '',
        action: query.action || '',
        notes: query.notes || ''
      };

      // Open the new action modal
      this.newActionModalVisible = true;
    }
  },
  methods: {
    // Get authentication config for API calls
    getAuthConfig() {
      const token = localStorage.getItem('token');
      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    },

    // Load action tracker data from API
    async loadActionTrackerData() {
      try {
        this.isLoading = true;
        this.loadingError = null;
        console.log('Loading action tracker data from API...');

        // Make API call to get action tracker data
        const response = await fetch('/api-statit2/get_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action tracker data loaded:', data);

        // Check if data has items property
        if (data.items) {
          // Format the data for display
          this.rows = data.items.map(item => ({
            id: item.id,
            commodity: item.commodity,
            group: item.group,
            pn: item.pn,
            editableTest: item.test || 'N/A',
            deadline: item.deadline || '',
            expectedResolution: item.expectedResolution || '',
            expectedImprovements: item.expectedImprovements || '',
            progress: item.progress || 0,
            isEditingTest: false,
            isEditingDL: false,
            isEditingER: false,
            action: item.action,
            status: item.status,
            assignee: item.assignee,
            notes: item.notes,
            issues: item.issues || [],
            updates: item.updates || [],
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));
        } else {
          this.rows = [];
          console.warn('No items found in action tracker data');
        }
      } catch (error) {
        console.error('Error loading action tracker data:', error);
        this.loadingError = error.message;
        this.rows = [];
      } finally {
        this.isLoading = false;
      }
    },

    // Save action tracker data to API
    async saveActionTrackerData(action) {
      try {
        console.log('Saving action tracker data to API:', action);

        // Make API call to save action tracker data
        const response = await fetch('/api-statit2/save_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify(action)
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to save action tracker data: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action tracker data saved:', data);

        return data;
      } catch (error) {
        console.error('Error saving action tracker data:', error);
        throw error;
      }
    },

    handleSeeMore(row) {
      this.selectedRow = row;
      this.modalVisible = true;
      console.log('See more clicked:', row);
    },

    editTest(row) {
      row.isEditingTest = true;
    },

    async stopEditingTest(row) {
      row.isEditingTest = false;
      console.log('Test name updated:', row.editableTest);

      try {
        // Update the action in the API
        await this.saveActionTrackerData({
          id: row.id,
          test: row.editableTest
        });
      } catch (error) {
        alert(`Failed to update test: ${error.message}`);
      }
    },

    editDL(row) {
      row.isEditingDL = true;
    },

    async stopEditingDL(row) {
      row.isEditingDL = false;
      console.log('Deadline updated:', row.deadline);

      try {
        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          deadline: row.deadline
        });
      } catch (error) {
        alert(`Failed to update deadline: ${error.message}`);
      }
    },

    editER(row) {
      row.isEditingER = true;
    },

    async stopEditingER(row) {
      row.isEditingER = false;
      console.log('Expected resolution updated:', row.expectedResolution);

      try {
        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          expectedResolution: row.expectedResolution
        });
      } catch (error) {
        alert(`Failed to update expected resolution: ${error.message}`);
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      // Check if the date is in YYYY-MM-DD format
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        const [year, month, day] = dateString.split('-');
        return `${month}/${day}/${year}`;
      }

      // If it's in MM/DD/YYYY format, return as is
      return dateString;
    },

    // Update an action item
    async updateActionItem(actionData) {
      try {
        console.log('Updating action item:', actionData);

        // Make API call to update action tracker data
        const response = await fetch('/api-statit2/update_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify(actionData)
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to update action item: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action item updated:', data);

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        return data;
      } catch (error) {
        console.error('Error updating action item:', error);
        throw error;
      }
    },

    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.slice(0, maxLength) + '...';
    },

    // Update progress for an action item
    async updateProgress(row) {
      try {
        console.log(`Updating progress for ${row.id} to ${row.progress}%`);

        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          progress: row.progress
        });

        // Add an update about the progress change
        await this.addUpdate(row, `Progress updated to ${row.progress}%`);

        // Show success message
        alert('Progress updated successfully!');
      } catch (error) {
        console.error('Error updating progress:', error);
        alert(`Failed to update progress: ${error.message}`);
      }
    },

    // Update the entire action item
    async updateEntireAction(row) {
      try {
        console.log('Updating entire action item:', row);

        // Create an update object with all editable fields
        const updateData = {
          id: row.id,
          commodity: row.commodity,
          group: row.group,
          pn: row.pn,
          test: row.editableTest,
          deadline: row.deadline,
          expectedResolution: row.expectedResolution,
          expectedImprovements: row.expectedImprovements,
          progress: row.progress,
          status: row.status,
          assignee: row.assignee,
          action: row.action,
          notes: row.notes
        };

        // Update the action in the API
        await this.updateActionItem(updateData);

        // Add an update about the action update
        await this.addUpdate(row, 'Action details updated');

        // Close the modal
        this.modalVisible = false;

        // Show success message
        alert('Action updated successfully!');
      } catch (error) {
        console.error('Error updating action:', error);
        alert(`Failed to update action: ${error.message}`);
      }
    },

    // Add an update to an action item
    async addUpdate(row, content) {
      try {
        const updateContent = content || this.newUpdate;

        if (!updateContent) {
          alert('Please enter update content');
          return;
        }

        console.log(`Adding update to ${row.id}: ${updateContent}`);

        // Make API call to add update
        const response = await fetch('/api-statit2/add_action_update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify({
            id: row.id,
            update: {
              content: updateContent,
              updatedBy: 'Current User' // In a real app, this would be the logged-in user
            }
          })
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to add update: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Update added:', data);

        // Clear the update input
        this.newUpdate = '';

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        // Update the selected row with the latest data
        if (this.selectedRow) {
          const updatedRow = this.rows.find(r => r.id === this.selectedRow.id);
          if (updatedRow) {
            this.selectedRow = updatedRow;
          }
        }

        // Show success message if this was a manual update
        if (!content) {
          alert('Update added successfully!');
        }

        return data;
      } catch (error) {
        console.error('Error adding update:', error);
        alert(`Failed to add update: ${error.message}`);
        throw error;
      }
    },

    openNewActionModal() {
      // Reset the form
      this.newAction = {
        commodity: this.commodityOptions.filter(item => item !== 'All')[0] || '',
        group: '',
        pn: '',
        test: '',
        deadline: new Date().toISOString().split('T')[0],
        status: 'In-Progress',
        assignee: '',
        action: '',
        notes: ''
      };
      // Open the modal
      this.newActionModalVisible = true;
    },

    async createNewAction() {
      // Validate required fields
      const requiredFields = ['commodity', 'group', 'action'];
      const missingFields = requiredFields.filter(field => !this.newAction[field]);

      if (missingFields.length > 0) {
        // Show error message for missing fields
        const fieldNames = missingFields.map(field => {
          switch(field) {
            case 'commodity': return 'Process/Commodity';
            case 'group': return 'Part Group';
            case 'action': return 'Action Description';
            default: return field;
          }
        });

        alert(`Please fill in the required fields: ${fieldNames.join(', ')}`);
        return;
      }

      try {
        // Ensure deadline is set
        if (!this.newAction.deadline) {
          // Set default deadline to 30 days from now if not provided
          const defaultDate = new Date();
          defaultDate.setDate(defaultDate.getDate() + 30);
          this.newAction.deadline = `${defaultDate.getFullYear()}-${String(defaultDate.getMonth() + 1).padStart(2, '0')}-${String(defaultDate.getDate()).padStart(2, '0')}`;
        }

        // Format the expected resolution date
        let formattedExpectedResolution = '';
        if (this.newAction.expectedResolution) {
          try {
            const expectedResolutionDate = new Date(this.newAction.expectedResolution);
            formattedExpectedResolution = `${expectedResolutionDate.getFullYear()}-${String(expectedResolutionDate.getMonth() + 1).padStart(2, '0')}-${String(expectedResolutionDate.getDate()).padStart(2, '0')}`;
          } catch (error) {
            console.error('Error formatting expected resolution date:', error);
            formattedExpectedResolution = this.newAction.expectedResolution; // Use as-is if there's an error
          }
        }

        // Create the new action object
        const newActionItem = {
          commodity: this.newAction.commodity,
          group: this.newAction.group,
          pn: this.newAction.pn || `${this.newAction.group}-${Date.now().toString().slice(-6)}`, // Generate a PN if not provided
          test: this.newAction.test || 'N/A',
          deadline: this.newAction.deadline,
          expectedResolution: formattedExpectedResolution,
          expectedImprovements: this.newAction.expectedImprovements || '',
          progress: this.newAction.progress || 0,
          action: this.newAction.action,
          status: this.newAction.status || 'In-Progress',
          assignee: this.newAction.assignee || 'Unassigned',
          notes: this.newAction.notes || ''
        };

        // Save the new action to the API
        const savedAction = await this.saveActionTrackerData(newActionItem);
        console.log('New action saved to API:', savedAction);

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        // Close the modal
        this.newActionModalVisible = false;

        // Show a success message
        alert('Action item created successfully!');
      } catch (error) {
        console.error('Error creating new action:', error);
        alert(`Failed to create action: ${error.message}`);
      }
    }
  },
};

</script>
