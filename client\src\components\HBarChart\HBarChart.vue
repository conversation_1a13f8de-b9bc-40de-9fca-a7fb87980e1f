<template>
  <div class="hbar-chart-container">
    <div v-if="!data || data.length === 0" class="no-data-message">
      No data available
    </div>
    <div v-else ref="chartHolder" class="chart-holder" :style="{ height: height }">
      <!-- Chart will be rendered here -->
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { SimpleBarChart } from '@carbon/charts';

Vue.use(chartsVue);

export default {
  name: 'HBarChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    eventType: {
      type: Function,
      default: null
    },
    height: {
      type: String,
      default: '400px'
    },
    title: {
      type: String,
      default: 'Failure Modes Analysis'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    console.log("Chart data:", this.data);
    const chartHolder = this.$refs.chartHolder;
    console.log("Chart holder:", chartHolder);

    // Ensure data has the required format for horizontal bar chart
    const formattedData = this.formatData(this.data);
    console.log("Formatted data:", formattedData);

    // Create chart options with proper configuration for horizontal bar chart
    const chartOptions = {
      title: this.title || `Failure Modes Analysis`,
      axes: {
        left: {
          title: "Failure Mode",
          mapsTo: "key",
          scaleType: "labels"
        },
        bottom: {
          title: "Count",
          mapsTo: "value",
          scaleType: "linear"
        }
      },
      height: "400px",
      legend: {
        enabled: false
      },
      tooltip: {
        enabled: true
      },
      color: {
        scale: {
          "Count": "#0f62fe"
        }
      }
    };

    try {
      this.chart = new SimpleBarChart(chartHolder, {
        data: formattedData,
        options: chartOptions
      });

      if (typeof this.eventType === 'function') {
        this.chart.services.events.addEventListener("bar-click", (e) => {
          this.eventType(e);
        });
      }
    } catch (error) {
      console.error("Error creating chart:", error);
    }
  },
  watch: {
    data(newData) {
      if (this.chart) {
        // Format the data before updating
        const formattedData = this.formatData(newData);
        // Update chart data
        this.chart.model.setData(formattedData);
      }
    }
  },
  methods: {
    formatData(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
      }

      // Check if data is already in the correct format
      if (data[0].group && data[0].key && data[0].value !== undefined) {
        return data;
      }

      // Convert from codeName/xFactor format to group/key/value format
      return data.map(item => {
        return {
          group: "Count",
          key: item.codeName || item.key || "Unknown",
          value: item.xFactor || item.value || 0
        };
      });
    }
  }
};
</script>

<style scoped>
.hbar-chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-holder {
  width: 100%;
  min-height: 300px;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #8d8d8d;
  font-style: italic;
}
</style>
