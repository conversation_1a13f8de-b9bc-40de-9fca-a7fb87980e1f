<template>
  <div class="bar-chart-wrapper" :style="{ height: height }">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>
    <div v-else-if="!data || data.length === 0" class="no-data-message">
      No data available
    </div>
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import { SimpleBarChart } from '@carbon/charts';
import '@carbon/charts/styles-g90.css';

export default {
  name: 'XFactorBarChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      error: null,
      options: {
        title: 'Average XFactor by Breakout Group',
        axes: {
          left: {
            title: 'XFactor',
            mapsTo: 'value',
            scaleType: 'linear',
            domain: [0, 5],
            thresholds: [
              {
                value: 1.5,
                label: 'Sustained Problem Threshold',
                fillColor: 'rgba(255, 204, 0, 0.2)',
                strokeColor: '#FFCC00'
              },
              {
                value: 3.0,
                label: 'Short-Term Spike Threshold',
                fillColor: 'rgba(255, 0, 0, 0.2)',
                strokeColor: '#FF0000'
              }
            ]
          },
          bottom: {
            title: 'Breakout Group',
            mapsTo: 'group',
            scaleType: 'labels'
          }
        },
        height: this.height,
        width: '100%',
        legend: {
          alignment: 'center',
          enabled: true
        },
        tooltip: {
          enabled: true,
          customHTML: (data) => {
            return `
              <div class="custom-tooltip">
                <p><strong>${data[0].data.group}</strong></p>
                <p>XFactor: ${data[0].data.value.toFixed(2)}</p>
                ${data[0].data.defects ? `<p>Defects: ${data[0].data.defects}</p>` : ''}
                ${data[0].data.volume ? `<p>Volume: ${data[0].data.volume}</p>` : ''}
              </div>
            `;
          }
        },
        color: {
          scale: {
            'XFactor': '#0F62FE',
            'Critical': '#DA1E28'
          },
          group: {
            name: 'groupMapsTo'
          }
        }
      }
    };
  },
  watch: {
    data: {
      handler() {
        this.renderChart();
      },
      deep: true
    },
    height(newHeight) {
      if (this.options) {
        this.options.height = newHeight;
        this.renderChart();
      }
    }
  },
  mounted() {
    this.renderChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    renderChart() {
      if (!this.data || this.data.length === 0 || !this.$refs.chartContainer) {
        return;
      }

      try {
        if (this.chart) {
          this.chart.destroy();
        }

        this.chart = new SimpleBarChart(this.$refs.chartContainer, {
          data: this.data,
          options: this.options
        });

        // Add click event listener to the chart
        this.chart.services.events.addEventListener('bar-click', (event) => {
          this.$emit('bar-click', event);
        });

        this.error = null;
      } catch (err) {
        console.error('Error rendering chart:', err);
        this.error = 'Error rendering chart: ' + err.message;
      }
    }
  }
};
</script>

<style scoped>
.bar-chart-wrapper {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(22, 22, 22, 0.7);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #0f62fe;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message,
.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #fa4d56;
  text-align: center;
  padding: 20px;
}

.no-data-message {
  color: #8d8d8d;
}

:deep(.custom-tooltip) {
  background-color: #262626;
  border: 1px solid #393939;
  padding: 10px;
  border-radius: 4px;
  color: #f4f4f4;
}

:deep(.custom-tooltip p) {
  margin: 5px 0;
}
</style>
