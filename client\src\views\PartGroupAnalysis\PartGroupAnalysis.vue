<template>
  <div class="x-factor-analysis">
    <!-- Main Header -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" @panel-toggle="toggleSideNav" />

    <main class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div>
          <h1 class="page-title">X-Factor Analysis</h1>
          <p class="page-subtitle">Quality Monitoring and Alert System</p>
        </div>
        <div class="page-actions">
          <cv-button kind="ghost" @click="showLastPeriod" class="last-period-btn">
            <svg class="calendar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4h-4V2h-2v2h-8V2h-2v2H6C4.9,4,4,4.9,4,6v20c0,1.1,0.9,2,2,2h20c1.1,0,2-0.9,2-2V6C28,4.9,27.1,4,26,4z M26,26H6V12h20	V26z M26,10H6V6h4v2h2V6h8v2h2V6h4V10z"/>
              <rect x="8" y="16" width="4" height="4"/>
              <rect x="14" y="16" width="4" height="4"/>
              <rect x="20" y="16" width="4" height="4"/>
            </svg>
            Show Last {{ selectedTimePeriod === 'Weekly' ? 'Week' : 'Month' }}
          </cv-button>
          <cv-button kind="ghost" @click="showInfoModal = true">
            <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor">
              <path d="M17 22L17 14 13 14 13 16 15 16 15 22 12 22 12 24 20 24 20 22 17 22zM16 8a1.5 1.5 0 100 3 1.5 1.5 0 000-3z"/>
              <path d="M16,30A14,14,0,1,1,30,16,14,14,0,0,1,16,30ZM16,4A12,12,0,1,0,28,16,12,12,0,0,0,16,4Z"/>
            </svg>
            How It Works
          </cv-button>
        </div>
      </div>

      <!-- Info Modal -->
      <cv-modal
        :visible="showInfoModal"
        @modal-hidden="closeInfoModal"
        @primary-click="closeInfoModal"
        @secondary-click="closeInfoModal"
        :size="'lg'"
        class="info-modal"
      >
        <template slot="title">Understanding the X-Factor Analysis</template>
        <template slot="content">
          <div class="info-content">
            <h3>Statistical Model Explained</h3>
            <p class="info-intro">This tool uses X-factor values to identify quality issues. Here's what happens behind the scenes:</p>

            <div class="info-grid">
              <div class="info-item">
                <h4>1. Data Collection</h4>
                <p>Data is loaded from Excel files containing actual volumes and failures for each part group.</p>
              </div>
              <div class="info-item">
                <h4>2. Target Rates</h4>
                <p>Each part group has a specific target rate from the targets.xlsx file (e.g., Artemis FUL: 0.39%, FAB: 0.73%).</p>
              </div>
              <div class="info-item">
                <h4>3. Expected Failures</h4>
                <p>Calculated as: <strong>Volume × Target Rate</strong></p>
                <p>Example: 100 parts with 0.5% target = 0.5 expected failures</p>
              </div>
              <div class="info-item">
                <h4>4. X-Factor Calculation</h4>
                <p>Measures how much the actual failure rate deviates from expected:</p>
                <p><strong>X-factor = Actual Rate / Target Rate</strong></p>
                <p>Example: 1.5% actual rate / 0.5% target rate = X-factor of 3.0</p>
              </div>
              <div class="info-item">
                <h4>5. 3-Month Averages</h4>
                <p>For each period, we calculate the average X-factor of the current period plus the previous two periods.</p>
                <p>This helps identify sustained problems vs. one-time spikes.</p>
              </div>
              <div class="info-item">
                <h4>6. Alert Generation</h4>
                <p>The system automatically generates alerts based on threshold violations:</p>
                <ul>
                  <li>Short-term Spike: Single period X-factor &gt; {{ shortTermThreshold }}</li>
                  <li>Sustained Problem: 3-month average X-factor &gt; {{ longTermThreshold }} for 3+ consecutive periods</li>
                  <li>Critical Issue: Both short-term spike and sustained problem present</li>
                  <li>Normal: X-factor within expected ranges</li>
                </ul>
              </div>
            </div>

            <h3>What is X-factor?</h3>
            <div class="info-item">
              <p>X-factor is a measure of how much the actual failure rate deviates from the expected (target) rate:</p>
              <ul>
                <li>X-factor = Actual Failure Rate / Target Failure Rate</li>
                <li>X-factor of 1.0 means the part is performing exactly at the target rate</li>
                <li>X-factor &gt; 1.0 means the part is performing worse than expected</li>
                <li>X-factor &lt; 1.0 means the part is performing better than expected</li>
              </ul>
            </div>

            <h3>Understanding the Chart</h3>
            <div class="chart-explanation">
              <p>The chart displays four key metrics:</p>
              <ul>
                <li><span class="chart-item-purple"></span> <strong>Expected Rate</strong> - The target rate for this part group</li>
                <li><span class="chart-item-violet"></span> <strong>X-Factor</strong> - Ratio of actual to expected failure rate</li>
                <li><span class="chart-item-pink"></span> <strong>3-Month X-Factor Average</strong> - Rolling average of X-factors</li>
              </ul>
              <p>Horizontal threshold lines indicate alert levels:</p>
              <ul>
                <li><span class="threshold-orange"></span> <strong>Short-term Alert (X-factor &gt; {{ shortTermThreshold }})</strong> - Significant spike above target</li>
                <li><span class="threshold-red"></span> <strong>Sustained Problem (avg X-factor &gt; {{ longTermThreshold }})</strong> - Ongoing issue above target</li>
              </ul>
            </div>

            <h3>Alert Types</h3>
            <div class="alert-examples">
              <div class="alert-example">
                <div class="status-indicator-circle status-warning"></div>
                <p><strong>Short-term Spike:</strong> When the X-factor exceeds {{ shortTermThreshold }} for a single period and volume is at least {{ minVolume }} parts. This indicates a significant problem that needs immediate attention.</p>
              </div>
              <div class="alert-example">
                <div class="status-indicator-circle status-error"></div>
                <p><strong>Sustained Problem:</strong> When the 3-month average X-factor stays above {{ longTermThreshold }} for 3 or more consecutive periods. This indicates a persistent quality issue that requires process improvement.</p>
              </div>
              <div class="alert-example">
                <div class="status-indicator-circle status-critical"></div>
                <p><strong>Critical Issue:</strong> When a part group has both a short-term spike (X-factor &gt; {{ shortTermThreshold }}) and a sustained problem (3+ periods with avg X-factor &gt; {{ longTermThreshold }}). This indicates a severe quality issue that requires immediate and comprehensive action.</p>
              </div>
              <div class="alert-example">
                <div class="status-indicator-circle status-info"></div>
                <p><strong>Normal:</strong> When the X-factor is within expected ranges (below {{ shortTermThreshold }}) and there is no sustained problem.</p>
              </div>
            </div>

            <h3>Example Calculation</h3>
            <table class="example-table" style="width:100%; border-collapse: collapse; margin-top: 1rem;">
              <tr class="table-header" style="background-color: #393939;">
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Month</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Volume</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Fails</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Actual Rate</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Target Rate</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">X-Factor</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">3-Mo Avg</th>
                <th style="padding: 0.5rem; text-align: left; border: 1px solid #525252;">Status</th>
              </tr>
              <tr>
                <td style="padding: 0.5rem; border: 1px solid #525252;">Oct</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">100</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.0%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.5%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;"><span class="status-indicator-circle status-success" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: #24a148;"></span> Good</td>
              </tr>
              <tr>
                <td style="padding: 0.5rem; border: 1px solid #525252;">Nov</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">100</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">3</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">3.0%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.5%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">6.0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">3.0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;"><span class="status-indicator-circle status-warning" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: #ff832b;"></span> Spike</td>
              </tr>
              <tr>
                <td style="padding: 0.5rem; border: 1px solid #525252;">Dec</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">100</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">2</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">2.0%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">0.5%</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">4.0</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;">3.3</td>
                <td style="padding: 0.5rem; border: 1px solid #525252;"><span class="status-indicator-circle status-error" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: #da1e28;"></span> Problem</td>
              </tr>
            </table>
          </div>
        </template>
        <template slot="secondary-button">Close</template>
        <template slot="primary-button">Got it!</template>
      </cv-modal>



      <!-- Charts and Results Section -->
      <div class="results-section">
        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">Analyzing part group data...</p>
        </div>

        <!-- Part Group Analysis Content -->
        <div v-else class="simplified-results">
          <!-- Tab Navigation -->
          <cv-tabs>
            <cv-tab label="Analysis" selected>
              <!-- Analysis Controls -->
              <cv-tile :light="lightTile" class="controls-tile">
                <div class="controls-grid">
                  <!-- Process Selection -->
                  <div class="control-item">
                    <cv-dropdown
                      label="Process"
                      v-model="selectedProcess"
                      :items="processOptions"
                      @change="handleProcessChange"
                    />
                  </div>

                  <!-- Part Group Selection -->
                  <div class="control-item">
                    <cv-dropdown
                      label="Part Group"
                      v-model="selectedPartGroup"
                      :items="partGroupOptions"
                      @change="handlePartGroupChange"
                    />
                  </div>

                  <!-- Time Period Selection -->
                  <div class="control-item">
                    <cv-dropdown
                      label="Time Period"
                      v-model="selectedTimePeriod"
                      :items="timePeriodOptions"
                      @change="handleTimePeriodChange"
                    />
                  </div>

                  <!-- Min Volume Setting -->
                  <div class="control-item">
                    <cv-number-input
                      label="Min Volume"
                      v-model="minVolume"
                      :min="1"
                      :max="1000"
                      :step="1"
                      :value="30"
                      @input="updateThresholds"
                    />
                  </div>

                  <!-- Start Date Selection -->
                  <div class="control-item">
                    <label class="date-label">Start Date</label>
                    <select
                      class="date-select"
                      v-model="startDate"
                      @change="handleDateChange"
                      :disabled="dateOptions.length === 0"
                    >
                      <option
                        v-for="(option, index) in dateOptions"
                        :key="`start-${index}`"
                        :value="option"
                      >
                        {{ formatMonth(option) }}
                      </option>
                    </select>
                  </div>

                  <!-- End Date Selection -->
                  <div class="control-item">
                    <label class="date-label">End Date</label>
                    <select
                      class="date-select"
                      v-model="endDate"
                      @change="handleDateChange"
                      :disabled="dateOptions.length === 0"
                    >
                      <option
                        v-for="(option, index) in dateOptions"
                        :key="`end-${index}`"
                        :value="option"
                      >
                        {{ formatMonth(option) }}
                      </option>
                    </select>
                  </div>

                  <!-- Time Range Selection -->
                  <div class="control-item">
                    <cv-dropdown
                      label="Time Range"
                      v-model="selectedTimeRange"
                      :items="timeRangeOptions"
                      @change="handleTimeRangeChange"
                    />
                  </div>

                  <div class="control-item analyze-button-container">
                    <cv-button kind="primary" @click="analyzeData" class="analyze-button">Analyze Data</cv-button>
                  </div>
                </div>
              </cv-tile>

              <!-- Combined Chart -->
              <cv-tile :light="lightTile" class="chart-tile">
                <div class="chart-header">
                  <h3 class="chart-title">Part Group Performance</h3>
                </div>
                <div class="chart-description">
                  <p>This chart shows X-factors that measure how much the actual failure rate deviates from the expected rate (X-factor = actual rate / expected rate).</p>
                </div>
                <DualFormatChart
                  v-if="combinedChartData.length > 0"
                  :data="combinedChartData"
                  :loading="false"
                  :height="'600px'"
                  :title="'Part Group Performance'"
                  :timeInterval="selectedTimePeriod === 'Weekly' ? 'weekly' : 'monthly'"
                  :startDate="startDate"
                  :endDate="endDate"
                />
                <div v-else class="empty-chart">
                  <p>No data available for the selected parameters.</p>
                </div>

                <!-- Alerts Table -->
                <div class="alerts-section">
                  <div class="alerts-header">
                    <h4 class="alerts-title">Alerts ({{ filteredAlerts.length }} found)</h4>
                  </div>
                  <div v-if="filteredAlerts.length === 0" class="empty-alerts">
                    <p>No alerts detected for the selected date range.</p>
                  </div>
                  <!-- Simple HTML table for alerts -->
                  <table class="debug-table" v-else>
                    <thead>
                      <tr>
                        <th>{{ selectedTimePeriod === 'Weekly' ? 'Week' : 'Date' }}</th>
                        <th>Actual Value (%)</th>
                        <th>Expected Value (%)</th>
                        <th>X-Factor</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(row, index) in filteredAlerts" :key="index">
                        <td>{{ formatMonth(row.date, selectedTimePeriod === 'Weekly') }}</td>
                        <td>{{ row.value.toFixed(2) }}%</td>
                        <td>{{ row.expectedValue ? row.expectedValue.toFixed(2) : getExpectedValue(row).toFixed(2) }}%</td>
                        <td :class="getDeviationScoreClass(row.deviationScore)">{{ row.deviationScore.toFixed(2) }}</td>
                        <td>
                          <div class="status-indicator-container">
                            <span v-if="row.type === 'Short-term Spike' && row.hasSustainedProblem" class="status-indicator-circle status-critical"></span>
                            <span v-else-if="row.type === 'Short-term Spike'" class="status-indicator-circle status-warning"></span>
                            <span v-else-if="row.type === 'Sustained Problem' && row.value > 0" class="status-indicator-circle status-error"></span>
                            <span v-else class="status-indicator-circle status-info"></span>

                            <span v-if="row.type === 'Short-term Spike' && row.hasSustainedProblem">Critical Issue ({{ row.consecutiveAboveThreshold || 3 }} {{ selectedTimePeriod === 'Weekly' ? 'weeks' : 'months' }})</span>
                            <span v-else-if="row.type === 'Short-term Spike'">Short-term Spike</span>
                            <span v-else-if="row.type === 'Sustained Problem' && row.value > 0">Sustained Problem ({{ row.consecutiveAboveThreshold || 3 }} {{ selectedTimePeriod === 'Weekly' ? 'weeks' : 'months' }})</span>
                            <span v-else>Normal</span>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- Monthly/Weekly Status Summary Table -->
                <div class="monthly-summary-section">
                  <h4 class="summary-title">{{ selectedTimePeriod === 'Weekly' ? 'Weekly' : 'Monthly' }} Status Summary ({{ filteredProcessedData.length }} {{ selectedTimePeriod === 'Weekly' ? 'weeks' : 'months' }})</h4>
                  <div v-if="filteredProcessedData.length === 0" class="empty-alerts">
                    <p>No {{ selectedTimePeriod === 'Weekly' ? 'weekly' : 'monthly' }} data available for the selected date range.</p>
                  </div>
                  <!-- Monthly data table -->
                  <table class="debug-table" v-if="filteredProcessedData.length > 0">
                    <thead>
                      <tr>
                        <th>{{ selectedTimePeriod === 'Weekly' ? 'Week' : 'Month' }}</th>
                        <th>Volume</th>
                        <th>Failures</th>
                        <th>Actual Rate (%)</th>
                        <th>Expected Rate (%)</th>
                        <th>X-Factor</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(row, index) in filteredProcessedData" :key="index">
                        <td>{{ formatMonth(row.date, selectedTimePeriod === 'Weekly') }}</td>
                        <td>{{ row.volume }}</td>
                        <td>{{ row.failures }}</td>
                        <td>{{ (row.falloutRate * 100).toFixed(2) }}%</td>
                        <td>{{ (row.targetRate * 100).toFixed(2) }}%</td>
                        <td :class="getDeviationScoreClass(row.deviationScore)">
                          {{ row.deviationScore !== null ? row.deviationScore.toFixed(2) : 'N/A' }}
                        </td>
                        <td>
                          <div class="status-indicator-container">
                            <span v-if="row.deviationScore !== null && row.deviationScore > shortTermThreshold && row.volume >= minVolume && row.failures > 0 && row.avgDeviationScore !== null && row.avgDeviationScore > longTermThreshold && (row.consecutiveAboveThreshold >= 3 || row.isSustainedProblem)" class="status-indicator-circle status-critical"></span>
                            <span v-else-if="row.deviationScore !== null && row.deviationScore > shortTermThreshold && row.volume >= minVolume" class="status-indicator-circle status-warning"></span>
                            <span v-else-if="row.failures > 0 && row.avgDeviationScore !== null && row.avgDeviationScore > longTermThreshold && (row.consecutiveAboveThreshold >= 3 || row.isSustainedProblem)" class="status-indicator-circle status-error"></span>
                            <span v-else class="status-indicator-circle status-info"></span>

                            <span v-if="row.deviationScore !== null && row.deviationScore > shortTermThreshold && row.volume >= minVolume && row.failures > 0 && row.avgDeviationScore !== null && row.avgDeviationScore > longTermThreshold && (row.consecutiveAboveThreshold >= 3 || row.isSustainedProblem)">Critical Issue ({{ row.consecutiveAboveThreshold || 3 }} {{ selectedTimePeriod === 'Weekly' ? 'weeks' : 'months' }})</span>
                            <span v-else-if="row.deviationScore !== null && row.deviationScore > shortTermThreshold && row.volume >= minVolume">Short-term Spike</span>
                            <span v-else-if="row.failures > 0 && row.avgDeviationScore !== null && row.avgDeviationScore > longTermThreshold && (row.consecutiveAboveThreshold >= 3 || row.isSustainedProblem)">Sustained Problem ({{ row.consecutiveAboveThreshold || 3 }} {{ selectedTimePeriod === 'Weekly' ? 'weeks' : 'months' }})</span>
                            <span v-else>Normal</span>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- Data Source Note with View Excel Data button -->
                <div class="data-source-note" style="margin-top: 20px; text-align: center;">
                  <span class="note-icon">ℹ️</span> Data is loaded from "z16 {{ selectedProcess }} {{ selectedTimePeriod.toLowerCase() }}.xls" using actual Volume and Fails columns
                  <span v-if="selectedTimePeriod === 'Weekly'"> (Weeks {{ startDate }} to {{ endDate }})</span>
                  <cv-button kind="ghost" size="small" class="view-excel-btn" @click="openExcelViewer()">
                    View Excel Data
                  </cv-button>
                </div>

                <!-- Excel Data Modal -->
                <cv-modal
                  :visible="showExcelData"
                  @modal-hidden="showExcelData = false"
                  size="large"
                  class="excel-data-modal"
                >
                  <template slot="title">
                    Excel Data: z16 {{ selectedProcess }} {{ selectedTimePeriod.toLowerCase() }}.xls
                    <span v-if="selectedTimePeriod === 'Weekly'"> (Weeks {{ startDate }} to {{ endDate }})</span>
                  </template>
                  <template slot="content">
                    <excel-data-viewer
                      :excel-data="excelData"
                      :file-name="`z16 ${selectedProcess} ${selectedTimePeriod.toLowerCase()}.xls`"
                      :loading="loadingExcelData"
                      :error="excelDataError"
                      @close="showExcelData = false"
                    />
                  </template>
                </cv-modal>
              </cv-tile>
            </cv-tab>

            <!-- Top Hitters Tab -->
            <cv-tab label="Top Hitters">
              <cv-tile :light="lightTile" class="chart-tile">
                <div class="chart-header">
                  <h3 class="chart-title">Top Critical Alerts</h3>
                </div>

                <!-- Top Hitters Controls -->
                <div class="top-hitters-controls">
                  <div class="control-group">
                    <div class="control-item">
                      <label class="control-label">Process</label>
                      <cv-dropdown
                        class="control-dropdown"
                        v-model="topHittersProcess"
                        label="Select Process"
                        :items="processOptions"
                        @change="handleTopHittersProcessChange"
                      />
                    </div>

                    <div class="control-item">
                      <label class="control-label">Time Period</label>
                      <cv-dropdown
                        class="control-dropdown"
                        v-model="topHittersTimePeriod"
                        label="Select Time Period"
                        :items="timePeriodOptions"
                        @change="handleTopHittersTimePeriodChange"
                      />
                    </div>

                    <div class="control-item">
                      <label class="control-label">Time Range</label>
                      <cv-dropdown
                        class="control-dropdown"
                        v-model="topHittersTimeRange"
                        label="Select Time Range"
                        :items="topHittersTimeRangeOptions"
                        @change="handleTopHittersTimeRangeChange"
                      />
                    </div>

                    <div class="control-item">
                      <cv-button
                        kind="primary"
                        class="load-top-hitters-button"
                        @click="loadAllPartGroupAlerts"
                      >
                        Load Top Hitters
                      </cv-button>
                    </div>
                  </div>
                </div>

                <div class="top-hitters-content">
                  <!-- Loading State -->
                  <div v-if="loadingTopHitters" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">Loading top critical alerts...</p>
                  </div>

                  <!-- Empty State -->
                  <div v-else-if="filteredTopHitters.length === 0" class="empty-alerts">
                    <p>No critical alerts detected for the selected options.</p>
                  </div>

                  <!-- Top Hitters List -->
                  <div v-else class="top-hitters-list">
                    <div
                      v-for="(alert, index) in filteredTopHitters"
                      :key="index"
                      class="top-hitter-item"
                    >
                      <div class="top-hitter-header">
                        <div class="top-hitter-info">
                          <div class="status-indicator-circle status-critical"></div>
                          <span class="top-hitter-part-group">{{ alert.partGroup }}</span>
                          <span class="top-hitter-date">{{ formatMonth(alert.date, topHittersTimePeriod === 'Weekly') }}</span>
                          <span class="top-hitter-values">
                            Actual: <strong>{{ alert.value.toFixed(2) }}%</strong> vs
                            Expected: <strong>{{ alert.expectedValue.toFixed(2) }}%</strong>
                          </span>
                          <span class="top-hitter-deviation">
                            X-Factor: <strong :class="getDeviationScoreClass(alert.deviationScore)">
                              {{ alert.deviationScore.toFixed(2) }}
                            </strong>
                            <span v-if="alert.consecutiveAboveThreshold && alert.consecutiveAboveThreshold >= 3">
                              ({{ alert.consecutiveAboveThreshold }} {{ topHittersTimePeriod === 'Weekly' ? 'weeks' : 'months' }})
                            </span>
                          </span>
                          <span class="top-hitter-volume-failures">
                            Volume: <strong>{{ alert.volume }}</strong> |
                            Defects: <strong>{{ alert.failures }}</strong>
                          </span>
                        </div>
                        <cv-button
                          kind="ghost"
                          size="small"
                          @click="toggleActionDetails(index)"
                        >
                          {{ expandedItems.includes(index) ? 'Hide Details' : 'Show Details' }}
                        </cv-button>
                      </div>

                      <!-- Expanded Details Section -->
                      <div v-if="expandedItems.includes(index)" class="top-hitter-details">
                        <div class="details-form">
                          <div class="form-row">
                            <label>Part Group:</label>
                            <span>{{ alert.partGroup }}</span>
                          </div>

                          <div class="form-row">
                            <label>Process:</label>
                            <span>{{ alert.process }}</span>
                          </div>

                          <div class="form-row">
                            <label>Volume:</label>
                            <span>{{ alert.volume }}</span>
                          </div>

                          <div class="form-row">
                            <label>Defects:</label>
                            <span>{{ alert.failures }}</span>
                          </div>

                          <div class="form-row">
                            <label>Root Cause:</label>
                            <cv-dropdown
                              v-model="alert.rootCause"
                              label="Select Root Cause"
                              :items="rootCauseOptions"
                            />
                          </div>

                          <div class="form-row">
                            <label>Comments:</label>
                            <cv-text-area
                              v-model="alert.comments"
                              label="Additional Comments"
                              placeholder="Enter any additional comments or notes"
                            />
                          </div>

                          <div class="form-actions">
                            <cv-button
                              kind="primary"
                              size="small"
                              @click="createTopHitterActionItem(alert)"
                            >
                              Create Action Item
                            </cv-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </cv-tile>
            </cv-tab>
          </cv-tabs>
        </div>


      </div>
    </main>
  </div>
</template>

<script>
import MainHeader from "@/components/MainHeader";
import DualFormatChart from "@/components/DualFormatChart/DualFormatChart.vue";
import ExcelDataViewer from "@/components/ExcelDataViewer";

export default {
  name: "PartGroupAnalysis",
  components: {
    MainHeader,
    DualFormatChart,
    ExcelDataViewer
  },
  data() {
    return {
      // UI state
      expandedSideNav: true,
      useFixed: false,
      lightTile: true,
      loading: false,
      showInfoModal: false,
      showExcelData: false,
      loadingExcelData: false,
      excelData: [],
      excelDataError: '',

      // Selection options
      selectedProcess: "FUL",
      processOptions: ["FUL", "FAB"],
      selectedPartGroup: "",
      partGroupOptions: [],
      selectedTimePeriod: "Monthly",
      timePeriodOptions: ["Monthly", "Weekly"],
      startDate: "",
      endDate: "",
      dateOptions: [],

      // Time range selection
      selectedTimeRange: "All Data",

      // Threshold settings
      longTermThreshold: 1.5, // X-factor > 1.5 for sustained problems
      shortTermThreshold: 3.0, // X-factor > 3.0 for short-term spikes
      alphaValue: 0.2, // EWMA smoothing parameter
      minVolume: 30, // Minimum volume threshold

      // Data
      rawData: [],
      processedData: [],
      falloutChartData: [],
      ewmaChartData: [],
      combinedChartData: [],
      alerts: [],

      // Summary statistics
      totalParts: 0,
      totalFailures: 0,
      averageFallout: 0,
      currentTrend: 0,
      shortTermAlertCount: 0,
      longTermAlertCount: 0,

      // Table columns
      alertColumns: [
        "Date",
        "Actual Value (%)",
        "Expected Value (%)",
        "X-Factor",
        "Status"
      ],

      // Top Hitters tab data
      expandedItems: [],
      rootCauseOptions: [
        "Component Failure",
        "Process Issue",
        "Design Flaw",
        "Supplier Quality",
        "Test Equipment",
        "Human Error",
        "Unknown"
      ],
      topHittersProcess: "FUL",
      topHittersTimePeriod: "Monthly",
      topHittersTimeRange: "All Data",
      allPartGroupAlerts: [],
      loadingTopHitters: false,

    };
  },
  mounted() {
    this.loadPartGroups();
    this.adjustMainContentPadding();

    // Initialize Top Hitters tab values
    this.topHittersProcess = "FUL";
    this.topHittersTimePeriod = "Monthly";
    this.topHittersTimeRange = "All Data";

    // Load Top Hitters data when the page loads
    this.loadAllPartGroupAlerts();
  },

  computed: {
    // Time range options based on selected time period
    timeRangeOptions() {
      if (this.selectedTimePeriod === 'Weekly') {
        return [
          "All Data",
          "Last Week",
          "Last 4 Weeks",
          "Last 12 Weeks"
        ];
      } else {
        return [
          "All Data",
          "Last Month",
          "Last 3 Months",
          "Last 6 Months"
        ];
      }
    },

    // Filter alerts based on selected date range
    filteredAlerts() {
      if (!this.alerts || this.alerts.length === 0) {
        return [];
      }

      return this.alerts.filter(alert => {
        return this.isDateInRange(alert.date);
      });
    },

    // Filter processed data based on selected date range
    filteredProcessedData() {
      if (!this.processedData || this.processedData.length === 0) {
        return [];
      }

      return this.processedData.filter(item => {
        return this.isDateInRange(item.date);
      });
    },

    // Time range options for Top Hitters tab
    topHittersTimeRangeOptions() {
      if (this.topHittersTimePeriod === 'Weekly') {
        return [
          "All Data",
          "Last Week",
          "Last 4 Weeks",
          "Last 12 Weeks"
        ];
      } else {
        return [
          "All Data",
          "Last Month",
          "Last 3 Months",
          "Last 6 Months"
        ];
      }
    },

    // Filter top hitters based on selected options
    filteredTopHitters() {
      if (!this.allPartGroupAlerts || this.allPartGroupAlerts.length === 0) {
        return [];
      }

      return this.allPartGroupAlerts.filter(alert => {
        // Filter by process
        if (alert.process !== this.topHittersProcess) {
          return false;
        }

        // Filter by time range
        return this.isDateInTopHittersRange(alert.date);
      });
    }
  },

  watch: {
    expandedSideNav() {
      this.adjustMainContentPadding();
    },
    selectedTimePeriod() {
      // Reset time range to "All Data" when time period changes
      this.selectedTimeRange = "All Data";
    }
  },
  methods: {
    // Load part groups based on selected process
    async loadPartGroups() {
      this.loading = true;
      try {
        let token = this.$store.getters.getToken;
        console.log('Token:', token ? 'Valid token' : 'No token');
        console.log('API Path:', process.env.VUE_APP_API_PATH);

        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_part_groups", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({
            process: this.selectedProcess,
            timePeriod: this.selectedTimePeriod
          }),
        });

        console.log('API Response status:', response.status);

        const data = await this.handleResponse(response);
        console.log('API Response data:', data);

        if (data && data.status_res === "success") {
          this.partGroupOptions = data.part_groups || [];
          console.log('Loaded part groups:', this.partGroupOptions);

          if (this.partGroupOptions.length > 0) {
            this.selectedPartGroup = this.partGroupOptions[0];
            // Load date options before loading data
            await this.loadDateOptions();
            this.loadData();
          } else {
            console.warn('No part groups found');
            this.resetCharts();
          }
        } else {
          console.error("Error loading part groups:", data);
          this.resetCharts();
        }
      } catch (error) {
        console.error("Error loading part groups:", error);
        this.resetCharts();
      } finally {
        this.loading = false;
      }
    },

    // Load data for analysis
    async loadData() {
      this.loading = true;
      try {
        let token = this.$store.getters.getToken;
        console.log('Loading data for part group:', this.selectedPartGroup);

        // Ensure we have valid start and end dates
        if (!this.startDate || !this.endDate) {
          console.log('Start or end date is missing, setting to first and last dates from options');
          if (this.dateOptions.length > 0) {
            this.startDate = this.dateOptions[0];
            this.endDate = this.dateOptions[this.dateOptions.length - 1];
          }
        }

        console.log('Using date range:', {
          startDate: this.startDate,
          endDate: this.endDate,
          formattedStartDate: this.formatMonth(this.startDate),
          formattedEndDate: this.formatMonth(this.endDate)
        });

        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_part_group_data", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({
            process: this.selectedProcess,
            partGroup: this.selectedPartGroup,
            startDate: this.startDate,
            endDate: this.endDate,
            timePeriod: this.selectedTimePeriod
          }),
        });

        console.log('API Response status for data:', response.status);

        const data = await this.handleResponse(response);
        console.log('API Response data:', data);

        if (data && data.status_res === "success" && data.data && data.data.length > 0) {
          this.rawData = data.data;
          console.log('Loaded data:', this.rawData);

          // Log alert counts from API if available
          if (data.alertCounts) {
            console.log('\n==== API ALERT COUNTS ====');
            console.log(`Short-term alerts: ${data.alertCounts.shortTermAlerts}`);
            console.log(`Sustained problems: ${data.alertCounts.sustainedProblems}`);
            console.log(`Resolved problems: ${data.alertCounts.resolvedProblems}`);
            console.log('=========================\n');
          }

          // Don't automatically analyze data - wait for user to click Analyze button
          this.resetCharts();
        } else {
          console.error("Error loading data or empty data:", data);
          this.resetCharts();
        }
      } catch (error) {
        console.error("Error loading data:", error);
        this.resetCharts();
      } finally {
        this.loading = false;
      }
    },

    // Process and analyze the data
    analyzeData() {
      console.log('Analyze button clicked');
      console.log('Raw data:', this.rawData);

      // If no data, show message and return
      if (!this.rawData || this.rawData.length === 0) {
        console.log('No data available');
        this.$bvToast.toast('No data available for analysis. Please select a different part group or time period.', {
          title: 'No Data',
          variant: 'warning',
          solid: true
        });
        return;
      }

      // Ensure we have valid start and end dates
      if (!this.startDate || !this.endDate) {
        console.log('Start or end date is missing, setting to first and last dates from options');
        if (this.dateOptions.length > 0) {
          this.startDate = this.dateOptions[0];
          this.endDate = this.dateOptions[this.dateOptions.length - 1];
        }
      }

      console.log('Analyzing data with date range:', {
        startDate: this.startDate,
        endDate: this.endDate,
        formattedStartDate: this.formatMonth(this.startDate),
        formattedEndDate: this.formatMonth(this.endDate)
      });

      // Set loading state
      this.loading = true;

      try {
        console.log('Processing data...');
        // Process the data
        this.processData();

        // Verify deviation scores
        this.verifyDeviationScores();

        console.log('Generating fallout chart...');
        // Generate charts
        this.generateFalloutChart();

        console.log('Generating EWMA chart...');
        this.generateEWMAChart();

        console.log('Detecting alerts...');
        // Detect alerts
        this.detectAlerts();

        console.log('Calculating statistics...');
        // Calculate summary statistics
        this.calculateSummaryStatistics();

        console.log('Analysis complete');
        console.log('Final alerts:', JSON.stringify(this.alerts));
        console.log('Processed data for tables:', JSON.stringify(this.processedData));
        console.log('Alert columns:', this.alertColumns);
      } catch (error) {
        console.error('Error during analysis:', error);
        this.resetCharts();
      } finally {
        this.loading = false;
      }
    },

    // Process the raw data for analysis
    processData() {
      console.log('Processing data with thresholds:', {
        longTerm: this.longTermThreshold,
        shortTerm: this.shortTermThreshold,
        minVolume: this.minVolume
      });

      // The data is already processed in the backend with the new model
      // We just need to copy it to our processedData array
      this.processedData = [...this.rawData];

      // Ensure dates are in the correct format for the chart
      this.processedData.forEach(item => {
        // Store the original date for reference
        item.originalDate = item.date;

        // Make sure date is in YYYY-MM or YYYY-WW format for the chart
        if (item.date && !item.date.includes('-')) {
          if (this.selectedTimePeriod === 'Weekly') {
            // For weekly data, ensure it's in YYYY-WW format
            // Try to extract year from the date options if available
            let year = new Date().getFullYear();

            // Look for a matching week in dateOptions to get the year
            const matchingOption = this.dateOptions.find(option => {
              if (option.includes('-')) {
                const parts = option.split('-');
                const optWeek = parts[1];
                return optWeek === item.date.padStart(2, '0');
              }
              return false;
            });

            if (matchingOption) {
              const [optYear] = matchingOption.split('-');
              year = optYear;
            }

            item.date = `${year}-${item.date.padStart(2, '0')}`;
          } else {
            // For monthly data, convert to YYYY-MM format
            item.date = this.convertMonthFormat(item.date);
          }
        }
      });

      // Sort the data by date
      this.sortDataByDate();

      // Debug log to verify data from Excel
      console.log('Raw data from Excel:', this.rawData.map(item => ({
        date: item.date,
        volume: item.volume,
        failures: item.failures,
        xFactorScore: item.xFactorScore
      })));

      // Calculate fallout rate for each item (for display purposes)
      this.processedData.forEach(item => {
        item.falloutRate = item.failures / item.volume;
      });

      // Debug log to check if we have the necessary properties
      console.log('Processed data sample:', this.processedData.length > 0 ? {
        date: this.processedData[0].date,
        volume: this.processedData[0].volume,
        failures: this.processedData[0].failures,
        targetRate: this.processedData[0].targetRate,
        xFactorScore: this.processedData[0].xFactorScore,
        expectedFailures: this.processedData[0].expectedFailures,
        deviationScore: this.processedData[0].deviationScore,
        avgDeviationScore: this.processedData[0].avgDeviationScore,
        isShortTermAlert: this.processedData[0].isShortTermAlert,
        isSustainedProblem: this.processedData[0].isSustainedProblem,
        isProblemResolved: this.processedData[0].isProblemResolved
      } : 'No data');

      // Check for missing properties and add defaults if needed
      this.processedData.forEach(item => {
        // Make sure targetRate is correctly set from the API
        if (item.targetRate === undefined || item.targetRate === null) {
          console.warn(`Item ${item.date} missing targetRate, adding default`);
          // Default to the appropriate target rate based on the part group
          // These values should match the targets.xlsx file
          const defaultTargets = {
            'Artemis': this.selectedProcess === 'FUL' ? 0.0039 : 0.0073,
            'Centaur-D': this.selectedProcess === 'FUL' ? 0.0014 : 0.0088,
            'Diana': this.selectedProcess === 'FUL' ? 0.005 : 0.015,
            'Parthenon': this.selectedProcess === 'FUL' ? 0.004 : 0.016,
            'Roosevelt2': 0.009,
            'Saiph': this.selectedProcess === 'FUL' ? 0.005 : 0.025,
            'Themis': this.selectedProcess === 'FUL' ? 0.0051 : 0.0005,
            'Victoria Crypto': this.selectedProcess === 'FUL' ? 0.019 : 0.016,
            'Zeus PSU': this.selectedProcess === 'FUL' ? 0.006 : 0.0006
          };

          item.targetRate = defaultTargets[this.selectedPartGroup] || 0.012; // Default to 1.2% if part group not found
        }
        console.log(`Target rate for ${item.date}: ${item.targetRate * 100}%`);

        // Calculate expected failures based on volume and target rate
        const expectedFailures = item.volume * item.targetRate;
        item.expectedFailures = expectedFailures;

        // Calculate X-factor (actual rate / expected rate)
        if (item.xFactorScore !== undefined && item.xFactorScore !== null) {
          // Use the X-factor from the Excel file if available
          item.deviationScore = item.xFactorScore;
          console.log(`Using X-factor from Excel for ${item.date}: ${item.deviationScore.toFixed(2)}`);
        } else if (item.deviationScore === undefined || item.deviationScore === null) {
          console.warn(`Item ${item.date} missing X-factor, calculating it`);
          if (item.volume > 0 && item.targetRate > 0) {
            // Calculate X-factor: actual rate / target rate
            const actualRate = item.falloutRate; // Already a decimal
            const expectedRate = item.targetRate; // Already a decimal
            item.deviationScore = actualRate / expectedRate;
            console.log(`Calculated X-factor for ${item.date}: ${(actualRate * 100).toFixed(2)}% / ${(expectedRate * 100).toFixed(2)}% = ${item.deviationScore.toFixed(2)}`);
          } else {
            item.deviationScore = 0; // If volume or target rate is 0, X-factor is 0
            console.log(`Setting X-factor to 0 for ${item.date}: volume=${item.volume}, targetRate=${item.targetRate}`);
          }
        } else {
          // Log existing X-factor for debugging
          console.log(`Using existing X-factor for ${item.date}: ${item.deviationScore.toFixed(2)}`);
          console.log(`  Actual Rate: ${(item.falloutRate * 100).toFixed(2)}%, Target Rate: ${(item.targetRate * 100).toFixed(2)}%`);
        }

        // Calculate 3-month average deviation score
        if (item.avgDeviationScore === undefined || item.avgDeviationScore === null) {
          console.warn(`Item ${item.date} missing avgDeviationScore, will calculate later`);
          item.avgDeviationScore = null;
        }

        // Set alert flags
        if (item.isShortTermAlert === undefined) {
          console.warn(`Item ${item.date} missing isShortTermAlert, calculating it`);
          const shortTermThreshold = this.shortTermThreshold || 3.0;
          item.isShortTermAlert = item.deviationScore !== null &&
                                 item.deviationScore > shortTermThreshold &&
                                 item.volume >= this.minVolume;
        }

        if (item.isSustainedProblem === undefined) {
          console.warn(`Item ${item.date} missing isSustainedProblem, adding default`);
          item.isSustainedProblem = false;
        }

        if (item.isProblemResolved === undefined) {
          console.warn(`Item ${item.date} missing isProblemResolved, adding default`);
          item.isProblemResolved = false;
        }
      });

      // Calculate 3-month average deviation scores and track previous problems
      let hadProblemInPast = false;

      this.processedData.forEach((item, index) => {
        // Check if there was a problem in previous months
        if (index > 0) {
          // Look at previous months to see if there was a spike or sustained problem
          for (let i = 0; i < index; i++) {
            const prevItem = this.processedData[i];
            const shortTermThreshold = this.shortTermThreshold || 0.3;
            const longTermThreshold = this.longTermThreshold || 0.15;
            if (prevItem &&
                ((prevItem.deviationScore !== null && prevItem.deviationScore > shortTermThreshold && prevItem.volume >= this.minVolume) ||
                 (prevItem.avgDeviationScore !== null && prevItem.avgDeviationScore > longTermThreshold))) {
              hadProblemInPast = true;
              break;
            }
          }
        }

        // Set the flag for this item
        item.hadPreviousProblem = hadProblemInPast;

        // Calculate 3-month average if needed
        if (item.avgDeviationScore === null && item.deviationScore !== null) {
          // Get previous 2 months if available
          const prevScores = [];
          for (let i = Math.max(0, index - 2); i < index; i++) {
            const prevItem = this.processedData[i];
            if (prevItem && prevItem.deviationScore !== null) {
              prevScores.push(prevItem.deviationScore);
            }
          }

          // Add current month's score
          prevScores.push(item.deviationScore);

          // Calculate average
          if (prevScores.length > 0) {
            const sum = prevScores.reduce((acc, val) => acc + val, 0);
            item.avgDeviationScore = sum / prevScores.length;
            console.log(`Calculated 3-month avg for ${item.date}: ${item.avgDeviationScore.toFixed(2)} from ${prevScores.length} scores`);
          }
        }

        // Update the hadProblemInPast flag based on current item's status
        const shortTermThreshold = this.shortTermThreshold || 0.3;
        const longTermThreshold = this.longTermThreshold || 0.15;
        if ((item.deviationScore !== null && item.deviationScore > shortTermThreshold && item.volume >= this.minVolume) ||
            (item.avgDeviationScore !== null && item.avgDeviationScore > longTermThreshold)) {
          hadProblemInPast = true;
        }
      });
    },

    // Generate expected rate chart data
    generateFalloutChart() {
      // Convert to percentage for display
      this.falloutChartData = this.processedData.map(item => {
        // Ensure date is in the correct format
        let formattedDate = item.date;
        if (!formattedDate.includes('-')) {
          formattedDate = this.convertMonthFormat(formattedDate);
        }

        return {
          date: formattedDate,
          group: "Expected Rate",
          value: item.targetRate * 100
        };
      });

      console.log('Generated expected rate chart data with', this.falloutChartData.length, 'points');
    },

    // Generate X-factor chart data
    generateEWMAChart() {
      // Filter out null X-factor scores
      this.ewmaChartData = this.processedData
        .filter(item => item.deviationScore !== null)
        .map(item => {
          // Ensure date is in the correct format
          let formattedDate = item.date;
          if (!formattedDate.includes('-')) {
            formattedDate = this.convertMonthFormat(formattedDate);
          }

          return {
            date: formattedDate,
            group: "X-Factor",
            value: item.deviationScore
          };
        });

      console.log('X-factor scores:', this.processedData.map(item => {
        return {
          date: item.date,
          xFactor: item.deviationScore,
          avgXFactor: item.avgDeviationScore
        };
      }));

      // Add average X-factor line
      const avgScoreData = this.processedData
        .filter(item => item.avgDeviationScore !== null)
        .map(item => {
          // Ensure date is in the correct format
          let formattedDate = item.date;
          if (!formattedDate.includes('-')) {
            formattedDate = this.convertMonthFormat(formattedDate);
          }

          return {
            date: formattedDate,
            group: "3-Month X-Factor Average",
            value: item.avgDeviationScore
          };
        });

      console.log('Average X-factor data points:', avgScoreData.length);

      // Combine both datasets
      this.ewmaChartData = [...this.ewmaChartData, ...avgScoreData];

      console.log('Generated X-factor chart data with', this.ewmaChartData.length, 'points');

      // Generate combined chart data
      this.generateCombinedChart();
    },

    // Generate combined chart with X-factor and expected rate
    generateCombinedChart() {
      // Create expected failure rate data
      const expectedRateData = this.processedData.map(item => {
        // Ensure expectedFailures is a valid number - simplified to just volume * target rate
        const expectedFailures = typeof item.expectedFailures === 'number' && !isNaN(item.expectedFailures)
          ? item.expectedFailures
          : item.volume * (item.targetRate || 0.02);

        return {
          date: item.date,
          originalDate: item.originalDate || item.date,
          group: "Expected Rate",
          value: (expectedFailures / item.volume) * 100
        };
      });

      // Create X-factor data (only for valid scores)
      const xFactorData = this.processedData
        .filter(item => item.deviationScore !== null)
        .map(item => ({
          date: item.date,
          originalDate: item.originalDate || item.date,
          group: "X-Factor",
          value: item.deviationScore
        }));

      // Create average X-factor data (only for valid scores)
      const avgXFactorData = this.processedData
        .filter(item => item.avgDeviationScore !== null)
        .map(item => ({
          date: item.date,
          originalDate: item.originalDate || item.date,
          group: "3-Month X-Factor Average",
          value: item.avgDeviationScore
        }));

      console.log('Chart data counts:', {
        expectedRateData: expectedRateData.length,
        xFactorData: xFactorData.length,
        avgXFactorData: avgXFactorData.length
      });

      // Combine all datasets
      this.combinedChartData = [
        ...expectedRateData,
        ...xFactorData,
        ...avgXFactorData
      ];

      console.log('Generated combined chart data with', this.combinedChartData.length, 'points');

      // Debug log to check the date format in the chart data
      if (this.combinedChartData.length > 0) {
        console.log('Chart data date format sample:', {
          timeInterval: this.selectedTimePeriod === 'Weekly' ? 'weekly' : 'monthly',
          sampleDates: this.combinedChartData.slice(0, 5).map(item => ({
            date: item.date,
            originalDate: item.originalDate
          }))
        });

        // Log the full date range
        const allDates = [...new Set(this.combinedChartData.map(item => item.date))];
        allDates.sort();
        console.log('All chart dates (sorted):', allDates);

        // Log the selected date range
        console.log('Selected date range:', {
          startDate: this.startDate,
          endDate: this.endDate,
          formattedStartDate: this.formatMonth(this.startDate),
          formattedEndDate: this.formatMonth(this.endDate)
        });
      }
    },

    // Detect alerts based on the processed data - directly from monthly summary table
    detectAlerts() {
      console.log('Detecting alerts directly from monthly summary table (processedData):', this.processedData.length, 'points');

      // Clear existing alerts
      this.alerts = [];

      // Process each row in the monthly summary table (processedData)
      this.processedData.forEach(item => {
        // Use the exact same criteria as in the monthly summary table status column

        // Check if this is a short-term spike (X-factor > 3.0)
        // Use the shortTermThreshold from user settings or default to 3.0
        const shortTermThreshold = this.shortTermThreshold || 3.0;
        const isShortTermSpike = item.deviationScore !== null &&
                                item.deviationScore > shortTermThreshold &&
                                item.volume >= this.minVolume;

        // Check if this is a sustained problem (avg X-factor > threshold and consecutive periods >= 3)
        // Use the longTermThreshold from user settings or default to 1.5
        const longTermThreshold = this.longTermThreshold || 1.5;
        const isSustainedProblem = item.failures > 0 &&
          item.avgDeviationScore !== null &&
          item.avgDeviationScore > longTermThreshold &&
          (item.consecutiveAboveThreshold >= 3 || item.isSustainedProblem);

        // Get the target rate for expected value
        const targetRate = item.targetRate || 0.02; // Default to 2% if not available
        const expectedValue = targetRate * 100;

        // Only add an alert if there's an issue (short-term spike or sustained problem)
        if (isShortTermSpike || isSustainedProblem) {
          // Determine the alert type and status - exactly matching the monthly summary table
          if (isShortTermSpike && isSustainedProblem) {
            // Critical issue - both short-term spike and sustained problem
            console.log(`Adding critical issue alert for ${item.date}: deviation ${item.deviationScore}, avgDeviation ${item.avgDeviationScore}`);
            this.alerts.push({
              date: item.date,
              type: "Short-term Spike",
              value: item.falloutRate * 100,
              expectedValue: expectedValue,
              deviationScore: item.deviationScore,
              hasSustainedProblem: true,
              consecutiveAboveThreshold: item.consecutiveAboveThreshold || 3,
              threshold: shortTermThreshold,
              status: "Critical"
            });
          } else if (isShortTermSpike) {
            // Short-term spike only
            console.log(`Adding short-term spike alert for ${item.date}: deviation ${item.deviationScore.toFixed(2)}`);
            console.log(`  Actual Rate: ${(item.falloutRate * 100).toFixed(2)}%, Target Rate: ${expectedValue.toFixed(2)}%, Deviation: ${item.deviationScore.toFixed(2)}`);
            this.alerts.push({
              date: item.date,
              type: "Short-term Spike",
              value: item.falloutRate * 100,
              expectedValue: expectedValue,
              deviationScore: item.deviationScore,
              hasSustainedProblem: false,
              threshold: shortTermThreshold,
              status: "Alert"
            });
          } else if (isSustainedProblem) {
            // Sustained problem only
            console.log(`Adding sustained problem alert for ${item.date}: avgDeviation ${item.avgDeviationScore}, consecutive ${item.consecutiveAboveThreshold}`);
            this.alerts.push({
              date: item.date,
              type: "Sustained Problem",
              value: item.falloutRate * 100,
              expectedValue: expectedValue,
              deviationScore: item.avgDeviationScore,
              consecutiveAboveThreshold: item.consecutiveAboveThreshold || 3,
              threshold: longTermThreshold,
              status: "Critical"
            });
          }
        }
      });

      // Sort alerts by date
      this.alerts.sort((a, b) => {
        // Handle both YYYY-MM format and month names
        const dateA = this.convertMonthFormat(a.date);
        const dateB = this.convertMonthFormat(b.date);

        return new Date(dateA) - new Date(dateB);
      });

      console.log('Total alerts added:', this.alerts.length);

      // Verify that the X-factor scores in the alerts match the ones in the processed data
      console.log('=== VERIFYING ALERT X-FACTOR SCORES ===');
      this.alerts.forEach(alert => {
        const matchingItem = this.processedData.find(item => item.date === alert.date);
        if (matchingItem) {
          const expectedXFactor = alert.type === 'Sustained Problem' ? matchingItem.avgDeviationScore : matchingItem.deviationScore;
          console.log(`Alert for ${alert.date} (${alert.type}):`);
          console.log(`  Alert X-Factor: ${alert.deviationScore.toFixed(2)}`);
          console.log(`  Expected X-Factor: ${expectedXFactor !== null ? expectedXFactor.toFixed(2) : 'null'}`);

          if (Math.abs(alert.deviationScore - expectedXFactor) > 0.01) {
            console.warn(`  MISMATCH: Alert X-factor for ${alert.date} doesn't match processed data!`);
            // Fix the X-factor in the alert
            alert.deviationScore = expectedXFactor;
            console.log(`  FIXED: Alert X-factor for ${alert.date} set to ${expectedXFactor.toFixed(2)}`);
          }
        } else {
          console.warn(`No matching processed data found for alert on ${alert.date}`);
        }
      });
    },

    // This method has been removed to ensure alerts are only generated from the monthly summary table

    // Verify X-factor scores for debugging
    verifyDeviationScores() {
      console.log('=== VERIFYING X-FACTOR SCORES ===');
      this.processedData.forEach(item => {
        if (item.volume > 0 && item.targetRate > 0) {
          // Calculate expected X-factor using the correct formula: actual rate / target rate
          const actualRate = item.falloutRate; // Already a decimal
          const expectedRate = item.targetRate; // Already a decimal
          const expectedXFactor = actualRate / expectedRate;
          const actualXFactor = item.deviationScore;

          console.log(`Date: ${item.date}`);
          console.log(`  Volume: ${item.volume}, Failures: ${item.failures}`);
          console.log(`  Actual Rate: ${(actualRate * 100).toFixed(2)}%, Target Rate: ${(expectedRate * 100).toFixed(2)}%`);
          console.log(`  Expected X-Factor: ${(actualRate * 100).toFixed(2)}% / ${(expectedRate * 100).toFixed(2)}% = ${expectedXFactor.toFixed(2)}`);
          console.log(`  Actual X-Factor: ${actualXFactor !== null ? actualXFactor.toFixed(2) : 'null'}`);

          if (actualXFactor !== null && Math.abs(expectedXFactor - actualXFactor) > 0.01) {
            console.warn(`  MISMATCH: X-factor for ${item.date} is incorrect!`);
            // Fix the X-factor
            item.deviationScore = expectedXFactor;
            console.log(`  FIXED: X-factor for ${item.date} set to ${expectedXFactor.toFixed(2)}`);
          }
        } else {
          console.log(`Date: ${item.date} - Zero volume or target rate, X-factor should be 0`);
          console.log(`  Volume: ${item.volume}, Target Rate: ${item.targetRate}`);
          console.log(`  Actual X-Factor: ${item.deviationScore !== null ? item.deviationScore.toFixed(2) : 'null'}`);

          if (item.deviationScore !== 0 && item.deviationScore !== null) {
            console.warn(`  MISMATCH: X-factor for ${item.date} should be 0 due to zero volume or target rate!`);
            // Fix the X-factor
            item.deviationScore = 0;
            console.log(`  FIXED: X-factor for ${item.date} set to 0`);
          }
        }
      });
    },

    // Calculate summary statistics
    calculateSummaryStatistics() {
      // Calculate total parts and failures
      this.totalParts = this.processedData.reduce((sum, item) => sum + item.volume, 0);
      this.totalFailures = this.processedData.reduce((sum, item) => sum + item.failures, 0);

      // Calculate average fallout rate
      this.averageFallout = this.totalParts > 0 ? (this.totalFailures / this.totalParts) * 100 : 0;

      // Get current trend value (from the latest data point with valid deviation score)
      const latestWithDeviation = [...this.processedData]
        .reverse()
        .find(item => item.avgDeviationScore !== null);

      this.currentTrend = latestWithDeviation
        ? latestWithDeviation.avgDeviationScore
        : 0;

      // Count alerts
      this.shortTermAlertCount = this.alerts.filter(alert => alert.type === "Short-term Spike").length;
      this.longTermAlertCount = this.alerts.filter(alert => alert.type === "Sustained Problem").length;

      console.log('Summary statistics:', {
        totalParts: this.totalParts,
        totalFailures: this.totalFailures,
        averageFallout: this.averageFallout,
        currentTrend: this.currentTrend,
        shortTermAlertCount: this.shortTermAlertCount,
        longTermAlertCount: this.longTermAlertCount
      });
    },

    // Reset charts and data
    resetCharts() {
      this.falloutChartData = [];
      this.ewmaChartData = [];
      this.combinedChartData = [];
      this.alerts = [];
      this.totalParts = 0;
      this.totalFailures = 0;
      this.averageFallout = 0;
      this.currentTrend = 0;
      this.shortTermAlertCount = 0;
      this.longTermAlertCount = 0;
      this.expandedItems = [];

      // Don't reset Top Hitters data here as it's separate from the Analysis tab
    },

    // Toggle action details for a top hitter
    toggleActionDetails(index) {
      const position = this.expandedItems.indexOf(index);
      if (position === -1) {
        // Add to expanded items
        this.expandedItems.push(index);

        // Initialize root cause and comments if not already set
        const alert = this.filteredTopHitters[index];
        if (alert) {
          if (!alert.rootCause) {
            alert.rootCause = this.rootCauseOptions[0];
          }
          if (!alert.comments) {
            alert.comments = '';
          }
        }
      } else {
        // Remove from expanded items
        this.expandedItems.splice(position, 1);
      }
    },

    // Create an action item in the Action Tracker from the Analysis tab
    createActionItem(alert) {
      // Validate required fields
      if (!alert.rootCause) {
        alert.rootCause = this.rootCauseOptions[0];
      }

      // Format the action description
      const actionDescription = `Address quality issue for ${this.selectedPartGroup} in ${this.selectedProcess} process.
      Date: ${this.formatMonth(alert.date)}
      Actual Rate: ${alert.value.toFixed(2)}%
      Expected Rate: ${alert.expectedValue.toFixed(2)}%
      Deviation: ${alert.deviationScore.toFixed(2)}
      Root Cause: ${alert.rootCause}
      ${alert.comments ? `\nComments: ${alert.comments}` : ''}`;

      // Navigate to the Action Tracker page with the data
      this.$router.push({
        path: '/action-tracker',
        query: {
          createAction: 'true',
          commodity: this.selectedProcess,
          group: this.selectedPartGroup,
          action: actionDescription,
          deadline: this.getDefaultDeadline()
        }
      });
    },

    // Create an action item in the Action Tracker from the Top Hitters tab
    createTopHitterActionItem(alert) {
      // Validate required fields
      if (!alert.rootCause) {
        alert.rootCause = this.rootCauseOptions[0];
      }

      // Format the action description
      const actionDescription = `Address quality issue for ${alert.partGroup} in ${alert.process} process.
      Date: ${this.formatMonth(alert.date)}
      Volume: ${alert.volume}
      Defects: ${alert.failures}
      Actual Rate: ${alert.value.toFixed(2)}%
      Expected Rate: ${alert.expectedValue.toFixed(2)}%
      Deviation: ${alert.deviationScore.toFixed(2)}
      Root Cause: ${alert.rootCause}
      ${alert.comments ? `\nComments: ${alert.comments}` : ''}`;

      // Create notes with additional details
      const notes = `This action item was created from the Top Hitters tab in the Part Group Analysis tool.

      Alert Type: ${alert.hasSustainedProblem ? 'Critical Issue (Short-term Spike + Sustained Problem)' : alert.type}
      Date: ${this.formatMonth(alert.date)}
      Part Group: ${alert.partGroup}
      Process: ${alert.process}

      Production Data:
      - Volume: ${alert.volume}
      - Defects: ${alert.failures}

      Statistical Details:
      - Actual Rate: ${alert.value.toFixed(2)}%
      - Expected Rate: ${alert.expectedValue.toFixed(2)}%
      - Deviation Score: ${alert.deviationScore.toFixed(2)}
      - Threshold: ${alert.threshold}

      Root Cause Analysis: ${alert.rootCause}
      ${alert.comments ? `\nAdditional Comments: ${alert.comments}` : ''}`;

      // Navigate to the Action Tracker page with the data
      this.$router.push({
        path: '/action-tracker',
        query: {
          createAction: 'true',
          commodity: alert.process,
          group: alert.partGroup,
          test: alert.type,
          pn: `${alert.partGroup}-${this.formatMonth(alert.date).replace(/\s/g, '')}`,
          action: actionDescription,
          notes: notes,
          deadline: this.getDefaultDeadline()
        }
      });
    },

    // Load alerts from all part groups
    async loadAllPartGroupAlerts() {
      this.loadingTopHitters = true;
      this.allPartGroupAlerts = [];
      this.expandedItems = [];

      // Ensure we have valid process and time period values
      if (!this.topHittersProcess) {
        this.topHittersProcess = "FUL"; // Default to FUL if not set
      }

      if (!this.topHittersTimePeriod) {
        this.topHittersTimePeriod = "Monthly"; // Default to Monthly if not set
      }

      try {
        let token = this.$store.getters.getToken;
        console.log('Loading all part group alerts for process:', this.topHittersProcess, 'and time period:', this.topHittersTimePeriod);

        // Fetch part groups for the selected process and time period
        const partGroupsResponse = await fetch(process.env.VUE_APP_API_PATH + "get_part_groups", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({
            process: this.topHittersProcess,
            timePeriod: this.topHittersTimePeriod
          }),
        });

        const partGroupsData = await this.handleResponse(partGroupsResponse);

        if (partGroupsData && partGroupsData.status_res === "success") {
          const partGroups = partGroupsData.part_groups || [];
          console.log('Loaded part groups for top hitters:', partGroups);

          // Get date options for the selected process and time period
          const dateOptionsResponse = await fetch(process.env.VUE_APP_API_PATH + "get_date_options", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            body: JSON.stringify({
              process: this.topHittersProcess,
              timePeriod: this.topHittersTimePeriod
            }),
          });

          const dateOptionsData = await this.handleResponse(dateOptionsResponse);

          if (dateOptionsData && dateOptionsData.status_res === "success") {
            const dateOptions = dateOptionsData.date_options || [];

            // Sort date options
            const sortedDateOptions = [...dateOptions].sort((a, b) => {
              if (this.topHittersTimePeriod === 'Weekly') {
                // For weekly data, sort by year and week number
                if (a.includes('-') && b.includes('-')) {
                  const [yearA, weekA] = a.split('-').map(Number);
                  const [yearB, weekB] = b.split('-').map(Number);

                  if (yearA !== yearB) {
                    return yearA - yearB;
                  }
                  return weekA - weekB;
                }
              } else {
                // For monthly data, sort by year and month
                if (a.includes('-') && b.includes('-')) {
                  const [yearA, monthA] = a.split('-').map(Number);
                  const [yearB, monthB] = b.split('-').map(Number);

                  if (yearA !== yearB) {
                    return yearA - yearB;
                  }
                  return monthA - monthB;
                }
              }
              return 0;
            });

            // Get start and end dates based on time range
            let startDate = sortedDateOptions[0];
            let endDate = sortedDateOptions[sortedDateOptions.length - 1];

            // For each part group, load data and extract alerts
            for (const partGroup of partGroups) {
              console.log(`Loading data for part group: ${partGroup}`);

              const response = await fetch(process.env.VUE_APP_API_PATH + "get_part_group_data", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: "Bearer " + token,
                },
                body: JSON.stringify({
                  process: this.topHittersProcess,
                  partGroup: partGroup,
                  startDate: startDate,
                  endDate: endDate,
                  timePeriod: this.topHittersTimePeriod
                }),
              });

              const data = await this.handleResponse(response);

              if (data && data.status_res === "success" && data.data && data.data.length > 0) {
                const rawData = data.data;

                // Process the data to detect alerts
                const processedData = [...rawData];

                // Calculate fallout rate and X-factor for each item
                processedData.forEach(item => {
                  // Calculate fallout rate
                  item.falloutRate = item.failures / item.volume;

                  // Ensure X-factor is correctly calculated
                  if (item.volume > 0 && item.targetRate > 0) {
                    // Calculate X-factor: actual rate / target rate
                    const actualRate = item.falloutRate; // Already a decimal
                    const expectedRate = item.targetRate; // Already a decimal
                    item.deviationScore = actualRate / expectedRate;
                    console.log(`Calculated X-factor for ${item.date}: ${(actualRate * 100).toFixed(2)}% / ${(expectedRate * 100).toFixed(2)}% = ${item.deviationScore.toFixed(2)}`);
                  } else {
                    item.deviationScore = 0; // If volume or target rate is 0, X-factor is 0
                  }
                });

                // Detect alerts for this part group
                const alerts = [];

                processedData.forEach(item => {
                  // Check if this is a short-term spike (X-factor > threshold)
                  // Use the shortTermThreshold from user settings or default to 3.0
                  const shortTermThreshold = this.shortTermThreshold || 3.0;
                  const isShortTermSpike = item.deviationScore !== null &&
                                          item.deviationScore > shortTermThreshold &&
                                          item.volume >= this.minVolume;

                  // Check if this is a sustained problem (avg X-factor > threshold and consecutive periods >= 3)
                  // Use the longTermThreshold from user settings or default to 1.5
                  const longTermThreshold = this.longTermThreshold || 1.5;
                  const isSustainedProblem = item.failures > 0 &&
                    item.avgDeviationScore !== null &&
                    item.avgDeviationScore > longTermThreshold &&
                    (item.consecutiveAboveThreshold >= 3 || item.isSustainedProblem);

                  // Get the target rate for expected value
                  const targetRate = item.targetRate || 0.02; // Default to 2% if not available
                  const expectedValue = targetRate * 100;

                  // Only add an alert if there's an issue (short-term spike or sustained problem)
                  if (isShortTermSpike || isSustainedProblem) {
                    // Determine the alert type and status
                    if (isShortTermSpike && isSustainedProblem) {
                      // Critical issue - both short-term spike and sustained problem
                      // Ensure date is properly formatted for weekly data
                      let formattedDate = item.date;
                      if (this.topHittersTimePeriod === 'Weekly' && !formattedDate.includes('-')) {
                        // For weekly data, ensure it's in YYYY-WW format
                        const weekNum = parseInt(formattedDate);
                        let year;
                        if (weekNum >= 40 && weekNum <= 52) {
                          year = '2024'; // Late weeks are from 2024
                        } else if (weekNum >= 1 && weekNum <= 13) {
                          year = '2025'; // Early weeks are from 2025
                        } else {
                          year = '2024'; // Default to 2024
                        }
                        formattedDate = `${year}-${weekNum.toString().padStart(2, '0')}`;
                      }

                      alerts.push({
                        date: formattedDate,
                        type: "Short-term Spike",
                        value: item.falloutRate * 100,
                        expectedValue: expectedValue,
                        deviationScore: item.deviationScore,
                        hasSustainedProblem: true,
                        consecutiveAboveThreshold: item.consecutiveAboveThreshold || 3,
                        threshold: shortTermThreshold,
                        status: "Critical",
                        partGroup: partGroup,
                        process: this.topHittersProcess,
                        volume: item.volume,
                        failures: item.failures
                      });
                    } else if (isShortTermSpike) {
                      // Short-term spike only
                      // Ensure date is properly formatted for weekly data
                      let formattedDate = item.date;
                      if (this.topHittersTimePeriod === 'Weekly' && !formattedDate.includes('-')) {
                        // For weekly data, ensure it's in YYYY-WW format
                        const weekNum = parseInt(formattedDate);
                        let year;
                        if (weekNum >= 40 && weekNum <= 52) {
                          year = '2024'; // Late weeks are from 2024
                        } else if (weekNum >= 1 && weekNum <= 13) {
                          year = '2025'; // Early weeks are from 2025
                        } else {
                          year = '2024'; // Default to 2024
                        }
                        formattedDate = `${year}-${weekNum.toString().padStart(2, '0')}`;
                      }

                      alerts.push({
                        date: formattedDate,
                        type: "Short-term Spike",
                        value: item.falloutRate * 100,
                        expectedValue: expectedValue,
                        deviationScore: item.deviationScore,
                        hasSustainedProblem: false,
                        threshold: shortTermThreshold,
                        status: "Alert",
                        partGroup: partGroup,
                        process: this.topHittersProcess,
                        volume: item.volume,
                        failures: item.failures
                      });
                    } else if (isSustainedProblem) {
                      // Sustained problem only
                      // Ensure date is properly formatted for weekly data
                      let formattedDate = item.date;
                      if (this.topHittersTimePeriod === 'Weekly' && !formattedDate.includes('-')) {
                        // For weekly data, ensure it's in YYYY-WW format
                        const weekNum = parseInt(formattedDate);
                        let year;
                        if (weekNum >= 40 && weekNum <= 52) {
                          year = '2024'; // Late weeks are from 2024
                        } else if (weekNum >= 1 && weekNum <= 13) {
                          year = '2025'; // Early weeks are from 2025
                        } else {
                          year = '2024'; // Default to 2024
                        }
                        formattedDate = `${year}-${weekNum.toString().padStart(2, '0')}`;
                      }

                      alerts.push({
                        date: formattedDate,
                        type: "Sustained Problem",
                        value: item.falloutRate * 100,
                        expectedValue: expectedValue,
                        deviationScore: item.avgDeviationScore,
                        consecutiveAboveThreshold: item.consecutiveAboveThreshold || 3,
                        threshold: this.longTermThreshold || 1.5,
                        status: "Critical",
                        partGroup: partGroup,
                        process: this.topHittersProcess,
                        volume: item.volume,
                        failures: item.failures
                      });
                    }
                  }
                });

                // Add critical alerts to the allPartGroupAlerts array
                const criticalAlerts = alerts.filter(alert => alert.status === "Critical");
                this.allPartGroupAlerts.push(...criticalAlerts);
              }
            }

            // Sort all alerts by deviation score (highest first)
            this.allPartGroupAlerts.sort((a, b) => b.deviationScore - a.deviationScore);

            console.log(`Loaded ${this.allPartGroupAlerts.length} critical alerts from all part groups`);
          }
        }
      } catch (error) {
        console.error("Error loading all part group alerts:", error);
      } finally {
        this.loadingTopHitters = false;
      }
    },

    // Handle process change in Top Hitters tab
    handleTopHittersProcessChange() {
      console.log('Top Hitters process changed to:', this.topHittersProcess);
      this.allPartGroupAlerts = [];
      this.expandedItems = [];
    },

    // Handle time period change in Top Hitters tab
    handleTopHittersTimePeriodChange() {
      console.log('Top Hitters time period changed to:', this.topHittersTimePeriod);
      this.topHittersTimeRange = "All Data";
      this.allPartGroupAlerts = [];
      this.expandedItems = [];
    },

    // Handle time range change in Top Hitters tab
    handleTopHittersTimeRangeChange() {
      console.log('Top Hitters time range changed to:', this.topHittersTimeRange);
      this.expandedItems = [];
    },

    // Check if a date is within the selected time range for Top Hitters
    isDateInTopHittersRange(dateStr) {
      if (this.topHittersTimeRange === "All Data") {
        return true;
      }

      const date = new Date(this.convertMonthFormat(dateStr));
      const now = new Date();

      if (this.topHittersTimePeriod === 'Weekly') {
        // Weekly time ranges
        switch (this.topHittersTimeRange) {
          case "Last Week": {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(now.getDate() - 7);
            return date >= oneWeekAgo;
          }
          case "Last 4 Weeks": {
            const fourWeeksAgo = new Date();
            fourWeeksAgo.setDate(now.getDate() - 28);
            return date >= fourWeeksAgo;
          }
          case "Last 12 Weeks": {
            const twelveWeeksAgo = new Date();
            twelveWeeksAgo.setDate(now.getDate() - 84);
            return date >= twelveWeeksAgo;
          }
          default:
            return true;
        }
      } else {
        // Monthly time ranges
        switch (this.topHittersTimeRange) {
          case "Last Month": {
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(now.getMonth() - 1);
            return date >= oneMonthAgo;
          }
          case "Last 3 Months": {
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(now.getMonth() - 3);
            return date >= threeMonthsAgo;
          }
          case "Last 6 Months": {
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(now.getMonth() - 6);
            return date >= sixMonthsAgo;
          }
          default:
            return true;
        }
      }
    },

    // Get a default deadline (30 days from now)
    getDefaultDeadline() {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    },

    // Event handlers
    handleProcessChange() {
      this.loadPartGroups();
    },

    handlePartGroupChange() {
      this.loadData();
    },

    handleTimePeriodChange() {
      console.log('Time period changed to:', this.selectedTimePeriod);
      // Reset chart data
      this.resetCharts();
      // Reset date options
      this.dateOptions = [];
      this.startDate = "";
      this.endDate = "";
      // Reset time range to "All Data"
      this.selectedTimeRange = "All Data";
      // Load part groups for the new time period
      this.loadPartGroups();
      // This will trigger loadDateOptions which will set the start and end dates
      // to the first and last dates from the Excel file
    },

    handleDateChange() {
      console.log('Date changed - Start:', this.startDate, 'End:', this.endDate);
      this.loadData();
    },

    updateThresholds() {
      console.log('Updating thresholds:', {
        shortTerm: this.shortTermThreshold,
        longTerm: this.longTermThreshold,
        minVolume: this.minVolume
      });

      // Re-analyze data with new thresholds if we have data
      if (this.rawData && this.rawData.length > 0) {
        this.analyzeData();
      }
    },

    // Load available dates based on the selected process and time period
    async loadDateOptions() {
      console.log('=== LOADING DATE OPTIONS ===');
      this.dateOptions = []; // Clear existing options
      this.startDate = "";
      this.endDate = "";

      try {
        let token = this.$store.getters.getToken;
        console.log('Loading date options for process:', this.selectedProcess, 'and time period:', this.selectedTimePeriod);

        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_date_options", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({
            process: this.selectedProcess,
            timePeriod: this.selectedTimePeriod
          }),
        });

        console.log('API Response status for date options:', response.status);

        const data = await this.handleResponse(response);
        console.log('API Response data for date options:', data);

        if (data && data.status_res === "success" && data.date_options && data.date_options.length > 0) {
          // Store the date options - these should be the actual dates from the spreadsheet
          this.dateOptions = data.date_options;
          console.log('Loaded date options from API:', this.dateOptions);

          // Sort the date options to ensure they're in chronological order
          this.sortDateOptions();
          console.log('Sorted date options:', this.dateOptions);

          // Set default start and end dates to the earliest and latest dates
          if (this.dateOptions.length > 0) {
            // Set the start date to the first date (earliest) and end date to the last date (latest)
            this.startDate = this.dateOptions[0];
            this.endDate = this.dateOptions[this.dateOptions.length - 1];
            console.log('Set start date to:', this.startDate, 'and end date to:', this.endDate);

            // Force update to ensure the UI reflects the changes
            this.$forceUpdate();

            // Log the selected date range for debugging
            console.log('Selected date range after setting:', {
              startDate: this.startDate,
              endDate: this.endDate,
              formattedStartDate: this.formatMonth(this.startDate),
              formattedEndDate: this.formatMonth(this.endDate)
            });
          } else {
            console.warn('No date options available');
          }

          // For debugging - check if the dropdown options are correctly set
          console.log('Date options after setting:', {
            dateOptions: this.dateOptions,
            startDate: this.startDate,
            endDate: this.endDate,
            startDateElement: document.querySelector('select[label="Start Date"]'),
            endDateElement: document.querySelector('select[label="End Date"]')
          });
        } else {
          console.error("Error loading date options or empty data:", data);
          this.dateOptions = [];
          this.startDate = "";
          this.endDate = "";

          // If we have no date options from the API, add some fallback options for testing
          this.addFallbackDateOptions();
        }
      } catch (error) {
        console.error("Error loading date options:", error);
        this.dateOptions = [];
        this.startDate = "";
        this.endDate = "";

        // Add fallback options in case of error
        this.addFallbackDateOptions();
      }
    },

    // Sort date options in chronological order
    sortDateOptions() {
      if (this.selectedTimePeriod === 'Weekly') {
        // For weekly data, sort by year and week number
        this.dateOptions.sort((a, b) => {
          // Handle YYYY-WW format
          if (a.includes('-') && b.includes('-')) {
            const [yearA, weekA] = a.split('-').map(Number);
            const [yearB, weekB] = b.split('-').map(Number);

            if (yearA !== yearB) {
              return yearA - yearB;
            }
            return weekA - weekB;
          }

          // Handle numeric week format
          if (!isNaN(parseInt(a)) && !isNaN(parseInt(b))) {
            return parseInt(a) - parseInt(b);
          }

          // Default string comparison
          return a.localeCompare(b);
        });
      } else {
        // For monthly data, sort by year and month
        this.dateOptions.sort((a, b) => {
          // Handle YYYY-MM format
          if (a.includes('-') && b.includes('-')) {
            const [yearA, monthA] = a.split('-').map(Number);
            const [yearB, monthB] = b.split('-').map(Number);

            if (yearA !== yearB) {
              return yearA - yearB;
            }
            return monthA - monthB;
          }

          // Handle month names (Jan, Feb, etc.)
          const monthOrder = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
          };

          if (monthOrder[a] && monthOrder[b]) {
            return monthOrder[a] - monthOrder[b];
          }

          // Default string comparison
          return a.localeCompare(b);
        });
      }
    },

    // Add fallback date options for testing
    addFallbackDateOptions() {
      if (this.selectedTimePeriod === 'Weekly') {
        console.log('Adding fallback weekly date options');
        // Create options for weeks 1-52 for years 2022, 2023, 2024, and 2025
        this.dateOptions = [];

        // Add weeks for 2022
        for (let i = 40; i <= 52; i++) {
          this.dateOptions.push(`2022-${i.toString().padStart(2, '0')}`);
        }

        // Add weeks for 2023
        for (let i = 1; i <= 52; i++) {
          this.dateOptions.push(`2023-${i.toString().padStart(2, '0')}`);
        }

        // Add weeks for 2024
        for (let i = 1; i <= 52; i++) {
          this.dateOptions.push(`2024-${i.toString().padStart(2, '0')}`);
        }

        // Add weeks for 2025
        for (let i = 1; i <= 13; i++) {
          this.dateOptions.push(`2025-${i.toString().padStart(2, '0')}`);
        }

        this.startDate = '2022-40';
        this.endDate = '2025-13';
      } else {
        console.log('Adding fallback monthly date options');
        // Create options for all months from April 2022 to March 2025 (based on z16 FUL monthly.xls)
        this.dateOptions = [];

        // Add months for 2022 (Apr-Dec)
        for (let i = 4; i <= 12; i++) {
          this.dateOptions.push(`2022-${i.toString().padStart(2, '0')}`);
        }

        // Add months for 2023
        for (let i = 1; i <= 12; i++) {
          this.dateOptions.push(`2023-${i.toString().padStart(2, '0')}`);
        }

        // Add months for 2024
        for (let i = 1; i <= 12; i++) {
          this.dateOptions.push(`2024-${i.toString().padStart(2, '0')}`);
        }

        // Add months for 2025 (Jan-Mar)
        for (let i = 1; i <= 3; i++) {
          this.dateOptions.push(`2025-${i.toString().padStart(2, '0')}`);
        }

        this.startDate = '2022-04';
        this.endDate = '2025-03';
      }
    },

    // This method has been removed as we no longer use default date options

    closeInfoModal() {
      console.log('Closing info modal');
      this.showInfoModal = false;
    },

    toggleSideNav() {
      console.log('Toggling side navigation');
      this.expandedSideNav = !this.expandedSideNav;
    },

    // Open Excel data viewer
    async openExcelViewer() {
      this.showExcelData = true;
      // Always reload Excel data to ensure we get the correct data for the current process
      await this.loadExcelData();
    },

    // Load all Excel data
    async loadExcelData() {
      console.log(`Loading Excel data for process: ${this.selectedProcess}`);
      this.loadingExcelData = true;
      this.excelDataError = '';
      this.excelData = [];

      try {
        let token = this.$store.getters.getToken;
        console.log('Loading Excel data for process:', this.selectedProcess);

        // Check if token exists
        if (!token) {
          console.error('No authentication token available');
          this.excelDataError = 'Authentication required. Please log in again.';
          this.loadingExcelData = false;
          this.generateSampleExcelData();
          return;
        }

        try {
          // Fetch data from the API
          console.log('Sending request to:', process.env.VUE_APP_API_PATH + "get_all_excel_data");
          console.log('Request body:', JSON.stringify({ process: this.selectedProcess }));

          const response = await fetch(process.env.VUE_APP_API_PATH + "get_all_excel_data", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            body: JSON.stringify({
              process: this.selectedProcess,
              timePeriod: this.selectedTimePeriod
            }),
          });

          console.log('API Response status for Excel data:', response.status);

          if (response.status === 401) {
            this.excelDataError = 'Authentication error. Please log in again.';
            console.error('Authentication error when loading Excel data');
            this.excelData = [];
            return;
          }

          if (!response.ok) {
            this.excelDataError = `Server error: ${response.status}. Unable to load Excel data.`;
            console.error(`Server error: ${response.status}`);
            this.excelData = [];
            return;
          }

          const data = await response.json();
          console.log('API Response data:', data);

          if (data && data.status_res === "success" && data.excel_data && data.excel_data.length > 0) {
            this.excelData = data.excel_data;
            console.log(`Loaded Excel data for ${this.selectedProcess}:`, this.excelData.length, 'rows');
            console.log('Sample row:', this.excelData[0]);
            console.log('File name from API:', data.file_name || 'Not provided');
          } else {
            console.error(`Error loading Excel data for ${this.selectedProcess} or empty data:`, data);
            this.excelDataError = data.message || "No data available in the Excel file.";
            this.excelData = [];
          }
        } catch (fetchError) {
          console.error("Error fetching Excel data:", fetchError);
          this.excelDataError = "Could not connect to server.";
          this.excelData = [];
        }
      } catch (error) {
        console.error("Error in loadExcelData:", error);
        this.excelDataError = `Error loading Excel data: ${error.message}.`;
        this.excelData = [];
      } finally {
        this.loadingExcelData = false;
      }
    },

    // This method has been removed as we no longer use sample Excel data

    // Helper function to convert date format to YYYY-MM or YYYY-WW
    convertMonthFormat(dateStr) {
      if (!dateStr) return dateStr;

      // If it's already in YYYY-MM or YYYY-WW format, return as is
      if (dateStr.includes('-')) return dateStr;

      // Check if we're using weekly data
      const isWeeklyData = this.selectedTimePeriod === 'Weekly';

      if (isWeeklyData) {
        // For weekly data, ensure it's in YYYY-WW format
        if (!isNaN(parseInt(dateStr))) {
          // If it's just a week number, add the year
          const year = new Date().getFullYear();
          return `${year}-${dateStr.padStart(2, '0')}`;
        }
        // Handle "Week XX YYYY" format
        if (dateStr.startsWith('Week')) {
          const parts = dateStr.split(' ');
          if (parts.length >= 3) {
            const week = parts[1];
            const year = parts[2];
            return `${year}-${week.padStart(2, '0')}`;
          }
        }
      } else {
        // For monthly data
        // Convert month names to YYYY-MM format if needed
        if (dateStr.length <= 3) {
          // Use the current year as default
          const currentYear = new Date().getFullYear();
          const monthMap = {
            'Jan': `${currentYear}-01`,
            'Feb': `${currentYear}-02`,
            'Mar': `${currentYear}-03`,
            'Apr': `${currentYear}-04`,
            'May': `${currentYear}-05`,
            'Jun': `${currentYear}-06`,
            'Jul': `${currentYear}-07`,
            'Aug': `${currentYear}-08`,
            'Sep': `${currentYear}-09`,
            'Oct': `${currentYear}-10`,
            'Nov': `${currentYear}-11`,
            'Dec': `${currentYear}-12`
          };
          return monthMap[dateStr] || dateStr;
        } else if (dateStr.includes(' ')) {
          // Handle "MMM YYYY" format (e.g., "Jan 2025")
          const parts = dateStr.split(' ');
          if (parts.length === 2) {
            const month = parts[0];
            const year = parts[1];

            const monthMap = {
              'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
              'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
              'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
            };

            if (monthMap[month] && !isNaN(parseInt(year))) {
              return `${year}-${monthMap[month]}`;
            }
          }
        }
      }

      return dateStr;
    },

    // Format month or week for display in the table
    formatMonth(dateStr, forceWeekly) {
      if (!dateStr) return '';

      // If it's already a short month name, return as is
      if (dateStr.length <= 3 && isNaN(parseInt(dateStr))) return dateStr;

      // Check if we're using weekly data
      // Use forceWeekly parameter to override the default behavior for Top Hitters tab
      const isWeeklyData = forceWeekly || this.selectedTimePeriod === 'Weekly' || this.topHittersTimePeriod === 'Weekly';

      // Handle "undefined YYYY" format that appears in the Top Hitters tab
      if (dateStr.startsWith('undefined')) {
        // Extract the year if present
        const parts = dateStr.split(' ');
        if (parts.length > 1 && !isNaN(parseInt(parts[1]))) {
          const year = parts[1];
          // Check if we can determine the week from context
          if (isWeeklyData) {
            // Default to week 1 if we can't determine the actual week
            return `Week 1 ${year}`;
          } else {
            // Default to January if we can't determine the actual month
            return `Jan ${year}`;
          }
        }
      }

      if (isWeeklyData) {
        // Handle weekly data format (YYYY-WW)
        if (dateStr.includes('-')) {
          const [year, week] = dateStr.split('-');
          return `Week ${parseInt(week)} ${year}`;
        }
        // If it's just a week number, format it with appropriate year
        if (!isNaN(parseInt(dateStr))) {
          // For standalone week numbers, use the year from context or determine based on week number
          let year, week;

          if (dateStr.length > 4) {
            // If it's a combined format like "202501"
            year = dateStr.substring(0, 4);
            week = dateStr.substring(4);
          } else if (dateStr.length === 4) {
            // If it might be a 2-digit year + 2-digit week (e.g., "2501")
            year = "20" + dateStr.substring(0, 2);
            week = dateStr.substring(2);
          } else {
            // Just a week number, determine year based on week number
            const weekNum = parseInt(dateStr);
            if (weekNum >= 40 && weekNum <= 52) {
              year = '2024'; // Late weeks are from 2024
            } else if (weekNum >= 1 && weekNum <= 13) {
              year = '2025'; // Early weeks are from 2025
            } else {
              year = '2024'; // Default to 2024
            }
            week = dateStr;
          }

          console.log(`Formatted week: ${dateStr} -> Week ${parseInt(week)} ${year}`);
          return `Week ${parseInt(week)} ${year}`;
        }
        // If it already starts with "Week", check if it has a year
        if (dateStr.startsWith('Week')) {
          const parts = dateStr.split(' ');
          if (parts.length < 3) {
            // Add year if missing
            const year = new Date().getFullYear();
            return `${dateStr} ${year}`;
          }
          return dateStr;
        }
      } else {
        // Handle monthly data format (YYYY-MM)
        if (dateStr.includes('-')) {
          const [year, month] = dateStr.split('-');
          const monthNames = {
            '01': 'Jan',
            '02': 'Feb',
            '03': 'Mar',
            '04': 'Apr',
            '05': 'May',
            '06': 'Jun',
            '07': 'Jul',
            '08': 'Aug',
            '09': 'Sep',
            '10': 'Oct',
            '11': 'Nov',
            '12': 'Dec'
          };

          // Always show the year for clarity
          return `${monthNames[month] || 'Jan'} ${year}`;
        }
      }

      // If it's already in a readable format, return as is
      return dateStr;
    },

    // Check if a row should be highlighted as an alert
    isAlertRow(item) {
      const shortTermThreshold = this.shortTermThreshold || 3.0;
      const longTermThreshold = this.longTermThreshold || 1.5;

      return (item.deviationScore !== null && item.deviationScore > shortTermThreshold && item.volume >= this.minVolume) ||
             (item.avgDeviationScore !== null && item.avgDeviationScore > longTermThreshold);
    },

    // Helper method for checking if a data point has an X-factor above threshold
    hasDeviationAboveThreshold(score) {
      if (score === null) return false;
      const shortTermThreshold = this.shortTermThreshold || 3.0;
      const longTermThreshold = this.longTermThreshold || 1.5;
      return score > shortTermThreshold || score > longTermThreshold;
    },

    // Sort data by date
    sortDataByDate() {
      if (this.processedData && this.processedData.length > 0) {
        this.processedData.sort((a, b) => {
          // Handle both YYYY-MM format and month names
          const dateA = this.convertMonthFormat(a.date);
          const dateB = this.convertMonthFormat(b.date);

          return new Date(dateA) - new Date(dateB);
        });
      }
    },





    // Get CSS class for issue type
    getIssueTypeClass(type) {
      switch (type) {
        case 'Short-term': return 'issue-short-term'; // Maps to status-warning
        case 'Long-term': return 'issue-long-term';   // Maps to status-error
        case 'Resolved': return 'issue-resolved';     // Maps to status-success
        default: return '';
      }
    },

    // Get CSS class for status
    getStatusClass(type, hasSustainedProblem) {
      if (type === 'Short-term Spike' && hasSustainedProblem) {
        return 'status-critical';
      }

      switch (type) {
        case 'Short-term Spike': return 'status-warning';
        case 'Sustained Problem': return 'status-error';
        default: return 'status-info';
      }
    },

    // Get CSS class for X-factor
    getDeviationScoreClass(score) {
      if (score === null || score === undefined) return '';
      if (score > 3.0) return 'deviation-high';
      if (score > 1.5) return 'deviation-critical';
      if (score < 1.0) return 'deviation-good';
      return 'deviation-normal';
    },



    // Get CSS class for alert level
    getAlertLevelClass(level) {
      switch (level) {
        case 0: return 'alert-normal';
        case 1: return 'alert-warning';
        case 2: return 'alert-critical';
        default: return '';
      }
    },

    // Calculate expected value for an alert
    getExpectedValue(alert) {
      // Try to find the corresponding data point
      const dataPoint = this.processedData.find(item => item.date === alert.date);

      if (dataPoint) {
        // Simplified to just use the target rate directly
        const targetRate = dataPoint.targetRate || 0.02; // Default to 2% if not available

        // Return target rate as percentage
        return targetRate * 100; // Target rate as percentage
      }

      // Default fallback
      return 2.0; // Default target rate of 2%
    },

    // Get text for alert level
    getAlertLevelText(level) {
      switch (level) {
        case 0: return 'Normal';
        case 1: return 'Short-term Spike';
        case 2: return 'Sustained Problem';
        default: return 'Unknown';
      }
    },

    adjustMainContentPadding() {
      // This method will be called when the side navigation state changes
      // You can add DOM manipulation here if needed
      console.log('Adjusting main content padding, side nav expanded:', this.expandedSideNav);

      // Apply CSS class to main content based on side nav state
      const mainContent = document.querySelector('.main-content');
      if (mainContent) {
        if (this.expandedSideNav) {
          mainContent.classList.add('main-content-expanded');
        } else {
          mainContent.classList.remove('main-content-expanded');
        }
      }
    },

    // This method has been removed as we no longer use test data

    // Threshold methods have been removed as we now use hard-coded thresholds in DualFormatChart



    // Handle time range selection from dropdown
    handleTimeRangeChange() {
      console.log('Time range changed to:', this.selectedTimeRange);

      if (this.dateOptions.length === 0) {
        console.warn('No date options available');
        return;
      }

      // Sort the date options to ensure they're in chronological order
      this.sortDateOptions();

      // Get the last date from the sorted options
      const lastDate = this.dateOptions[this.dateOptions.length - 1];
      console.log('Last date in options:', lastDate);

      // Set date range based on selected time range
      switch (this.selectedTimeRange) {
        case "All Data":
          // Set to full date range
          this.startDate = this.dateOptions[0];
          this.endDate = lastDate;
          console.log(`Set to show all data: ${this.formatMonth(this.startDate)} to ${this.formatMonth(lastDate)}`);
          break;

        case "Last Month":
          // For monthly data, set to last month
          this.startDate = lastDate;
          this.endDate = lastDate;
          console.log(`Set to show last month: ${this.formatMonth(lastDate)}`);
          break;

        case "Last 3 Months":
          // Set to last 3 months
          this.setLastNPeriods(3);
          break;

        case "Last 6 Months":
          // Set to last 6 months
          this.setLastNPeriods(6);
          break;

        case "Last Week":
          // For weekly data, set to last week
          this.startDate = lastDate;
          this.endDate = lastDate;
          console.log(`Set to show last week: ${this.formatMonth(lastDate)}`);
          break;

        case "Last 4 Weeks":
          // Set to last 4 weeks
          this.setLastNPeriods(4);
          break;

        case "Last 12 Weeks":
          // Set to last 12 weeks
          this.setLastNPeriods(12);
          break;
      }

      // Analyze the data with the new date range
      this.analyzeData();
    },

    // Helper method to set date range to last N periods
    setLastNPeriods(n) {
      if (this.dateOptions.length === 0) {
        console.warn('No date options available');
        return;
      }

      // Sort the date options to ensure they're in chronological order
      this.sortDateOptions();

      // Get the last date from the sorted options
      const lastDate = this.dateOptions[this.dateOptions.length - 1];

      // Calculate the start date (n periods before the last date)
      const startIndex = Math.max(0, this.dateOptions.length - n);
      const startDate = this.dateOptions[startIndex];

      this.startDate = startDate;
      this.endDate = lastDate;

      console.log(`Set to show last ${n} periods: ${this.formatMonth(startDate)} to ${this.formatMonth(lastDate)}`);
    },

    // Show the last week or month in the data (for the button in the header)
    showLastPeriod() {
      if (this.dateOptions.length === 0) {
        console.warn('No date options available');
        return;
      }

      // Sort the date options to ensure they're in chronological order
      this.sortDateOptions();

      // Get the last date from the sorted options
      const lastDate = this.dateOptions[this.dateOptions.length - 1];
      console.log('Last date in options:', lastDate);

      if (this.selectedTimePeriod === 'Weekly') {
        // For weekly data, set both start and end date to the last week
        this.startDate = lastDate;
        this.endDate = lastDate;
        console.log(`Set to show last week: ${this.formatMonth(lastDate)}`);
        // Update the dropdown to match
        this.selectedTimeRange = "Last Week";
      } else {
        // For monthly data, set both start and end date to the last month
        this.startDate = lastDate;
        this.endDate = lastDate;
        console.log(`Set to show last month: ${this.formatMonth(lastDate)}`);
        // Update the dropdown to match
        this.selectedTimeRange = "Last Month";
      }

      // Analyze the data with the new date range
      this.analyzeData();
    },

    // Helper methods for alerts

    getAlertCardClass(alertType) {
      switch (alertType) {
        case "Short-term Spike":
          return "alert-card-warning";
        case "Sustained Problem":
          return "alert-card-error";
        case "Problem Resolved":
          return "alert-card-success";
        default:
          return "alert-card-info";
      }
    },

    getSimplifiedAlertType(alertType) {
      switch (alertType) {
        case "Short-term Spike":
          return "Sudden Spike";
        case "Sustained Problem":
          return "Ongoing Issue";
        case "Problem Resolved":
          return "Issue Resolved";
        default:
          return alertType;
      }
    },

    getAlertMessage(alert) {
      let periodType, consecutivePeriods;

      // Special case for critical issues (both short-term spike and sustained problem)
      if (alert.type === "Short-term Spike" && alert.hasSustainedProblem) {
        periodType = this.selectedTimePeriod === 'Weekly' ? 'weeks' : 'months';
        consecutivePeriods = alert.consecutiveAboveThreshold || 3;
        return `CRITICAL ISSUE: Both a short-term spike (X-factor > ${alert.threshold}) and a sustained problem (${consecutivePeriods} ${periodType}). This requires immediate and comprehensive action.`;
      }

      switch (alert.type) {
        case "Short-term Spike":
          return `X-factor exceeds the threshold of ${alert.threshold}. This indicates a significant unexpected increase in failures.`;
        case "Sustained Problem":
          periodType = this.selectedTimePeriod === 'Weekly' ? 'weeks' : 'months';
          consecutivePeriods = alert.consecutiveAboveThreshold || 3;
          return `This issue has persisted for ${consecutivePeriods} ${periodType}. The average X-factor is above the threshold of ${alert.threshold}. This requires attention.`;
        default:
          return "";
      }
    },

    async handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized
          console.error("Unauthorized access");
          // Redirect to login if needed
          if (this.$router) {
            this.$router.push('/login');
          }
        }
        console.error(`HTTP error! status: ${response.status}`);
        return { status_res: "error", message: `HTTP error! status: ${response.status}` };
      }

      try {
        const data = await response.json();
        return data;
      } catch (error) {
        console.error("Error parsing JSON response:", error);
        return { status_res: "error", message: "Error parsing JSON response" };
      }
    },

    // Helper method to check if a date is within the selected range
    isDateInRange(dateStr) {
      if (!dateStr || !this.startDate || !this.endDate) {
        return true; // If no date range is selected, include all dates
      }

      // Normalize dates for comparison
      const normalizedDate = this.normalizeDate(dateStr);
      const normalizedStart = this.normalizeDate(this.startDate);
      const normalizedEnd = this.normalizeDate(this.endDate);

      // Check if the date is within the range (inclusive)
      return normalizedDate >= normalizedStart && normalizedDate <= normalizedEnd;
    },

    // Normalize date format for comparison
    normalizeDate(dateStr) {
      if (!dateStr) return '';

      // If it's already in YYYY-MM or YYYY-WW format, return as is
      if (/^\d{4}-\d{2}$/.test(dateStr)) {
        return dateStr;
      }

      // For weekly data
      if (this.selectedTimePeriod === 'Weekly') {
        // Handle "Week XX YYYY" format
        if (dateStr.startsWith('Week')) {
          const parts = dateStr.split(' ');
          if (parts.length >= 3) {
            const weekNum = parts[1].padStart(2, '0');
            const year = parts[2];
            return `${year}-${weekNum}`;
          }
        }

        // If it's just a week number
        if (!isNaN(parseInt(dateStr))) {
          // Try to extract year from context or determine based on week number
          if (dateStr.length > 4) {
            // If it's a combined format like "202501"
            const year = dateStr.substring(0, 4);
            const week = dateStr.substring(4);
            return `${year}-${week.padStart(2, '0')}`;
          } else if (dateStr.length === 4) {
            // If it might be a 2-digit year + 2-digit week (e.g., "2501")
            const year = "20" + dateStr.substring(0, 2);
            const week = dateStr.substring(2);
            return `${year}-${week.padStart(2, '0')}`;
          } else {
            // Just a week number, determine year based on week number
            const weekNum = parseInt(dateStr);
            let year;

            if (weekNum >= 40 && weekNum <= 52) {
              year = '2024'; // Late weeks are from 2024
            } else if (weekNum >= 1 && weekNum <= 13) {
              year = '2025'; // Early weeks are from 2025
            } else {
              year = '2024'; // Default to 2024
            }

            console.log(`Normalized week: ${dateStr} -> ${year}-${weekNum.toString().padStart(2, '0')}`);
            return `${year}-${weekNum.toString().padStart(2, '0')}`;
          }
        }

        // Handle "WXX YY" format (e.g., "W01 '24" or "W01 '25")
        if (dateStr.startsWith('W') && dateStr.includes("'")) {
          const weekNum = dateStr.substring(1, dateStr.indexOf(' ')).padStart(2, '0');
          const yearPart = dateStr.split("'")[1]; // Extract '24 or '25
          // Make sure we handle the case where the year part might have additional characters
          const yearDigits = yearPart.trim().split(' ')[0];
          const year = '20' + yearDigits; // Convert '24 to 2024 or '25 to 2025
          console.log(`Normalized "W" format date: "${dateStr}" to "${year}-${weekNum}"`);
          return `${year}-${weekNum}`;
        }
      } else {
        // For monthly data
        // Handle month abbreviations (Jan, Feb, etc.)
        const monthMap = {
          'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
          'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
          'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        // If it's a month abbreviation
        if (monthMap[dateStr]) {
          const year = new Date().getFullYear();
          return `${year}-${monthMap[dateStr]}`;
        }

        // Handle "MMM YYYY" format (e.g., "Jan 2023")
        const parts = dateStr.split(' ');
        if (parts.length === 2 && monthMap[parts[0]] && !isNaN(parseInt(parts[1]))) {
          return `${parts[1]}-${monthMap[parts[0]]}`;
        }
      }

      // Default fallback
      return dateStr;
    }
  }
};
</script>

<style scoped>
.x-factor-analysis {
  background-color: #161616;
  min-height: 100vh;
  color: #f4f4f4;
  display: flex;
  flex-direction: column;
}

.main-content {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
  flex: 1;
  transition: padding-left 0.3s;
}

.main-content-expanded {
  padding-left: 16rem; /* Adjust this value based on your side nav width */
}

.page-header {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0;
  color: #f4f4f4;
}

.page-subtitle {
  color: #c6c6c6;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-icon, .calendar-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.last-period-btn {
  margin-right: 10px;
}

/* Info Modal Styles */
.info-content {
  color: #f4f4f4;
  line-height: 1.5;
}

.info-intro {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
}

.chart-explanation {
  margin-bottom: 1.5rem;
}

.chart-explanation ul {
  list-style-type: none;
  padding-left: 0.5rem;
  margin-bottom: 1.5rem;
}

.chart-explanation li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.chart-item-blue {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #0f62fe;
  margin-right: 0.5rem;
}

.chart-item-purple {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6929c4;
  margin-right: 0.5rem;
}

.chart-item-violet {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #8a3ffc;
  margin-right: 0.5rem;
}

.chart-item-pink {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ee5396;
  margin-right: 0.5rem;
}

.threshold-orange {
  display: inline-block;
  width: 20px;
  height: 3px;
  background-color: #ff832b;
  margin-right: 0.5rem;
}

.threshold-red {
  display: inline-block;
  width: 20px;
  height: 3px;
  background-color: #da1e28;
  margin-right: 0.5rem;
}

.threshold-green {
  display: inline-block;
  width: 20px;
  height: 3px;
  background-color: #24a148;
  margin-right: 0.5rem;
}

.info-content h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  color: #f4f4f4;
}

.info-content h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #f4f4f4;
}

.info-content p {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #c6c6c6;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.example-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.example-table th,
.example-table td {
  padding: 0.5rem;
  text-align: left;
  border: 1px solid #525252;
}

.example-table .table-header {
  background-color: #393939;
  font-weight: bold;
}

.info-item {
  background-color: #262626;
  padding: 1rem;
  border-radius: 4px;
}

.alert-examples {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-example {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: #262626;
  padding: 1rem;
  border-radius: 4px;
}

.alert-example .status-indicator-circle {
  flex-shrink: 0;
  margin-right: 0.75rem;
}

.alert-example p {
  margin: 0;
}

.controls-section {
  margin-bottom: 2rem;
}

.controls-tile {
  padding: 1.5rem;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.simplified-results {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.chart-tile, .alerts-tile, .stats-tile {
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-start;
  margin-left: auto;
}

.chart-controls .control-item {
  min-width: 180px;
}

.chart-title, .alerts-title, .stats-title {
  font-size: 1.25rem;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 0;
  color: #f4f4f4;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  grid-column: span 2;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #c6c6c6;
}

.empty-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: #262626;
  border-radius: 4px;
  color: #c6c6c6;
}

.empty-alerts {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background-color: #262626;
  border-radius: 4px;
  color: #c6c6c6;
  margin-bottom: 1rem;
}

/* Alert Table Styles */
.alert-table {
  width: 100%;
  background-color: #262626;
  border-radius: 4px;
  overflow: hidden;
}

.alert-table-header {
  background-color: #393939;
  padding: 1rem;
}

.alert-table-title {
  font-size: 1rem;
  font-weight: 600;
  color: #f4f4f4;
}

.alert-table-column-headers {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
  background-color: #393939;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #525252;
}

.alert-column-header {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f4f4f4;
}

.alert-table-rows {
  max-height: 300px;
  overflow-y: auto;
}

.alert-table-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #393939;
}

.alert-table-row:hover {
  background-color: #353535;
}

.alert-cell {
  font-size: 0.875rem;
  color: #f4f4f4;
  display: flex;
  align-items: center;
}

/* Chart Description Styles */
.chart-description {
  margin-bottom: 1rem;
  color: #c6c6c6;
  font-size: 0.875rem;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
  border-top: 1px solid #393939;
  padding-top: 1rem;
}

.legend-section {
  margin-bottom: 0.5rem;
}

.legend-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #f4f4f4;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  color: #f4f4f4;
}

.legend-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.legend-line {
  width: 24px;
  height: 2px;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

/* Alerts and Stats Styles */
.alerts-stats-tile {
  padding: 1.5rem;
}

.alerts-stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.alerts-stats-title {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
  color: #f4f4f4;
}

.stats-summary {
  display: flex;
  gap: 0.75rem;
}

.stat-pill {
  background-color: #393939;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
}

.stat-label {
  color: #c6c6c6;
}

.stat-value {
  color: #f4f4f4;
  font-weight: 600;
}

.stat-value.alert-count {
  color: #ff832b;
  font-weight: 700;
}

/* Alert Cards Styles */
.simplified-alerts {
  margin-top: 1rem;
}

.alert-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-card {
  background-color: #262626;
  border-radius: 4px;
  overflow: hidden;
  border-left: 4px solid #6f6f6f;
}

.alert-card-warning {
  border-left-color: #ff832b;
}

.alert-card-error {
  border-left-color: #da1e28;
}

.alert-card-success {
  border-left-color: #24a148;
}

.alert-card-info {
  border-left-color: #0f62fe;
}

.alert-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.1);
}

.alert-info {
  display: flex;
  flex-direction: column;
}

.alert-date {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f4f4f4;
}

.alert-type {
  font-size: 0.75rem;
  color: #c6c6c6;
}

.alert-card-body {
  padding: 0.75rem 1rem;
}

.alert-value {
  font-size: 1rem;
  font-weight: 600;
  color: #f4f4f4;
  margin-bottom: 0.5rem;
}

.alert-message {
  font-size: 0.875rem;
  color: #c6c6c6;
  line-height: 1.4;
}

.alert-score {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #262626;
  border-radius: 4px;
  display: inline-block;
}

.score-label {
  color: #c6c6c6;
  margin-right: 0.25rem;
}

.score-value {
  font-weight: 600;
  color: #f4f4f4;
}

.alert-summary {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-count-badge {
  background-color: #393939;
  color: #f4f4f4;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-block;
}

/* Empty Alerts Styles */
.empty-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-alerts-icon {
  font-size: 2.5rem;
  color: #24a148;
  margin-bottom: 1rem;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(36, 161, 72, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-alerts-sub {
  color: #c6c6c6;
  margin-top: 0.25rem;
}

/* Status Indicator Styles */
.status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #6f6f6f;
}

.status-warning {
  background-color: #ff832b;
}

.status-error {
  background-color: #da1e28;
}

.status-critical {
  background-color: #8a3ffc; /* Purple for critical issues */
}

.status-success {
  background-color: #24a148;
}

.status-info {
  background-color: #0f62fe;
}

/* Issue Status Styles */
.issue-short-term {
  background-color: #ff832b; /* Same as status-warning */
}

.issue-long-term {
  background-color: #da1e28; /* Same as status-error */
}

.issue-resolved {
  background-color: #24a148; /* Same as status-success */
}

.no-issues {
  color: #8d8d8d;
  font-style: italic;
  font-size: 0.75rem;
}



.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.stat-item {
  background-color: #262626;
  border-radius: 4px;
  padding: 1rem;
  text-align: center;
}

.stat-label {
  font-size: 0.875rem;
  color: #c6c6c6;
  margin: 0 0 0.5rem 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 400;
  margin: 0;
  color: #f4f4f4;
}

/* Monthly Data Table Styles */
.monthly-data-section {
  margin-top: 2rem;
  border-top: 1px solid #393939;
  padding-top: 1.5rem;
}

.monthly-data-title {
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0 0 1rem 0;
  color: #f4f4f4;
}

.monthly-data-table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.monthly-data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  color: #f4f4f4;
}

.monthly-data-table th {
  background-color: #393939;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  position: sticky;
  top: 0;
}

.monthly-data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #393939;
}

.monthly-data-table tr:hover {
  background-color: #353535;
}

.monthly-data-table .alert-row {
  background-color: rgba(255, 131, 43, 0.1);
}

.monthly-data-table .alert-row:hover {
  background-color: rgba(255, 131, 43, 0.15);
}

.monthly-data-table .debug-info {
  text-align: center;
  color: #8d8d8d;
  font-style: italic;
  padding: 2rem;
}

.data-source-note {
  margin: 1rem 0;
  font-size: 0.875rem;
  color: #8d8d8d;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.note-icon {
  margin-right: 0.25rem;
}

.view-excel-btn {
  margin-left: 0.5rem;
}

.alerts-section, .monthly-summary-section {
  margin-top: 2rem;
  margin-bottom: 1rem;
  background-color: #262626;
  border-radius: 4px;
  padding: 1rem;
}

.summary-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #f4f4f4;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.alerts-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #f4f4f4;
}

.alert-counts {
  display: flex;
  gap: 1rem;
}

.alert-count {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.status-warning {
  background-color: rgba(255, 131, 43, 0.15);
  color: #ff832b;
  border: 1px solid rgba(255, 131, 43, 0.3);
}

.status-error {
  background-color: rgba(218, 30, 40, 0.15);
  color: #da1e28;
  border: 1px solid rgba(218, 30, 40, 0.3);
}

.status-success {
  background-color: rgba(36, 161, 72, 0.15);
  color: #24a148;
  border: 1px solid rgba(36, 161, 72, 0.3);
}

.status-info {
  background-color: rgba(15, 98, 254, 0.15);
  color: #0f62fe;
  border: 1px solid rgba(15, 98, 254, 0.3);
}

.status-error {
  background-color: rgba(218, 30, 40, 0.15);
  color: #da1e28;
  border: 1px solid rgba(218, 30, 40, 0.3);
}

.status-success {
  background-color: rgba(36, 161, 72, 0.15);
  color: #24a148;
  border: 1px solid rgba(36, 161, 72, 0.3);
}

.status-info {
  background-color: rgba(15, 98, 254, 0.15);
  color: #0f62fe;
  border: 1px solid rgba(15, 98, 254, 0.3);
}

.excel-data-modal .cv-modal-container {
  max-width: 90%;
  height: 80vh;
}

.excel-data-modal .cv-modal-content {
  height: calc(100% - 4rem);
  padding: 0;
  overflow: hidden;
}

.deviation-high {
  color: #ff832b;
  font-weight: 600;
}

.deviation-critical {
  color: #da1e28;
  font-weight: 600;
}

.deviation-normal {
  color: #f4f4f4;
}

.deviation-good {
  color: #24a148;
  font-weight: 600;
}

/* Alerts Section Styles */
.alerts-section {
  margin-top: 2rem;
  border-top: 1px solid #393939;
  padding-top: 1.5rem;
}

.alerts-title {
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0 0 1rem 0;
  color: #f4f4f4;
}

.alert-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.alert-count-badge {
  background-color: #393939;
  color: #f4f4f4;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.alert-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.alert-card {
  background-color: #262626;
  border-radius: 4px;
  overflow: hidden;
  border-left: 4px solid transparent;
}

.alert-card.warning {
  border-left-color: #ff832b;
}

.alert-card.error {
  border-left-color: #da1e28;
}

.alert-card.success {
  border-left-color: #24a148;
}

.alert-card-header {
  padding: 0.75rem 1rem;
  background-color: #393939;
  display: flex;
  align-items: center;
}

.status-indicator-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator-circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-warning {
  background-color: #ff832b;
}

.status-error {
  background-color: #da1e28;
}

.status-success {
  background-color: #24a148;
}

.alert-info {
  flex: 1;
}

.alert-date {
  font-size: 0.875rem;
  color: #f4f4f4;
  font-weight: 600;
}

.alert-type {
  font-size: 0.75rem;
  color: #c6c6c6;
}

.alert-card-body {
  padding: 1rem;
}

.alert-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #f4f4f4;
  margin-bottom: 0.5rem;
}

.alert-score {
  font-size: 0.875rem;
  color: #c6c6c6;
  margin-bottom: 0.5rem;
}

.score-value {
  font-weight: 600;
  color: #f4f4f4;
}

.alert-message {
  font-size: 0.875rem;
  color: #c6c6c6;
  line-height: 1.4;
}

.empty-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-alerts-icon {
  font-size: 2rem;
  color: #24a148;
  margin-bottom: 1rem;
  width: 48px;
  height: 48px;
  background-color: rgba(36, 161, 72, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-alerts-sub {
  color: #c6c6c6;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .results-grid {
    grid-template-columns: 1fr;
  }

  .loading-container {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .controls-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.analyze-button-container {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.analyze-button {
  width: 100%;
  font-weight: 600;
  height: 48px;
  transition: all 0.2s ease;
}

.analyze-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Top Hitters Tab Styles */
.top-hitters-content {
  margin-top: 1rem;
}

.top-hitters-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.top-hitter-item {
  background-color: #262626;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #393939;
}

.top-hitter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #333333;
}

.top-hitters-controls {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  background-color: #262626;
  border-radius: 4px;
  padding: 1rem;
  border: 1px solid #393939;
}

.control-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.control-item {
  flex: 1;
  min-width: 200px;
}

.control-label {
  display: block;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  color: #f4f4f4;
}

.control-dropdown {
  width: 100%;
}

.load-top-hitters-button {
  width: 100%;
  height: 40px;
}

.top-hitter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.top-hitter-part-group {
  font-weight: 600;
  background-color: #393939;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  min-width: 120px;
}

.top-hitter-date {
  font-weight: 600;
  min-width: 100px;
}

.top-hitter-values {
  color: #c6c6c6;
}

.top-hitter-deviation {
  margin-left: 1rem;
}

.top-hitter-volume-failures {
  font-size: 0.875rem;
  color: #c6c6c6;
  background-color: rgba(15, 98, 254, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(15, 98, 254, 0.2);
  margin-left: 1rem;
}

.top-hitter-details {
  padding: 1rem;
  background-color: #262626;
  border-top: 1px solid #393939;
}

.details-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row label {
  font-weight: 600;
  color: #f4f4f4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

/* Debug table styles */
.debug-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  color: #f4f4f4;
  font-size: 0.875rem;
}

.debug-table th {
  background-color: #393939;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
}

.debug-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #393939;
}

.debug-table tr:hover {
  background-color: #353535;
}

/* Deviation score classes */
.deviation-high {
  color: #fa4d56;
  font-weight: bold;
}

.deviation-critical {
  color: #ff832b;
  font-weight: bold;
}

.deviation-normal {
  color: #f4f4f4;
}

.deviation-good {
  color: #42be65;
  font-weight: bold;
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .analyze-button-container {
    grid-column: 1 / -1;
    margin-top: 1rem;
  }
}

/* Custom date select styles */
.date-label {
  display: block;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  color: #f4f4f4;
}

.date-select {
  width: 100%;
  padding: 0.5rem;
  background-color: #262626;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  height: 40px;
  font-size: 0.875rem;
  appearance: menulist;
}

.date-select:focus {
  outline: 2px solid #0f62fe;
  outline-offset: -2px;
}

.date-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.date-select option {
  background-color: #262626;
  color: #f4f4f4;
  padding: 0.5rem;
}
</style>
