<template>
  <div class="stacked-bar-chart-container">
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>Loading chart data...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="no-data-message">
      No data available for the chart
    </div>
    <div v-else ref="chartContainer" class="chart-container" :style="{ height }"></div>
  </div>
</template>

<script>
import { StackedBarChart } from '@carbon/charts';
import '@carbon/charts/styles.css';

export default {
  name: 'StackedBarChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    options: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    initChart() {
      if (!this.data || this.data.length === 0) return;

      const chartContainer = this.$refs.chartContainer;
      if (!chartContainer) return;

      // Create chart options with event handlers
      const chartOptions = {
        ...this.options,
        data: {
          ...this.options.data,
          onclick: (data) => {
            console.log('Bar clicked via onclick:', data);
            this.$emit('bar-click', data);
          }
        }
      };

      // Initialize the chart
      this.chart = new StackedBarChart(chartContainer, {
        data: this.data,
        options: chartOptions
      });

      // Add direct click handler to the chart container
      chartContainer.addEventListener('click', (event) => {
        // This is a fallback in case the Carbon Charts click event doesn't work
        console.log('Container clicked directly:', event);

        // Find the closest bar element
        const barElement = event.target.closest('.bar');
        if (barElement) {
          console.log('Bar element found:', barElement);

          // Try to find the data for this bar
          const barData = this.findBarData(barElement);
          if (barData) {
            console.log('Found bar data:', barData);
            this.$emit('bar-click', { data: barData });
          } else {
            // If we couldn't find the data, try to get it from the class name
            const classes = barElement.getAttribute('class').split(' ');

            for (const cls of classes) {
              if (cls.startsWith('fill-')) {
                const parts = cls.split('-');
                if (parts.length >= 3) {
                  // The format is typically fill-{groupIndex}-{keyIndex}
                  const groupIndex = parseInt(parts[1]);
                  const keyIndex = parseInt(parts[2]);

                  // Try to find the corresponding data point
                  if (!isNaN(groupIndex) && !isNaN(keyIndex) && this.data.length > 0) {
                    // Get unique groups and keys
                    const groups = [...new Set(this.data.map(d => d.group))];
                    const keys = [...new Set(this.data.map(d => d.key))];

                    if (groupIndex < groups.length && keyIndex < keys.length) {
                      const group = groups[groupIndex];
                      const key = keys[keyIndex];

                      // Find the data point
                      const dataPoint = this.data.find(d => d.group === group && d.key === key);
                      if (dataPoint) {
                        console.log('Found data point from class:', dataPoint);
                        this.$emit('bar-click', { data: dataPoint });
                        return;
                      }
                    }
                  }
                }
              }
            }

            // Last resort: emit the first data point for the clicked month
            if (this.data.length > 0) {
              // Try to extract the month from the position
              const svgElement = barElement.closest('svg');
              if (svgElement) {
                const rect = barElement.getBoundingClientRect();
                const svgRect = svgElement.getBoundingClientRect();
                const relativeX = rect.left - svgRect.left;
                const totalWidth = svgRect.width;

                // Calculate which month this might be
                const monthIndex = Math.floor((relativeX / totalWidth) * this.getUniqueMonths().length);
                const months = this.getUniqueMonths();

                if (monthIndex >= 0 && monthIndex < months.length) {
                  const month = months[monthIndex];
                  const dataForMonth = this.data.filter(d => d.key === month);

                  if (dataForMonth.length > 0) {
                    console.log('Using first data point for month:', month);
                    this.$emit('bar-click', { data: dataForMonth[0] });
                  }
                }
              }
            }
          }
        }
      });
    },
    updateChart() {
      if (!this.chart) {
        this.initChart();
        return;
      }

      if (!this.data || this.data.length === 0) {
        if (this.chart) {
          this.chart.destroy();
          this.chart = null;
        }
        return;
      }

      // Update chart options with event handlers
      const chartOptions = {
        ...this.options,
        data: {
          ...this.options.data,
          onclick: (data) => {
            console.log('Bar clicked via onclick (update):', data);
            this.$emit('bar-click', data);
          }
        }
      };

      // Update the chart data and options
      this.chart.model.setData(this.data);
      this.chart.model.setOptions(chartOptions);
    },

    // Helper method to find data for a bar element
    findBarData(barElement) {
      if (!barElement || !this.data) return null;

      // Try to get data attributes
      const group = barElement.getAttribute('data-group');
      const key = barElement.getAttribute('data-key');

      if (group && key) {
        // Find the matching data item
        return this.data.find(item => item.group === group && item.key === key);
      }

      // If data attributes aren't available, try to use class names
      const classes = barElement.getAttribute('class').split(' ');
      let foundGroup = null;
      let foundKey = null;

      for (const cls of classes) {
        if (cls.startsWith('group-')) {
          foundGroup = cls.replace('group-', '');
        } else if (cls.startsWith('key-')) {
          foundKey = cls.replace('key-', '');
        }
      }

      if (foundGroup && foundKey) {
        // Find the matching data item
        return this.data.find(item =>
          item.group.toString().toLowerCase().replace(/\s+/g, '-') === foundGroup &&
          item.key.toString().toLowerCase().replace(/\s+/g, '-') === foundKey
        );
      }

      return null;
    },

    // Helper method to get unique months from the data
    getUniqueMonths() {
      if (!this.data || this.data.length === 0) return [];

      // Extract all unique keys (months) from the data
      const months = [...new Set(this.data.map(item => item.key))];

      // Sort months chronologically
      months.sort((a, b) => {
        // Assuming format is YYYY-MM
        return a.localeCompare(b);
      });

      return months;
    }
  }
};
</script>

<style scoped>
.stacked-bar-chart-container {
  position: relative;
  width: 100%;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: #666;
  font-style: italic;
}

.chart-container {
  width: 100%;
}
</style>
