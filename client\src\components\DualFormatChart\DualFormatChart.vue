<template>
  <div style="position: relative;" class="carbon-chart-container">
    <ccv-line-chart ref="lineChart" :data="formattedData" :options="options"></ccv-line-chart>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';

Vue.use(chartsVue);

export default {
  name: 'DualFormatChart',
  props: {
    data: Array,
    loading: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: "400px"
    },
    eventType: {
      type: Function,
      default: () => {}
    },
    title: {
      type: String,
      default: 'Line Chart with Thresholds'
    },
    timeInterval: {
      type: String,
      default: 'monthly'
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formattedData: [],
      options: {
        title: `${this.title}`,
        tooltip: {
          showTotal: false,
          customHTML: (data) => {
            const dateLabel = this.timeInterval === 'weekly' ? 'Week' : 'Date';

            // Format the date for display in tooltip
            let displayDate = data[0].date;

            return `
              <div style="padding: 10px; background: #262626; color: #f4f4f4; border-radius: 4px;">
                <div><strong>${dateLabel}:</strong> ${displayDate}</div>
                <div><strong>Group:</strong> ${data[0].group}</div>
                <div><strong>Value:</strong> ${data[0].value.toFixed(2)}${data[0].group.includes('Rate') ? '%' : ''}</div>
              </div>
            `;
          }
        },
        axes: {
          bottom: {
            title: this.timeInterval === 'weekly' ? 'Week / Year' : 'Month / Year',
            mapsTo: 'date',
            scaleType: 'labels',
            ticks: {
              rotation: this.timeInterval === 'weekly' ? -45 : -45,
              formatter: (label) => {
                // For weekly data, we might want to shorten the display
                if (this.timeInterval === 'weekly' && label.startsWith('Week')) {
                  // Extract week number and year
                  const parts = label.split(' ');
                  if (parts.length >= 3) {
                    // Format as "W## YY" to save space
                    const weekNum = parts[1];
                    const year = parts[2].slice(2); // Get last 2 digits of year
                    return `W${weekNum} '${year}`;
                  }
                }
                return label;
              }
            }
          },
          left: {
            mapsTo: 'value',
            title: 'Value (%)',
            scaleType: 'linear',
            includeZero: true
          }
        },
        grid: {
          x: {enabled: false},
          y: {enabled: true}
        },
        color: {
          scale: {
            'Expected Rate': '#6929c4',
            'X-Factor': '#8a3ffc',
            '3-Month X-Factor Average': '#ee5396'
          }
        },
        curve: 'curveMonotoneX',
        legend: {
          alignment: 'center',
          enabled: true,
          clickable: true,
          truncation: {
            type: 'none'
          },
          color: '#f4f4f4',
          position: 'bottom'
        },
        data: {
          loading: this.loading
        },
        height: this.height,
        resizable: true,
        animations: true
      }
    };
  },
  watch: {
    data: {
      handler(newData) {
        console.log('DualFormatChart data updated:', newData);
        this.formatData();
        this.updateChart();
      },
      deep: true
    },
    title() {
      this.updateChart();
    },
    loading() {
      this.updateChart();
    },
    height() {
      this.updateChart();
    },
    timeInterval() {
      console.log('DualFormatChart timeInterval updated:', this.timeInterval);
      this.formatData();
      this.updateChart();
    },
    startDate() {
      console.log('DualFormatChart startDate updated:', this.startDate);
      this.formatData();
      this.updateChart();
    },
    endDate() {
      console.log('DualFormatChart endDate updated:', this.endDate);
      this.formatData();
      this.updateChart();
    }
  },
  methods: {
    formatData() {
      if (!this.data || this.data.length === 0) {
        this.formattedData = [];
        return;
      }

      // Filter data based on start and end dates if provided
      let filteredData = [...this.data];

      if (this.startDate && this.endDate) {
        console.log('Filtering chart data by date range:', this.startDate, 'to', this.endDate);

        filteredData = this.data.filter(item => {
          // Use originalDate for comparison if available
          const dateToCompare = item.originalDate || item.date;

          // For comparison, we need to handle different date formats
          let isAfterStart = true;
          let isBeforeEnd = true;

          // Convert dates to comparable format
          const itemDate = this.normalizeDate(dateToCompare);
          const startDate = this.normalizeDate(this.startDate);
          const endDate = this.normalizeDate(this.endDate);

          if (startDate) {
            isAfterStart = itemDate >= startDate;
          }

          if (endDate) {
            isBeforeEnd = itemDate <= endDate;
          }

          return isAfterStart && isBeforeEnd;
        });

        console.log(`Filtered data from ${this.data.length} to ${filteredData.length} points`);
      }

      // Format the data based on the time interval
      if (this.timeInterval === 'weekly') {
        // For weekly data, ensure the date is in "Week XX" format
        this.formattedData = filteredData.map(item => {
          let formattedDate = item.date;

          // If it already starts with "Week", keep it as is
          if (formattedDate.startsWith('Week')) {
            return {
              ...item,
              date: formattedDate,
              // Store original date for sorting
              originalDate: item.originalDate || formattedDate
            };
          }

          // If it's a number or contains a dash, format it as "Week XX YYYY"
          if (!isNaN(parseInt(formattedDate)) || formattedDate.includes('-')) {
            // Extract week number and year
            let weekNum, year;
            if (formattedDate.includes('-')) {
              const parts = formattedDate.split('-');
              year = parts[0];
              weekNum = parseInt(parts[1] || parts[0]);
            } else if (formattedDate.length > 4) {
              // Handle formats like "202501" (year + week)
              year = formattedDate.substring(0, 4);
              weekNum = parseInt(formattedDate.substring(4));
            } else {
              weekNum = parseInt(formattedDate);
              year = new Date().getFullYear();
            }
            formattedDate = `Week ${weekNum} ${year}`;
          }

          // Store original date for sorting
          const originalDate = item.originalDate || formattedDate;

          return {
            ...item,
            date: formattedDate,
            originalDate: originalDate
          };
        });

        // Sort the data by year and week number for proper display
        this.formattedData.sort((a, b) => {
          // Extract week and year from "Week XX YYYY" format
          const partsA = a.date.split(' ');
          const partsB = b.date.split(' ');

          // Get week numbers
          const weekA = parseInt(partsA[1] || '0');
          const weekB = parseInt(partsB[1] || '0');

          // Get years (if available)
          const yearA = partsA.length > 2 ? parseInt(partsA[2]) : new Date().getFullYear();
          const yearB = partsB.length > 2 ? parseInt(partsB[2]) : new Date().getFullYear();

          console.log(`Sorting: ${a.date} (${yearA}-${weekA}) vs ${b.date} (${yearB}-${weekB})`);

          // First sort by year
          if (yearA !== yearB) {
            return yearA - yearB;
          }

          // Then sort by week
          return weekA - weekB;
        });

        // Log the sorted data for debugging
        console.log('Sorted formatted data:', this.formattedData.map(item => item.date));
      } else {
        // For monthly data, ensure the date is in "MMM YYYY" format
        this.formattedData = filteredData.map(item => {
          let formattedDate = item.date;

          // If it's already a month name (Jan, Feb, etc.), keep it as is
          if (['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].includes(formattedDate)) {
            return {
              ...item,
              date: formattedDate,
              originalDate: item.originalDate || formattedDate
            };
          }

          // If it contains a dash, format it as "MMM YYYY"
          if (formattedDate.includes('-')) {
            const [year, month] = formattedDate.split('-');
            const monthNames = {
              '01': 'Jan', '02': 'Feb', '03': 'Mar', '04': 'Apr',
              '05': 'May', '06': 'Jun', '07': 'Jul', '08': 'Aug',
              '09': 'Sep', '10': 'Oct', '11': 'Nov', '12': 'Dec'
            };
            formattedDate = `${monthNames[month]} ${year}`;
          }

          // Store original date for sorting
          const originalDate = item.originalDate || formattedDate;

          return {
            ...item,
            date: formattedDate,
            originalDate: originalDate
          };
        });

        // Sort the data by month for proper display
        const monthOrder = {
          'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
          'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        };

        this.formattedData.sort((a, b) => {
          // Extract month and year
          const aMonth = a.date.split(' ')[0];
          const bMonth = b.date.split(' ')[0];

          // If both have years (e.g., "Jan 2025")
          if (a.date.includes(' ') && b.date.includes(' ')) {
            const aYear = parseInt(a.date.split(' ')[1]);
            const bYear = parseInt(b.date.split(' ')[1]);

            if (aYear !== bYear) {
              return aYear - bYear;
            }
          }

          // Sort by month
          return monthOrder[aMonth] - monthOrder[bMonth];
        });
      }

      console.log('Formatted data for chart:', this.formattedData);
    },
    // Helper method to normalize dates for comparison
    normalizeDate(dateStr) {
      if (!dateStr) return '';

      // Handle different date formats for comparison

      // If it's already in YYYY-MM or YYYY-WW format, return as is
      if (/^\d{4}-\d{2}$/.test(dateStr)) {
        return dateStr;
      }

      // If it's a week number like "01", "02", etc.
      if (/^\d{1,2}$/.test(dateStr)) {
        // For weekly data, use a standard year (2000) for comparison
        // This allows comparing week numbers regardless of year
        return `2000-${dateStr.padStart(2, '0')}`;
      }

      // If it's a month abbreviation like "Jan", "Feb", etc.
      const monthMap = {
        'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
        'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
        'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
      };

      if (monthMap[dateStr]) {
        // For monthly data, use a standard year (2000) for comparison
        return `2000-${monthMap[dateStr]}`;
      }

      // If it's "Week XX" format
      if (dateStr.startsWith('Week ')) {
        const weekNum = dateStr.split(' ')[1];
        if (!isNaN(parseInt(weekNum))) {
          return `2000-${weekNum.padStart(2, '0')}`;
        }
      }

      // If it's "MMM YYYY" format (e.g., "Jan 2023")
      const parts = dateStr.split(' ');
      if (parts.length === 2 && monthMap[parts[0]] && !isNaN(parseInt(parts[1]))) {
        return `${parts[1]}-${monthMap[parts[0]]}`;
      }

      // Default fallback
      return dateStr;
    },

    updateChart() {
      if (this.$refs.lineChart) {
        // Update the options
        const updatedOptions = {
          ...this.options,
          title: this.title,
          data: {
            loading: this.loading
          },
          height: this.height
        };

        // Update axes based on timeInterval
        if (!updatedOptions.axes) updatedOptions.axes = {};
        if (!updatedOptions.axes.bottom) updatedOptions.axes.bottom = {};

        updatedOptions.axes.bottom.title = this.timeInterval === 'weekly' ? 'Week / Year' : 'Month / Year';
        updatedOptions.axes.bottom.scaleType = 'labels';
        updatedOptions.axes.bottom.ticks = {
          rotation: this.timeInterval === 'weekly' ? -45 : -45,
          formatter: (label) => {
            console.log('Formatting chart label:', label);
            // For weekly data, we might want to shorten the display
            if (this.timeInterval === 'weekly' && label.startsWith('Week')) {
              // Extract week number and year
              const parts = label.split(' ');
              console.log('Label parts:', parts);
              if (parts.length >= 3) {
                // Format as "W## 'YY" to save space
                const weekNum = parts[1].padStart(2, '0');
                // Make sure we handle all years correctly, including 2025
                let year = parts[2];
                // Ensure we have a 4-digit year before slicing
                if (year && year.length >= 4) {
                  year = year.slice(2); // Get last 2 digits of year
                }
                const formattedLabel = `W${weekNum} '${year}`;
                console.log('Formatted label:', formattedLabel, 'from year:', parts[2]);
                return formattedLabel;
              }
            }
            return label;
          }
        };

        // Always set hard-coded thresholds for alerts
        if (!updatedOptions.axes) updatedOptions.axes = {};
        if (!updatedOptions.axes.left) updatedOptions.axes.left = {};

        // Set the hard-coded thresholds in the correct structure
        // This matches the structure used in LineChart.vue
        updatedOptions.axes.left.thresholds = [
          {
            value: 3.0,
            fillColor: '#ff832b',
            label: 'Short-term Alert'
          },
          {
            value: 1.5,
            fillColor: '#da1e28',
            label: 'Sustained Problem'
          }
        ];

        this.$refs.lineChart.options = updatedOptions;
      }
    }
  },
  mounted() {
    this.formatData();
    this.updateChart();
  }
};
</script>

<style scoped>
.carbon-chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

:deep(.cds--cc--chart) text {
  fill: #f4f4f4 !important;
}

/* Ensure Carbon Charts styles are properly applied */
:deep(.cds--cc--legend-item) {
  display: flex;
  align-items: center;
  color: #f4f4f4 !important;
}

:deep(.cds--cc--legend-circle) {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  margin-right: 8px !important;
}

/* Threshold styling */
:deep(.cds--cc--threshold) {
  stroke-width: 2px !important;
}

:deep(.cds--cc--threshold line) {
  stroke-width: 2px !important;
  stroke-dasharray: none !important;
}

:deep(.cds--cc--threshold-line) {
  stroke-width: 2px !important;
  stroke-dasharray: none !important;
}

/* Ensure threshold lines are visible */
:deep(.cds--cc--axes g.cds--cc--threshold) {
  stroke-opacity: 1 !important;
  fill-opacity: 0 !important;
}

/* Additional threshold styling to ensure visibility */
:deep(g.cds--cc--threshold) {
  stroke-width: 2px !important;
  stroke-opacity: 1 !important;
}

:deep(line.cds--cc--threshold-line) {
  stroke-width: 2px !important;
  stroke-dasharray: none !important;
}

:deep(.cds--cc--tooltip) {
  background: #262626 !important;
  color: #f4f4f4 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

:deep(.cds--cc--legend-item text) {
  fill: #f4f4f4 !important;
  color: #f4f4f4 !important;
}
</style>
