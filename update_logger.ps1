$filePath = "api/app/controllers/authentication.js"
$content = Get-Content $filePath -Raw

# Replace info logs
$content = $content -replace 'await logger\.logging\((.*?), ''info'',(.*?)\);', 'logger.logInfo($1, ''authentication'');'

# Replace error logs
$content = $content -replace 'await logger\.logging\((.*?), ''error'',(.*?)\);', 'logger.logError($1, null, ''authentication'');'

# Write the updated content back to the file
$content | Set-Content $filePath

Write-Host "Updated logger calls in $filePath"
