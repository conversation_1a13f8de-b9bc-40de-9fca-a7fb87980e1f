{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=style&index=0&id=924ed4a2&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748532409555}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb290LWNhdXNlLWNoYXJ0LWNvbnRhaW5lciB7CiAgd2lkdGg6IDEwMCU7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgoubG9hZGluZy1pbmRpY2F0b3IgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGhlaWdodDogMjAwcHg7CiAgY29sb3I6ICNmNGY0ZjQ7Cn0KCi5sb2FkaW5nLXNwaW5uZXIgewogIHdpZHRoOiAzMnB4OwogIGhlaWdodDogMzJweDsKICBib3JkZXI6IDNweCBzb2xpZCAjMzkzOTM5OwogIGJvcmRlci10b3A6IDNweCBzb2xpZCAjMGY2MmZlOwogIGJvcmRlci1yYWRpdXM6IDUwJTsKICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlOwogIG1hcmdpbi1ib3R0b206IDE2cHg7Cn0KCkBrZXlmcmFtZXMgc3BpbiB7CiAgMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfQogIDEwMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9Cn0KCi5uby1kYXRhLW1lc3NhZ2UgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBoZWlnaHQ6IDIwMHB4OwogIGNvbG9yOiAjYzZjNmM2OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLmNoYXJ0LWhlYWRlciB7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKfQoKLmNoYXJ0LWhlYWRlciBoNSB7CiAgY29sb3I6ICNmNGY0ZjQ7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgbWFyZ2luOiAwOwp9CgouY2hhcnQtY29udGFpbmVyIHsKICB3aWR0aDogMTAwJTsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjE2OwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBwYWRkaW5nOiAxNnB4Owp9CgovKiBEYXJrIHRoZW1lIHN0eWxlcyBmb3IgQ2FyYm9uIENoYXJ0cyAqLwouY2hhcnQtY29udGFpbmVyIDpkZWVwKC5ieC0tY2MtLWNoYXJ0LXdyYXBwZXIpIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjE2Owp9CgouY2hhcnQtY29udGFpbmVyIDpkZWVwKC5ieC0tY2MtLWNoYXJ0LXN2ZykgewogIGJhY2tncm91bmQtY29sb3I6ICMxNjE2MTY7Cn0KCi5jaGFydC1jb250YWluZXIgOmRlZXAoLmJ4LS1jYy0tYXhpcy10aXRsZSkgewogIGZpbGw6ICNmNGY0ZjQ7Cn0KCi5jaGFydC1jb250YWluZXIgOmRlZXAoLmJ4LS1jYy0tYXhpcy1sYWJlbCkgewogIGZpbGw6ICNjNmM2YzY7Cn0KCi5jaGFydC1jb250YWluZXIgOmRlZXAoLmJ4LS1jYy0tbGVnZW5kLWl0ZW0tdGV4dCkgewogIGZpbGw6ICNmNGY0ZjQ7Cn0KCi5jaGFydC1jb250YWluZXIgOmRlZXAoLmJ4LS1jYy0tZ3JpZC1saW5lKSB7CiAgc3Ryb2tlOiAjMzkzOTM5Owp9Cg=="}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AA6NA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["<template>\n  <div class=\"root-cause-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart ({{ data ? data.length : 'null' }} items)\n    </div>\n    <div v-else>\n      <div class=\"chart-header\" v-if=\"title\">\n        <h5>{{ title }}</h5>\n      </div>\n      <div ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { StackedBarChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      chart: null,\n      defaultOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            includeZero: true\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        color: {\n          scale: {\n            // Common root cause categories\n            'FUNC': '#0f62fe',\n            'OMI': '#6929c4',\n            'RAIM DEGRADE': '#1192e8',\n            'OTHER': '#005d5d',\n            'LANE DEGRADE': '#9f1853',\n            'IML': '#fa4d56',\n            'PMIC COMM LOST': '#570408',\n            'Unknown': '#198038',\n            'FLAG': '#8a3ffc',\n            'LINK': '#002d9c',\n            'NONFAIL': '#009d9a',\n            'KRAKEN': '#ee538b',\n            'I2C': '#b28600',\n            'CODE': '#ff832b',\n            'DIAG': '#24a148',\n            'BIOS': '#d12771',\n            'HLA': '#08bdba'\n          }\n        },\n        bars: { width: 60 },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100',\n        stacked: true\n      }\n    };\n  },\n  mounted() {\n    console.log('RootCauseChart mounted with data:', this.data ? this.data.length : 'null');\n    this.initChart();\n  },\n  watch: {\n    data: {\n      handler(newData) {\n        console.log('RootCauseChart data changed:', newData ? newData.length : 'null', 'items');\n        this.updateChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      console.log('RootCauseChart initChart called with data:', this.data ? this.data.length : 'null');\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart initChart: No data, returning');\n        return;\n      }\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) {\n        console.log('RootCauseChart initChart: No chart container, returning');\n        return;\n      }\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Initialize the chart\n      console.log('RootCauseChart creating chart with data:', this.data.length, 'items');\n      console.log('Sample data:', this.data.slice(0, 3));\n      try {\n        this.chart = new StackedBarChart(chartContainer, {\n          data: this.data,\n          options: chartOptions\n        });\n        console.log('RootCauseChart chart created successfully');\n      } catch (error) {\n        console.error('RootCauseChart error creating chart:', error);\n      }\n    },\n\n    updateChart() {\n      console.log('RootCauseChart updateChart called');\n      if (!this.chart) {\n        console.log('RootCauseChart updateChart: No chart, calling initChart');\n        this.initChart();\n        return;\n      }\n\n      if (!this.data || this.data.length === 0) {\n        console.log('RootCauseChart updateChart: No data, destroying chart');\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      // Merge default options with provided options\n      const chartOptions = {\n        ...this.defaultOptions,\n        ...this.options,\n        height: this.height,\n        data: {\n          ...this.defaultOptions.data,\n          ...this.options.data,\n          onclick: (data) => {\n            console.log('Root cause bar clicked:', data);\n            this.$emit('bar-click', data);\n          }\n        }\n      };\n\n      // Update the chart data and options\n      console.log('RootCauseChart updateChart: Updating chart with', this.data.length, 'data points');\n      try {\n        this.chart.model.setData(this.data);\n        this.chart.model.setOptions(chartOptions);\n        console.log('RootCauseChart updateChart: Chart updated successfully');\n      } catch (error) {\n        console.error('RootCauseChart updateChart: Error updating chart:', error);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.root-cause-chart-container {\n  width: 100%;\n  position: relative;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #f4f4f4;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #393939;\n  border-top: 3px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #c6c6c6;\n  font-size: 14px;\n}\n\n.chart-header {\n  margin-bottom: 16px;\n}\n\n.chart-header h5 {\n  color: #f4f4f4;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.chart-container {\n  width: 100%;\n  background-color: #161616;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n/* Dark theme styles for Carbon Charts */\n.chart-container :deep(.bx--cc--chart-wrapper) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--chart-svg) {\n  background-color: #161616;\n}\n\n.chart-container :deep(.bx--cc--axis-title) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--axis-label) {\n  fill: #c6c6c6;\n}\n\n.chart-container :deep(.bx--cc--legend-item-text) {\n  fill: #f4f4f4;\n}\n\n.chart-container :deep(.bx--cc--grid-line) {\n  stroke: #393939;\n}\n</style>\n"]}]}