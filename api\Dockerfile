FROM registry.cirrus.ibm.com/public/nodejs-14:latest


# Set the working directory to /api
WORKDIR /api
USER root

#modify permissions and ownership of root dir
#RUN chgrp -R 0 /home/<USER>
#RUN chmod -R g+rwX /home/<USER>
#RUN yum -y install make gcc gcc-c++  openssl-devel bzip2-devel
#RUN  yum -y install python2



# copy package.json into the container at /api
#COPY db2inst1 /api/db2inst1
#RUN export IBM_DB_HOME=/api/db2inst1/dsdriver
#COPY package*.json /api
COPY . /api
RUN rm -rf node_modules
# install dependencies
RUN npm install
RUN npm install --unsafe-perm ibm_db --allow-root
#RUN npm install cors
#RUN npm install ldapjs
#RUN npm install mongodb
#RUN npm install body-parser

RUN  chmod -R 777 /api

# Copy the current directory contents into the container at /api

#RUN source /api/db2inst1/sqllib/db2profile
# Make port 80 available to the world outside this container
EXPOSE 8080
# Run the app when the container launches
CMD ["node", "app.js"]
