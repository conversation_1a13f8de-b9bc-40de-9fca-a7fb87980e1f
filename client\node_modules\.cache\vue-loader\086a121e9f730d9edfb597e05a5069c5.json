{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue?vue&type=style&index=0&id=5d10017b&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue", "mtime": 1748528237445}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["QEDashboard.vue"], "names": [], "mappings": ";AA2z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file": "QEDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"qe-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section -->\n      <div class=\"dashboard-header\">\n        <h3>QE Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- PQE Overview Section -->\n      <div class=\"section-card\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">PQE Owner Overview</h4>\n            <div class=\"section-subtitle\">Prioritized by critical issues count</div>\n          </div>\n        </div>\n\n        <div v-if=\"isLoading\" class=\"loading-container\">\n          <cv-inline-loading\n            status=\"active\"\n            loading-text=\"Loading PQE data...\"\n          ></cv-inline-loading>\n        </div>\n\n        <div v-else-if=\"pqeOwners.length > 0\" class=\"pqe-owners-grid\">\n          <div\n            v-for=\"pqe in sortedPQEOwners\"\n            :key=\"pqe.name\"\n            class=\"pqe-owner-card\"\n            :class=\"getPriorityClass(pqe)\"\n          >\n            <div class=\"pqe-owner-header\">\n              <h5 class=\"pqe-owner-name\">{{ pqe.name }}</h5>\n              <div class=\"pqe-owner-metrics\">\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Critical</div>\n                  <div class=\"metric-value\" :class=\"getCriticalIssuesClass(pqe.criticalIssues)\">\n                    {{ pqe.criticalIssues }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Outstanding</div>\n                  <div class=\"metric-value\" :class=\"getOutstandingClass(pqe.outstandingIssuesCount)\">\n                    {{ pqe.outstandingIssuesCount }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Resolved</div>\n                  <div class=\"metric-value\" :class=\"getResolvedClass(pqe.resolvedIssuesCount)\">\n                    {{ pqe.resolvedIssuesCount }}\n                  </div>\n                </div>\n                <div class=\"metric-item\">\n                  <div class=\"metric-label\">Pending</div>\n                  <div class=\"metric-value\" :class=\"getPendingClass(pqe.pendingActionTrackerCount)\">\n                    {{ pqe.pendingActionTrackerCount }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"pqe-owner-details\">\n              <div class=\"groups-section\">\n                <div class=\"groups-header\">\n                  <div class=\"groups-title\">Critical Breakout Groups</div>\n                  <div class=\"groups-count\">{{ pqe.criticalGroups.length }}</div>\n                </div>\n                <div class=\"groups-list\">\n                  <div\n                    v-for=\"group in pqe.criticalGroups.slice(0, 3)\"\n                    :key=\"group.name\"\n                    class=\"group-item\"\n                    :class=\"getGroupHighlightClass(group.xFactor)\"\n                    @click.stop=\"viewGroupCriticalIssues(pqe, group.name)\"\n                  >\n                    <div class=\"group-name\">{{ group.name }}</div>\n                    <div class=\"group-metrics\">\n                      <span class=\"group-metric\" title=\"Outstanding Issues\">\n                        <span class=\"metric-icon\">O:</span>{{ group.outstandingIssuesCount }}\n                      </span>\n                      <span class=\"group-metric\" title=\"Resolved Issues\">\n                        <span class=\"metric-icon\">R:</span>{{ group.resolvedIssuesCount }}\n                      </span>\n                      <span class=\"group-xfactor\" :class=\"getXFactorClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}x\n                      </span>\n                    </div>\n                  </div>\n                  <div v-if=\"pqe.criticalGroups.length > 3\" class=\"more-groups\">\n                    + {{ pqe.criticalGroups.length - 3 }} more\n                  </div>\n                </div>\n              </div>\n\n              <!-- Removed collective X-Factor as per requirements -->\n            </div>\n\n            <div class=\"pqe-owner-footer\">\n              <div class=\"button-group\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  class=\"issues-button\"\n                  @click.stop=\"viewCriticalIssues(pqe)\"\n                  :disabled=\"pqe.criticalIssues === 0 && pqe.outstandingIssuesCount === 0 && pqe.resolvedIssuesCount === 0\"\n                >\n                  Show Issues\n                </cv-button>\n                <cv-button\n                  kind=\"ghost\"\n                  size=\"small\"\n                  class=\"view-button\"\n                  @click.stop=\"viewPQEDashboard(pqe.name)\"\n                  :disabled=\"pqe.pendingActionTrackerCount === 0\"\n                >\n                  Pending Action Tracker Updates ({{ pqe.pendingActionTrackerCount }})\n                </cv-button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else class=\"no-data-message\">\n          No PQE owners found. Please check the breakout_targets Excel file.\n        </div>\n      </div>\n\n      <!-- Summary Statistics Section -->\n      <div class=\"summary-section\">\n        <div class=\"summary-card\">\n          <div class=\"summary-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalCriticalIssues }}</div>\n            <div class=\"summary-label\">Total Critical Issues</div>\n          </div>\n        </div>\n\n        <div class=\"summary-card\">\n          <div class=\"summary-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalUnvalidated }}</div>\n            <div class=\"summary-label\">Unvalidated Fails</div>\n          </div>\n        </div>\n\n        <div class=\"summary-card\">\n          <div class=\"summary-icon critical-groups\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"summary-content\">\n            <div class=\"summary-value\">{{ totalCriticalGroups }}</div>\n            <div class=\"summary-label\">Critical Breakout Groups</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Issues Modal -->\n    <cv-modal\n      :visible=\"showCriticalIssuesModal\"\n      @modal-hidden=\"closeCriticalIssuesModal\"\n      class=\"issues-modal\"\n      :size=\"'lg'\"\n    >\n      <template slot=\"title\">\n        <span v-if=\"selectedPQE && selectedGroupName\">\n          Issues for {{ selectedGroupName }} ({{ selectedPQE.name }})\n        </span>\n        <span v-else-if=\"selectedPQE\">\n          Issues for {{ selectedPQE.name }}\n        </span>\n        <span v-else>\n          Issues\n        </span>\n      </template>\n      <template slot=\"content\">\n        <div v-if=\"selectedPQE\" class=\"modal-content\">\n          <!-- Issue Type Tabs -->\n          <div class=\"issue-type-tabs\">\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'critical' }\"\n              @click=\"activeIssueTab = 'critical'\"\n            >\n              Critical ({{ criticalIssues.length }})\n            </cv-button>\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'outstanding' }\"\n              @click=\"activeIssueTab = 'outstanding'\"\n            >\n              Outstanding ({{ outstandingIssues.length }})\n            </cv-button>\n            <cv-button\n              kind=\"ghost\"\n              size=\"small\"\n              class=\"tab-button\"\n              :class=\"{ 'active': activeIssueTab === 'resolved' }\"\n              @click=\"activeIssueTab = 'resolved'\"\n            >\n              Resolved ({{ resolvedIssues.length }})\n            </cv-button>\n          </div>\n\n          <!-- Critical Issues -->\n          <div v-if=\"activeIssueTab === 'critical'\" class=\"issues-list\">\n            <div v-if=\"criticalIssues.length === 0\" class=\"no-issues-message\">\n              No critical issues found.\n            </div>\n            <div\n              v-for=\"issue in criticalIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card\"\n              @click=\"toggleIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                    :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                    {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues -->\n          <div v-if=\"activeIssueTab === 'outstanding'\" class=\"issues-list\">\n            <div v-if=\"outstandingIssues.length === 0\" class=\"no-issues-message\">\n              No outstanding issues found.\n            </div>\n            <div\n              v-for=\"issue in outstandingIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card outstanding-issue-card\"\n              @click=\"toggleOutstandingIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    kind=\"blue\"\n                    label=\"Outstanding\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-severity' : 'low-severity'\">\n                    {{ issue.currentPerformance }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                <div class=\"acceptance-details\">\n                  <div class=\"acceptance-date\">\n                    <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                  </div>\n                  <div class=\"accepted-by\">\n                    <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                  </div>\n                </div>\n\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues -->\n          <div v-if=\"activeIssueTab === 'resolved'\" class=\"issues-list\">\n            <div v-if=\"resolvedIssues.length === 0\" class=\"no-issues-message\">\n              No resolved issues found.\n            </div>\n            <div\n              v-for=\"issue in resolvedIssues\"\n              :key=\"issue.id\"\n              class=\"issue-card resolved-issue-card\"\n              @click=\"toggleResolvedIssueExpanded(issue)\"\n              :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n            >\n              <div class=\"issue-header\">\n                <div class=\"issue-tags\">\n                  <cv-tag\n                    kind=\"green\"\n                    label=\"Resolved\"\n                  />\n                  <cv-tag\n                    :kind=\"getAnalysisTypeTagKind(issue.analysisType)\"\n                    class=\"analysis-tag\"\n                    :label=\"issue.analysisType\"\n                  />\n                </div>\n                <span class=\"issue-title\">{{ issue.category }}</span>\n                <div class=\"issue-metadata\">\n                  <cv-tag\n                    kind=\"cool-gray\"\n                    class=\"month-tag\"\n                    :label=\"issue.month\"\n                  />\n                  <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-severity'\">\n                    Current: {{ issue.currentPerformance }}\n                  </span>\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                <div class=\"resolution-details\">\n                  <div class=\"resolution-date\">\n                    <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                  </div>\n                  <div class=\"original-severity\">\n                    <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                    ({{ issue.increaseMultiplier }}x)\n                  </div>\n                </div>\n\n                <div class=\"ai-description\">\n                  <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                </div>\n\n                <!-- Action Comment Text Box -->\n                <div class=\"action-comment\">\n                  <cv-text-area\n                    v-model=\"issue.comment\"\n                    label=\"Action Comments\"\n                    placeholder=\"Add your comments or action plan here...\"\n                    :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                  ></cv-text-area>\n                </div>\n\n                <div class=\"issue-actions\">\n                  <cv-button\n                    kind=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"updateIssue(issue)\"\n                  >\n                    Update\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else class=\"loading-container\">\n          <cv-inline-loading\n            status=\"active\"\n            loading-text=\"Loading issues...\"\n          ></cv-inline-loading>\n        </div>\n      </template>\n      <template slot=\"footer\">\n        <cv-button\n          kind=\"secondary\"\n          @click=\"closeCriticalIssuesModal\"\n        >\n          Close\n        </cv-button>\n      </template>\n    </cv-modal>\n  </div>\n</template>\n\n<script>\nimport { CvButton, CvInlineLoading, CvModal, CvTag, CvTextArea } from '@carbon/vue';\n\nexport default {\n  name: 'QEDashboard',\n  components: {\n    CvButton,\n    CvInlineLoading,\n    CvModal,\n    CvTag,\n    CvTextArea\n  },\n  data() {\n    return {\n      isLoading: true,\n      pqeOwners: [],\n      showCriticalIssuesModal: false,\n      selectedPQE: null,\n      selectedGroupName: null,\n      criticalIssues: [],\n      outstandingIssues: [],\n      resolvedIssues: [],\n      expandedIssueIds: [],\n      expandedOutstandingIssueIds: [],\n      expandedResolvedIssueIds: [],\n      activeIssueTab: 'critical'\n    };\n  },\n  computed: {\n    // Sort PQE owners by critical issues count (highest first)\n    sortedPQEOwners() {\n      return [...this.pqeOwners].sort((a, b) => b.criticalIssues - a.criticalIssues);\n    },\n    // Calculate total critical issues across all PQEs\n    totalCriticalIssues() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalIssues, 0);\n    },\n    // Calculate total unvalidated fails across all PQEs\n    totalUnvalidated() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.unvalidatedCount, 0);\n    },\n    // Calculate total critical breakout groups across all PQEs\n    totalCriticalGroups() {\n      return this.pqeOwners.reduce((total, pqe) => total + pqe.criticalGroups.length, 0);\n    }\n  },\n  mounted() {\n    this.loadPQEOwnersData();\n  },\n  methods: {\n    async loadPQEOwnersData() {\n      this.isLoading = true;\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch PQE owners data from the API\n        const response = await fetch('/api-statit2/get_all_pqe_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n          console.log(`Loaded data for ${this.pqeOwners.length} PQE owners`);\n        } else {\n          console.error('Failed to load PQE data:', data.message);\n          // Use sample data for development\n          this.loadSampleData();\n        }\n      } catch (error) {\n        console.error('Error loading PQE data:', error);\n        // Use sample data for development\n        this.loadSampleData();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleData() {\n      // Sample data for development\n      this.pqeOwners = [\n        {\n          name: 'Albert G.',\n          criticalIssues: 4,\n          unvalidatedCount: 12,\n          collectiveXFactor: 3.2,\n          criticalGroups: [\n            { name: 'Fan Themis', xFactor: 3.2 },\n            { name: 'Victoria Crypto', xFactor: 1.8 },\n            { name: 'Quantum Nexus', xFactor: 1.6 }\n          ]\n        },\n        {\n          name: 'Sarah L.',\n          criticalIssues: 2,\n          unvalidatedCount: 8,\n          collectiveXFactor: 2.5,\n          criticalGroups: [\n            { name: 'Stellar Core', xFactor: 2.5 },\n            { name: 'Nebula Drive', xFactor: 1.7 }\n          ]\n        },\n        {\n          name: 'Michael T.',\n          criticalIssues: 1,\n          unvalidatedCount: 5,\n          collectiveXFactor: 1.9,\n          criticalGroups: [\n            { name: 'Pulsar Matrix', xFactor: 1.9 }\n          ]\n        },\n        {\n          name: 'Jennifer K.',\n          criticalIssues: 0,\n          unvalidatedCount: 3,\n          collectiveXFactor: 0.9,\n          criticalGroups: []\n        }\n      ];\n    },\n\n    viewPQEDashboard(pqeName) {\n      // Emit event to select the PQE owner in the parent component\n      this.$emit('select-pqe', pqeName);\n    },\n\n    viewCriticalIssues(pqe) {\n      this.selectedPQE = pqe;\n      this.selectedGroupName = null; // Reset group name when viewing all critical issues\n      this.activeIssueTab = 'critical'; // Set active tab to critical issues\n      this.loadAllIssues(pqe.name);\n      this.showCriticalIssuesModal = true;\n    },\n\n    closeCriticalIssuesModal() {\n      this.showCriticalIssuesModal = false;\n      this.selectedPQE = null;\n      this.selectedGroupName = null;\n      this.criticalIssues = [];\n      this.outstandingIssues = [];\n      this.resolvedIssues = [];\n      this.expandedIssueIds = [];\n      this.expandedOutstandingIssueIds = [];\n      this.expandedResolvedIssueIds = [];\n    },\n\n    async loadAllIssues(pqeOwner) {\n      console.log(`Loading all issues for PQE owner: ${pqeOwner}`);\n\n      // Load all issue types in parallel\n      await Promise.all([\n        this.loadCriticalIssues(pqeOwner),\n        this.loadOutstandingIssues(pqeOwner),\n        this.loadResolvedIssues(pqeOwner)\n      ]);\n    },\n\n    async loadCriticalIssues(pqeOwner) {\n      console.log(`Loading critical issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.criticalIssues = data.critical_issues || [];\n          console.log(`Loaded ${this.criticalIssues.length} critical issues`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues(pqeOwner);\n      }\n    },\n\n    loadSampleCriticalIssues(pqeOwner) {\n      // Use our helper method to get sample issues for this PQE owner\n      this.criticalIssues = this.getSampleIssuesForPQE(pqeOwner);\n    },\n\n    async loadOutstandingIssues(pqeOwner) {\n      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.outstandingIssues = data.outstanding_issues || [];\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues(pqeOwner);\n      }\n    },\n\n    loadSampleOutstandingIssues(pqeOwner) {\n      // Sample data for development\n      if (pqeOwner === 'Albert G.') {\n        this.outstandingIssues = [\n          {\n            id: 'oi1',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.4',\n            aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n            comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n            analysisType: 'Root Cause',\n            acceptanceDate: '2024-04-10',\n            currentPerformance: '1.3x',\n            acceptedBy: 'Engineering Team'\n          },\n          {\n            id: 'oi2',\n            category: 'Victoria Crypto',\n            month: '2024-05',\n            severity: 'medium',\n            increaseMultiplier: '1.3',\n            aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known supplier issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n            comment: 'Supplier variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n            analysisType: 'Supplier',\n            acceptanceDate: '2024-03-22',\n            currentPerformance: '1.2x',\n            acceptedBy: 'Quality Team'\n          }\n        ];\n      } else if (pqeOwner === 'Sarah L.') {\n        this.outstandingIssues = [\n          {\n            id: 'oi3',\n            category: 'Stellar Core',\n            month: '2024-05',\n            severity: 'medium',\n            increaseMultiplier: '1.3',\n            aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n            comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n            analysisType: 'Root Cause',\n            acceptanceDate: '2024-04-05',\n            currentPerformance: '1.2x',\n            acceptedBy: 'Engineering Team'\n          }\n        ];\n      } else {\n        this.outstandingIssues = [];\n      }\n    },\n\n    async loadResolvedIssues(pqeOwner) {\n      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.resolvedIssues = data.resolved_issues || [];\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues(pqeOwner);\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues(pqeOwner);\n      }\n    },\n\n    loadSampleResolvedIssues(pqeOwner) {\n      // Sample data for development\n      if (pqeOwner === 'Albert G.') {\n        this.resolvedIssues = [\n          {\n            id: 'ri1',\n            category: 'Fan Themis',\n            month: '2024-05',\n            severity: 'high',\n            increaseMultiplier: '2.8',\n            aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n            comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n            analysisType: 'Root Cause',\n            resolutionDate: '2024-05-15',\n            currentPerformance: '0.9x'\n          },\n          {\n            id: 'ri2',\n            category: 'Victoria Crypto',\n            month: '2024-04',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific supplier batch. Supplier corrected their process.',\n            comment: 'Worked with supplier to improve their quality control. Monitoring new batches closely.',\n            analysisType: 'Supplier',\n            resolutionDate: '2024-04-28',\n            currentPerformance: '0.8x'\n          }\n        ];\n      } else if (pqeOwner === 'Sarah L.') {\n        this.resolvedIssues = [\n          {\n            id: 'ri3',\n            category: 'Stellar Core',\n            month: '2024-05',\n            severity: 'high',\n            increaseMultiplier: '2.5',\n            aiDescription: 'Stellar Core showed 2.5x spike in May 2024. Root cause was identified as a thermal issue in the power delivery subsystem. Issue was resolved with a design change.',\n            comment: 'Implemented design change to improve thermal performance. Monitoring field data.',\n            analysisType: 'Root Cause',\n            resolutionDate: '2024-05-22',\n            currentPerformance: '0.8x'\n          }\n        ];\n      } else {\n        this.resolvedIssues = [];\n      }\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    async updateIssue(issue) {\n      console.log('Update issue:', issue);\n\n      if (!this.selectedPQE) {\n        console.error('No PQE owner selected');\n        return;\n      }\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Call the API to update the action tracker\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment,\n            severity: issue.severity,\n            pqeOwner: this.selectedPQE.name,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          alert(`Action tracker updated for issue: ${issue.category}`);\n        } else {\n          console.error('Failed to update action tracker:', data.message);\n          alert('Failed to update action tracker. Please try again.');\n        }\n      } catch (error) {\n        console.error('Error updating action tracker:', error);\n        alert('Error updating action tracker. Please try again.');\n      }\n    },\n\n    getPriorityClass(pqe) {\n      if (pqe.collectiveXFactor >= 3.0) return 'high-priority';\n      if (pqe.collectiveXFactor >= 1.5) return 'medium-priority';\n      return 'normal-priority';\n    },\n\n    getCriticalIssuesClass(count) {\n      if (count >= 3) return 'high-value';\n      if (count >= 1) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getUnvalidatedClass(count) {\n      if (count >= 10) return 'high-value';\n      if (count >= 5) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getOutstandingClass(count) {\n      if (count >= 4) return 'high-value';\n      if (count >= 2) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getResolvedClass(count) {\n      if (count >= 5) return 'high-value';\n      if (count >= 3) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getPendingClass(count) {\n      if (count >= 3) return 'high-value';\n      if (count >= 1) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getXFactorClass(xFactor) {\n      if (xFactor >= 3.0) return 'high-value';\n      if (xFactor >= 1.5) return 'medium-value';\n      return 'normal-value';\n    },\n\n    getGroupHighlightClass(xFactor) {\n      if (xFactor >= 3.0) return 'high-highlight';\n      if (xFactor >= 1.5) return 'medium-highlight';\n      return 'low-highlight';\n    },\n\n    viewGroupCriticalIssues(pqe, groupName) {\n      this.selectedPQE = pqe;\n      this.selectedGroupName = groupName;\n      this.activeIssueTab = 'critical'; // Set active tab to critical issues\n      this.loadAllIssuesForGroup(pqe.name, groupName);\n      this.showCriticalIssuesModal = true;\n    },\n\n    async loadAllIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading all issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      // Load all issue types in parallel\n      await Promise.all([\n        this.loadCriticalIssuesForGroup(pqeOwner, groupName),\n        this.loadOutstandingIssuesForGroup(pqeOwner, groupName),\n        this.loadResolvedIssuesForGroup(pqeOwner, groupName)\n      ]);\n    },\n\n    async loadCriticalIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading critical issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // If we have a specific group name, filter issues to only include those for this group\n          if (groupName) {\n            const allIssues = data.critical_issues || [];\n            this.criticalIssues = allIssues.filter(issue =>\n              issue.category.includes(groupName) || groupName.includes(issue.category)\n            );\n          } else {\n            this.criticalIssues = data.critical_issues || [];\n          }\n          console.log(`Loaded ${this.criticalIssues.length} critical issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleCriticalIssuesForGroup(pqeOwner, groupName) {\n      // Get all sample issues for this PQE owner\n      const allSampleIssues = this.getSampleIssuesForPQE(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.criticalIssues = allSampleIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    async loadOutstandingIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading outstanding issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter issues to only include those for this group\n          const allIssues = data.outstanding_issues || [];\n          this.outstandingIssues = allIssues.filter(issue =>\n            issue.category.includes(groupName) || groupName.includes(issue.category)\n          );\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleOutstandingIssuesForGroup(pqeOwner, groupName) {\n      // Load all sample outstanding issues for this PQE owner\n      this.loadSampleOutstandingIssues(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.outstandingIssues = this.outstandingIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    async loadResolvedIssuesForGroup(pqeOwner, groupName) {\n      console.log(`Loading resolved issues for PQE owner: ${pqeOwner}, group: ${groupName}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: pqeOwner,\n            groupName: groupName\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter issues to only include those for this group\n          const allIssues = data.resolved_issues || [];\n          this.resolvedIssues = allIssues.filter(issue =>\n            issue.category.includes(groupName) || groupName.includes(issue.category)\n          );\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for group ${groupName}`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssuesForGroup(pqeOwner, groupName);\n      }\n    },\n\n    loadSampleResolvedIssuesForGroup(pqeOwner, groupName) {\n      // Load all sample resolved issues for this PQE owner\n      this.loadSampleResolvedIssues(pqeOwner);\n\n      // Filter to only include issues for this group\n      this.resolvedIssues = this.resolvedIssues.filter(issue =>\n        issue.category.includes(groupName) || groupName.includes(issue.category)\n      );\n    },\n\n    getSampleIssuesForPQE(pqeOwner) {\n      // Sample data for development based on PQE owner\n      if (pqeOwner === 'Albert G.') {\n        return [\n          {\n            id: 'ci1',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '3.2',\n            aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci2',\n            category: 'Victoria Crypto',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.8',\n            aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci3',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '2.5',\n            aiDescription: 'Fan Themis showing higher failure rates for units manufactured in March 2024. Vintage analysis indicates a 2.5x increase in failures for this manufacturing period.',\n            comment: '',\n            analysisType: 'Vintage'\n          },\n          {\n            id: 'ci7',\n            category: 'Fan Themis',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Fan Themis units from Supplier XYZ showing higher failure rates. Quality audit recommended.',\n            comment: '',\n            analysisType: 'Supplier'\n          },\n          {\n            id: 'ci8',\n            category: 'Victoria Crypto',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.6',\n            aiDescription: 'Victoria Crypto units in North sector showing higher failure rates. Site-specific issue suspected.',\n            comment: '',\n            analysisType: 'Sector'\n          }\n        ];\n      }\n      else if (pqeOwner === 'Sarah L.') {\n        return [\n          {\n            id: 'ci4',\n            category: 'Stellar Core',\n            month: '2024-06',\n            severity: 'high',\n            increaseMultiplier: '2.8',\n            aiDescription: 'Stellar Core showing 2.8x spike in June 2024. Root cause appears to be related to power delivery issues.',\n            comment: '',\n            analysisType: 'Root Cause'\n          },\n          {\n            id: 'ci5',\n            category: 'Nebula Drive',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.7',\n            aiDescription: 'Nebula Drive has sustained problem with 1.7x target rate for 2 consecutive months. Consistent pattern of thermal issues.',\n            comment: '',\n            analysisType: 'Supplier'\n          },\n          {\n            id: 'ci9',\n            category: 'Stellar Core',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.9',\n            aiDescription: 'Stellar Core units manufactured in April 2024 showing higher failure rates.',\n            comment: '',\n            analysisType: 'Vintage'\n          }\n        ];\n      }\n      else {\n        return [\n          {\n            id: 'ci6',\n            category: 'Generic Component',\n            month: '2024-06',\n            severity: 'medium',\n            increaseMultiplier: '1.6',\n            aiDescription: 'Generic component showing 1.6x target rate. Further investigation needed.',\n            comment: '',\n            analysisType: 'Sector'\n          }\n        ];\n      }\n    },\n\n    getAnalysisTypeTagKind(analysisType) {\n      // Return different tag colors based on analysis type\n      switch (analysisType) {\n        case 'Root Cause':\n          return 'purple';\n        case 'Vintage':\n          return 'teal';\n        case 'Sector':\n          return 'blue';\n        case 'Supplier':\n          return 'green';\n        default:\n          return 'gray';\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.qe-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n  margin-bottom: 1.5rem;\n  padding: 1.5rem;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.pqe-owners-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.pqe-owner-card {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  flex-direction: column;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n  border-left: 4px solid transparent;\n}\n\n.pqe-owner-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\n}\n\n.pqe-owner-card.high-priority {\n  border-left-color: #fa4d56;\n}\n\n.pqe-owner-card.medium-priority {\n  border-left-color: #ff832b;\n}\n\n.pqe-owner-card.normal-priority {\n  border-left-color: #24a148;\n}\n\n.pqe-owner-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.pqe-owner-name {\n  margin: 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.pqe-owner-metrics {\n  display: flex;\n  gap: 1rem;\n}\n\n.metric-item {\n  text-align: center;\n}\n\n.metric-label {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.metric-value.high-value {\n  color: #fa4d56;\n}\n\n.metric-value.medium-value {\n  color: #ff832b;\n}\n\n.metric-value.normal-value {\n  color: #24a148;\n}\n\n.pqe-owner-details {\n  flex-grow: 1;\n  margin-bottom: 1rem;\n}\n\n.groups-section {\n  margin-bottom: 1rem;\n}\n\n.groups-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.groups-title {\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.groups-count {\n  font-size: 0.75rem;\n  background-color: #393939;\n  padding: 0.125rem 0.375rem;\n  border-radius: 1rem;\n}\n\n.groups-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.group-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: #262626;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  border-left: 3px solid transparent;\n}\n\n.group-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.group-item.high-highlight {\n  border-left-color: #fa4d56;\n  background-color: rgba(250, 77, 86, 0.1);\n}\n\n.group-item.medium-highlight {\n  border-left-color: #ff832b;\n  background-color: rgba(255, 131, 43, 0.1);\n}\n\n.group-item.low-highlight {\n  border-left-color: #24a148;\n  background-color: rgba(36, 161, 72, 0.1);\n}\n\n.group-name {\n  font-size: 0.875rem;\n}\n\n.group-xfactor {\n  font-size: 0.875rem;\n  font-weight: 600;\n}\n\n.more-groups {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n  text-align: center;\n  margin-top: 0.5rem;\n}\n\n.collective-xfactor {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  background-color: #262626;\n  border-radius: 4px;\n  margin-top: 1rem;\n}\n\n.xfactor-label {\n  font-size: 0.875rem;\n}\n\n.xfactor-value {\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.pqe-owner-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.view-button, .issues-button {\n  width: 100%;\n}\n\n.summary-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.summary-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.summary-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.summary-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.summary-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.summary-icon.critical-groups {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.summary-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.summary-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.summary-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.no-data-message, .no-issues-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n/* Critical Issues Modal Styles */\n.issues-modal {\n  max-width: 800px;\n}\n\n.issue-type-tabs {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #333333;\n  padding-bottom: 1rem;\n}\n\n.tab-button {\n  min-width: 120px;\n}\n\n.tab-button.active {\n  background-color: #0f62fe;\n  color: #ffffff;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.acceptance-details, .resolution-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.low-severity {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.group-metrics {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.group-metric {\n  font-size: 0.75rem;\n  color: #c6c6c6;\n  background-color: #333333;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.metric-icon {\n  font-weight: 600;\n  margin-right: 0.125rem;\n}\n\n.modal-content {\n  padding: 1rem 0;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n</style>\n"]}]}