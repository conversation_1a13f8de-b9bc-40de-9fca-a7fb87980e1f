const path = require('path');

module.exports = {
  pages: {
    index: {
      entry: 'src/main.js',
      title: 'STATIT 2.0',
    },
  },
  devServer: {
    port: 8081,
    proxy: {
      '/api-statit2': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '/api': {
        target: process.env.VUE_APP_API_PATH, // Define your API base path here
        changeOrigin: true,
        pathRewrite: { '^/api': '' },
      },
      '/watsonx': {
        target: 'https://us-south.ml.cloud.ibm.com',  // WatsonX API base URL
        changeOrigin: true,
        pathRewrite: { '^/watsonx': '/ml/v1/text/generation' },  // Rewriting path to WatsonX endpoint
      },
      '/watsonx-direct': {
        target: 'https://us-south.ml.cloud.ibm.com',
        changeOrigin: true,
        secure: true,
        pathRewrite: { '^/watsonx-direct': '' },
      },
    },
  },
  chainWebpack: config => {
    // Resolve .mjs files
    config.resolve.extensions
      .add('.mjs')
      .end();

    // Handle .mjs files in Babel loader
    config.module
      .rule('js')
      .test(/\.m?js$/)
      .include
      .add(path.resolve(__dirname, 'node_modules/@carbon/charts-vue'))
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .tap(options => {
        return {
          ...options,
          presets: ['@babel/preset-env'],
          plugins: [
            '@babel/plugin-proposal-nullish-coalescing-operator',
            '@babel/plugin-proposal-optional-chaining'
          ]
        };
      });

    // Ensure all JS files are processed by Babel
    config.module
      .rule('js')
      .exclude
      .clear()
      .add(/node_modules/)
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .tap(options => {
        return {
          ...options,
          presets: ['@babel/preset-env'],
          plugins: [
            '@babel/plugin-proposal-nullish-coalescing-operator',
            '@babel/plugin-proposal-optional-chaining'
          ]
        };
      });
  },
};
