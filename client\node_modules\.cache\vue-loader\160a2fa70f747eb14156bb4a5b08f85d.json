{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue?vue&type=style&index=0&id=56d4aac8&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue", "mtime": 1748527827772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MetisXFactors.vue"], "names": [], "mappings": ";AAm+JA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "MetisXFactors.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"metis-xfactors-container\">\n    <MainHeader title=\"Metis XFactors Analysis\" />\n\n    <div class=\"bx--grid\">\n      <div class=\"bx--row\">\n        <div class=\"bx--col-lg-16\">\n          <!-- Global Date Controls -->\n          <cv-tile class=\"global-controls-tile\">\n            <div class=\"global-controls\">\n              <div class=\"date-controls-container\">\n                <div class=\"date-controls-layout\">\n                  <div class=\"custom-range-section\">\n                    <div class=\"date-inputs\">\n                      <div class=\"date-section-label\">Start Date:</div>\n                      <div class=\"date-dropdown-container\">\n                        <cv-dropdown\n                          id=\"start-month\"\n                          v-model=\"startMonthStr\"\n                          @change=\"updateStartDate\"\n                          class=\"month-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"(month, index) in months\"\n                            :key=\"index\"\n                            :value=\"String(index+1)\"\n                          >\n                            {{ month }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                        <cv-dropdown\n                          id=\"start-year\"\n                          v-model=\"startYear\"\n                          @change=\"updateStartDate\"\n                          class=\"year-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"year in availableYears\"\n                            :key=\"year\"\n                            :value=\"year\"\n                          >\n                            {{ year }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                      </div>\n\n                      <div class=\"date-section-label\">End Date:</div>\n                      <div class=\"date-dropdown-container\">\n                        <cv-dropdown\n                          id=\"end-month\"\n                          v-model=\"endMonthStr\"\n                          @change=\"updateEndDate\"\n                          class=\"month-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"(month, index) in months\"\n                            :key=\"index\"\n                            :value=\"String(index+1)\"\n                          >\n                            {{ month }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                        <cv-dropdown\n                          id=\"end-year\"\n                          v-model=\"endYear\"\n                          @change=\"updateEndDate\"\n                          class=\"year-dropdown\"\n                        >\n                          <cv-dropdown-item\n                            v-for=\"year in availableYears\"\n                            :key=\"year\"\n                            :value=\"year\"\n                          >\n                            {{ year }}\n                          </cv-dropdown-item>\n                        </cv-dropdown>\n                      </div>\n                    </div>\n\n                    <div class=\"analyze-button-container\">\n                      <cv-button @click=\"analyzeAllData\">Analyze Data</cv-button>\n                    </div>\n                  </div>\n\n                  <div class=\"date-separator\">\n                    <span class=\"or-text\">OR</span>\n                  </div>\n\n                  <div class=\"quick-select-section\">\n                    <div class=\"date-section-label\">Quick Select:</div>\n                    <cv-dropdown\n                      id=\"quick-select\"\n                      v-model=\"selectedTimeRange\"\n                      @change=\"applyTimeRange\"\n                      class=\"quick-select-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"\">Custom Range</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-month\">Last Month</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-3-months\">Last 3 Months</cv-dropdown-item>\n                      <cv-dropdown-item value=\"last-6-months\">Last 6 Months</cv-dropdown-item>\n                      <cv-dropdown-item value=\"ytd\">Year to Date</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tile>\n\n          <!-- Tab Navigation -->\n          <cv-tabs v-model=\"activeTab\">\n            <!-- Heatmap Tab (renamed from Dashboard) -->\n            <cv-tab label=\"Heatmap\" :selected=\"activeTab === 0\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"dashboard-controls\">\n                  <div class=\"dashboard-header\">\n                    <h3>XFactor Heatmap</h3>\n                  </div>\n\n                  <div class=\"last-updated-text\">\n                    Last Updated: {{ new Date().toLocaleString() }}\n                  </div>\n                </div>\n\n                <div class=\"dashboard-content\">\n                  <!-- Critical Issues Summary -->\n                  <div class=\"dashboard-critical-issues-summary\" v-if=\"dashboardData.length > 0 && currentMonthCriticalIssues > 0\">\n                    <h4>\n                      <span class=\"critical-count\">{{ currentMonthCriticalIssues }}</span> critical issues detected this month ({{ getCurrentMonthName() }})\n                    </h4>\n                  </div>\n\n                  <div class=\"heatmap-container\" v-if=\"dashboardData.length > 0\">\n                    <div class=\"heatmap-header\">\n                      <h4>XFactor Status Heatmap</h4>\n\n                      <div class=\"heatmap-controls\">\n                        <div class=\"filter-container\">\n                          <div class=\"filter-type\">\n                            <label for=\"filterType\">Filter By:</label>\n                            <cv-dropdown\n                              id=\"filterType\"\n                              v-model=\"selectedFilterType\"\n                              @change=\"onFilterTypeChange\"\n                              class=\"carbon-dropdown filter-type-dropdown\"\n                            >\n                              <cv-dropdown-item\n                                v-for=\"type in filterTypes\"\n                                :key=\"type.value\"\n                                :value=\"type.value\"\n                              >\n                                {{ type.text }}\n                              </cv-dropdown-item>\n                            </cv-dropdown>\n                          </div>\n\n                          <div class=\"owner-filter\">\n                            <label for=\"ownerSelect\">{{ selectedFilterType === 'group' ? 'Commodity' : selectedFilterType === 'dev_owner' ? 'Dev Owner' : 'PQE Owner' }}:</label>\n                            <cv-dropdown\n                              id=\"ownerSelect\"\n                              v-model=\"selectedOwner\"\n                              @change=\"onOwnerChange\"\n                              class=\"carbon-dropdown owner-dropdown\"\n                            >\n                              <cv-dropdown-item\n                                v-for=\"option in ownerOptions[selectedFilterType]\"\n                                :key=\"option\"\n                                :value=\"option\"\n                              >\n                                {{ option }}\n                              </cv-dropdown-item>\n                            </cv-dropdown>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"heatmap-legend\">\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #0062ff;\"></div>\n                        <span>Normal</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #ff9a00;\"></div>\n                        <span>Sustained Problem</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #fa4d56;\"></div>\n                        <span>Short-Term Spike</span>\n                      </div>\n                      <div class=\"legend-item\">\n                        <div class=\"status-indicator\" style=\"background-color: #da1e28;\"></div>\n                        <span>Critical (Both)</span>\n                      </div>\n                    </div>\n\n                    <div class=\"heatmap-table-container\">\n                      <table class=\"heatmap-table\">\n                        <thead>\n                          <tr>\n                            <th>Breakout Group</th>\n                            <th v-for=\"(month, index) in dashboardMonths\" :key=\"index\">{{ month }}</th>\n                            <th>Actions</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          <tr v-for=\"(row, rowIndex) in dashboardData\" :key=\"rowIndex\">\n                            <td\n                              class=\"breakout-name\"\n                              @mouseover=\"showAiSummaryTooltip($event)\"\n                              @mouseout=\"hideAiSummaryTooltip()\"\n                              @click=\"getAiSummary(row)\"\n                            >\n                              <div class=\"breakout-name-content\">\n                                {{ row.breakoutName }}\n                                <span class=\"ai-summary-indicator\" v-if=\"row.breakoutName === loadingAiSummaryFor\">\n                                  <span class=\"loading-dots\"></span>\n                                </span>\n                              </div>\n                            </td>\n                            <td\n                              v-for=\"(cell, cellIndex) in row.months\"\n                              :key=\"cellIndex\"\n                              :class=\"getCellClass(cell)\"\n                              @mouseover=\"showCellTooltip(cell, $event)\"\n                              @mouseout=\"hideCellTooltip()\"\n                              @click=\"selectBreakoutFromDashboardWithMonth(row.breakoutName, cell.month)\"\n                              style=\"cursor: pointer;\"\n                            >\n                              <div class=\"cell-content\">\n                                <span v-if=\"cell.xFactor !== null && cell.xFactor !== undefined\">{{ cell.xFactor.toFixed(1) }}</span>\n                                <span v-else>0.0</span>\n                              </div>\n                            </td>\n                            <td class=\"view-data-cell\">\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"selectBreakoutFromDashboard(row.breakoutName)\"\n                              >\n                                View Data\n                              </cv-button>\n                            </td>\n                          </tr>\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n\n                  <div class=\"no-data-message\" v-if=\"dashboardNoDataMessage\">\n                    {{ dashboardNoDataMessage }}\n                  </div>\n                </div>\n\n                <!-- Cell tooltip -->\n                <div class=\"cell-tooltip\" v-if=\"showTooltip\" :style=\"tooltipStyle\">\n                  <div class=\"tooltip-header\">\n                    <strong>{{ tooltipData.breakoutName }}</strong>\n                    <span>{{ tooltipData.month }}</span>\n                  </div>\n                  <div class=\"tooltip-content\">\n                    <p>XFactor: {{ tooltipData.xFactor !== null && tooltipData.xFactor !== undefined ? tooltipData.xFactor.toFixed(2) : '0.00' }}</p>\n                    <p>Target Rate: {{ typeof tooltipData.targetRate === 'number' ? tooltipData.targetRate.toFixed(6) : tooltipData.targetRate }}</p>\n                    <p>Status: {{ tooltipData.status || 'Normal' }}</p>\n                    <p v-if=\"tooltipData.duration\">Duration: {{ tooltipData.duration }} months</p>\n                    <p>Defects: {{ tooltipData.defects !== null && tooltipData.defects !== undefined ? tooltipData.defects : 0 }}</p>\n                    <p>Volume: {{ tooltipData.volume !== null && tooltipData.volume !== undefined ? tooltipData.volume : 0 }}</p>\n                    <p v-if=\"tooltipData.criticalIssues > 0\" class=\"critical-issues-tooltip\">\n                      <strong>{{ tooltipData.criticalIssues }} critical issues detected</strong>\n                    </p>\n                    <p class=\"click-to-view-tooltip\">Click to view detailed analysis</p>\n                  </div>\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n            <!-- XFactor Analysis Tab (renamed to Overall) -->\n            <cv-tab label=\"Overall\" :selected=\"activeTab === 1\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"controls-section\">\n                  <div class=\"selected-date-range\">\n                    <p>Selected Date Range: {{ formatDateRange(startDate, endDate) }}</p>\n                  </div>\n                </div>\n\n                <div class=\"breakout-selection\">\n                  <h4>Select Breakout Group</h4>\n                  <div class=\"breakout-dropdown\">\n                    <cv-dropdown\n                      v-model=\"selectedBreakout\"\n                      @change=\"handleBreakoutChange\"\n                      :disabled=\"isLoading || breakoutNames.length === 0\"\n                    >\n                      <cv-dropdown-item value=\"\">All Breakout Groups ({{ breakoutNames.length }} total)</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"name in breakoutNames\"\n                        :key=\"name\"\n                        :value=\"name\"\n                      >\n                        {{ name }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                    <div v-if=\"isLoading\" class=\"loading-indicator\">Loading...</div>\n                    <div v-else-if=\"breakoutNames.length === 0 && !isLoading\" class=\"no-data-message\">\n                      No breakout groups available\n                    </div>\n                  </div>\n\n                  <!-- Part Numbers Section for Main Tab -->\n                  <div class=\"part-numbers-section\" v-if=\"selectedBreakout && mainTabPartNumbers.length > 0\">\n                    <h4>Part Numbers in {{ selectedBreakout }}</h4>\n                    <p class=\"part-count\">Total: {{ mainTabPartNumbers.length }} part numbers</p>\n                    <div class=\"part-numbers-container\">\n                      <div v-for=\"(pn, index) in mainTabPartNumbers\" :key=\"index\" class=\"part-number-item\">\n                        {{ pn }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"charts-section\">\n                  <div class=\"chart-container\" v-if=\"chartData.length > 0\">\n                    <h4>XFactor Trend Analysis</h4>\n                    <div class=\"chart-wrapper\">\n                      <LineChart\n                        :data=\"chartData\"\n                        :options=\"chartOptions\"\n                        :loading=\"isLoading\"\n                      />\n                    </div>\n\n                    <div class=\"threshold-info\">\n                      <div class=\"threshold-item\">\n                        <div class=\"threshold-color sustained\"></div>\n                        <span>Sustained Problem Threshold (X-Factor > 1.5 for 3+ months)</span>\n                      </div>\n                      <div class=\"threshold-item\">\n                        <div class=\"threshold-color spike\"></div>\n                        <span>Short-Term Spike Threshold (X-Factor > 3.0)</span>\n                      </div>\n                    </div>\n\n                    <div class=\"info-button\" @click=\"showInfoModal = true\">\n                      <span>ⓘ</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"alerts-container\" v-if=\"alerts.length > 0\">\n                  <h4>Alerts</h4>\n                  <table class=\"alerts-table\">\n                    <thead>\n                      <tr>\n                        <th>Breakout Group</th>\n                        <th>Status</th>\n                        <th>Period</th>\n                        <th>X-Factor</th>\n                        <th>Defects</th>\n                        <th>Volume</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      <tr v-for=\"(alert, index) in alerts\" :key=\"index\">\n                        <td>{{ alert.breakoutName }}</td>\n                        <td>\n                          <div class=\"status-indicator\" :class=\"getStatusClass(alert.status)\">\n                            {{ alert.status }}\n                          </div>\n                        </td>\n                        <td>{{ alert.period }}</td>\n                        <td>{{ alert.xFactor.toFixed(2) }}</td>\n                        <td>{{ alert.defects }}</td>\n                        <td>{{ alert.volume }}</td>\n                      </tr>\n                    </tbody>\n                  </table>\n                </div>\n\n                <div class=\"no-data-message\" v-if=\"noDataMessage\">\n                  {{ noDataMessage }}\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n\n\n            <!-- PQE Dashboard Tab -->\n            <cv-tab label=\"PQE Dashboard\" :selected=\"activeTab === 2\">\n              <PQEDashboard />\n            </cv-tab>\n\n            <!-- Group Tab -->\n            <cv-tab label=\"Group\" :selected=\"activeTab === 3\">\n              <cv-tile class=\"content-tile\">\n                <div class=\"group2-tab-container\">\n                  <div class=\"group2-tab-header\">\n                    <h4>Advanced Group Analysis</h4>\n                    <p>Analyze breakout groups with different time periods and analysis types</p>\n                  </div>\n\n                  <div class=\"group2-tab-controls\">\n                    <div class=\"breakout-group-select\">\n                      <label for=\"group2-tab-select\">Select Breakout Group:</label>\n                      <cv-dropdown\n                        id=\"group2-tab-select\"\n                        v-model=\"group2TabSelectedGroup\"\n                        :disabled=\"isGroup2TabLoading || breakoutNames.length === 0\"\n                      >\n                        <cv-dropdown-item\n                          v-for=\"name in breakoutNames\"\n                          :key=\"name\"\n                          :value=\"name\"\n                        >\n                          {{ name }}\n                        </cv-dropdown-item>\n                      </cv-dropdown>\n                    </div>\n                  </div>\n\n                  <div class=\"group2-tab-content\" v-if=\"group2TabSelectedGroup\">\n                    <div class=\"analysis-grid\">\n                      <table class=\"analysis-table\">\n                        <thead>\n                          <tr>\n                            <th>Analysis Type</th>\n                            <th>1 Month</th>\n                            <th>3 Months</th>\n                            <th>6 Months</th>\n                            <th>Custom</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n\n                          <tr>\n                            <td>Root Cause Analysis</td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(1)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(3)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewRootCauseAnalysis(6)\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                            <td>\n                              <cv-button\n                                kind=\"tertiary\"\n                                size=\"small\"\n                                @click=\"viewCustomRootCauseAnalysis()\"\n                              >\n                                View\n                              </cv-button>\n                            </td>\n                          </tr>\n\n                        </tbody>\n                      </table>\n                    </div>\n\n\n\n                    <!-- Root Cause Analysis Section -->\n                    <div class=\"root-cause-section\" v-if=\"rootCauseChartData.length > 0 || isRootCauseDataLoading\">\n                      <div class=\"section-header\">\n                        <h4>Root Cause Analysis - {{ group2TabSelectedGroup }} ({{ rootCauseTimeRange }})</h4>\n                      </div>\n\n                      <!-- AI Summary Section - Moved above chart -->\n                      <div class=\"ai-summary-section\" v-if=\"rootCauseAiSummary\">\n                        <div class=\"ai-summary-content\">\n                          <p>{{ rootCauseAiSummary }}</p>\n                        </div>\n                      </div>\n                      <div class=\"ai-summary-loading\" v-else-if=\"isRootCauseAiLoading\">\n                        <div class=\"loading-spinner\"></div>\n                        <span>Generating AI analysis...</span>\n                      </div>\n\n                      <!-- Chart Section -->\n                      <div class=\"root-cause-chart-container\" v-if=\"rootCauseChartData.length > 0\">\n                        <h5>Root Cause Categories</h5>\n                        <div class=\"chart-wrapper\">\n                          <StackedBarChart\n                            :data=\"rootCauseChartData\"\n                            :loading=\"isRootCauseDataLoading\"\n                            :height=\"'400px'\"\n                            :options=\"rootCauseChartOptions\"\n                            @bar-click=\"handleRootCauseBarClick\"\n                          />\n                        </div>\n                      </div>\n                      <div class=\"no-data-message\" v-else-if=\"isRootCauseDataLoading\">\n                        Loading root cause data...\n                      </div>\n                      <div class=\"no-data-message\" v-else>\n                        No root cause data available for this time period\n                      </div>\n\n                      <!-- Critical Issues Section -->\n                      <div class=\"critical-issues-section\" v-if=\"hasCriticalRootCauseIssue\">\n                        <h5>Critical Issues Detected</h5>\n\n                        <!-- Critical Issues List -->\n                        <div v-for=\"issue in criticalIssues\" :key=\"issue.id\" class=\"critical-issue-item\">\n                          <div class=\"critical-issue-header\" @click=\"toggleCriticalIssue(issue.id)\">\n                            <cv-tag\n                              :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                              :label=\"issue.severity === 'high' ? 'High Severity' : 'Medium Severity'\"\n                            />\n                            <span class=\"critical-issue-title\">{{ issue.category }}</span>\n                            <cv-tag\n                              kind=\"cool-gray\"\n                              class=\"month-tag\"\n                              :label=\"issue.month\"\n                            />\n                            <span class=\"critical-issue-multiplier\">\n                              <template v-if=\"issue.increaseMultiplier === '(new)'\">\n                                ({{ issue.increaseMultiplier }})\n                              </template>\n                              <template v-else>\n                                ({{ issue.increaseMultiplier }}x spike)\n                              </template>\n                            </span>\n                            <span class=\"critical-issue-status\" v-if=\"hasCriticalIssueBeenUpdated(issue.id)\">\n                              <cv-tag kind=\"green\" label=\"Updated\" />\n                            </span>\n                            <span class=\"expand-icon\">{{ isIssueExpanded(issue.id) ? '▼' : '▶' }}</span>\n                          </div>\n\n                          <div v-if=\"isIssueExpanded(issue.id)\" class=\"critical-issue-content\">\n                            <!-- AI Description -->\n                            <div class=\"critical-issue-ai-description\">\n                              <p>{{ issue.description }}</p>\n                            </div>\n\n                            <!-- Update Form -->\n                            <div class=\"critical-issue-update-form\">\n                              <h6>Provide an update on this issue:</h6>\n                              <cv-text-area\n                                :value=\"getCriticalIssueUpdateText(issue.id)\"\n                                @input=\"updateCriticalIssueText(issue.id, $event)\"\n                                placeholder=\"Enter your update on this critical issue...\"\n                                :label=\"'Update for ' + issue.category\"\n                                hide-label\n                                rows=\"3\"\n                              />\n                              <cv-button\n                                @click=\"saveCriticalIssueUpdate(issue.id, getCriticalIssueUpdateText(issue.id))\"\n                                :disabled=\"!getCriticalIssueUpdateText(issue.id).trim()\"\n                                class=\"save-update-button\"\n                              >\n                                Save Update\n                              </cv-button>\n                            </div>\n\n                            <!-- Previous Updates -->\n                            <div class=\"previous-updates\" v-if=\"getCriticalIssueHistory(issue.id).length > 0\">\n                              <h6>Previous Updates</h6>\n                              <cv-structured-list condensed>\n                                <template slot=\"headings\">\n                                  <cv-structured-list-heading>Date</cv-structured-list-heading>\n                                  <cv-structured-list-heading>Update</cv-structured-list-heading>\n                                </template>\n                                <template slot=\"items\">\n                                  <cv-structured-list-row\n                                    v-for=\"(update, index) in getCriticalIssueHistory(issue.id)\"\n                                    :key=\"index\"\n                                  >\n                                    <cv-structured-list-cell>{{ formatDate(update.timestamp) }}</cv-structured-list-cell>\n                                    <cv-structured-list-cell>{{ update.content }}</cv-structured-list-cell>\n                                  </cv-structured-list-row>\n                                </template>\n                              </cv-structured-list>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div class=\"no-data-message\" v-if=\"!group2TabSelectedGroup\">\n                    Please select a breakout group to analyze\n                  </div>\n                </div>\n              </cv-tile>\n            </cv-tab>\n\n\n\n\n          </cv-tabs>\n        </div>\n      </div>\n    </div>\n\n    <!-- Info Modal -->\n    <div class=\"info-modal\" v-if=\"showInfoModal\">\n      <div class=\"modal-content\">\n        <span class=\"close-button\" @click=\"showInfoModal = false\">&times;</span>\n        <h3>About XFactor Analysis</h3>\n        <p>\n          The XFactor is a measure that compares the current defect rate to the target defect rate.\n          It helps identify when a part group is experiencing unusual quality issues.\n        </p>\n        <h4>How XFactor is Calculated:</h4>\n        <p>\n          XFactor = Current Period Defect Rate / Target Rate\n        </p>\n        <p>\n          Target rates are defined for each breakout group in the breakout_targets.xlsx file.\n        </p>\n        <h4>Alert Thresholds:</h4>\n        <ul>\n          <li><strong>Short-Term Spike:</strong> XFactor > 3.0 in a single month</li>\n          <li><strong>Sustained Problem:</strong> XFactor > 1.5 for three or more consecutive months</li>\n        </ul>\n        <p>\n          The data is grouped by \"Full Breakout Name\" from the Metis test data, allowing for analysis\n          of quality trends across different part categories.\n        </p>\n      </div>\n    </div>\n\n    <!-- AI Summary Modal -->\n    <div class=\"info-modal\" v-if=\"showAiSummaryModal\">\n      <div class=\"modal-content ai-summary-modal\">\n        <span class=\"close-button\" @click=\"showAiSummaryModal = false\">&times;</span>\n        <h3>AI Summary: {{ aiSummaryBreakoutName }}</h3>\n        <div v-if=\"isLoadingAiSummary\" class=\"ai-summary-loading\">\n          <div class=\"loading-spinner\"></div>\n          <p>Generating AI summary...</p>\n        </div>\n        <div v-else class=\"ai-summary-content\">\n          <p v-html=\"aiSummaryText\"></p>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Summary Tooltip -->\n    <div class=\"ai-tooltip\" v-if=\"showAiTooltip\" :style=\"aiTooltipStyle\">\n      <div class=\"ai-tooltip-content\">\n        <span>AI summary, click to see</span>\n      </div>\n    </div>\n\n    <!-- Failure Modes Modal -->\n    <cv-modal\n      :visible=\"showFailureModesModal\"\n      @modal-hidden=\"showFailureModesModal = false\"\n      class=\"failure-modes-modal\"\n    >\n      <template slot=\"title\">\n        Failure Modes Analysis - {{ selectedCategory }} ({{ selectedMonth }})\n      </template>\n      <template slot=\"content\">\n        <div class=\"failure-modes-content\">\n          <div class=\"failure-modes-chart-container\" v-if=\"failureModesChartData.length > 0\">\n            <HBarChart\n              :data=\"failureModesChartData\"\n              :loading=\"isFailureModesLoading\"\n              :height=\"'400px'\"\n              :title=\"`Failure Modes for ${selectedCategory} (${selectedMonth})`\"\n            />\n          </div>\n          <div class=\"no-data-message\" v-else-if=\"isFailureModesLoading\">\n            Loading failure modes data...\n          </div>\n          <div class=\"no-data-message\" v-else>\n            No failure modes data available for this category and month.\n          </div>\n        </div>\n      </template>\n      <template slot=\"secondary-button\">\n        Close\n      </template>\n    </cv-modal>\n\n    <!-- Custom Date Modal -->\n    <cv-modal\n      :visible=\"showCustomDateModal\"\n      @modal-hidden=\"hideCustomDateModal\"\n      class=\"custom-date-modal\"\n    >\n      <template slot=\"title\">\n        Custom Date Range for {{ customDateAnalysisType }} Analysis\n      </template>\n      <template slot=\"content\">\n        <div class=\"custom-date-content\">\n          <div class=\"date-inputs\">\n            <div class=\"date-section\">\n              <div class=\"date-section-label\">Start Date:</div>\n              <div class=\"date-dropdown-container\">\n                <cv-dropdown\n                  id=\"custom-start-month\"\n                  v-model=\"customStartMonthStr\"\n                  class=\"month-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"(month, index) in months\"\n                    :key=\"index\"\n                    :value=\"String(index+1)\"\n                  >\n                    {{ month }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n                <cv-dropdown\n                  id=\"custom-start-year\"\n                  v-model=\"customStartYear\"\n                  class=\"year-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"year in availableYears\"\n                    :key=\"year\"\n                    :value=\"year\"\n                  >\n                    {{ year }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"date-section\">\n              <div class=\"date-section-label\">End Date:</div>\n              <div class=\"date-dropdown-container\">\n                <cv-dropdown\n                  id=\"custom-end-month\"\n                  v-model=\"customEndMonthStr\"\n                  class=\"month-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"(month, index) in months\"\n                    :key=\"index\"\n                    :value=\"String(index+1)\"\n                  >\n                    {{ month }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n                <cv-dropdown\n                  id=\"custom-end-year\"\n                  v-model=\"customEndYear\"\n                  class=\"year-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"year in availableYears\"\n                    :key=\"year\"\n                    :value=\"year\"\n                  >\n                    {{ year }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"custom-date-buttons\">\n            <cv-button @click=\"applyCustomDateRange\">Apply</cv-button>\n            <cv-button kind=\"secondary\" @click=\"hideCustomDateModal\">Cancel</cv-button>\n          </div>\n        </div>\n      </template>\n    </cv-modal>\n\n\n  </div>\n</template>\n\n<script>\nimport MainHeader from '@/components/MainHeader';\nimport LineChart from '@/components/LineChart/LineChart.vue';\nimport StackedBarChart from '@/components/StackedBarChart.vue';\nimport HBarChart from '@/components/HBarChart';\nimport PQEDashboard from '@/components/PQEDashboard/PQEDashboard.vue';\nimport axios from 'axios';\nimport { formatDashboardSummaryPrompt } from '@/utils/watsonxPrompts';\nimport logger from '@/utils/logger';\nimport {\n  CvDropdown,\n  CvDropdownItem,\n  CvButton,\n  CvModal,\n  CvTile,\n  CvTag,\n  CvTextArea,\n  CvStructuredList,\n  CvStructuredListRow,\n  CvStructuredListCell,\n  CvStructuredListHeading\n} from '@carbon/vue';\n\nexport default {\n  name: 'MetisXFactors',\n  components: {\n    MainHeader,\n    LineChart,\n    StackedBarChart,\n    HBarChart,\n    PQEDashboard,\n    CvDropdown,\n    CvDropdownItem,\n    CvButton,\n    CvModal,\n    CvTile,\n    CvTag,\n    CvTextArea,\n    CvStructuredList,\n    CvStructuredListRow,\n    CvStructuredListCell,\n    CvStructuredListHeading\n  },\n  data() {\n    return {\n      // Modal visibility\n      showCustomDateModal: false,\n\n      // Main tab data\n      startDate: '',\n      endDate: '',\n      minDate: '2024-01', // January 2024\n      maxDate: new Date().toISOString().split('T')[0].substring(0, 7), // YYYY-MM format\n      // Month and year dropdowns\n      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],\n      availableYears: [],\n      startMonth: 1,\n      startMonthStr: '1',\n      startYear: '2024', // String for Carbon Vue components\n      endMonth: new Date().getMonth() + 1,\n      endMonthStr: String(new Date().getMonth() + 1),\n      endYear: String(new Date().getFullYear()), // String for Carbon Vue components\n      selectedTimeRange: '',\n      breakoutNames: [],\n      selectedBreakout: '',\n      xFactorData: {},\n      chartData: [],\n      alerts: [],\n      mainTabPartNumbers: [],\n      showInfoModal: false,\n      noDataMessage: '',\n      isLoading: false,\n\n      // Breakout tab data\n      breakoutTabSelectedGroup: '',\n      breakoutTabStartDate: '',\n      breakoutTabEndDate: '',\n      breakoutTabTimeRange: 'last-month',\n      // Breakout tab month and year dropdowns\n      breakoutTabStartMonth: 1,\n      breakoutTabStartYear: '2024', // String for Carbon Vue components\n      breakoutTabEndMonth: new Date().getMonth() + 1,\n      breakoutTabEndYear: String(new Date().getFullYear()), // String for Carbon Vue components\n      breakoutTabChartData: [],\n      breakoutTabBarChartData: [],\n      breakoutTabAlerts: [],\n      breakoutTabPartNumbers: [],\n      breakoutTabNoDataMessage: '',\n      isBreakoutTabLoading: false,\n      breakoutTabAllXFactorData: {},\n\n      // Dashboard tab data\n      dashboardData: [],\n      dashboardMonths: [],\n      dashboardNoDataMessage: '',\n      isDashboardLoading: false,\n      showTooltip: false,\n      tooltipStyle: {\n        top: '0px',\n        left: '0px'\n      },\n      tooltipData: {\n        breakoutName: '',\n        month: '',\n        xFactor: null,\n        status: '',\n        duration: null,\n        defects: null,\n        volume: null,\n        criticalIssues: 0\n      },\n      currentMonthCriticalIssues: 0,\n      // Owner filter dropdowns\n      filterTypes: [\n        { value: 'group', text: 'Commodity' },\n        { value: 'dev_owner', text: 'Dev Owner' },\n        { value: 'pqe_owner', text: 'PQE Owner' }\n      ],\n      selectedFilterType: 'group',\n      ownerOptions: {\n        group: [],\n        dev_owner: [],\n        pqe_owner: []\n      },\n      selectedOwner: 'All',\n\n      // AI Summary data\n      showAiTooltip: false,\n      aiTooltipStyle: {\n        top: '0px',\n        left: '0px'\n      },\n      showAiSummaryModal: false,\n      aiSummaryBreakoutName: '',\n      aiSummaryText: '',\n      isLoadingAiSummary: false,\n      loadingAiSummaryFor: '',\n\n      // AI Test tab data\n      selectedAiModel: 'ibm/granite-13b-instruct-v2',\n      aiTemperature: 0.7,\n      aiPrompt: '',\n      aiResponse: '',\n      isAiLoading: false,\n\n      // Category Analysis tab data\n      categoryTabSelectedGroup: '',\n      categoryTabChartData: [],\n      categoryTabCategories: [],\n      categoryTabPartNumbers: [],\n      categoryTabNoDataMessage: '',\n      isCategoryTabLoading: false,\n      categoryChartOptions: {\n        title: 'Defect Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            // Domain will be set dynamically based on data\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        bars: {width: 60},\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        color: {\n          scale: {}\n        },\n        data: {\n          onclick: (data) => {\n            console.log('Chart clicked from options:', data);\n            this.handleCategoryBarClick(data);\n          }\n        }\n      },\n\n      // Group 2 Tab data\n      group2TabSelectedGroup: '',\n      group2TabNoDataMessage: '',\n      isGroup2TabLoading: false,\n\n      // Custom Date Modal data\n      customDateAnalysisType: '',\n      customStartMonthStr: String(new Date().getMonth() + 1),\n      customStartYear: String(new Date().getFullYear()),\n      customEndMonthStr: String(new Date().getMonth() + 1),\n      customEndYear: String(new Date().getFullYear()),\n\n\n\n      // Root Cause Analysis data\n      rootCauseTimeRange: '',\n      rootCauseMonths: 1,\n      rootCauseStartDate: '',\n      rootCauseEndDate: '',\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n      rootCauseAiSummary: '',\n      isRootCauseAiLoading: false,\n      hasCriticalRootCauseIssue: false,\n      criticalIssueDescription: '',\n      criticalIssues: [], // Array of critical issues with details\n      criticalIssueUpdates: {}, // Object mapping issue ID to updates\n      expandedIssues: {}, // Track which issues are expanded\n      rootCauseChartOptions: {\n        title: 'Root Cause Categories by Month',\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true\n            // Domain will be set dynamically based on data\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        bars: {width: 60},\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            if (!dataPoints || dataPoints.length === 0) return '';\n\n            const dataPoint = dataPoints[0];\n            const isCritical = dataPoint.data && dataPoint.data.isCritical;\n            const category = dataPoint.group;\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${category}</strong>${isCritical ? ' <span style=\"color: #fa4d56;\">(Critical)</span>' : ''}</p>\n                <p>Month: ${dataPoint.key}</p>\n                <p>Fail Rate: ${dataPoint.value.toFixed(2)}%</p>\n                <p>Defects: ${dataPoint.data && dataPoint.data.defects ? dataPoint.data.defects : 0}</p>\n                <p>Volume: ${dataPoint.data && dataPoint.data.volume ? dataPoint.data.volume.toLocaleString() : 'N/A'}</p>\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {}  // Will be populated dynamically based on categories\n        },\n        data: {\n          onclick: (data) => {\n            console.log('Root cause chart clicked:', data);\n            this.handleRootCauseBarClick(data);\n          }\n        }\n      },\n\n      // Failure Modes Modal data\n      showFailureModesModal: false,\n      selectedMonth: '',\n      selectedCategory: '',\n\n      // Selected month for dashboard navigation\n      selectedDashboardMonth: '',\n\n      // Active tab index for programmatic tab switching\n      activeTab: 0,\n      failureModesChartData: [],\n      isFailureModesLoading: false,\n      failureModesChartOptions: {\n        title: 'Failure Modes Analysis',\n        axes: {\n          left: {\n            title: 'Count',\n            mapsTo: 'value',\n            scaleType: 'linear'\n          },\n          right: {\n            title: 'Cumulative %',\n            mapsTo: 'percentage',\n            scaleType: 'linear',\n            domain: [0, 100]\n          },\n          bottom: {\n            title: 'Failure Mode',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            // Find the Count data point\n            const countPoint = dataPoints.find(point => point.group === 'Count');\n            // Find the Percentage data point\n            const percentPoint = dataPoints.find(point => point.group === 'Cumulative %');\n\n            if (!countPoint) return '';\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${countPoint.key}</strong></p>\n                <p>Count: ${countPoint.value}</p>\n                ${percentPoint ? `<p>Cumulative %: ${percentPoint.value.toFixed(1)}%</p>` : ''}\n                ${countPoint.data.category ? `<p>Category: ${countPoint.data.category}</p>` : ''}\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {\n            'Count': '#0f62fe',\n            'Cumulative %': '#da1e28'\n          }\n        },\n        comboChartTypes: [\n          {\n            type: 'simple-bar',\n            options: {\n              fillColors: ['#0f62fe']\n            },\n            correspondingDatasets: ['Count']\n          },\n          {\n            type: 'line',\n            options: {\n              points: {\n                enabled: true,\n                radius: 5\n              },\n              strokeWidth: 2\n            },\n            correspondingDatasets: ['Cumulative %']\n          }\n        ]\n      },\n      chartOptions: {\n        title: 'XFactor Trend by Breakout Group',\n        axes: {\n          bottom: {\n            title: 'Period',\n            mapsTo: 'date',\n            scaleType: 'time',\n            domain: [new Date('2024-01-01'), new Date('2025-12-31')], // Set explicit domain to show all dates\n            ticks: {\n              number: 12 // Show more ticks for better readability\n            },\n            formatters: {\n              tick: (date) => {\n                // Format the date to show month and year\n                const d = new Date(date);\n                return d.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n              }\n            }\n          },\n          left: {\n            title: 'XFactor',\n            mapsTo: 'value',\n            scaleType: 'linear',\n            domain: [0, 5],\n            thresholds: [\n              {\n                value: 1.5,\n                label: 'Sustained Problem Threshold',\n                fillColor: 'rgba(255, 204, 0, 0.2)',\n                strokeColor: '#FFCC00'\n              },\n              {\n                value: 3.0,\n                label: 'Short-Term Spike Threshold',\n                fillColor: 'rgba(255, 0, 0, 0.2)',\n                strokeColor: '#FF0000'\n              }\n            ]\n          }\n        },\n        curve: 'curveMonotoneX',\n        height: '400px',\n        legend: {\n          alignment: 'center',\n          enabled: true,\n          truncation: {\n            type: 'end',\n            threshold: 20\n          }\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (dataPoints) => {\n            const dataPoint = dataPoints[0];\n            if (!dataPoint) return '';\n\n            // Ensure we're working with a proper date object\n            const date = dataPoint.date instanceof Date ? dataPoint.date : new Date(dataPoint.date);\n            const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });\n\n            // Get target rate if available\n            const targetRate = dataPoint.data && dataPoint.data.targetRate ?\n              dataPoint.data.targetRate :\n              (this.xFactorData[dataPoint.group] && this.xFactorData[dataPoint.group].targetRate ?\n                this.xFactorData[dataPoint.group].targetRate : 'N/A');\n\n            return `\n              <div class=\"custom-tooltip\">\n                <p><strong>${dataPoint.group}</strong></p>\n                <p>Period: ${formattedDate}</p>\n                <p>XFactor: ${dataPoint.value.toFixed(2)}</p>\n                <p>Target Rate: ${typeof targetRate === 'number' ? targetRate.toFixed(6) : targetRate}</p>\n                ${dataPoint.data.defects ? `<p>Defects: ${dataPoint.data.defects}</p>` : ''}\n                ${dataPoint.data.volume ? `<p>Volume: ${dataPoint.data.volume}</p>` : ''}\n              </div>\n            `;\n          }\n        },\n        color: {\n          scale: {\n            'Ariel HLA': '#0F62FE',\n            'Parthenon Base': '#6929C4',\n            'Metis': '#1192E8',\n            'Themis': '#005D5D',\n            'Felis HLA': '#9F1853',\n            'Petra HLA': '#FA4D56',\n            'Orion Base': '#570408',\n            'Optic': '#198038',\n            'Victoria Crypto HLA': '#002D9C'\n          }\n        },\n        data: {\n          loading: this.isLoading\n        },\n        zoomBar: {\n          top: {\n            enabled: true\n          }\n        }\n      }\n    };\n  },\n  watch: {\n    activeTab(newVal) {\n      console.log(`Active tab changed to: ${newVal}`);\n\n      // If switching to the Group tab and we have a selected breakout group,\n      // make sure the data is loaded\n      if (newVal === 3 && this.group2TabSelectedGroup) {\n        console.log(`Loading Group tab data for ${this.group2TabSelectedGroup}`);\n\n        // If we have a selected month, use that to determine the time period\n        if (this.selectedDashboardMonth) {\n          console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);\n          this.viewRootCauseAnalysis(1);\n        } else {\n          console.log('No specific month selected, showing 3 months of data');\n          this.viewRootCauseAnalysis(3);\n        }\n      }\n    },\n\n    selectedProcess(newVal) {\n      console.log(`Process changed to: ${newVal}`);\n      this.loadBreakoutGroups();\n    },\n\n    group2TabSelectedGroup(newVal) {\n      console.log(`Group tab selected group changed to: ${newVal}`);\n      if (newVal && this.activeTab === 3) {\n        // If we're on the Group tab and a group is selected, load the data\n        this.analyzeGroup2Data();\n      }\n    }\n  },\n  created() {\n    // Set default date range (last 6 months)\n    const today = new Date();\n    const sixMonthsAgo = new Date();\n    sixMonthsAgo.setMonth(today.getMonth() - 6);\n\n    // Initialize available years (2024 to 2025)\n    // Including 2025 since the API returns data for 2025\n    // Convert years to strings for Carbon Vue components\n    for (let year = 2024; year <= 2025; year++) {\n      this.availableYears.push(String(year));\n    }\n\n    // Set default values for dropdowns\n    this.startMonth = sixMonthsAgo.getMonth() + 1; // 1-based month\n    this.startMonthStr = String(this.startMonth);\n    this.startYear = String(sixMonthsAgo.getFullYear()); // Convert to string for Carbon Vue\n    this.endMonth = today.getMonth() + 1; // 1-based month\n    this.endMonthStr = String(this.endMonth);\n    this.endYear = String(today.getFullYear()); // Convert to string for Carbon Vue\n\n    // Format dates as YYYY-MM for internal use\n    this.startDate = this.formatMonthDate(sixMonthsAgo);\n    this.endDate = this.formatMonthDate(today);\n\n    // Set default date range for breakout tab (last month)\n    const lastMonth = new Date();\n    lastMonth.setMonth(today.getMonth() - 1);\n\n    // Set default values for breakout tab dropdowns\n    this.breakoutTabStartMonth = lastMonth.getMonth() + 1; // 1-based month\n    this.breakoutTabStartYear = String(lastMonth.getFullYear()); // Convert to string for Carbon Vue\n    this.breakoutTabEndMonth = today.getMonth() + 1; // 1-based month\n    this.breakoutTabEndYear = String(today.getFullYear()); // Convert to string for Carbon Vue\n\n    this.breakoutTabStartDate = this.formatMonthDate(lastMonth);\n    this.breakoutTabEndDate = this.formatMonthDate(today);\n\n    // Set min and max dates in the correct format\n    this.minDate = '2024-01'; // January 2024\n    this.maxDate = this.formatMonthDate(today);\n\n    // Load breakout names and owners data\n    this.loadBreakoutNames();\n    this.loadOwners();\n\n    // Automatically analyze data on page load\n    this.$nextTick(() => {\n      this.analyzeData();\n\n      // Load dashboard data with last 6 months\n      this.loadDashboardData();\n    });\n  },\n  mounted() {\n    // Add direct click handler for the chart\n    this.$nextTick(() => {\n      this.setupChartClickHandlers();\n    });\n  },\n  methods: {\n    // Helper method to format date as YYYY-MM for month input\n    formatMonthDate(date) {\n      const year = date.getFullYear();\n      // Month needs to be padded with leading zero if less than 10\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      return `${year}-${month}`;\n    },\n\n    // Helper method to get the first day of a month from YYYY-MM format\n    getFirstDayOfMonth(yearMonth) {\n      if (!yearMonth || yearMonth.length < 7) return null;\n\n      // Extract year and month directly from the string to avoid timezone issues\n      const [year, month] = yearMonth.split('-');\n\n      console.log(`Getting first day of month for ${yearMonth}`);\n      console.log(`Year: ${year}, Month: ${month}`);\n\n      // Return the first day of the month in YYYY-MM-DD format\n      return `${year}-${month}-01`;\n    },\n\n    // Helper method to get the last day of a month from YYYY-MM format\n    getLastDayOfMonth(yearMonth) {\n      if (!yearMonth || yearMonth.length < 7) return null;\n\n      // Extract year and month directly from the string to avoid timezone issues\n      const [year, month] = yearMonth.split('-');\n\n      console.log(`Getting last day of month for ${yearMonth}`);\n      console.log(`Year: ${year}, Month: ${month}`);\n\n      // Calculate the last day of the month\n      // For month 12 (December), we need to handle the year change\n      const nextMonth = parseInt(month) === 12 ? 1 : parseInt(month) + 1;\n      const nextMonthYear = parseInt(month) === 12 ? parseInt(year) + 1 : parseInt(year);\n\n      // Create a date for the first day of the next month, then subtract one day\n      // Use UTC methods to avoid timezone issues\n      const lastDay = new Date(Date.UTC(nextMonthYear, nextMonth - 1, 0));\n      const lastDayOfMonth = lastDay.getUTCDate();\n\n      console.log(`Last day of month: ${lastDayOfMonth}`);\n\n      // Return the last day of the month in YYYY-MM-DD format\n      return `${year}-${month}-${String(lastDayOfMonth).padStart(2, '0')}`;\n    },\n\n    // Helper method to get authentication config\n    getAuthConfig() {\n      const config = {};\n      const token = localStorage.getItem('token');\n\n      // For debugging\n      console.log('Token from localStorage:', token ? 'Token exists' : 'No token found');\n\n      if (token) {\n        config.headers = {\n          'Authorization': `Bearer ${token}`\n        };\n        console.log('Using authentication token for request');\n      } else {\n        // For testing purposes, we'll proceed without authentication\n        // since we've configured the server to skip authentication for Metis routes\n        console.log('No authentication token available, proceeding without authentication');\n      }\n\n      return config;\n    },\n\n    loadOwners() {\n      console.log('Loading owners data...');\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // First, get the Dan OwningGroup values from new_metis_test.xlsx\n      console.log('Requesting owning groups from API...');\n\n      axios.post('/api-statit2/get_metis_owning_groups', {}, config)\n        .then(response => {\n          console.log('Received owning groups response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            // Store the Dan OwningGroup values in the group option\n            this.ownerOptions.group = response.data.owning_groups || [];\n\n            // Add \"All\" option at the beginning\n            this.ownerOptions.group.unshift('All');\n\n            console.log(`Loaded ${this.ownerOptions.group.length} commodities from Dan OwningGroup column`);\n          } else {\n            console.error('Failed to load owning groups:', response.data.error_msg);\n          }\n\n          // Now get the dev and PQE owners\n          return axios.post('/api-statit2/get_metis_owners', {}, config);\n        })\n        .then(response => {\n          console.log('Received owners data response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            const ownersData = response.data.owners_data;\n\n            // Store the dev and PQE owners in the ownerOptions object\n            this.ownerOptions.dev_owner = ownersData.dev_owners || [];\n            this.ownerOptions.pqe_owner = ownersData.pqe_owners || [];\n\n            // Add \"All\" option at the beginning of each list\n            this.ownerOptions.dev_owner.unshift('All');\n            this.ownerOptions.pqe_owner.unshift('All');\n\n            console.log(`Loaded ${this.ownerOptions.dev_owner.length} dev owners and ${this.ownerOptions.pqe_owner.length} PQE owners`);\n\n            // Set default selection\n            this.selectedOwner = 'All';\n          } else {\n            console.error('Failed to load owners data:', response.data.error_msg);\n          }\n        })\n        .catch(error => {\n          console.error('Error loading owners data:', error);\n        });\n    },\n\n    // For backward compatibility - will be removed in future\n    loadOwningGroups() {\n      // This method is kept for backward compatibility\n      // It now calls the new loadOwners method\n      this.loadOwners();\n    },\n\n    loadBreakoutNames() {\n      console.log('Loading breakout names...');\n      this.isLoading = true;\n      this.noDataMessage = 'Loading breakout names...';\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Log the API request\n      console.log('Requesting breakout names from API...');\n\n      axios.post('/api-statit2/get_metis_breakout_names', {}, config)\n        .then(response => {\n          console.log('Received breakout names response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            // Filter out empty or null breakout names\n            const filteredNames = response.data.breakout_names\n              .filter(name => name && name.trim() !== '')\n              .sort((a, b) => a.localeCompare(b)); // Sort alphabetically\n\n            console.log(`Found ${filteredNames.length} valid breakout names out of ${response.data.breakout_names.length} total`);\n\n            // Log some sample breakout names for verification\n            if (filteredNames.length > 0) {\n              console.log('Sample breakout names from client:');\n              filteredNames.slice(0, 5).forEach((name, index) => {\n                console.log(`  ${index + 1}. ${name}`);\n              });\n\n              this.breakoutNames = filteredNames;\n              this.noDataMessage = '';\n            } else {\n              this.breakoutNames = [];\n              this.noDataMessage = 'No valid breakout names found in the data.';\n            }\n          } else {\n            console.error('Error loading breakout names:', response.data);\n            this.breakoutNames = [];\n\n            if (response.data.error_msg) {\n              this.noDataMessage = `Error loading breakout names: ${response.data.error_msg}`;\n            } else {\n              this.noDataMessage = 'Error loading breakout names. Please try again.';\n            }\n          }\n          this.isLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading breakout names:', error);\n          this.breakoutNames = [];\n\n          if (error.response) {\n            console.error('Error response data:', error.response.data);\n\n            if (error.response.status === 401) {\n              this.noDataMessage = 'Authentication error. Please log in again.';\n              // Optionally redirect to login page\n              // this.$router.push('/login');\n            } else {\n              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;\n            }\n          } else if (error.request) {\n            console.error('No response received:', error.request);\n            this.noDataMessage = 'No response received from server. Please check your connection.';\n          } else {\n            console.error('Error message:', error.message);\n            this.noDataMessage = `Error: ${error.message}`;\n          }\n\n          this.isLoading = false;\n        });\n    },\n    analyzeData() {\n      // Validate date range\n      if (!this.startDate || !this.endDate) {\n        this.noDataMessage = 'Please select a valid date range';\n        return;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.startDate + '-01');\n      const end = new Date(this.endDate + '-01');\n\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.noDataMessage = 'Invalid date format';\n        return;\n      }\n\n      if (start > end) {\n        this.noDataMessage = 'Start date must be before end date';\n        return;\n      }\n\n      this.noDataMessage = 'Loading data...';\n      this.isLoading = true;\n      this.chartData = [];\n      this.alerts = [];\n\n      console.log(`Analyzing data from ${this.startDate} to ${this.endDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the YYYY-MM format directly for API requests\n      const requestData = {\n        startDate: this.startDate, // Use the YYYY-MM format directly\n        endDate: this.endDate, // Use the YYYY-MM format directly\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format\n      };\n\n      // Calculate base rate period (1 year back from start date by default)\n      const baseStartDateObj = new Date(this.startDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      // Create a new object with all properties including baseStartDate\n      const apiRequestData = {\n        ...requestData,\n        baseStartDate: baseStartDate\n      };\n\n      console.log(`Date range: ${apiRequestData.startDate} to ${apiRequestData.endDate}`);\n      console.log(`Base rate calculation period starts at: ${apiRequestData.baseStartDate}`);\n\n      // If a specific breakout group is selected, only query for that one\n      if (this.selectedBreakout) {\n        apiRequestData.breakoutName = this.selectedBreakout;\n        console.log(`Requesting XFactors only for breakout group: ${this.selectedBreakout}`);\n      }\n\n      // Log the API request\n      console.log(`Requesting XFactors from API for date range: ${this.startDate} to ${this.endDate}`);\n\n      axios.post('/api-statit2/get_metis_xfactors', apiRequestData, config)\n        .then(response => {\n          console.log('Received response:', response.data.status_res);\n\n          if (response.data.status_res === 'success') {\n            this.xFactorData = response.data.xfactors;\n\n            if (Object.keys(this.xFactorData).length === 0) {\n              this.noDataMessage = 'No data found for the selected date range. Please try a different range.';\n              console.warn('No data found in the response');\n            } else {\n              console.log(`Received data for ${Object.keys(this.xFactorData).length} breakout groups`);\n              this.updateChart();\n              this.generateAlerts();\n\n              // If a specific breakout group is selected, get its part numbers\n              if (this.selectedBreakout) {\n                this.getMainTabPartNumbers();\n              }\n\n              this.noDataMessage = '';\n            }\n          } else {\n            console.error('Error analyzing data:', response.data);\n\n            // Check for SQL error message\n            if (response.data.sql_error_msg) {\n              this.noDataMessage = `Database error: ${response.data.sql_error_msg}`;\n            } else {\n              this.noDataMessage = 'Error analyzing data. Please try again.';\n            }\n          }\n          this.isLoading = false;\n        })\n        .catch(error => {\n          console.error('API error:', error);\n\n          if (error.response) {\n            console.error('Error response data:', error.response.data);\n\n            if (error.response.status === 401) {\n              this.noDataMessage = 'Authentication error. Please log in again.';\n              // Optionally redirect to login page\n              // this.$router.push('/login');\n            } else {\n              this.noDataMessage = `Server error (${error.response.status}): ${error.response.data.message || 'Unknown error'}`;\n            }\n          } else if (error.request) {\n            console.error('No response received:', error.request);\n            this.noDataMessage = 'No response received from server. Please check your connection.';\n          } else {\n            console.error('Error message:', error.message);\n            this.noDataMessage = `Error: ${error.message}`;\n          }\n\n          this.isLoading = false;\n        });\n    },\n    handleBreakoutChange() {\n      console.log(`Breakout group changed to: ${this.selectedBreakout || 'All groups'}`);\n\n      // Update the chart with the selected breakout group\n      this.updateChart();\n\n      // If a specific breakout group is selected, get its part numbers\n      if (this.selectedBreakout) {\n        console.log(`Getting part numbers for selected breakout: ${this.selectedBreakout}`);\n        this.getMainTabPartNumbers();\n\n        // Also update alerts to only show for the selected breakout\n        this.generateAlerts();\n      } else {\n        // Clear part numbers if no breakout group is selected\n        console.log('No breakout group selected, clearing part numbers');\n        this.mainTabPartNumbers = [];\n\n        // Show alerts for all breakout groups\n        this.generateAlerts();\n      }\n    },\n\n    // Get part numbers for the selected breakout group in the main tab\n    getMainTabPartNumbers() {\n      if (!this.selectedBreakout) return;\n\n      console.log(`Getting part numbers for main tab breakout group: ${this.selectedBreakout}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get part numbers for the selected breakout group\n      axios.post('/api-statit2/get_metis_part_numbers', {\n        breakoutName: this.selectedBreakout\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success' && response.data.pns) {\n            this.mainTabPartNumbers = response.data.pns;\n            console.log(`Received ${this.mainTabPartNumbers.length} part numbers for ${this.selectedBreakout}`);\n          } else {\n            console.error('Error getting part numbers for main tab:', response.data);\n            this.mainTabPartNumbers = [];\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting part numbers for main tab:', error);\n          this.mainTabPartNumbers = [];\n        });\n    },\n\n    updateChart() {\n      if (!this.xFactorData || Object.keys(this.xFactorData).length === 0) {\n        this.noDataMessage = 'No data available. Please analyze data first.';\n        console.warn('No xFactorData available for chart');\n        return;\n      }\n\n      this.chartData = [];\n\n      // Get all breakout groups or filter by selected breakout\n      const breakoutsToShow = this.selectedBreakout\n        ? [this.selectedBreakout]\n        : Object.keys(this.xFactorData).slice(0, 10); // Limit to top 10 groups for better performance and readability\n\n      console.log(`Showing ${breakoutsToShow.length} breakout groups in chart`);\n\n      if (breakoutsToShow.length === 0) {\n        this.noDataMessage = 'No breakout groups available to display.';\n        console.warn('No breakout groups to show');\n        return;\n      }\n\n      // Track min and max dates for chart domain\n      let minDate = new Date('2025-12-31');\n      let maxDate = new Date('2024-01-01');\n      let hasValidData = false;\n\n      breakoutsToShow.forEach(breakoutName => {\n        const breakoutData = this.xFactorData[breakoutName];\n        if (!breakoutData || !breakoutData.xFactors) {\n          console.log(`No data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        const dataPoints = [];\n        const xFactorPeriods = Object.keys(breakoutData.xFactors);\n\n        if (xFactorPeriods.length === 0) {\n          console.log(`No periods found for breakout: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing ${xFactorPeriods.length} periods for ${breakoutName}`);\n\n        Object.entries(breakoutData.xFactors).forEach(([period, data]) => {\n          // Add data point for this period\n          // Make sure period is in YYYY-MM format and convert to a proper date\n          const dateParts = period.split('-');\n          if (dateParts.length >= 2) {\n            const year = parseInt(dateParts[0]);\n            const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed\n            const date = new Date(year, month, 1);\n\n            // Validate the date\n            if (isNaN(date.getTime())) {\n              console.error(`Invalid date created from period ${period}`);\n              return;\n            }\n\n            // Update min and max dates\n            if (date < minDate) minDate = new Date(date);\n            if (date > maxDate) maxDate = new Date(date);\n\n            // Ensure xFactor is a valid number\n            const xFactor = typeof data.xFactor === 'number' ? data.xFactor : parseFloat(data.xFactor);\n            if (isNaN(xFactor)) {\n              console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${data.xFactor}`);\n              return;\n            }\n\n            dataPoints.push({\n              date: date,\n              value: xFactor,\n              group: breakoutName,\n              defects: data.defects || 0,\n              volume: data.volume || 0,\n              targetRate: data.targetRate || (breakoutData.targetRate || 0)\n            });\n\n            hasValidData = true;\n          } else {\n            console.error(`Invalid period format: ${period}`);\n          }\n        });\n\n        // Sort by date\n        dataPoints.sort((a, b) => a.date - b.date);\n\n        console.log(`Added ${dataPoints.length} data points for ${breakoutName}`);\n\n        // Add to chart data\n        this.chartData = [...this.chartData, ...dataPoints];\n      });\n\n      console.log(`Chart data has ${this.chartData.length} data points for ${breakoutsToShow.length} breakout groups`);\n\n      if (hasValidData) {\n        console.log(`Date range in data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);\n\n        // Update chart domain based on actual data\n        // Add padding to the date range (1 month before and after)\n        minDate.setMonth(minDate.getMonth() - 1);\n        maxDate.setMonth(maxDate.getMonth() + 1);\n\n        // Update chart options with dynamic domain\n        this.chartOptions.axes.bottom.domain = [minDate, maxDate];\n\n        // Clear any error message\n        this.noDataMessage = '';\n      } else if (this.chartData.length === 0) {\n        this.noDataMessage = 'No data available for the selected criteria.';\n        console.warn('No valid data points found for chart');\n      }\n    },\n\n\n    generateAlerts() {\n      console.log('Generating alerts...');\n      this.alerts = [];\n\n      // Filter breakout groups if a specific one is selected\n      const breakoutsToProcess = this.selectedBreakout\n        ? { [this.selectedBreakout]: this.xFactorData[this.selectedBreakout] }\n        : this.xFactorData;\n\n      Object.entries(breakoutsToProcess).forEach(([breakoutName, data]) => {\n        if (!data || !data.xFactors) {\n          console.log(`No xFactors data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        // Sort periods chronologically\n        const sortedPeriods = Object.keys(data.xFactors).sort();\n\n        if (sortedPeriods.length === 0) {\n          console.log(`No periods found for breakout: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing ${sortedPeriods.length} periods for alerts in ${breakoutName}`);\n\n        // Check for short-term spikes (X-Factor > 3.0)\n        sortedPeriods.forEach(period => {\n          if (!data.xFactors[period]) {\n            console.error(`Missing data for period ${period} in breakout ${breakoutName}`);\n            return;\n          }\n\n          const xFactor = data.xFactors[period].xFactor;\n\n          // Ensure xFactor is a valid number\n          if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {\n            console.error(`Invalid xFactor for ${breakoutName} in period ${period}: ${xFactor}`);\n            return;\n          }\n\n          if (xFactor > 3.0) {\n            console.log(`Found short-term spike for ${breakoutName} in ${period}: ${xFactor.toFixed(2)}`);\n            this.alerts.push({\n              breakoutName,\n              status: 'Short-Term Spike',\n              period,\n              xFactor,\n              defects: data.xFactors[period].defects || 0,\n              volume: data.xFactors[period].volume || 0\n            });\n          }\n        });\n\n        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n        for (let i = 0; i < sortedPeriods.length - 2; i++) {\n          const period1 = sortedPeriods[i];\n          const period2 = sortedPeriods[i + 1];\n          const period3 = sortedPeriods[i + 2];\n\n          // Ensure all periods have valid data\n          if (!data.xFactors[period1] || !data.xFactors[period2] || !data.xFactors[period3]) {\n            console.error(`Missing data for one of the periods in sustained problem check: ${period1}, ${period2}, ${period3}`);\n            continue;\n          }\n\n          const xFactor1 = data.xFactors[period1].xFactor;\n          const xFactor2 = data.xFactors[period2].xFactor;\n          const xFactor3 = data.xFactors[period3].xFactor;\n\n          // Ensure all xFactors are valid numbers\n          if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||\n              typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||\n              typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {\n            console.error(`Invalid xFactor values for sustained problem check in ${breakoutName}`);\n            continue;\n          }\n\n          if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {\n            // Count how many consecutive months have X-Factor > 1.5\n            let consecutiveMonths = 3;\n            for (let j = i + 3; j < sortedPeriods.length; j++) {\n              if (data.xFactors[sortedPeriods[j]] && data.xFactors[sortedPeriods[j]].xFactor > 1.5) {\n                consecutiveMonths++;\n              } else {\n                break;\n              }\n            }\n\n            console.log(`Found sustained problem for ${breakoutName} from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);\n\n            // Calculate average xFactor for the sustained problem\n            const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;\n\n            this.alerts.push({\n              breakoutName,\n              status: `Sustained Problem (${consecutiveMonths} months)`,\n              period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,\n              xFactor: avgXFactor,\n              defects: data.xFactors[period3].defects || 0,\n              volume: data.xFactors[period3].volume || 0\n            });\n\n            // Skip the periods we've already included in this sustained problem\n            i += consecutiveMonths - 1;\n          }\n        }\n      });\n\n      // Sort alerts by X-Factor (highest first)\n      this.alerts.sort((a, b) => b.xFactor - a.xFactor);\n\n      console.log(`Generated ${this.alerts.length} alerts`);\n    },\n    // New method to handle quick select button clicks\n    selectTimeRange(range) {\n      this.selectedTimeRange = range;\n      this.applyTimeRange();\n    },\n\n    applyTimeRange() {\n      const today = new Date();\n      const currentMonth = today.getMonth() + 1; // 1-based month\n      const currentYear = today.getFullYear();\n\n      // Variables for calculations\n      let startMonth, startYear, endMonth, endYear;\n\n      switch (this.selectedTimeRange) {\n        case 'last-month': {\n          // Calculate last month directly without using Date object\n          startMonth = currentMonth - 1;\n          startYear = currentYear;\n\n          // Handle January case\n          if (startMonth === 0) {\n            startMonth = 12;\n            startYear = currentYear - 1;\n          }\n\n          // For last month, both start and end should be the previous month\n          endMonth = startMonth;\n          endYear = startYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-3-months': {\n          // Calculate 2 months ago for a total of 3 months including current month\n          startMonth = currentMonth - 2;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-6-months': {\n          // Calculate 5 months ago for a total of 6 months including current month\n          startMonth = currentMonth - 5;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.startMonth = startMonth;\n          this.startMonthStr = String(startMonth);\n          this.startYear = startYear;\n          this.endMonth = endMonth;\n          this.endMonthStr = String(endMonth);\n          this.endYear = endYear;\n\n          // Update date strings directly\n          this.startDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'ytd': {\n          // Update dropdown values\n          this.startMonth = 1; // January\n          this.startMonthStr = '1';\n          this.startYear = currentYear;\n          this.endMonth = currentMonth;\n          this.endMonthStr = String(currentMonth);\n          this.endYear = currentYear;\n\n          // Update date strings directly\n          this.startDate = `${currentYear}-01`;\n          this.endDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;\n          break;\n        }\n      }\n\n      // Synchronize with breakout tab\n      this.breakoutTabStartDate = this.startDate;\n      this.breakoutTabEndDate = this.endDate;\n      this.breakoutTabStartMonth = this.startMonth;\n      this.breakoutTabStartYear = this.startYear;\n      this.breakoutTabEndMonth = this.endMonth;\n      this.breakoutTabEndYear = this.endYear;\n\n      // Also update the breakout tab time range to match\n      this.breakoutTabTimeRange = this.selectedTimeRange;\n\n      // Analyze data for all tabs when time range changes\n      this.analyzeAllData();\n    },\n    getStatusClass(status) {\n      if (status.includes('Short-Term Spike')) {\n        return 'status-spike';\n      } else if (status.includes('Sustained Problem')) {\n        return 'status-sustained';\n      }\n      return 'status-normal';\n    },\n\n    // Breakout tab methods\n    applyBreakoutTabTimeRange() {\n      const today = new Date();\n      const currentMonth = today.getMonth() + 1; // 1-based month\n      const currentYear = today.getFullYear();\n\n      // Variables for calculations\n      let startMonth, startYear, endMonth, endYear;\n\n      switch (this.breakoutTabTimeRange) {\n        case 'last-month': {\n          // Calculate last month directly without using Date object\n          startMonth = currentMonth - 1;\n          startYear = currentYear;\n\n          // Handle January case\n          if (startMonth === 0) {\n            startMonth = 12;\n            startYear = currentYear - 1;\n          }\n\n          // For last month, both start and end should be the previous month\n          endMonth = startMonth;\n          endYear = startYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-3-months': {\n          // Calculate 2 months ago for a total of 3 months including current month\n          startMonth = currentMonth - 2;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'last-6-months': {\n          // Calculate 5 months ago for a total of 6 months including current month\n          startMonth = currentMonth - 5;\n          startYear = currentYear;\n\n          // Handle month wrapping\n          if (startMonth <= 0) {\n            startMonth = startMonth + 12;\n            startYear = currentYear - 1;\n          }\n\n          endMonth = currentMonth;\n          endYear = currentYear;\n\n          // Update dropdown values\n          this.breakoutTabStartMonth = startMonth;\n          this.breakoutTabStartYear = startYear;\n          this.breakoutTabEndMonth = endMonth;\n          this.breakoutTabEndYear = endYear;\n\n          // Update date strings directly\n          this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n          this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          break;\n        }\n\n        case 'custom': {\n          // For custom range, don't change the dates automatically\n          // The user will input them manually\n          console.log('Custom date range selected. Using user-provided dates.');\n\n          // If no dates are set, initialize with reasonable defaults\n          if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n            // Calculate 2 months ago for a total of 3 months including current month\n            startMonth = currentMonth - 2;\n            startYear = currentYear;\n\n            // Handle month wrapping\n            if (startMonth <= 0) {\n              startMonth = startMonth + 12;\n              startYear = currentYear - 1;\n            }\n\n            endMonth = currentMonth;\n            endYear = currentYear;\n\n            // Update dropdown values\n            this.breakoutTabStartMonth = startMonth;\n            this.breakoutTabStartYear = startYear;\n            this.breakoutTabEndMonth = endMonth;\n            this.breakoutTabEndYear = endYear;\n\n            // Update date strings directly\n            this.breakoutTabStartDate = `${startYear}-${String(startMonth).padStart(2, '0')}`;\n            this.breakoutTabEndDate = `${endYear}-${String(endMonth).padStart(2, '0')}`;\n          }\n\n          return; // Don't automatically analyze for custom range\n        }\n      }\n\n      // Automatically analyze the data when time range changes (except for custom)\n      this.analyzeBreakoutData();\n    },\n\n    analyzeBreakoutData() {\n      // Validate date range\n      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n        this.breakoutTabNoDataMessage = 'Please select a valid date range';\n        return;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.breakoutTabStartDate + '-01');\n      const end = new Date(this.breakoutTabEndDate + '-01');\n\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.breakoutTabNoDataMessage = 'Invalid date format';\n        return;\n      }\n\n      if (start > end) {\n        this.breakoutTabNoDataMessage = 'Start date must be before end date';\n        return;\n      }\n\n      this.breakoutTabNoDataMessage = 'Loading data...';\n      this.isBreakoutTabLoading = true;\n      this.breakoutTabChartData = [];\n      this.breakoutTabBarChartData = [];\n      this.breakoutTabAlerts = [];\n      this.breakoutTabPartNumbers = [];\n      this.breakoutTabAllXFactorData = {};\n\n      console.log(`Analyzing breakout data from ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the YYYY-MM format directly for API requests\n      const startDate = this.breakoutTabStartDate;\n      const endDate = this.breakoutTabEndDate;\n\n      // Calculate base rate period (1 year back from start date by default)\n      const baseStartDateObj = new Date(this.breakoutTabStartDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      console.log(`Breakout tab date range: ${startDate} to ${endDate}`);\n      console.log(`Base rate calculation period starts at: ${baseStartDate}`);\n\n      // First, get data for all breakout groups to populate the bar chart\n      axios.post('/api-statit2/get_metis_xfactors', {\n        startDate: startDate, // Use the YYYY-MM format directly\n        endDate: endDate, // Use the YYYY-MM format directly\n        baseStartDate: baseStartDate,\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true // Add flag to indicate we're using YYYY-MM format\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            this.breakoutTabAllXFactorData = response.data.xfactors;\n\n            if (Object.keys(this.breakoutTabAllXFactorData).length === 0) {\n              this.breakoutTabNoDataMessage = 'No data found for the selected date range. Please try a different range.';\n              this.isBreakoutTabLoading = false;\n              return;\n            }\n\n            console.log(`Received data for ${Object.keys(this.breakoutTabAllXFactorData).length} breakout groups`);\n\n            // Process bar chart data for all breakout groups\n            this.updateBreakoutBarChart();\n\n            // If a specific breakout group is selected, get its details using the consolidated endpoint\n            if (this.breakoutTabSelectedGroup) {\n              this.getBreakoutAnalysis();\n            } else {\n              this.breakoutTabNoDataMessage = 'Please select a breakout group to see detailed analysis';\n              this.isBreakoutTabLoading = false;\n            }\n          } else {\n            console.error('Error analyzing breakout data:', response.data);\n            this.breakoutTabNoDataMessage = 'Error analyzing data. Please try again.';\n            this.isBreakoutTabLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error:', error);\n          this.breakoutTabNoDataMessage = `Error: ${error.message}`;\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Get detailed analysis for a specific breakout group using the consolidated endpoint\n    getBreakoutAnalysis() {\n      if (!this.breakoutTabSelectedGroup) {\n        console.error('No breakout group selected for detailed analysis');\n        return;\n      }\n\n      console.log(`Getting detailed analysis for breakout group: ${this.breakoutTabSelectedGroup}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the consolidated endpoint to get all data for this breakout group\n      axios.post('/api-statit2/get_metis_breakout_analysis', {\n        breakoutName: this.breakoutTabSelectedGroup,\n        startDate: this.breakoutTabStartDate,\n        endDate: this.breakoutTabEndDate,\n        baseStartDate: this.baseStartDate,\n        exactDateRange: true,\n        useMonthFormat: true\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            console.log('Received detailed analysis for breakout group:', response.data);\n\n            // Update part numbers\n            if (response.data.partNumbers) {\n              this.breakoutTabPartNumbers = response.data.partNumbers;\n              console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);\n            }\n\n            // Update XFactor data for line chart\n            if (response.data.xFactorData && response.data.xFactorData.xFactors) {\n              // Create a structure compatible with the existing updateBreakoutLineChart method\n              const breakoutData = {\n                xFactors: response.data.xFactorData.xFactors\n              };\n\n              // Process the data for the line chart\n              this.processBreakoutLineChartData(breakoutData);\n\n              // Generate alerts for this breakout group\n              this.generateBreakoutAlerts(breakoutData);\n            } else {\n              console.error('No XFactor data found in the response');\n              this.breakoutTabNoDataMessage = 'No XFactor data found for the selected breakout group';\n            }\n\n            this.isBreakoutTabLoading = false;\n          } else {\n            console.error('Error getting detailed analysis:', response.data);\n            this.breakoutTabNoDataMessage = 'Error getting detailed analysis. Please try again.';\n            this.isBreakoutTabLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting detailed analysis:', error);\n          this.breakoutTabNoDataMessage = `Error: ${error.message}`;\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Get part numbers for the selected breakout group\n    getBreakoutPartNumbers() {\n      if (!this.breakoutTabSelectedGroup) return;\n\n      console.log(`Getting part numbers for breakout group: ${this.breakoutTabSelectedGroup}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get part numbers for the selected breakout group\n      axios.post('/api-statit2/get_metis_part_numbers', {\n        breakoutName: this.breakoutTabSelectedGroup\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success' && response.data.pns) {\n            this.breakoutTabPartNumbers = response.data.pns;\n            console.log(`Received ${this.breakoutTabPartNumbers.length} part numbers for ${this.breakoutTabSelectedGroup}`);\n          } else {\n            console.error('Error getting part numbers:', response.data);\n            this.breakoutTabPartNumbers = [];\n          }\n          this.isBreakoutTabLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when getting part numbers:', error);\n          this.breakoutTabPartNumbers = [];\n          this.isBreakoutTabLoading = false;\n        });\n    },\n\n    // Process data for the line chart from the consolidated endpoint\n    processBreakoutLineChartData(breakoutData) {\n      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {\n        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;\n        return;\n      }\n\n      // Process chart data\n      this.breakoutTabChartData = [];\n      const dataPoints = [];\n\n      // Track min and max dates for chart domain\n      let minDate = new Date('2025-12-31');\n      let maxDate = new Date('2024-01-01');\n\n      Object.entries(breakoutData.xFactors).forEach(([period, data]) => {\n        // Make sure period is in YYYY-MM format and convert to a proper date\n        const dateParts = period.split('-');\n        if (dateParts.length >= 2) {\n          const year = parseInt(dateParts[0]);\n          const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed\n          const date = new Date(year, month, 1);\n\n          // Update min and max dates\n          if (date < minDate) minDate = new Date(date);\n          if (date > maxDate) maxDate = new Date(date);\n\n          dataPoints.push({\n            date: date,\n            value: data.xFactor,\n            group: this.breakoutTabSelectedGroup,\n            defects: data.defects,\n            volume: data.volume\n          });\n        }\n      });\n\n      // Sort by date\n      dataPoints.sort((a, b) => a.date - b.date);\n      this.breakoutTabChartData = dataPoints;\n\n      // Update chart domain based on actual data\n      if (dataPoints.length > 0) {\n        // Add padding to the date range (1 month before and after)\n        minDate.setMonth(minDate.getMonth() - 1);\n        maxDate.setMonth(maxDate.getMonth() + 1);\n\n        // Update chart options with dynamic domain\n        this.chartOptions.axes.bottom.domain = [minDate, maxDate];\n\n        console.log(`Date range in breakout data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);\n      }\n\n      console.log(`Successfully processed line chart data for ${this.breakoutTabSelectedGroup} with ${dataPoints.length} data points`);\n    },\n\n    // Update the line chart for the selected breakout group (legacy method, kept for compatibility)\n    updateBreakoutLineChart() {\n      if (!this.breakoutTabSelectedGroup || !this.breakoutTabAllXFactorData) {\n        return;\n      }\n\n      const breakoutData = this.breakoutTabAllXFactorData[this.breakoutTabSelectedGroup];\n\n      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {\n        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;\n        return;\n      }\n\n      // Process the data for the line chart\n      this.processBreakoutLineChartData(breakoutData);\n\n      // Generate alerts for this breakout group\n      this.generateBreakoutAlerts(breakoutData);\n    },\n\n    // Update the bar chart with data for all breakout groups\n    updateBreakoutBarChart() {\n      if (!this.breakoutTabAllXFactorData || Object.keys(this.breakoutTabAllXFactorData).length === 0) {\n        return;\n      }\n\n      this.breakoutTabBarChartData = [];\n\n      // Calculate average X-Factor for each breakout group\n      Object.entries(this.breakoutTabAllXFactorData).forEach(([breakoutName, data]) => {\n        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {\n          console.log(`No XFactors data for breakout: ${breakoutName}`);\n          return;\n        }\n\n        let totalXFactor = 0;\n        let totalDefects = 0;\n        let totalVolume = 0;\n        let count = 0;\n        let isCritical = false;\n\n        // Calculate average X-Factor and check if any period is critical\n        Object.values(data.xFactors).forEach(periodData => {\n          totalXFactor += periodData.xFactor;\n          totalDefects += periodData.defects;\n          totalVolume += periodData.volume;\n          count++;\n\n          // Check if this period has a critical X-Factor (> 3.0)\n          if (periodData.xFactor > 3.0) {\n            isCritical = true;\n          }\n        });\n\n        if (count > 0) {\n          const avgXFactor = totalXFactor / count;\n\n          // Create a simpler group name if it's too long\n          const displayName = breakoutName.length > 30\n            ? breakoutName.substring(0, 27) + '...'\n            : breakoutName;\n\n          this.breakoutTabBarChartData.push({\n            group: displayName,\n            value: avgXFactor,\n            defects: totalDefects,\n            volume: totalVolume,\n            // Use the 'Critical' group for breakouts with any critical period\n            groupMapsTo: isCritical ? 'Critical' : 'XFactor'\n          });\n\n          console.log(`Added bar chart data for ${displayName}: XFactor=${avgXFactor.toFixed(2)}, Critical=${isCritical}`);\n        }\n      });\n\n      // Sort by X-Factor value (highest first)\n      this.breakoutTabBarChartData.sort((a, b) => b.value - a.value);\n\n      // Limit to top 15 breakout groups for better visualization\n      this.breakoutTabBarChartData = this.breakoutTabBarChartData.slice(0, 15);\n\n      console.log(`Final bar chart data has ${this.breakoutTabBarChartData.length} items`);\n    },\n\n    // Handle click events on the chart\n    handleBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Bar clicked for breakout group: ${clickedData.group}`);\n\n        // Update the selected breakout group\n        this.breakoutTabSelectedGroup = clickedData.group;\n\n        // Get detailed analysis for this breakout group\n        this.getBreakoutAnalysis();\n      }\n    },\n\n    // Update breakout tab start date from dropdowns\n    updateBreakoutStartDate() {\n      // Format the date as YYYY-MM\n      const month = String(this.breakoutTabStartMonth).padStart(2, '0');\n      this.breakoutTabStartDate = `${this.breakoutTabStartYear}-${month}`;\n\n      console.log(`Breakout tab start date updated to: ${this.breakoutTabStartDate}`);\n\n      // Validate the date range\n      this.validateBreakoutTabDateRange();\n    },\n\n    // Update breakout tab end date from dropdowns\n    updateBreakoutEndDate() {\n      // Format the date as YYYY-MM\n      const month = String(this.breakoutTabEndMonth).padStart(2, '0');\n      this.breakoutTabEndDate = `${this.breakoutTabEndYear}-${month}`;\n\n      console.log(`Breakout tab end date updated to: ${this.breakoutTabEndDate}`);\n\n      // Validate the date range\n      this.validateBreakoutTabDateRange();\n    },\n\n    // Validate breakout tab date range and trigger analysis if valid\n    validateBreakoutTabDateRange() {\n      if (!this.breakoutTabStartDate || !this.breakoutTabEndDate) {\n        this.breakoutTabNoDataMessage = 'Please select both start and end dates';\n        console.error('Missing breakout tab start or end date');\n        return false;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.breakoutTabStartDate + '-01');\n      const end = new Date(this.breakoutTabEndDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.breakoutTabNoDataMessage = 'Invalid date format';\n        console.error('Invalid breakout tab date format', {\n          start: this.breakoutTabStartDate,\n          end: this.breakoutTabEndDate\n        });\n        return false;\n      }\n\n      if (start > end) {\n        console.error('Invalid date range: Start date is after end date', {\n          start: this.breakoutTabStartDate,\n          end: this.breakoutTabEndDate\n        });\n        this.breakoutTabNoDataMessage = 'Start date must be before end date';\n        return false;\n      }\n\n      // Set the time range to custom\n      this.breakoutTabTimeRange = 'custom';\n\n      // Clear any error message\n      this.breakoutTabNoDataMessage = '';\n\n      // Analyze the data with the new date range\n      console.log(`Breakout tab date range validated: ${this.breakoutTabStartDate} to ${this.breakoutTabEndDate}`);\n      this.analyzeBreakoutData();\n      return true;\n    },\n\n    // Dashboard methods\n\n    loadDashboardData() {\n      console.log('Loading dashboard data...');\n      this.dashboardNoDataMessage = 'Loading dashboard data...';\n      this.isDashboardLoading = true;\n      this.dashboardData = [];\n\n      // Use the global date range\n      const startDate = this.startDate;\n      const endDate = this.endDate;\n\n      if (!startDate || !endDate) {\n        this.dashboardNoDataMessage = 'Please select a valid date range';\n        this.isDashboardLoading = false;\n        console.error('Invalid date range for dashboard', { startDate, endDate });\n        return;\n      }\n\n      console.log(`Dashboard date range: ${startDate} to ${endDate}`);\n\n      // Calculate a new start date that is 2 months before the selected start date\n      // This allows us to detect critical issues that started before the visible range\n      const extendedStartDate = this.getDateMonthsAgo(startDate, 2);\n      console.log(`Extended start date (2 months before): ${extendedStartDate}`);\n\n      // Convert to full date format for API\n      const apiStartDate = this.getFirstDayOfMonth(extendedStartDate);\n      const apiEndDate = this.getLastDayOfMonth(endDate);\n\n      console.log(`Dashboard API date range: ${apiStartDate} to ${apiEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Calculate base rate period (1 year back from start date)\n      const baseStartDateObj = new Date(startDate + '-01');\n      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1);\n      const baseStartDate = baseStartDateObj.toISOString().split('T')[0];\n\n      console.log(`Dashboard base rate period starts at: ${baseStartDate}`);\n\n      // Get data for all breakout groups\n      // Use the YYYY-MM format directly for the API to ensure exact month matching\n      const requestData = {\n        startDate: extendedStartDate, // Use the extended start date (2 months before) for the API\n        endDate: endDate, // Use the YYYY-MM format directly\n        baseStartDate: baseStartDate,\n        exactDateRange: true, // Add flag to indicate exact date range should be used\n        useMonthFormat: true, // Add flag to indicate we're using YYYY-MM format\n        displayStartDate: startDate // Add the original start date for display purposes\n      };\n\n      // Add filter based on selected type and value\n      if (this.selectedOwner && this.selectedOwner !== 'All') {\n        if (this.selectedFilterType === 'group') {\n          requestData.owningGroup = this.selectedOwner;\n          console.log(`Filtering dashboard by group: ${this.selectedOwner}`);\n        } else if (this.selectedFilterType === 'dev_owner') {\n          requestData.filterType = 'dev_owner';\n          requestData.owner = this.selectedOwner;\n          console.log(`Filtering dashboard by dev owner: ${this.selectedOwner}`);\n        } else if (this.selectedFilterType === 'pqe_owner') {\n          requestData.filterType = 'pqe_owner';\n          requestData.owner = this.selectedOwner;\n          console.log(`Filtering dashboard by PQE owner: ${this.selectedOwner}`);\n        }\n      }\n\n      console.log('Dashboard API request data:', requestData);\n\n      axios.post('/api-statit2/get_metis_xfactors', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const xfactorData = response.data.xfactors;\n            console.log(`Dashboard received data for ${Object.keys(xfactorData).length} breakout groups`);\n\n            if (Object.keys(xfactorData).length === 0) {\n              this.dashboardNoDataMessage = 'No data found for the selected time range';\n              this.isDashboardLoading = false;\n              console.warn('No data found for dashboard');\n              return;\n            }\n\n            // Process data for dashboard\n            this.processDashboardData(xfactorData, startDate, endDate);\n            this.dashboardNoDataMessage = '';\n          } else {\n            console.error('Error loading dashboard data:', response.data);\n            this.dashboardNoDataMessage = 'Error loading dashboard data';\n            if (response.data.sql_error_msg) {\n              console.error('SQL error:', response.data.sql_error_msg);\n              this.dashboardNoDataMessage += `: ${response.data.sql_error_msg}`;\n            }\n          }\n          this.isDashboardLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading dashboard data:', error);\n          this.dashboardNoDataMessage = `Error: ${error.message}`;\n          this.isDashboardLoading = false;\n        });\n    },\n\n    processDashboardData(xfactorData, startDate, endDate) {\n      console.log(`\\n=== PROCESSING DASHBOARD DATA ===`);\n      console.log(`Input Start Date: ${startDate}`);\n      console.log(`Input End Date: ${endDate}`);\n\n      // Extract all unique months from the API response\n      const allApiMonths = new Set();\n\n      // Collect all months from the API data\n      Object.values(xfactorData).forEach(breakoutData => {\n        if (breakoutData.xFactors) {\n          Object.keys(breakoutData.xFactors).forEach(month => {\n            allApiMonths.add(month);\n          });\n        }\n      });\n\n      // Convert to array and sort\n      const apiMonths = Array.from(allApiMonths).sort();\n      console.log('All months from API:', apiMonths);\n\n      // Use the display start date if provided in the API response\n      const displayStartDate = xfactorData.displayStartDate || startDate;\n      console.log(`Using display start date: ${displayStartDate}`);\n\n      // Extract year and month directly from the input strings for comparison\n      let startYear, startMonth, endYear, endMonth;\n\n      // Parse start date (use display start date for filtering)\n      if (displayStartDate.length === 7) {\n        // Format is YYYY-MM\n        [startYear, startMonth] = displayStartDate.split('-');\n      } else if (displayStartDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [startYear, startMonth] = displayStartDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid start date format: ${displayStartDate}`);\n        return;\n      }\n\n      // Parse end date\n      if (endDate.length === 7) {\n        // Format is YYYY-MM\n        [endYear, endMonth] = endDate.split('-');\n      } else if (endDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [endYear, endMonth] = endDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid end date format: ${endDate}`);\n        return;\n      }\n\n      // Convert to numbers for comparison\n      startYear = parseInt(startYear, 10);\n      startMonth = parseInt(startMonth, 10);\n      endYear = parseInt(endYear, 10);\n      endMonth = parseInt(endMonth, 10);\n\n      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);\n      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);\n\n      // Filter API months to only include those within the selected date range\n      // Strictly enforce the date range boundaries\n      const filteredMonths = apiMonths.filter(month => {\n        // Extract year and month for comparison\n        const [monthYear, monthMonth] = month.split('-').map(num => parseInt(num, 10));\n\n        // Check if the month is within the exact range (inclusive)\n        const isAfterOrEqualStart = (monthYear > startYear) ||\n                                   (monthYear === startYear && monthMonth >= startMonth);\n\n        const isBeforeOrEqualEnd = (monthYear < endYear) ||\n                                  (monthYear === endYear && monthMonth <= endMonth);\n\n        return isAfterOrEqualStart && isBeforeOrEqualEnd;\n      });\n\n      console.log('Filtered months based on selected range:', filteredMonths);\n\n      // Use the filtered months from the API data\n      const months = filteredMonths;\n      console.log('Using filtered months from API data:', months);\n      console.log(`===================================\\n`);\n\n      // Format months for display in the table header\n      this.dashboardMonths = months.map(date => {\n        // Parse the YYYY-MM format directly\n        const [year, month] = date.split('-');\n\n        // Create a date object with UTC to avoid timezone issues\n        const d = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, 1));\n\n        // Log the date conversion for debugging\n        console.log(`Converting date ${date} to formatted month: ${d.toUTCString()}`);\n\n        // Format using the numeric month and year\n        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];\n        return `${monthNames[parseInt(month) - 1]} ${year}`;\n      });\n\n      console.log('Formatted dashboard months:', this.dashboardMonths);\n\n      // Process data for each breakout group\n      const dashboardData = [];\n\n      // Log the number of breakout groups\n      console.log(`Processing ${Object.keys(xfactorData).length} breakout groups`);\n\n      Object.entries(xfactorData).forEach(([breakoutName, data]) => {\n        if (!data.xFactors || Object.keys(data.xFactors).length === 0) {\n          console.log(`No data for breakout group: ${breakoutName}`);\n          return;\n        }\n\n        console.log(`Processing breakout group: ${breakoutName} with ${Object.keys(data.xFactors).length} data points`);\n\n        const row = {\n          breakoutName,\n          months: []\n        };\n\n        // Initialize cells for each month\n        months.forEach(month => {\n          row.months.push({\n            month,\n            breakoutName,\n            xFactor: null,\n            status: null,\n            duration: null,\n            defects: null,\n            volume: null\n          });\n        });\n\n        // Log available periods in the data\n        console.log(`Available periods for ${breakoutName}:`, Object.keys(data.xFactors));\n\n        // Fill in data for months that have data\n        Object.entries(data.xFactors).forEach(([period, monthData]) => {\n          // Find the index of this period in our months array\n          const monthIndex = months.findIndex(m => m === period);\n\n          if (monthIndex >= 0) {\n            console.log(`Found data for ${period} at index ${monthIndex}`);\n            row.months[monthIndex].xFactor = monthData.xFactor;\n            row.months[monthIndex].defects = monthData.defects;\n            row.months[monthIndex].volume = monthData.volume;\n\n            // Determine status\n            if (monthData.xFactor > 3.0) {\n              row.months[monthIndex].status = 'Short-Term Spike';\n            }\n          } else {\n            console.warn(`Period ${period} not found in months array for ${breakoutName}`);\n          }\n        });\n\n        // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n        for (let i = 0; i < row.months.length - 2; i++) {\n          const cell1 = row.months[i];\n          const cell2 = row.months[i + 1];\n          const cell3 = row.months[i + 2];\n\n          if (cell1.xFactor > 1.5 && cell2.xFactor > 1.5 && cell3.xFactor > 1.5) {\n            // Count consecutive months\n            let consecutiveMonths = 3;\n            for (let j = i + 3; j < row.months.length; j++) {\n              if (row.months[j].xFactor > 1.5) {\n                consecutiveMonths++;\n              } else {\n                break;\n              }\n            }\n\n            // Mark all cells in the sustained problem\n            for (let j = i; j < i + consecutiveMonths; j++) {\n              // If already marked as spike, mark as critical (both)\n              if (row.months[j].status === 'Short-Term Spike') {\n                row.months[j].status = 'Critical';\n              } else {\n                row.months[j].status = 'Sustained Problem';\n              }\n              row.months[j].duration = consecutiveMonths;\n\n              // Mark the last month of the sustained problem to make it blink\n              if (j === i + consecutiveMonths - 1) {\n                row.months[j].isLastMonthOfSustained = true;\n                console.log(`Marking last month of sustained problem for ${row.breakoutName} at index ${j}`);\n              }\n            }\n\n            // Skip the cells we've already processed\n            i += consecutiveMonths - 1;\n          }\n        }\n\n        dashboardData.push(row);\n      });\n\n      // Sort by breakout name\n      dashboardData.sort((a, b) => a.breakoutName.localeCompare(b.breakoutName));\n\n      this.dashboardData = dashboardData;\n      console.log(`Processed dashboard data for ${dashboardData.length} breakout groups`);\n\n      // Count critical issues for the current month\n      this.countCurrentMonthCriticalIssues();\n    },\n\n    // Count critical issues for the current month\n    countCurrentMonthCriticalIssues() {\n      if (!this.dashboardData || this.dashboardData.length === 0 || !this.dashboardMonths || this.dashboardMonths.length === 0) {\n        this.currentMonthCriticalIssues = 0;\n        return;\n      }\n\n      // Get the most recent month (last in the array)\n      const currentMonthIndex = this.dashboardMonths.length - 1;\n\n      // Count critical issues across all breakout groups for the current month\n      let criticalIssueCount = 0;\n\n      this.dashboardData.forEach(row => {\n        if (row.months && row.months.length > currentMonthIndex) {\n          const cell = row.months[currentMonthIndex];\n          if (cell && (cell.status === 'Critical' || cell.status === 'Short-Term Spike')) {\n            criticalIssueCount++;\n          }\n        }\n      });\n\n      this.currentMonthCriticalIssues = criticalIssueCount;\n      console.log(`Found ${criticalIssueCount} critical issues for the current month (${this.getCurrentMonthName()})`);\n    },\n\n    getMonthsBetweenDates(startDate, endDate) {\n      console.log(`\\n=== GETTING MONTHS BETWEEN DATES ===`);\n      console.log(`Input Start Date: ${startDate}`);\n      console.log(`Input End Date: ${endDate}`);\n\n      // Generate months between start and end dates\n      const result = [];\n\n      // Extract year and month directly from the input strings\n      let startYear, startMonth, endYear, endMonth;\n\n      // Parse start date\n      if (startDate.length === 7) {\n        // Format is YYYY-MM\n        [startYear, startMonth] = startDate.split('-');\n      } else if (startDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [startYear, startMonth] = startDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid start date format: ${startDate}`);\n        return result;\n      }\n\n      // Parse end date\n      if (endDate.length === 7) {\n        // Format is YYYY-MM\n        [endYear, endMonth] = endDate.split('-');\n      } else if (endDate.length >= 10) {\n        // Format is YYYY-MM-DD or longer\n        [endYear, endMonth] = endDate.substring(0, 7).split('-');\n      } else {\n        console.error(`Invalid end date format: ${endDate}`);\n        return result;\n      }\n\n      // Convert to numbers\n      startYear = parseInt(startYear, 10);\n      startMonth = parseInt(startMonth, 10);\n      endYear = parseInt(endYear, 10);\n      endMonth = parseInt(endMonth, 10);\n\n      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);\n      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);\n\n      // Validate the date range\n      if (startYear > endYear || (startYear === endYear && startMonth > endMonth)) {\n        console.error(`Invalid date range: start date is after end date`);\n        return result;\n      }\n\n      // Generate all months in the range\n      let currentYear = startYear;\n      let currentMonth = startMonth;\n\n      console.log(`Generating monthly entries from ${startYear}-${startMonth} to ${endYear}-${endMonth}`);\n\n      while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {\n        // Format as YYYY-MM\n        const yearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;\n        result.push(yearMonth);\n        console.log(`Added month: ${yearMonth}`);\n\n        // Move to the next month\n        currentMonth++;\n        if (currentMonth > 12) {\n          currentMonth = 1;\n          currentYear++;\n        }\n      }\n\n      console.log(`Generated ${result.length} months between dates`);\n      console.log(`===================================\\n`);\n      return result;\n    },\n\n    getCellClass(cell) {\n      // Only return cell-empty if the cell is completely missing or has no month\n      if (!cell || !cell.month) {\n        return 'cell-empty';\n      }\n\n      if (cell.status === 'Critical') {\n        // Only add the blink class if this is the last month of a sustained problem\n        if (cell.isLastMonthOfSustained) {\n          return 'cell-critical blink';\n        } else {\n          return 'cell-critical';\n        }\n      } else if (cell.status === 'Short-Term Spike') {\n        return 'cell-spike'; // Removed 'blink' class to prevent flashing\n      } else if (cell.status === 'Sustained Problem') {\n        // Only add the blink class if this is the last month of a sustained problem\n        if (cell.isLastMonthOfSustained) {\n          return 'cell-sustained blink';\n        } else {\n          return 'cell-sustained';\n        }\n      } else {\n        // Always return cell-normal for cells with valid month, even if xFactor is 0\n        return 'cell-normal';\n      }\n    },\n\n    showCellTooltip(cell, event) {\n      // Allow tooltips even for cells with 0 defects (xFactor might be 0)\n      if (!cell || !cell.month) return;\n\n      // Parse the YYYY-MM format directly\n      const [year, month] = cell.month.split('-');\n\n      // Get month name using a simple lookup\n      const monthNames = [\n        'January', 'February', 'March', 'April', 'May', 'June',\n        'July', 'August', 'September', 'October', 'November', 'December'\n      ];\n\n      // Get target rate if available\n      const targetRate = this.xFactorData &&\n                        this.xFactorData[cell.breakoutName] &&\n                        this.xFactorData[cell.breakoutName].targetRate ?\n                        this.xFactorData[cell.breakoutName].targetRate : 'N/A';\n\n      // Count critical issues for this cell\n      const criticalIssues = this.countCriticalIssuesForCell(cell.breakoutName, cell.month);\n\n      this.tooltipData = {\n        breakoutName: cell.breakoutName,\n        month: `${monthNames[parseInt(month) - 1]} ${year}`,\n        xFactor: cell.xFactor !== undefined ? cell.xFactor : 0,\n        targetRate: targetRate,\n        status: cell.status || 'Normal',\n        duration: cell.duration,\n        defects: cell.defects !== undefined ? cell.defects : 0,\n        volume: cell.volume !== undefined ? cell.volume : 0,\n        criticalIssues: criticalIssues\n      };\n\n      // Position tooltip near the mouse\n      const offset = 10;\n      this.tooltipStyle = {\n        top: `${event.clientY + offset}px`,\n        left: `${event.clientX + offset}px`\n      };\n\n      this.showTooltip = true;\n    },\n\n    // Count critical issues for a specific breakout group and month\n    countCriticalIssuesForCell(breakoutName, month) {\n      // This is a placeholder implementation - in a real implementation,\n      // you would query the actual critical issues from root cause, vintage, and supplier analyses\n\n      // For now, we'll use a simple heuristic:\n      // If the cell has a status of 'Critical' or 'Short-Term Spike', count it as 1 critical issue\n      // In a real implementation, you would need to query the actual critical issues\n\n      // Find the cell in the dashboard data\n      const row = this.dashboardData.find(row => row.breakoutName === breakoutName);\n      if (!row) return 0;\n\n      const cell = row.months.find(cell => cell.month === month);\n      if (!cell) return 0;\n\n      // Count as a critical issue if the status is Critical or Short-Term Spike\n      if (cell.status === 'Critical' || cell.status === 'Short-Term Spike') {\n        return 1;\n      }\n\n      return 0;\n    },\n\n    // Get the current month name (most recent month in the dashboard)\n    getCurrentMonthName() {\n      if (!this.dashboardMonths || this.dashboardMonths.length === 0) {\n        return '';\n      }\n\n      // Get the most recent month (last in the array)\n      return this.dashboardMonths[this.dashboardMonths.length - 1];\n    },\n\n    hideCellTooltip() {\n      this.showTooltip = false;\n    },\n\n    selectBreakoutFromDashboard(breakoutName) {\n      console.log(`Navigating to Group tab for ${breakoutName}`);\n\n      // Set the selected breakout group first\n      this.group2TabSelectedGroup = breakoutName;\n\n      // Use a more direct approach to select the Group tab\n      this.activeTab = 3; // Set the active tab index to 3 (Group tab)\n\n      // Find all tabs and click the Group tab directly\n      this.$nextTick(() => {\n        // Try different selector approaches\n        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');\n        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);\n\n        if (tabElements && tabElements.length > 3) {\n          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');\n          tabElements[3].click();\n\n          // Trigger root cause analysis for this breakout group\n          this.$nextTick(() => {\n            console.log(`Viewing root cause analysis for ${breakoutName}`);\n            this.viewRootCauseAnalysis(3); // Show 3 months of data\n          });\n        } else {\n          // Try an alternative selector\n          const altTabElements = document.querySelectorAll('.cv-tab');\n          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);\n\n          if (altTabElements && altTabElements.length > 3) {\n            console.log('Clicking Group tab using .cv-tab selector');\n            altTabElements[3].click();\n\n            // Trigger root cause analysis for this breakout group\n            this.$nextTick(() => {\n              console.log(`Viewing root cause analysis for ${breakoutName}`);\n              this.viewRootCauseAnalysis(3); // Show 3 months of data\n            });\n          } else {\n            console.error('Group tab not found with any selector');\n          }\n        }\n      });\n    },\n\n    selectBreakoutFromDashboardWithMonth(breakoutName, month) {\n      console.log(`Navigating to Group tab for ${breakoutName} with month ${month}`);\n\n      // Set the selected breakout group first\n      this.group2TabSelectedGroup = breakoutName;\n\n      // Store the selected month for potential use in analysis\n      this.selectedDashboardMonth = month;\n\n      // Use a more direct approach to select the Group tab\n      this.activeTab = 3; // Set the active tab index to 3 (Group tab)\n\n      // Find all tabs and click the Group tab directly\n      this.$nextTick(() => {\n        // Try different selector approaches\n        const tabElements = document.querySelectorAll('.cv-tabs .cv-tab');\n        console.log(`Found ${tabElements.length} tabs with .cv-tabs .cv-tab selector`);\n\n        if (tabElements && tabElements.length > 3) {\n          console.log('Clicking Group tab using .cv-tabs .cv-tab selector');\n          tabElements[3].click();\n\n          // Trigger root cause analysis for this breakout group and month\n          this.$nextTick(() => {\n            console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);\n            this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month\n          });\n        } else {\n          // Try an alternative selector\n          const altTabElements = document.querySelectorAll('.cv-tab');\n          console.log(`Found ${altTabElements.length} tabs with .cv-tab selector`);\n\n          if (altTabElements && altTabElements.length > 3) {\n            console.log('Clicking Group tab using .cv-tab selector');\n            altTabElements[3].click();\n\n            // Trigger root cause analysis for this breakout group and month\n            this.$nextTick(() => {\n              console.log(`Viewing root cause analysis for ${breakoutName} with month ${month}`);\n              this.viewRootCauseAnalysis(1); // Show 1 month of data centered on the selected month\n            });\n          } else {\n            console.error('Group tab not found with any selector');\n          }\n        }\n      });\n    },\n\n    formatDashboardDateRange() {\n      // Format the date range for display in the dashboard info section\n      const today = new Date();\n      let startDate, endDate;\n\n      if (this.dashboardTimeRange === 'custom' && this.dashboardStartDate && this.dashboardEndDate) {\n        // Format custom date range\n        const startDateObj = new Date(this.dashboardStartDate + '-01');\n        const endDateObj = new Date(this.dashboardEndDate + '-01');\n\n        // Format as \"MMM YYYY to MMM YYYY\" (e.g., \"Nov 2024 to May 2025\")\n        startDate = startDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n        endDate = endDateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n\n        // For display in the dashboard info section\n        return `${startDate} to ${endDate}`;\n      }\n\n      switch (this.dashboardTimeRange) {\n        case 'last-month': {\n          // For last month, we want the entire previous month\n          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);\n          startDate = lastMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });\n          endDate = startDate; // Same month for start and end\n          return startDate; // Just return the month name for last-month\n        }\n\n        case 'last-3-months': {\n          // For last 3 months, we want the current month and previous 2 months\n          const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 2, 1);\n          startDate = threeMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          break;\n        }\n\n        case 'last-6-months': {\n          // For last 6 months, we want the current month and previous 5 months\n          const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);\n          startDate = sixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          break;\n        }\n\n        default: {\n          // Default to last 6 months\n          const defaultSixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);\n          startDate = defaultSixMonthsAgo.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n          endDate = today.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n        }\n      }\n\n      return `${startDate} to ${endDate}`;\n    },\n\n    // Format date range for display\n    formatDateRange(startDate, endDate) {\n      if (!startDate || !endDate) return 'No date range selected';\n\n      const startObj = new Date(startDate + '-01');\n      const endObj = new Date(endDate + '-01');\n\n      const startFormatted = startObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n      const endFormatted = endObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n\n      return `${startFormatted} to ${endFormatted}`;\n    },\n\n    // Analyze data for all tabs\n    analyzeAllData() {\n      // Validate date range\n      if (this.validateDateRange()) {\n        console.log('Date range is valid, synchronizing across tabs');\n\n        // Synchronize date ranges across all tabs\n        this.breakoutTabStartDate = this.startDate;\n        this.breakoutTabEndDate = this.endDate;\n        this.breakoutTabStartMonth = this.startMonth;\n        this.breakoutTabStartYear = this.startYear;\n        this.breakoutTabEndMonth = this.endMonth;\n        this.breakoutTabEndYear = this.endYear;\n\n        // Analyze data for main tab\n        this.analyzeData();\n\n        // Analyze data for breakout tab if a group is selected\n        if (this.breakoutTabSelectedGroup) {\n          console.log(`Analyzing data for selected breakout group: ${this.breakoutTabSelectedGroup}`);\n          this.analyzeBreakoutData();\n        }\n\n        // Load dashboard data with the same date range\n        this.loadDashboardData();\n      } else {\n        console.error('Date range validation failed');\n      }\n    },\n\n    // Update start date from dropdowns\n    updateStartDate() {\n      // Convert string to number for internal use\n      this.startMonth = parseInt(this.startMonthStr, 10);\n\n      // Ensure startMonth is a valid number\n      if (isNaN(this.startMonth)) {\n        console.error('Invalid startMonth value:', this.startMonthStr);\n        return;\n      }\n\n      // Format the date as YYYY-MM\n      const month = String(this.startMonth).padStart(2, '0');\n      this.startDate = `${this.startYear}-${month}`;\n\n      // Synchronize with breakout tab\n      this.breakoutTabStartDate = this.startDate;\n      this.breakoutTabStartMonth = this.startMonth;\n      this.breakoutTabStartYear = this.startYear;\n\n      // Set time range to custom since user manually selected dates\n      this.selectedTimeRange = '';\n      this.breakoutTabTimeRange = '';\n\n      // Validate the date range\n      this.validateDateRange();\n    },\n\n    // Update end date from dropdowns\n    updateEndDate() {\n      // Convert string to number for internal use\n      this.endMonth = parseInt(this.endMonthStr, 10);\n\n      // Ensure endMonth is a valid number\n      if (isNaN(this.endMonth)) {\n        console.error('Invalid endMonth value:', this.endMonthStr);\n        return;\n      }\n\n      // Format the date as YYYY-MM\n      const month = String(this.endMonth).padStart(2, '0');\n      this.endDate = `${this.endYear}-${month}`;\n\n      // Synchronize with breakout tab\n      this.breakoutTabEndDate = this.endDate;\n      this.breakoutTabEndMonth = this.endMonth;\n      this.breakoutTabEndYear = this.endYear;\n\n      // Set time range to custom since user manually selected dates\n      this.selectedTimeRange = '';\n      this.breakoutTabTimeRange = '';\n\n      // Validate the date range\n      this.validateDateRange();\n    },\n\n    // Handle filter type change\n    onFilterTypeChange() {\n      console.log(`Filter type changed to: ${this.selectedFilterType}`);\n      // Reset the owner selection when changing filter type\n      this.selectedOwner = 'All';\n      // Don't reload dashboard data until owner is selected\n      console.log('Dashboard data will be reloaded when an owner is selected');\n    },\n\n    // AI Summary tooltip methods\n    showAiSummaryTooltip(event) {\n      // Position the tooltip near the cursor\n      const rect = event.target.getBoundingClientRect();\n      this.aiTooltipStyle = {\n        top: `${rect.bottom + window.scrollY + 5}px`,\n        left: `${rect.left + window.scrollX}px`\n      };\n      this.showAiTooltip = true;\n    },\n\n    hideAiSummaryTooltip() {\n      this.showAiTooltip = false;\n    },\n\n    // Get AI summary for a breakout group using WatsonX.ai\n    async getAiSummary(row) {\n      logger.logInfo(`Getting AI summary for breakout group: ${row.breakoutName}`, 'getAiSummary');\n      this.hideAiSummaryTooltip();\n      this.showAiSummaryModal = true;\n      this.aiSummaryBreakoutName = row.breakoutName;\n      this.isLoadingAiSummary = true;\n      this.loadingAiSummaryFor = row.breakoutName;\n      this.aiSummaryText = '';\n\n      logger.logInfo('Preparing data for WatsonX.ai...', 'getAiSummary');\n\n      try {\n        // Get Action Tracker data for this breakout group\n        const actionTrackerData = await this.getActionTrackerData(row.breakoutName);\n        logger.logInfo('Action Tracker data retrieved', 'getAiSummary');\n\n        // Prepare data for WatsonX.ai\n        const data = this.prepareBreakoutDataForAi(row, actionTrackerData);\n        logger.logInfo(`Data preparation complete. Data length: ${data.length}`, 'getAiSummary');\n\n        // Parse the data to extract actionTrackerInsights if available\n        const parsedData = JSON.parse(data);\n        const actionTrackerInsights = parsedData.actionTrackerInsights;\n        logger.logInfo(`Action tracker insights available: ${!!actionTrackerInsights}`, 'getAiSummary');\n\n        // Format the prompt using the template from watsonxPrompts.js\n        const prompt = formatDashboardSummaryPrompt(\n          data,\n          actionTrackerInsights ? JSON.stringify(actionTrackerInsights) : null\n        );\n\n        logger.logInfo(`Formatted prompt for WatsonX.ai. Length: ${prompt.length}`, 'getAiSummary');\n        logger.logInfo(`Prompt (first 200 chars): ${prompt.substring(0, 200)}...`, 'getAiSummary');\n\n        // Get token from localStorage\n        const token = localStorage.getItem('token');\n        logger.logInfo(`Auth token available: ${!!token}`, 'getAiSummary');\n\n        // Prepare request data for WatsonX.ai\n        const requestData = {\n          model_id: 'ibm/granite-13b-instruct-v2', // Using Granite 13B Chat model\n          prompt: prompt,\n          temperature: 0.3, // Lower temperature for more focused responses\n          api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n          project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek\n        };\n\n        logger.logInfo('Request data prepared', 'getAiSummary');\n\n        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'getAiSummary');\n\n        // Call WatsonX.ai API using fetch\n        const response = await fetch('/api-statit2/watsonx_prompt', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token ? `Bearer ${token}` : ''\n          },\n          body: JSON.stringify(requestData)\n        });\n\n        logger.logInfo(`Received response from API. Status: ${response.status}`, 'getAiSummary');\n\n        // Check if response is ok\n        if (!response.ok) {\n          const errorText = await response.text();\n          logger.logError('Error response text', errorText, 'getAiSummary');\n\n          // Try to parse as JSON if possible\n          try {\n            const errorJson = JSON.parse(errorText);\n            logger.logError('Error response JSON', errorJson, 'getAiSummary');\n            throw new Error(`HTTP error! Status: ${response.status}, ${JSON.stringify(errorJson)}`);\n          } catch (jsonError) {\n            // Not JSON, use as text\n            throw new Error(`HTTP error! Status: ${response.status}, ${errorText}`);\n          }\n        }\n\n        // Parse the JSON response\n        const responseText = await response.text();\n        logger.logInfo('Raw response received', 'getAiSummary');\n\n        let responseData;\n        try {\n          responseData = JSON.parse(responseText);\n          logger.logInfo('Successfully parsed WatsonX.ai response', 'getAiSummary');\n        } catch (jsonError) {\n          logger.logError('Error parsing JSON response', jsonError, 'getAiSummary');\n          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);\n        }\n\n        // Process the response data\n        if (responseData.status === 'success') {\n          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'getAiSummary');\n\n          if (responseData.generated_text) {\n            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'getAiSummary');\n            this.aiSummaryText = responseData.generated_text;\n            logger.logInfo('AI summary set successfully', 'getAiSummary');\n          } else {\n            logger.logError('No generated_text in success response', responseData, 'getAiSummary');\n            this.aiSummaryText = 'No summary generated';\n          }\n        } else {\n          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'getAiSummary');\n          throw new Error(responseData.error || 'Unknown error occurred');\n        }\n      } catch (error) {\n        logger.logError('Error getting AI summary', error, 'getAiSummary');\n\n        // Create a detailed error message for the user\n        let errorMessage = 'Error generating AI summary: ';\n\n        if (error.message) {\n          errorMessage += error.message;\n        }\n\n        // Add HTTP status code if available\n        if (error.response && error.response.status) {\n          errorMessage += ` (Status: ${error.response.status})`;\n        }\n\n        // Add more details if available\n        if (error.response && error.response.data) {\n          errorMessage += `<br><br><strong>Details:</strong><br>${JSON.stringify(error.response.data)}`;\n        }\n\n        // Add troubleshooting suggestions\n        errorMessage += '<br><br><strong>Troubleshooting:</strong><br>' +\n          '• Check your internet connection<br>' +\n          '• Verify that the WatsonX.ai API key is valid<br>' +\n          '• Try again in a few minutes';\n\n        this.aiSummaryText = errorMessage;\n      } finally {\n        this.isLoadingAiSummary = false;\n        this.loadingAiSummaryFor = '';\n      }\n    },\n\n    // Get Action Tracker data for a specific breakout group\n    async getActionTrackerData(breakoutName) {\n      console.log('Getting Action Tracker data' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n      try {\n        // Get authentication config for API call\n        const config = this.getAuthConfig();\n\n        // Make API call to get action tracker data\n        console.log('Fetching action tracker data from API' + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n        // Use fetch instead of axios for API call\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': config.headers && config.headers.Authorization ? config.headers.Authorization : ''\n          },\n          body: JSON.stringify({ breakoutName })\n        });\n\n        // Check if response is ok\n        if (!response.ok) {\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\n        }\n\n        // Parse response data\n        const data = await response.json();\n        console.log(`Retrieved ${data.items ? data.items.length : 0} action items` + (breakoutName ? ` for breakout group: ${breakoutName}` : ''));\n\n        // If no items property exists, create an empty array\n        if (!data.items) {\n          data.items = [];\n        }\n\n        return data;\n      } catch (error) {\n        console.error('Error getting Action Tracker data:', error);\n        // Return empty data on error\n        return { items: [] };\n      }\n    },\n\n    // Prepare breakout data for AI\n    prepareBreakoutDataForAi(row, actionTrackerData = { items: [] }) {\n      // Create a simplified data structure for the AI\n      const data = {\n        breakoutName: row.breakoutName,\n        months: [],\n        actionTrackerInsights: null\n      };\n\n      // Add month data\n      row.months.forEach((cell, index) => {\n        if (cell && this.dashboardMonths[index]) {\n          data.months.push({\n            month: this.dashboardMonths[index],\n            xFactor: cell.xFactor !== null && cell.xFactor !== undefined ? cell.xFactor : 0,\n            status: cell.status || 'Normal',\n            defects: cell.defects !== null && cell.defects !== undefined ? cell.defects : 0,\n            volume: cell.volume !== null && cell.volume !== undefined ? cell.volume : 0,\n            targetRate: cell.targetRate !== null && cell.targetRate !== undefined ? cell.targetRate : 0\n          });\n        }\n      });\n\n      // Add Action Tracker data if available\n      if (actionTrackerData && actionTrackerData.items && actionTrackerData.items.length > 0) {\n        // Format the Action Tracker data for the AI\n        data.actionTrackerInsights = {\n          totalItems: actionTrackerData.items.length,\n          items: actionTrackerData.items.map(item => ({\n            commodity: item.commodity,\n            group: item.group,\n            partNumber: item.pn,\n            test: item.test,\n            deadline: item.deadline,\n            status: item.status,\n            action: item.action,\n            assignee: item.assignee,\n            notes: item.notes\n          }))\n        };\n\n        console.log('Including Action Tracker data in AI summary:', data.actionTrackerInsights);\n      } else {\n        console.log('No Action Tracker data available for this breakout group');\n      }\n\n      const jsonData = JSON.stringify(data);\n      console.log('Data prepared for AI summary:', data);\n      console.log('JSON data being sent to NeuralSeek:', jsonData.substring(0, 200) + (jsonData.length > 200 ? '...' : ''));\n\n      return jsonData;\n    },\n\n    // This method has been replaced by direct calls to WatsonX.ai API\n\n    // Handle owner selection change\n    onOwnerChange() {\n      console.log(`Owner selection changed to: ${this.selectedOwner} (type: ${this.selectedFilterType})`);\n      // Reload dashboard data with the new owner filter\n      this.loadDashboardData();\n    },\n\n    // Validate date range\n    validateDateRange() {\n      if (!this.startDate || !this.endDate) {\n        this.noDataMessage = 'Please select both start and end dates';\n        console.error('Missing start or end date');\n        return false;\n      }\n\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(this.startDate + '-01');\n      const end = new Date(this.endDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        this.noDataMessage = 'Invalid date format';\n        console.error('Invalid date format', { start: this.startDate, end: this.endDate });\n        return false;\n      }\n\n      if (start > end) {\n        this.noDataMessage = 'Start date must be before end date';\n        console.error('Start date is after end date', { start: this.startDate, end: this.endDate });\n        return false;\n      }\n\n      // Set the time range to custom when manually validating\n      this.selectedTimeRange = '';\n\n      // Clear any error message\n      this.noDataMessage = '';\n\n      console.log(`Date range validated: ${this.startDate} to ${this.endDate}`);\n      return true;\n    },\n\n    generateBreakoutAlerts(breakoutData) {\n      console.log('Generating breakout tab alerts...');\n      this.breakoutTabAlerts = [];\n\n      if (!breakoutData || !breakoutData.xFactors) {\n        console.log('No xFactors data for breakout tab alerts');\n        return;\n      }\n\n      // Sort periods chronologically\n      const sortedPeriods = Object.keys(breakoutData.xFactors).sort();\n\n      if (sortedPeriods.length === 0) {\n        console.log('No periods found for breakout tab alerts');\n        return;\n      }\n\n      console.log(`Processing ${sortedPeriods.length} periods for breakout tab alerts`);\n\n      // Check for short-term spikes (X-Factor > 3.0)\n      sortedPeriods.forEach(period => {\n        if (!breakoutData.xFactors[period]) {\n          console.error(`Missing data for period ${period} in breakout tab`);\n          return;\n        }\n\n        const xFactor = breakoutData.xFactors[period].xFactor;\n\n        // Ensure xFactor is a valid number\n        if (typeof xFactor !== 'number' && isNaN(parseFloat(xFactor))) {\n          console.error(`Invalid xFactor in breakout tab for period ${period}: ${xFactor}`);\n          return;\n        }\n\n        if (xFactor > 3.0) {\n          console.log(`Found short-term spike in breakout tab for ${period}: ${xFactor.toFixed(2)}`);\n          this.breakoutTabAlerts.push({\n            status: 'Short-Term Spike',\n            period,\n            xFactor,\n            defects: breakoutData.xFactors[period].defects || 0,\n            volume: breakoutData.xFactors[period].volume || 0\n          });\n        }\n      });\n\n      // Check for sustained problems (X-Factor > 1.5 for 3+ consecutive months)\n      for (let i = 0; i < sortedPeriods.length - 2; i++) {\n        const period1 = sortedPeriods[i];\n        const period2 = sortedPeriods[i + 1];\n        const period3 = sortedPeriods[i + 2];\n\n        // Ensure all periods have valid data\n        if (!breakoutData.xFactors[period1] || !breakoutData.xFactors[period2] || !breakoutData.xFactors[period3]) {\n          console.error(`Missing data for one of the periods in breakout tab sustained problem check: ${period1}, ${period2}, ${period3}`);\n          continue;\n        }\n\n        const xFactor1 = breakoutData.xFactors[period1].xFactor;\n        const xFactor2 = breakoutData.xFactors[period2].xFactor;\n        const xFactor3 = breakoutData.xFactors[period3].xFactor;\n\n        // Ensure all xFactors are valid numbers\n        if (typeof xFactor1 !== 'number' && isNaN(parseFloat(xFactor1)) ||\n            typeof xFactor2 !== 'number' && isNaN(parseFloat(xFactor2)) ||\n            typeof xFactor3 !== 'number' && isNaN(parseFloat(xFactor3))) {\n          console.error(`Invalid xFactor values for breakout tab sustained problem check`);\n          continue;\n        }\n\n        if (xFactor1 > 1.5 && xFactor2 > 1.5 && xFactor3 > 1.5) {\n          // Count how many consecutive months have X-Factor > 1.5\n          let consecutiveMonths = 3;\n          for (let j = i + 3; j < sortedPeriods.length; j++) {\n            if (breakoutData.xFactors[sortedPeriods[j]] && breakoutData.xFactors[sortedPeriods[j]].xFactor > 1.5) {\n              consecutiveMonths++;\n            } else {\n              break;\n            }\n          }\n\n          console.log(`Found sustained problem in breakout tab from ${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}: ${consecutiveMonths} months`);\n\n          // Calculate average xFactor for the sustained problem\n          const avgXFactor = (xFactor1 + xFactor2 + xFactor3) / 3;\n\n          this.breakoutTabAlerts.push({\n            status: `Sustained Problem (${consecutiveMonths} months)`,\n            period: `${period1} to ${sortedPeriods[i + consecutiveMonths - 1]}`,\n            xFactor: avgXFactor,\n            defects: breakoutData.xFactors[period3].defects || 0,\n            volume: breakoutData.xFactors[period3].volume || 0\n          });\n\n          // Skip the periods we've already included in this sustained problem\n          i += consecutiveMonths - 1;\n        }\n      }\n\n      // Sort alerts by X-Factor (highest first)\n      this.breakoutTabAlerts.sort((a, b) => b.xFactor - a.xFactor);\n\n      console.log(`Generated ${this.breakoutTabAlerts.length} breakout tab alerts`);\n    },\n\n    // Helper method to get a date X months ago in YYYY-MM format\n    getDateMonthsAgo(yearMonth, monthsAgo) {\n      const [year, month] = yearMonth.split('-').map(num => parseInt(num, 10));\n\n      // Calculate new month and year\n      let newMonth = month - monthsAgo;\n      let newYear = year;\n\n      // Handle month wrapping\n      while (newMonth <= 0) {\n        newMonth += 12;\n        newYear -= 1;\n      }\n\n      // Format as YYYY-MM\n      return `${newYear}-${String(newMonth).padStart(2, '0')}`;\n    },\n\n    // Category Analysis tab methods\n    analyzeCategoryData() {\n      if (!this.categoryTabSelectedGroup) {\n        this.categoryTabNoDataMessage = 'Please select a breakout group to analyze';\n        return;\n      }\n\n      this.categoryTabNoDataMessage = 'Loading category data...';\n      this.isCategoryTabLoading = true;\n      this.categoryTabChartData = [];\n      this.categoryTabCategories = [];\n      this.categoryTabPartNumbers = [];\n\n      console.log(`Analyzing category data for ${this.categoryTabSelectedGroup} from ${this.startDate} to ${this.endDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Use the global date range\n      const requestData = {\n        breakoutName: this.categoryTabSelectedGroup,\n        startDate: this.startDate,\n        endDate: this.endDate\n      };\n\n      axios.post('/api-statit2/get_metis_category_analysis', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const categoryData = response.data.categoryData;\n\n            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {\n              this.categoryTabNoDataMessage = 'No category data found for the selected breakout group and date range';\n              this.isCategoryTabLoading = false;\n              return;\n            }\n\n            // Store the categories and part numbers\n            this.categoryTabCategories = categoryData.categories || [];\n            this.categoryTabPartNumbers = response.data.partNumbers || [];\n\n            // Set up color scale for categories\n            const colorScale = {};\n            const colors = [\n              '#0f62fe', // blue\n              '#6929c4', // purple\n              '#1192e8', // cyan\n              '#005d5d', // teal\n              '#9f1853', // magenta\n              '#fa4d56', // red\n              '#570408', // maroon\n              '#198038', // green\n              '#002d9c', // navy\n              '#ee538b', // pink\n              '#b28600', // yellow\n              '#8a3800', // orange\n              '#a56eff', // light purple\n              '#08bdba', // light teal\n              '#bae6ff'  // light blue\n            ];\n\n            this.categoryTabCategories.forEach((category, index) => {\n              colorScale[category] = colors[index % colors.length];\n            });\n\n            // Update chart options with color scale\n            this.categoryChartOptions.color.scale = colorScale;\n\n            // Set chart data\n            this.categoryTabChartData = categoryData.chartData;\n            this.categoryTabNoDataMessage = '';\n\n            console.log(`Loaded ${this.categoryTabChartData.length} category data points for ${this.categoryTabSelectedGroup}`);\n            console.log(`Found ${this.categoryTabCategories.length} categories: ${this.categoryTabCategories.join(', ')}`);\n\n            // Calculate the maximum fail rate to set the Y-axis domain\n            this.updateChartYAxisDomain();\n\n            // Setup chart click handlers after chart is updated\n            this.$nextTick(() => {\n              this.setupChartClickHandlers();\n            });\n          } else {\n            console.error('Error analyzing category data:', response.data);\n            this.categoryTabNoDataMessage = 'Error analyzing category data. Please try again.';\n          }\n          this.isCategoryTabLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when analyzing category data:', error);\n          this.categoryTabNoDataMessage = `Error: ${error.message}`;\n          this.isCategoryTabLoading = false;\n        });\n    },\n\n    // Handle click on category bar chart\n    handleCategoryBarClick(event) {\n      console.log('Bar click event received:', event);\n\n      // Check if we have a valid event\n      if (!event) {\n        console.error('Invalid click event');\n        return;\n      }\n\n      let clickedData = null;\n\n      // Try to extract data from different event formats\n      if (event.datum) {\n        // Carbon Charts format\n        clickedData = event.datum;\n      } else if (event.data) {\n        // Alternative format\n        clickedData = event.data;\n      } else if (event.target && event.target.dataset) {\n        // DOM element with dataset\n        clickedData = {\n          group: event.target.dataset.group,\n          key: event.target.dataset.key\n        };\n      } else if (event.element === 'bar') {\n        // Carbon Charts v3 format\n        clickedData = event.data;\n      }\n\n      console.log('Extracted clicked data:', clickedData);\n\n      // If we couldn't extract data, try to find it in the chart data\n      if (!clickedData || (!clickedData.key && !clickedData.group)) {\n        console.log('Trying to find data from chart data...');\n\n        // Try to extract data from the event object\n        if (event.data && typeof event.data === 'object') {\n          // Try different property names that might contain the data\n          const possibleDataProps = ['data', 'datum', 'point', 'bar', 'value'];\n\n          for (const prop of possibleDataProps) {\n            if (event.data[prop] && event.data[prop].group && event.data[prop].key) {\n              clickedData = event.data[prop];\n              console.log(`Found data in event.data.${prop}:`, clickedData);\n              break;\n            }\n          }\n        }\n\n        // If still no data, use the first data point for the clicked month\n        if (!clickedData && event.target) {\n          // Try to determine which month was clicked based on position\n          const barElement = event.target.closest('.bar');\n          if (barElement) {\n            const svgElement = barElement.closest('svg');\n            if (svgElement) {\n              const rect = barElement.getBoundingClientRect();\n              const svgRect = svgElement.getBoundingClientRect();\n              const relativeX = rect.left - svgRect.left;\n              const totalWidth = svgRect.width;\n\n              // Get all unique months\n              const months = [...new Set(this.categoryTabChartData.map(d => d.key))].sort();\n\n              // Calculate which month this might be\n              const monthIndex = Math.floor((relativeX / totalWidth) * months.length);\n\n              if (monthIndex >= 0 && monthIndex < months.length) {\n                const month = months[monthIndex];\n\n                // Find all data points for this month\n                const dataForMonth = this.categoryTabChartData.filter(d => d.key === month);\n\n                if (dataForMonth.length > 0) {\n                  // Try to determine which category was clicked based on the fill color\n                  let categoryIndex = 0;\n                  if (barElement.classList.contains('bar')) {\n                    // Try to extract category index from class name\n                    const fillClass = Array.from(barElement.classList).find(cls => cls.startsWith('fill-'));\n                    if (fillClass) {\n                      const parts = fillClass.split('-');\n                      if (parts.length >= 2) {\n                        categoryIndex = parseInt(parts[1]);\n                      }\n                    }\n                  }\n\n                  // Get all unique categories\n                  const categories = [...new Set(this.categoryTabChartData.map(d => d.group))];\n\n                  // Use the determined category if valid, otherwise use the first one\n                  const category = (categoryIndex >= 0 && categoryIndex < categories.length)\n                    ? categories[categoryIndex]\n                    : dataForMonth[0].group;\n\n                  // Find the specific data point\n                  const dataPoint = this.categoryTabChartData.find(d => d.key === month && d.group === category);\n\n                  if (dataPoint) {\n                    clickedData = dataPoint;\n                    console.log('Found data point from position analysis:', clickedData);\n                  } else {\n                    // Fallback to first data point for this month\n                    clickedData = dataForMonth[0];\n                    console.log('Using first data point for month:', clickedData);\n                  }\n                }\n              }\n            }\n          }\n        }\n\n        // Last resort fallback\n        if (!clickedData && this.categoryTabChartData && this.categoryTabChartData.length > 0) {\n          // For simplicity, just use the first category and month\n          const firstDataPoint = this.categoryTabChartData[0];\n          clickedData = firstDataPoint;\n          console.log('Using fallback data point:', clickedData);\n        } else if (!clickedData) {\n          console.error('No chart data available');\n          return;\n        }\n      }\n\n      // Make sure we have the necessary properties\n      if (!clickedData.key || !clickedData.group) {\n        console.error('Missing key or group in clicked data');\n        return;\n      }\n\n      console.log(`Category bar clicked: ${clickedData.group}, Month: ${clickedData.key}`);\n\n      // Set selected month and category for the modal\n      this.selectedMonth = clickedData.key;\n      this.selectedCategory = clickedData.group;\n\n      // Load failure modes data\n      this.loadFailureModes(this.categoryTabSelectedGroup, clickedData.key, clickedData.group);\n\n      // Show the modal\n      this.showFailureModesModal = true;\n    },\n\n    // Load failure modes data for the selected month and category\n    loadFailureModes(breakoutName, month, category) {\n      this.isFailureModesLoading = true;\n      this.failureModesChartData = [];\n\n      console.log(`Loading failure modes for ${breakoutName}, Month: ${month}, Category: ${category}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Prepare request data\n      const requestData = {\n        breakoutName: breakoutName,\n        month: month,\n        category: category\n      };\n\n      axios.post('/api-statit2/get_metis_failure_modes', requestData, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const failureModeData = response.data.failureModeData;\n\n            if (!failureModeData || !failureModeData.chartData || failureModeData.chartData.length === 0) {\n              console.log('No failure modes data found');\n              this.isFailureModesLoading = false;\n              return;\n            }\n\n            // Get the Count data points\n            const countData = failureModeData.chartData.filter(item => item.group === 'Count');\n\n            // Sort by count in descending order\n            countData.sort((a, b) => b.value - a.value);\n\n            // Format data for horizontal bar chart\n            const hBarData = countData.map(item => ({\n              group: \"Count\",\n              key: item.key,\n              value: item.value,\n              category: item.category || category\n            }));\n\n            // Set chart data\n            this.failureModesChartData = hBarData;\n\n            console.log(`Created horizontal bar chart with ${hBarData.length} failure modes`);\n          } else {\n            console.error('Error loading failure modes:', response.data);\n          }\n          this.isFailureModesLoading = false;\n        })\n        .catch(error => {\n          console.error('API error when loading failure modes:', error);\n          this.isFailureModesLoading = false;\n        });\n    },\n\n    // Get color for category\n    getCategoryColor(category) {\n      const colors = {\n        'Electrical': '#0f62fe',\n        'Mechanical': '#6929c4',\n        'Thermal': '#1192e8',\n        'Material': '#005d5d',\n        'Process': '#9f1853',\n        'Design': '#fa4d56',\n        'Unknown': '#570408',\n        'Other': '#198038'\n      };\n\n      return colors[category] || '#0f62fe';\n    },\n\n    // Group 2 Tab methods\n    analyzeGroup2Data() {\n      if (!this.group2TabSelectedGroup) {\n        this.group2TabNoDataMessage = 'Please select a breakout group to analyze';\n        return;\n      }\n\n      console.log(`Analyzing Group 2 data for ${this.group2TabSelectedGroup}`);\n      this.group2TabNoDataMessage = '';\n\n      // Clear any existing data\n      this.rootCauseChartData = [];\n      this.hasCriticalRootCauseIssue = false;\n\n      // If we have a selected dashboard month, use that to determine the time period\n      // Otherwise, default to 3 months\n      if (this.selectedDashboardMonth) {\n        console.log(`Using selected dashboard month: ${this.selectedDashboardMonth}`);\n        // View root cause analysis for 1 month centered on the selected month\n        this.viewRootCauseAnalysis(1);\n      } else {\n        // Default to 3 months of data\n        console.log('No specific month selected, showing 3 months of data');\n        this.viewRootCauseAnalysis(3);\n      }\n    },\n\n    // Custom Date Modal methods\n    showCustomDateModal2(analysisType) {\n      this.customDateAnalysisType = analysisType;\n      this.showCustomDateModal = true;\n    },\n\n    hideCustomDateModal() {\n      this.showCustomDateModal = false;\n    },\n\n    // View custom analysis methods\n    viewCustomRootCauseAnalysis() {\n      // Show the custom date modal with root cause analysis type\n      this.showCustomDateModal2('rootcause');\n    },\n\n    applyCustomDateRange() {\n      // Format the dates as YYYY-MM\n      const startMonth = String(this.customStartMonthStr).padStart(2, '0');\n      const endMonth = String(this.customEndMonthStr).padStart(2, '0');\n      const startDate = `${this.customStartYear}-${startMonth}`;\n      const endDate = `${this.customEndYear}-${endMonth}`;\n\n      console.log(`Custom date range: ${startDate} to ${endDate}`);\n\n      // Validate the date range\n      if (this.validateCustomDateRange(startDate, endDate)) {\n        if (this.customDateAnalysisType === 'rootcause') {\n          this.viewRootCauseAnalysis(0, startDate, endDate);\n        }\n\n        this.hideCustomDateModal();\n      }\n    },\n\n    validateCustomDateRange(startDate, endDate) {\n      // For month inputs, we need to create dates from the first day of each month\n      const start = new Date(startDate + '-01');\n      const end = new Date(endDate + '-01');\n\n      // Check if dates are valid\n      if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        console.error('Invalid custom date format', {\n          start: startDate,\n          end: endDate\n        });\n        return false;\n      }\n\n      if (start > end) {\n        console.error('Invalid date range: Start date is after end date', {\n          start: startDate,\n          end: endDate\n        });\n        return false;\n      }\n\n      return true;\n    },\n\n\n\n\n\n\n\n\n\n\n\n    // Root Cause Analysis methods\n    viewRootCauseAnalysis(months, customStartDate, customEndDate) {\n      if (!this.group2TabSelectedGroup) {\n        console.error('No breakout group selected for root cause analysis');\n        return;\n      }\n\n      // Clear any existing chart data\n      this.rootCauseChartData = [];\n      this.hasCriticalRootCauseIssue = false;\n      this.criticalIssues = [];\n      this.criticalIssueDescription = '';\n\n      this.isRootCauseDataLoading = true;\n      this.isRootCauseAiLoading = true;\n      this.rootCauseChartData = [];\n      this.rootCauseAiSummary = '';\n      this.hasCriticalRootCauseIssue = false;\n      this.criticalIssueUpdate = '';\n      this.criticalIssueUpdates = [];\n\n      // Set the time range description\n      if (months === 0 && customStartDate && customEndDate) {\n        this.rootCauseTimeRange = `Custom: ${this.formatDateForDisplay(customStartDate)} to ${this.formatDateForDisplay(customEndDate)}`;\n        this.rootCauseStartDate = customStartDate;\n        this.rootCauseEndDate = customEndDate;\n      } else {\n        this.rootCauseMonths = months;\n\n        // Calculate the date range based on the number of months\n        const endDate = new Date();\n        const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');\n        const endYear = endDate.getFullYear();\n        this.rootCauseEndDate = `${endYear}-${endMonth}`;\n\n        // Calculate start date by going back the specified number of months\n        const startDate = new Date();\n        startDate.setMonth(startDate.getMonth() - (months - 1));\n        const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');\n        const startYear = startDate.getFullYear();\n        this.rootCauseStartDate = `${startYear}-${startMonth}`;\n\n        this.rootCauseTimeRange = months === 1 ? 'Last Month' : `Last ${months} Months`;\n      }\n\n      console.log(`Root cause analysis for ${this.group2TabSelectedGroup} from ${this.rootCauseStartDate} to ${this.rootCauseEndDate}`);\n\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Get category data for the selected breakout group\n      axios.post('/api-statit2/get_metis_category_analysis', {\n        breakoutName: this.group2TabSelectedGroup,\n        startDate: this.rootCauseStartDate,\n        endDate: this.rootCauseEndDate\n      }, config)\n        .then(response => {\n          if (response.data.status_res === 'success') {\n            const categoryData = response.data.categoryData;\n\n            if (!categoryData || !categoryData.chartData || categoryData.chartData.length === 0) {\n              console.log('No category data found');\n              this.isRootCauseDataLoading = false;\n              return;\n            }\n\n            // Process the data for the bar chart\n            this.processRootCauseChartData(categoryData);\n\n            // Generate AI summary\n            this.generateRootCauseAiSummary(categoryData);\n          } else {\n            console.error('Error getting category analysis:', response.data);\n            this.isRootCauseDataLoading = false;\n            this.isRootCauseAiLoading = false;\n          }\n        })\n        .catch(error => {\n          console.error('API error when getting category analysis:', error);\n          this.isRootCauseDataLoading = false;\n          this.isRootCauseAiLoading = false;\n        });\n\n      // No need to show a modal as content will be displayed inline\n    },\n\n\n\n    processRootCauseChartData(categoryData) {\n      // Extract all unique categories and months\n      const categories = [...new Set(categoryData.chartData.map(item => item.group))];\n      const months = [...new Set(categoryData.chartData.map(item => item.key))];\n\n      console.log(`Found ${categories.length} categories: ${categories.join(', ')}`);\n      console.log(`Found ${months.length} months: ${months.join(', ')}`);\n\n      // Sort months chronologically\n      months.sort((a, b) => new Date(a + '-01') - new Date(b + '-01'));\n\n      // Create a map to store data by category and month\n      const dataByMonth = {};\n      const previousMonthRates = {};\n      let hasCriticalIssue = false;\n\n      // Initialize data structures\n      categories.forEach(category => {\n        previousMonthRates[category] = [];\n      });\n\n      // Process the data\n      categoryData.chartData.forEach(item => {\n        const { group: category, key: month, value } = item;\n\n        // Store the monthly rates for each category to detect spikes\n        previousMonthRates[category].push({\n          month,\n          value\n        });\n\n        // Initialize month data if it doesn't exist\n        if (!dataByMonth[month]) {\n          dataByMonth[month] = {};\n        }\n\n        // Store the value for this category and month\n        dataByMonth[month][category] = value;\n      });\n\n      // Check for critical issues based on new criteria:\n      // 1) Full month fail rate must be above target rate\n      // 2) Root cause for a month is 3x the fail rate from cumulative fails in past months\n      const criticalIssueDetails = [];\n\n      // Get target rate for this breakout group from the breakout_targets file\n      // This will be used to check if the full month fail rate is above target\n      let targetRate = 0.001; // Default target rate if not found\n\n      // Try to get the target rate from the data if available\n      if (categoryData.targetRate !== undefined) {\n        targetRate = categoryData.targetRate;\n        console.log(`Using target rate from API data: ${targetRate}`);\n      } else {\n        // If not available in the data, we'll use a default or try to fetch it\n        console.log(`No target rate found in API data, using default: ${targetRate}`);\n      }\n\n      // Calculate total fail rate for each month to compare against target\n      const monthlyTotalFailRates = {};\n      months.forEach(month => {\n        let totalFailRate = 0;\n        let totalDefects = 0;\n        let totalVolume = 0;\n\n        // Sum up all categories for this month\n        categories.forEach(category => {\n          const categoryData = previousMonthRates[category].find(data => data.month === month);\n          if (categoryData) {\n            totalFailRate += categoryData.value;\n\n            // If we have actual defect counts, use those\n            if (categoryData.defects !== undefined && categoryData.volume !== undefined) {\n              totalDefects += categoryData.defects;\n              totalVolume = categoryData.volume; // Volume should be the same for all categories in a month\n            }\n          }\n        });\n\n        // Store the total fail rate for this month\n        monthlyTotalFailRates[month] = {\n          failRate: totalFailRate,\n          defects: totalDefects,\n          volume: totalVolume,\n          isAboveTarget: totalFailRate > (targetRate * 100) // Convert target to percentage for comparison\n        };\n\n        console.log(`Month ${month} total fail rate: ${totalFailRate.toFixed(2)}%, target: ${(targetRate * 100).toFixed(2)}%, above target: ${totalFailRate > (targetRate * 100)}`);\n      });\n\n      categories.forEach(category => {\n        // Sort the monthly rates by date\n        previousMonthRates[category].sort((a, b) => {\n          return new Date(a.month + '-01') - new Date(b.month + '-01');\n        });\n\n        const rates = previousMonthRates[category];\n\n        if (rates.length >= 2) {\n          for (let i = 1; i < rates.length; i++) {\n            const currentMonth = rates[i];\n            const previousMonth = rates[i-1];\n\n            // Check if the full month fail rate is above target\n            const isMonthAboveTarget = monthlyTotalFailRates[currentMonth.month] &&\n                                      monthlyTotalFailRates[currentMonth.month].isAboveTarget;\n\n            // Only proceed if the month's total fail rate is above target\n            if (!isMonthAboveTarget) {\n              console.log(`Skipping critical issue check for ${category} in ${currentMonth.month}: month's total fail rate is not above target`);\n              continue;\n            }\n\n            // Check if this root cause is 3x the fail rate from previous month\n            // OR if it's a new root cause that wasn't present before (previousMonth.value is 0)\n            // but only if the current month's value is significant (above 0.1%)\n            if ((currentMonth.value >= previousMonth.value * 3 && previousMonth.value > 0) ||\n                (previousMonth.value === 0 && currentMonth.value > 0.1)) {\n              console.log(`Potential critical issue detected in ${category}: ${previousMonth.value.toFixed(2)}% to ${currentMonth.value.toFixed(2)}%`);\n\n              // Get actual defect counts from the category data if available\n              const previousDefects = (categoryData.defectCounts &&\n                                      categoryData.defectCounts[previousMonth.month] &&\n                                      categoryData.defectCounts[previousMonth.month][category]) || 0;\n              const currentDefects = (categoryData.defectCounts &&\n                                     categoryData.defectCounts[currentMonth.month] &&\n                                     categoryData.defectCounts[currentMonth.month][category]) || 0;\n\n              // If actual counts aren't available, estimate based on percentages\n              const previousVolume = (categoryData.volumeData &&\n                                     categoryData.volumeData[previousMonth.month]) || 10000;\n              const currentVolume = (categoryData.volumeData &&\n                                    categoryData.volumeData[currentMonth.month]) || 10000;\n\n              const previousFails = previousDefects || Math.round(previousVolume * (previousMonth.value / 100));\n              const currentFails = currentDefects || Math.round(currentVolume * (currentMonth.value / 100));\n\n              // This is a critical issue - the month is above target and this root cause has a 3x spike\n              console.log(`Confirmed critical issue in ${category}: month is above target and root cause has 3x spike`);\n              hasCriticalIssue = true;\n\n              // Store details about this critical issue\n              const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n              const [year, monthNum] = currentMonth.month.split('-');\n              const monthName = monthNames[parseInt(monthNum) - 1];\n\n              criticalIssueDetails.push({\n                category,\n                previousValue: previousMonth.value.toFixed(2),\n                currentValue: currentMonth.value.toFixed(2),\n                increaseMultiplier: previousMonth.value === 0 ? \"(new)\" : (currentMonth.value / previousMonth.value).toFixed(1),\n                month: `${monthName} ${year}`,\n                monthShort: currentMonth.month,\n                previousFails,\n                currentFails,\n                failsIncrease: currentFails - previousFails,\n                totalFailRate: monthlyTotalFailRates[currentMonth.month].failRate.toFixed(2),\n                targetRate: (targetRate * 100).toFixed(2),\n                isNew: previousMonth.value === 0\n              });\n\n              // We're no longer changing the category name for critical issues\n              // Just keep track of which ones are critical for coloring\n            }\n          }\n        }\n      });\n\n      // Process critical issues\n      if (criticalIssueDetails.length > 0) {\n        // Sort by increase multiplier (highest first)\n        criticalIssueDetails.sort((a, b) => parseFloat(b.increaseMultiplier) - parseFloat(a.increaseMultiplier));\n\n        // Store all critical issues with unique IDs\n        this.criticalIssues = criticalIssueDetails.map((issue, index) => {\n          const issueId = `${this.group2TabSelectedGroup.replace(/\\s+/g, '-')}-${issue.category.replace(/\\s+/g, '-')}-${issue.month.replace(/\\s+/g, '-')}`;\n\n          // Generate a description that sounds like it was written by an AI\n          let description;\n          if (issue.isNew) {\n            // New root cause that wasn't present before\n            description = `${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant new issue that requires immediate investigation.`;\n          } else {\n            // Existing root cause with a spike\n            description = `${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%. This represents a significant deviation from expected performance that requires immediate investigation.`;\n          }\n\n          // Initialize update field if it doesn't exist\n          if (!this.criticalIssueUpdates[issueId]) {\n            this.criticalIssueUpdates[issueId] = {\n              updateText: '',\n              lastUpdated: null,\n              history: []\n            };\n          }\n\n          // Expand the first critical issue by default\n          if (index === 0) {\n            this.$set(this.expandedIssues, issueId, true);\n          }\n\n          return {\n            id: issueId,\n            category: issue.category,\n            previousValue: issue.previousValue,\n            currentValue: issue.currentValue,\n            increaseMultiplier: issue.increaseMultiplier,\n            month: issue.month,\n            description: description,\n            severity: parseFloat(issue.increaseMultiplier) > 5 ? 'high' : 'medium'\n          };\n        });\n\n        // Generate overall description for the most severe issue\n        const issue = criticalIssueDetails[0];\n        if (issue.isNew) {\n          // New root cause that wasn't present before\n          this.criticalIssueDescription = `Critical issue detected: ${issue.category} emerged as a new failure mode in ${issue.month} with a rate of ${issue.currentValue}%, resulting in ${issue.currentFails} failures. The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;\n        } else {\n          // Existing root cause with a spike\n          this.criticalIssueDescription = `Critical issue detected: ${issue.category} failure rate spiked ${issue.increaseMultiplier}x from ${issue.previousValue}% to ${issue.currentValue}% in ${issue.month}, with failures increasing from ${issue.previousFails} to ${issue.currentFails} units (${issue.failsIncrease} additional failures). The total fail rate for the month was ${issue.totalFailRate}%, which exceeds the target rate of ${issue.targetRate}%.`;\n        }\n\n        // If there are multiple critical issues, add a note\n        if (criticalIssueDetails.length > 1) {\n          this.criticalIssueDescription += ` Additionally, ${criticalIssueDetails.length - 1} other root cause categories show critical spikes.`;\n        }\n      } else {\n        this.criticalIssues = [];\n        this.criticalIssueDescription = '';\n      }\n\n      // Format data for stacked bar chart\n      const chartData = [];\n\n      // Create a color scale for the categories\n      const colorScale = {};\n\n      // Define colors for each category\n      const categoryColors = {\n        'Electrical': '#0f62fe',\n        'Mechanical': '#6929c4',\n        'Thermal': '#1192e8',\n        'Material': '#005d5d',\n        'Process': '#9f1853',\n        'Design': '#fa4d56',\n        'Unknown': '#570408',\n        'Other': '#198038',\n        'FLAG': '#8a3ffc',\n        'LINK': '#002d9c',\n        'NONFAIL': '#009d9a',\n        'KRAKEN': '#ee538b',\n        'I2C': '#b28600'\n      };\n\n      // Assign colors to categories\n      categories.forEach(category => {\n        // Use predefined color if available, otherwise use a default color\n        colorScale[category] = categoryColors[category] || `#${Math.floor(Math.random()*16777215).toString(16)}`;\n      });\n\n      // Update the chart options with the color scale\n      this.rootCauseChartOptions.color.scale = colorScale;\n\n      // Create data points for each month and category\n      months.forEach(month => {\n        const monthData = dataByMonth[month] || {};\n\n        // Format the month for display\n        const [year, monthNum] = month.split('-');\n        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formattedMonth = `${monthNames[parseInt(monthNum) - 1]} ${year}`;\n\n        // Add data for each category in this month\n        Object.entries(monthData).forEach(([category, value]) => {\n          // Check if this category/month combination is a critical issue\n          const criticalIssue = criticalIssueDetails.find(issue =>\n            issue.category === category && issue.monthShort === month);\n          const isCritical = !!criticalIssue;\n\n          // Get defect counts from the API data if available\n          const defects = (categoryData.defectCounts &&\n                          categoryData.defectCounts[month] &&\n                          categoryData.defectCounts[month][category]) || 0;\n\n          // Get volume data if available\n          const volume = (categoryData.volumeData &&\n                         categoryData.volumeData[month]) || 10000;\n\n          // Calculate estimated defects if actual count is not available\n          const estimatedDefects = defects || Math.round(volume * (value / 100));\n\n          // Instead of changing the category name for critical issues,\n          // we'll just store the critical status in the data object\n          chartData.push({\n            group: category, // Keep the original category name\n            key: formattedMonth,\n            value: value,\n            data: {\n              defects: estimatedDefects,\n              volume: volume,\n              month: month,\n              originalMonth: month,\n              category: category,\n              isCritical: isCritical\n            }\n          });\n        });\n      });\n\n      // Update the chart data\n      this.rootCauseChartData = chartData;\n      this.hasCriticalRootCauseIssue = hasCriticalIssue;\n\n      // Load previous updates if there are critical issues\n      if (hasCriticalIssue) {\n        this.loadCriticalIssueUpdates();\n      }\n\n      this.isRootCauseDataLoading = false;\n      console.log(`Processed ${chartData.length} data points for root cause analysis`);\n    },\n\n    generateRootCauseAiSummary(categoryData) {\n      // Get authentication config\n      const config = this.getAuthConfig();\n\n      // Wait a moment to ensure critical issues have been processed\n      setTimeout(() => {\n        // Prepare the data for the AI summary\n        const summaryData = {\n          breakoutGroup: this.group2TabSelectedGroup,\n          timeRange: this.rootCauseTimeRange,\n          startDate: this.rootCauseStartDate,\n          endDate: this.rootCauseEndDate,\n          categories: categoryData.chartData,\n          hasCriticalIssues: this.hasCriticalRootCauseIssue,\n          criticalIssuesCount: this.criticalIssues.length,\n          criticalIssues: this.criticalIssues.map(issue => ({\n            category: issue.category,\n            month: issue.month,\n            increaseMultiplier: issue.increaseMultiplier,\n            isNew: issue.increaseMultiplier === '(new)'\n          }))\n        };\n\n        // Force the AI to acknowledge critical issues if they exist\n        if (this.criticalIssues.length > 0) {\n          summaryData.forceCriticalIssueAcknowledgment = true;\n        }\n\n        console.log(`Generating AI summary with critical issues: ${this.hasCriticalRootCauseIssue}, count: ${this.criticalIssues.length}`);\n\n        // Get action tracker data for this breakout group\n        this.getActionTrackerData(this.group2TabSelectedGroup)\n          .then(actionTrackerData => {\n            // Format the prompt with the data and action tracker insights\n            const prompt = this.formatRootCauseAiPrompt(summaryData, actionTrackerData);\n\n            // Call WatsonX.ai API\n            return axios.post('/api-statit2/watsonx_prompt', {\n              model_id: 'ibm/granite-13b-instruct-v2',\n              prompt: prompt,\n              temperature: 0.3,\n              api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n              project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'\n            }, config);\n          })\n          .then(response => {\n            if (response.data.status === 'success') {\n              this.rootCauseAiSummary = response.data.generated_text;\n              console.log('AI summary generated successfully');\n            } else {\n              console.error('Error generating AI summary:', response.data);\n              this.rootCauseAiSummary = 'Unable to generate AI summary at this time.';\n            }\n            this.isRootCauseAiLoading = false;\n          })\n          .catch(error => {\n            console.error('API error when generating AI summary:', error);\n            this.rootCauseAiSummary = 'Error generating AI summary. Please try again later.';\n            this.isRootCauseAiLoading = false;\n          });\n      }, 500); // Wait 500ms to ensure critical issues are processed\n    },\n\n    formatRootCauseAiPrompt(data, actionTrackerData) {\n      // Import the prompt template from watsonxPrompts.js\n      const { formatRootCauseAnalysisPrompt } = require('@/utils/watsonxPrompts');\n\n      // Format the prompt with the data\n      return formatRootCauseAnalysisPrompt(data, actionTrackerData);\n    },\n\n    loadCriticalIssueUpdates() {\n      // In a real implementation, this would load updates from a database\n      // For now, we'll just use a mock implementation\n      console.log('Loading critical issue updates (mock implementation)');\n\n      // We're now using an object instead of an array, so we don't need to reset it here\n      // The updates are initialized for each issue in the processRootCauseChartData method\n    },\n\n    saveCriticalIssueUpdate(issueId, updateText) {\n      // In a real implementation, this would save the update to a database\n      if (updateText && updateText.trim()) {\n        // Get the current date and time\n        const timestamp = new Date();\n\n        // Add the update to the history\n        if (!this.criticalIssueUpdates[issueId]) {\n          this.criticalIssueUpdates[issueId] = {\n            updateText: '',\n            lastUpdated: null,\n            history: []\n          };\n        }\n\n        // Add to history\n        this.criticalIssueUpdates[issueId].history.push({\n          content: updateText,\n          timestamp: timestamp\n        });\n\n        // Update the last updated timestamp\n        this.criticalIssueUpdates[issueId].lastUpdated = timestamp;\n\n        // Clear the update text\n        this.criticalIssueUpdates[issueId].updateText = '';\n\n        console.log(`Critical issue update saved for ${issueId}`);\n\n        // In a real implementation, you would save this to a database\n        // For example:\n        // axios.post('/api-statit2/save_critical_issue_update', {\n        //   issueId,\n        //   updateText,\n        //   timestamp\n        // });\n\n        return true;\n      }\n\n      return false;\n    },\n\n    // Update the text for a critical issue\n    updateCriticalIssueText(issueId, text) {\n      if (!this.criticalIssueUpdates[issueId]) {\n        this.criticalIssueUpdates[issueId] = {\n          updateText: text,\n          lastUpdated: null,\n          history: []\n        };\n      } else {\n        this.criticalIssueUpdates[issueId].updateText = text;\n      }\n    },\n\n    // Get the update text for a critical issue\n    getCriticalIssueUpdateText(issueId) {\n      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].updateText) || '';\n    },\n\n    // Get the history for a critical issue\n    getCriticalIssueHistory(issueId) {\n      return (this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].history) || [];\n    },\n\n    // Check if a critical issue has been updated\n    hasCriticalIssueBeenUpdated(issueId) {\n      return this.criticalIssueUpdates[issueId] && this.criticalIssueUpdates[issueId].lastUpdated !== null;\n    },\n\n    // Toggle expanded state of a critical issue\n    toggleCriticalIssue(issueId) {\n      this.$set(this.expandedIssues, issueId, !this.isIssueExpanded(issueId));\n    },\n\n    // Check if a critical issue is expanded\n    isIssueExpanded(issueId) {\n      return !!this.expandedIssues[issueId];\n    },\n\n    formatDate(date) {\n      if (!date) return '';\n\n      if (typeof date === 'string') {\n        date = new Date(date);\n      }\n\n      return date.toLocaleString();\n    },\n\n    formatDateForDisplay(dateStr) {\n      if (!dateStr) return '';\n\n      const [year, month] = dateStr.split('-');\n      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n\n      return `${monthNames[parseInt(month) - 1]} ${year}`;\n    },\n\n    // Handle click events on the root cause chart\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.key}, month: ${clickedData.group}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // about the selected category or filtering the data\n      }\n    },\n\n\n\n\n\n    // Setup direct click handlers for the chart\n    setupChartClickHandlers() {\n      console.log('Setting up chart click handlers');\n\n      // We're now handling clicks in the StackedBarChart component\n      // This method is kept for compatibility but doesn't need to do anything\n    },\n\n    // Update the Y-axis domain based on the maximum fail rate\n    updateChartYAxisDomain() {\n      if (!this.categoryTabChartData || this.categoryTabChartData.length === 0) return;\n\n      // Calculate the maximum fail rate across all categories and months\n      let maxFailRate = 0;\n\n      // For stacked bar charts, we need to calculate the total for each month\n      const monthTotals = {};\n\n      // First, calculate the total for each month\n      this.categoryTabChartData.forEach(item => {\n        const month = item.key;\n        if (!monthTotals[month]) {\n          monthTotals[month] = 0;\n        }\n        monthTotals[month] += item.value || 0;\n      });\n\n      // Find the maximum monthly total\n      Object.values(monthTotals).forEach(total => {\n        if (total > maxFailRate) {\n          maxFailRate = total;\n        }\n      });\n\n      console.log(`Maximum fail rate: ${maxFailRate}%`);\n\n      // Set a reasonable Y-axis domain based on the maximum fail rate\n      // Add 10% padding to the top for better visualization\n      const yAxisMax = Math.ceil(maxFailRate * 1.1);\n\n      // Ensure the maximum is at least 0.1% for very small values\n      const finalMax = Math.max(0.1, yAxisMax);\n\n      console.log(`Setting Y-axis domain to [0, ${finalMax}]`);\n\n      // Update the chart options\n      this.categoryChartOptions = {\n        ...this.categoryChartOptions,\n        axes: {\n          ...this.categoryChartOptions.axes,\n          left: {\n            ...this.categoryChartOptions.axes.left,\n            domain: [0, finalMax]\n          }\n        }\n      };\n    },\n\n    // AI Test tab methods\n    async sendAiPrompt() {\n      if (!this.aiPrompt.trim()) {\n        logger.logError('Prompt is empty', null, 'sendAiPrompt');\n        return;\n      }\n\n      this.isAiLoading = true;\n      this.aiResponse = '';\n\n      logger.logInfo(`Sending prompt to WatsonX.ai model: ${this.selectedAiModel}`, 'sendAiPrompt');\n      logger.logInfo(`Prompt length: ${this.aiPrompt.length}`, 'sendAiPrompt');\n      logger.logInfo(`Prompt (first 100 chars): ${this.aiPrompt.substring(0, 100)}...`, 'sendAiPrompt');\n      logger.logInfo(`Temperature: ${this.aiTemperature}`, 'sendAiPrompt');\n\n      // Get token from localStorage\n      const token = localStorage.getItem('token');\n      logger.logInfo(`Auth token available: ${!!token}`, 'sendAiPrompt');\n\n      // Prepare request data\n      const requestData = {\n        model_id: this.selectedAiModel,\n        prompt: this.aiPrompt,\n        temperature: parseFloat(this.aiTemperature),\n        api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n        project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de' // Same as NeuralSeek\n      };\n\n      logger.logInfo('Request data prepared', 'sendAiPrompt');\n\n      try {\n        logger.logInfo('Calling WatsonX.ai API endpoint: /api-statit2/watsonx_prompt', 'sendAiPrompt');\n\n        // Call WatsonX.ai API using fetch\n        const response = await fetch('/api-statit2/watsonx_prompt', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token ? `Bearer ${token}` : ''\n          },\n          body: JSON.stringify(requestData)\n        });\n\n        logger.logInfo(`Received response from API. Status: ${response.status}`, 'sendAiPrompt');\n\n        // Handle different response statuses\n        if (!response.ok) {\n          let errorMessage = `HTTP error! Status: ${response.status}`;\n\n          if (response.status === 401) {\n            errorMessage = 'Authentication failed. Please check the API key.';\n          } else if (response.status === 403) {\n            errorMessage = 'Access denied. Please check permissions for the project ID.';\n          } else if (response.status === 404) {\n            errorMessage = 'Model not found. Please check the model ID.';\n          }\n\n          // Try to get more error details from the response\n          try {\n            const errorText = await response.text();\n            logger.logError('Error response text', errorText, 'sendAiPrompt');\n\n            // Try to parse as JSON if possible\n            try {\n              const errorJson = JSON.parse(errorText);\n              logger.logError('Error response JSON', errorJson, 'sendAiPrompt');\n              if (errorJson.error) {\n                errorMessage += ` - ${errorJson.error}`;\n              } else {\n                errorMessage += ` - ${errorText}`;\n              }\n            } catch (jsonError) {\n              // Not JSON, use as text\n              if (errorText) {\n                errorMessage += ` - ${errorText}`;\n              }\n            }\n          } catch (textError) {\n            logger.logError('Error parsing error response', textError, 'sendAiPrompt');\n          }\n\n          throw new Error(errorMessage);\n        }\n\n        // Parse the JSON response\n        const responseText = await response.text();\n        logger.logInfo('Raw response received', 'sendAiPrompt');\n\n        let responseData;\n        try {\n          responseData = JSON.parse(responseText);\n          logger.logInfo('Successfully parsed WatsonX.ai response', 'sendAiPrompt');\n        } catch (jsonError) {\n          logger.logError('Error parsing JSON response', jsonError, 'sendAiPrompt');\n          throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);\n        }\n\n        if (responseData.status === 'success') {\n          logger.logInfo(`Success response received. Generated text length: ${responseData.generated_text ? responseData.generated_text.length : 0}`, 'sendAiPrompt');\n\n          if (responseData.generated_text) {\n            logger.logInfo(`Generated text (first 100 chars): ${responseData.generated_text.substring(0, 100)}`, 'sendAiPrompt');\n            this.aiResponse = responseData.generated_text;\n          } else {\n            logger.logError('No generated_text in success response', responseData, 'sendAiPrompt');\n            this.aiResponse = 'No response generated';\n          }\n        } else {\n          logger.logError(`Error status in response: ${responseData.status}`, responseData.error, 'sendAiPrompt');\n          this.aiResponse = `Error: ${responseData.error || 'Unknown error occurred'}`;\n        }\n      } catch (error) {\n        logger.logError('API error when calling WatsonX.ai', error, 'sendAiPrompt');\n\n        // Provide detailed error information\n        let errorMessage = error.message || 'Failed to connect to WatsonX.ai';\n\n        if (error.name === 'TypeError' && errorMessage.includes('Failed to fetch')) {\n          errorMessage = 'Network error. Please check your internet connection.';\n        }\n\n        this.aiResponse = `Error: ${errorMessage}`;\n      } finally {\n        this.isAiLoading = false;\n      }\n    },\n\n    clearAiPrompt() {\n      this.aiPrompt = '';\n      this.aiResponse = '';\n    },\n\n    useTestPrompt() {\n      this.aiPrompt = `You are a helpful assistant. Please provide a brief summary of manufacturing quality data.\n\nThe XFactor for part group ABC-123 was 2.5 in January, 1.8 in February, and 1.2 in March, showing improvement.\nThere are 2 open action items for this part group.\n\nPlease summarize this trend in 2-3 sentences.`;\n    },\n\n    async callWatsonXDirectly() {\n      this.isAiLoading = true;\n      this.aiResponse = '';\n\n      try {\n        // First, get an IAM token\n        const iamResponse = await fetch('https://iam.cloud.ibm.com/identity/token', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n            'Accept': 'application/json'\n          },\n          body: new URLSearchParams({\n            'grant_type': 'urn:ibm:params:oauth:grant-type:apikey',\n            'apikey': 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl'\n          })\n        });\n\n        if (!iamResponse.ok) {\n          throw new Error(`IAM authentication failed: ${iamResponse.status}`);\n        }\n\n        const iamData = await iamResponse.json();\n        const accessToken = iamData.access_token;\n\n        if (!accessToken) {\n          throw new Error('No access token received from IAM');\n        }\n\n        // Now call WatsonX.ai directly\n        const watsonxResponse = await fetch('/watsonx-direct/ml/v1/text/generation?version=2023-05-29', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Accept': 'application/json',\n            'Authorization': `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            model_id: 'ibm/granite-13b-instruct-v2',\n            input: this.aiPrompt,\n            parameters: {\n              temperature: 0.7,\n              max_new_tokens: 1024,\n              min_new_tokens: 0,\n              decoding_method: 'greedy'\n            },\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'\n          })\n        });\n\n        if (!watsonxResponse.ok) {\n          const errorText = await watsonxResponse.text();\n          throw new Error(`WatsonX.ai API error: ${watsonxResponse.status} - ${errorText}`);\n        }\n\n        const responseData = await watsonxResponse.json();\n        console.log('Direct WatsonX.ai response:', responseData);\n\n        if (responseData.results && responseData.results.length > 0) {\n          this.aiResponse = responseData.results[0].generated_text || 'No text generated';\n        } else {\n          this.aiResponse = 'No results returned from WatsonX.ai';\n        }\n      } catch (error) {\n        console.error('Error in direct WatsonX.ai call:', error);\n        this.aiResponse = `Error: ${error.message}`;\n      } finally {\n        this.isAiLoading = false;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.metis-xfactors-container {\n  padding: 20px;\n}\n\n/* Tab Styles */\n:deep(.bx--tabs) {\n  margin-bottom: 20px;\n}\n\n:deep(.bx--tabs__nav-item--selected) {\n  box-shadow: inset 0 2px 0 0 #0f62fe;\n}\n\n:deep(.bx--tab-content) {\n  padding: 0;\n}\n\n.content-tile {\n  padding: 20px;\n  margin-bottom: 20px;\n  background-color: #262626;\n  color: #f4f4f4;\n}\n\n/* Common Controls Styles */\n.controls-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.date-controls {\n  display: flex;\n  gap: 15px;\n  align-items: flex-start;\n}\n\n.date-picker {\n  display: flex;\n  flex-direction: column;\n}\n\n.date-picker label {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n.date-picker input {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\n.date-dropdown-container {\n  display: flex;\n  gap: 8px;\n}\n\n.date-dropdown-container select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\n.date-dropdown-container select:first-child {\n  min-width: 120px;\n}\n\n.date-dropdown-container select:last-child {\n  min-width: 80px;\n}\n\n.time-range-dropdown {\n  display: flex;\n  flex-direction: column;\n}\n\n.time-range-dropdown label {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n.time-range-dropdown select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  min-width: 150px;\n}\n\n.analyze-button {\n  padding: 8px 16px;\n  background-color: #0f62fe;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  height: 38px;\n}\n\n.analyze-button:hover {\n  background-color: #0353e9;\n}\n\n/* Main Tab Styles */\n.breakout-selection {\n  margin-bottom: 20px;\n}\n\n.breakout-dropdown {\n  position: relative;\n}\n\n.breakout-dropdown select {\n  padding: 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  min-width: 300px;\n  width: 100%;\n}\n\n.breakout-dropdown select:disabled {\n  background-color: #f4f4f4;\n  color: #999;\n  cursor: not-allowed;\n}\n\n.breakout-dropdown .loading-indicator {\n  margin-top: 5px;\n  font-size: 14px;\n  color: #0f62fe;\n}\n\n.breakout-dropdown .no-data-message {\n  margin-top: 5px;\n  font-size: 14px;\n  color: #da1e28;\n}\n\n/* Chart Styles */\n.charts-section {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  margin-bottom: 30px;\n}\n\n.chart-container, .breakout-chart-container, .breakout-bar-chart-container {\n  position: relative;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  margin-bottom: 20px;\n}\n\n.chart-container h4, .breakout-chart-container h4, .breakout-bar-chart-container h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n  color: #f4f4f4;\n}\n\n.chart-description {\n  color: #8d8d8d;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 14px;\n}\n\n.chart-wrapper {\n  height: 400px;\n  margin-bottom: 15px;\n}\n\n.threshold-info, .chart-legend {\n  display: flex;\n  gap: 20px;\n  margin-top: 10px;\n  flex-wrap: wrap;\n}\n\n.threshold-item, .legend-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.threshold-color, .legend-color {\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n}\n\n.threshold-color.sustained, .legend-color.normal {\n  background-color: #0F62FE;\n}\n\n.threshold-color.spike, .legend-color.critical {\n  background-color: #DA1E28;\n}\n\n:deep(.custom-tooltip) {\n  background-color: #262626;\n  border: 1px solid #393939;\n  padding: 10px;\n  border-radius: 4px;\n  color: #f4f4f4;\n}\n\n:deep(.custom-tooltip p) {\n  margin: 5px 0;\n}\n\n@media (min-width: 1200px) {\n  .charts-section {\n    flex-direction: row;\n    align-items: stretch;\n  }\n\n  .chart-container {\n    flex: 1;\n    min-width: 0;\n  }\n}\n\n.info-button {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #0f62fe;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n/* Alerts Styles */\n.alerts-container, .breakout-tab-alerts {\n  margin-top: 30px;\n}\n\n.alerts-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 10px;\n}\n\n.alerts-table th, .alerts-table td {\n  padding: 10px;\n  text-align: left;\n  border-bottom: 1px solid #ddd;\n}\n\n.alerts-table th {\n  background-color: #f4f4f4;\n}\n\n.status-indicator {\n  padding: 4px 8px;\n  border-radius: 12px;\n  display: inline-block;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.status-spike {\n  background-color: #ffebeb;\n  color: #da1e28;\n}\n\n.status-sustained {\n  background-color: #fff8e1;\n  color: #b28600;\n}\n\n.status-normal {\n  background-color: #e0f5ea;\n  color: #24a148;\n}\n\n.status-critical {\n  background-color: #990000;\n  color: white;\n}\n\n/* Dashboard styles */\n.dashboard-controls {\n  margin-bottom: 30px;\n}\n\n.dashboard-header {\n  margin-bottom: 20px;\n  border-bottom: 2px solid #0062ff;\n  padding-bottom: 15px;\n}\n\n.dashboard-header h3 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  font-size: 24px;\n  font-weight: 500;\n  color: #161616;\n}\n\n.dashboard-header p {\n  margin: 0;\n  color: #6f6f6f;\n  font-size: 16px;\n}\n\n.dashboard-options {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 20px;\n  background-color: #f4f4f4;\n  border-radius: 8px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-select {\n  padding: 10px 15px;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  font-size: 14px;\n  min-width: 200px;\n  background-color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.dashboard-button {\n  padding: 10px 20px;\n  background-color: #0062ff;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  transition: background-color 0.2s;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-button:hover {\n  background-color: #0353e9;\n}\n\n.button-icon {\n  font-size: 16px;\n}\n\n.heatmap-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.heatmap-header h4 {\n  margin: 0;\n}\n\n.heatmap-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.filter-container {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.filter-type, .owner-filter {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-type label, .owner-filter label {\n  font-weight: 500;\n  color: #f4f4f4;\n  white-space: nowrap;\n}\n\n/* Carbon dropdown styling */\n.carbon-dropdown {\n  background-color: white;\n}\n\n.filter-type-dropdown {\n  min-width: 120px;\n}\n\n.owner-dropdown {\n  min-width: 180px;\n}\n\n.last-updated-text {\n  margin-bottom: 15px;\n  color: #6f6f6f;\n  font-size: 14px;\n  text-align: right;\n  font-style: italic;\n}\n\n.dashboard-date-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-top: 15px;\n  padding: 15px;\n  background-color: #f4f4f4;\n  border-radius: 8px;\n  width: 100%;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.info-label {\n  font-size: 12px;\n  color: #6f6f6f;\n  font-weight: 500;\n}\n\n.info-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #161616;\n}\n\n.heatmap-container {\n  margin-top: 30px;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.heatmap-container h4 {\n  color: #f4f4f4;\n  margin-top: 0;\n  margin-bottom: 20px;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.heatmap-legend {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 20px;\n  background-color: #333333;\n  padding: 15px;\n  border-radius: 6px;\n  justify-content: flex-start;\n}\n\n.heatmap-legend .legend-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.heatmap-legend .status-indicator {\n  width: 16px;\n  height: 16px;\n  border-radius: 4px;\n  display: inline-block;\n}\n\n.heatmap-legend span {\n  color: #f4f4f4;\n  font-size: 14px;\n}\n\n.heatmap-table-container {\n  overflow-x: auto;\n  max-height: 600px;\n  overflow-y: auto;\n  border-radius: 8px;\n  margin-top: 20px;\n  background-color: #262626;\n}\n\n.heatmap-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 2px;\n  border: none;\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.heatmap-table th,\n.heatmap-table td {\n  padding: 12px;\n  text-align: center;\n  border: none;\n  font-size: 14px;\n}\n\n.heatmap-table th {\n  background-color: #333333;\n  color: #f4f4f4;\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 12px;\n}\n\n.heatmap-table .breakout-name {\n  text-align: left;\n  font-weight: bold;\n  position: sticky;\n  left: 0;\n  background-color: #333333;\n  color: #f4f4f4;\n  z-index: 2;\n  padding-left: 15px;\n  min-width: 200px;\n  max-width: 250px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.cell-content {\n  padding: 10px;\n  border-radius: 6px;\n  font-weight: bold;\n  font-size: 16px;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform 0.2s, box-shadow 0.2s;\n  cursor: pointer;\n}\n\n.cell-content:hover {\n  transform: scale(1.05);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  z-index: 5;\n}\n\n.cell-normal {\n  background-color: #0062ff;\n  color: white;\n  opacity: 0.8;\n}\n\n.cell-sustained {\n  background-color: #ff9a00;\n  color: white;\n}\n\n.cell-spike {\n  background-color: #fa4d56;\n  color: white;\n}\n\n.cell-critical {\n  background-color: #da1e28;\n  color: white;\n}\n\n.cell-empty {\n  background-color: #393939;\n  color: #8d8d8d;\n}\n\n.no-data {\n  color: #8d8d8d;\n  font-style: italic;\n  font-size: 14px;\n}\n\n/* Blinking animation for critical cells */\n@keyframes blink {\n  0% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }\n  50% { opacity: 0.8; box-shadow: 0 0 15px rgba(250, 77, 86, 0.7); }\n  100% { opacity: 1; box-shadow: 0 0 0 rgba(250, 77, 86, 0); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n\n.blink {\n  animation: blink 2s infinite, pulse 2s infinite;\n}\n\n/* Tooltip styles */\n.cell-tooltip {\n  position: fixed;\n  background-color: #161616;\n  color: white;\n  padding: 16px;\n  border-radius: 8px;\n  z-index: 1000;\n  min-width: 200px;\n  max-width: 300px;\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\n  border: 1px solid #393939;\n  font-size: 14px;\n  backdrop-filter: blur(5px);\n}\n\n.tooltip-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  padding-bottom: 8px;\n}\n\n.tooltip-header strong {\n  font-size: 16px;\n  color: #f4f4f4;\n}\n\n.tooltip-header span {\n  font-size: 14px;\n  color: #8d8d8d;\n}\n\n.tooltip-content p {\n  margin: 8px 0;\n  font-size: 14px;\n  display: flex;\n  justify-content: space-between;\n}\n\n.tooltip-content p:before {\n  content: \"•\";\n  margin-right: 8px;\n  color: #0062ff;\n}\n\n.tooltip-content p:nth-child(2):before {\n  color: #fa4d56;\n}\n\n.no-data-message {\n  padding: 20px;\n  text-align: center;\n  color: #8d8d8d;\n}\n\n/* Modal styles */\n.info-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #262626;\n  color: #f4f4f4;\n  padding: 20px;\n  border-radius: 4px;\n  max-width: 600px;\n  position: relative;\n}\n\n.ai-summary-modal {\n  width: 90%;\n  max-width: 900px;\n}\n\n.ai-summary-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 30px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.ai-summary-content {\n  line-height: 1.6;\n}\n\n.close-button {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  font-size: 24px;\n  cursor: pointer;\n  color: #aaa;\n}\n\n.close-button:hover {\n  color: #f4f4f4;\n}\n\n.ai-tooltip {\n  position: absolute;\n  z-index: 999;\n  background-color: #0f62fe;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  pointer-events: none;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n.ai-tooltip-content {\n  white-space: nowrap;\n}\n\n.breakout-name {\n  position: relative;\n  cursor: pointer;\n}\n\n.breakout-name:hover {\n  text-decoration: underline;\n  color: #0f62fe;\n}\n\n.breakout-name-content {\n  display: flex;\n  align-items: center;\n}\n\n.ai-summary-indicator {\n  margin-left: 8px;\n}\n\n.loading-dots {\n  display: inline-block;\n  position: relative;\n  width: 40px;\n  height: 10px;\n}\n\n.loading-dots:after {\n  content: '...';\n  position: absolute;\n  animation: dots 1.5s infinite;\n  font-weight: bold;\n  color: #0f62fe;\n}\n\n@keyframes dots {\n  0%, 20% { content: '.'; }\n  40% { content: '..'; }\n  60%, 100% { content: '...'; }\n}\n\n.modal-content h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n}\n\n.modal-content h4 {\n  margin-top: 15px;\n  margin-bottom: 5px;\n}\n\n.modal-content p {\n  margin-bottom: 10px;\n}\n\n.modal-content ul {\n  margin-top: 5px;\n  padding-left: 20px;\n}\n\n/* Breakout Tab Specific Styles */\n.breakout-tab-controls {\n  margin-bottom: 20px;\n}\n\n.breakout-tab-header {\n  margin-bottom: 15px;\n}\n\n.breakout-tab-header h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.breakout-tab-header p {\n  margin: 0;\n  color: #8d8d8d;\n}\n\n.content-tile {\n  background-color: #262626;\n  color: #f4f4f4;\n}\n\n.breakout-tab-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 15px;\n  align-items: flex-end;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.breakout-group-select, .time-range-select {\n  display: flex;\n  flex-direction: column;\n}\n\n.breakout-group-select label, .time-range-select label {\n  margin-bottom: 5px;\n  font-size: 14px;\n}\n\n/* Carbon Dropdown Styling */\n.date-dropdown-container {\n  display: flex;\n  gap: 10px;\n}\n\n.month-dropdown, .year-dropdown {\n  min-width: 150px;\n}\n\n.time-range-dropdown .bx--dropdown {\n  min-width: 200px;\n}\n\n.breakout-dropdown .bx--dropdown {\n  min-width: 250px;\n}\n\n.carbon-dropdown {\n  margin-bottom: 10px;\n}\n\n/* Button styling */\n.bx--btn {\n  margin-top: 10px;\n}\n\n.breakout-tab-date-controls {\n  display: flex;\n  gap: 15px;\n}\n\n.breakout-tab-content {\n  margin-top: 20px;\n}\n\n/* Part Numbers Section Styles */\n.part-numbers-section {\n  margin-top: 30px;\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n.part-numbers-section h4 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  color: #f4f4f4;\n}\n\n.part-count {\n  color: #8d8d8d;\n  margin-bottom: 15px;\n  font-size: 14px;\n}\n\n.part-numbers-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n  max-height: 200px;\n  overflow-y: auto;\n  padding-right: 10px;\n}\n\n.part-number-item {\n  background-color: #393939;\n  border-radius: 4px;\n  padding: 6px 12px;\n  font-size: 13px;\n  color: #f4f4f4;\n  font-family: 'IBM Plex Mono', monospace;\n}\n\n/* Date Controls Container Styles */\n.date-controls-container {\n  margin-bottom: 15px;\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.date-controls-layout {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex-wrap: nowrap;\n  gap: 15px;\n}\n\n.custom-range-section {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.date-inputs {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.date-section-label {\n  color: #f4f4f4;\n  font-size: 14px;\n  white-space: nowrap;\n}\n\n.date-dropdown-container {\n  display: flex;\n  gap: 5px;\n}\n\n.month-dropdown {\n  width: 120px;\n}\n\n.year-dropdown {\n  width: 90px;\n}\n\n.date-section-title {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #f4f4f4;\n}\n\n.date-separator {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  margin: 0 5px;\n}\n\n.date-separator::before, .date-separator::after {\n  content: \"\";\n  width: 1px;\n  height: 20px;\n  background-color: #393939;\n  display: none;\n}\n\n.or-text {\n  padding: 5px 10px;\n  font-weight: 600;\n  color: #f4f4f4;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.quick-select-section {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.quick-select-dropdown {\n  width: 150px;\n}\n\n.selected-range {\n  background-color: #0f62fe !important;\n  border-color: #0f62fe !important;\n  color: #ffffff !important;\n}\n\n.analyze-button-container {\n  display: flex;\n  align-items: center;\n  margin-left: 10px;\n}\n\n.selected-date-range {\n  color: #f4f4f4;\n}\n\n.global-controls-tile {\n  background-color: #333333;\n  color: #f4f4f4;\n}\n\n/* Category Analysis Tab Styles */\n.category-tab-controls {\n  margin-bottom: 20px;\n}\n\n.category-tab-header {\n  margin-bottom: 15px;\n}\n\n.category-tab-header h4 {\n  margin-bottom: 5px;\n  font-size: 18px;\n}\n\n.category-tab-options {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.category-chart-container {\n  margin-bottom: 20px;\n}\n\n.category-tab-part-numbers {\n  margin-top: 20px;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #262626;\n}\n\n.part-numbers-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.part-number-tag {\n  background-color: #393939;\n  border-radius: 4px;\n  padding: 5px 10px;\n  font-size: 14px;\n  color: #f4f4f4;\n}\n\n/* Failure Modes Modal Styles */\n.failure-modes-modal {\n  max-width: 800px;\n}\n\n.failure-modes-content {\n  padding: 20px 0;\n}\n\n.failure-modes-chart-container {\n  height: 400px;\n}\n\n.custom-tooltip {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 10px;\n  font-size: 14px;\n  color: #f4f4f4;\n  max-width: 200px;\n}\n\n/* Group 2 Tab Styles */\n.group2-tab-container {\n  padding: 20px 0;\n}\n\n.group2-tab-header {\n  margin-bottom: 20px;\n}\n\n.group2-tab-header h4 {\n  margin-bottom: 5px;\n  font-size: 18px;\n}\n\n.group2-tab-controls {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.analysis-grid {\n  margin-top: 20px;\n}\n\n.analysis-table {\n  width: 100%;\n  border-collapse: collapse;\n  border: 1px solid #393939;\n}\n\n.analysis-table th,\n.analysis-table td {\n  padding: 12px;\n  text-align: center;\n  border: 1px solid #393939;\n}\n\n.analysis-table th {\n  background-color: #262626;\n  font-weight: 600;\n}\n\n.analysis-table td:first-child {\n  text-align: left;\n  font-weight: 600;\n}\n\n/* Custom Date Modal Styles */\n.custom-date-content {\n  padding: 20px 0;\n}\n\n.date-inputs {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.date-section {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.date-section-label {\n  width: 100px;\n  font-weight: 600;\n}\n\n.custom-date-buttons {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n}\n\n/* Root Cause Section Styles */\n.root-cause-section {\n  margin-top: 30px;\n  padding: 20px;\n  background-color: #161616;\n  border: 1px solid #393939;\n  border-radius: 4px;\n}\n\n.section-header {\n  margin-bottom: 20px;\n  border-bottom: 1px solid #393939;\n  padding-bottom: 10px;\n}\n\n.section-header h4 {\n  font-size: 18px;\n  font-weight: 600;\n  color: #f4f4f4;\n}\n\n.ai-summary-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #161616;\n  border-left: 4px solid #0f62fe;\n  border-radius: 0 4px 4px 0;\n}\n\n.ai-summary-content {\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.ai-summary-content p {\n  margin: 0;\n}\n\n.ai-summary-loading {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n}\n\n.loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #0f62fe;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.root-cause-chart-container {\n  margin-bottom: 20px;\n}\n\n.root-cause-chart-container h5 {\n  margin-bottom: 10px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.chart-wrapper {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 15px;\n}\n\n/* Critical Issues Section Styles */\n.critical-issues-section {\n  margin-top: 20px;\n  padding: 20px;\n  background-color: #262626;\n  border: 1px solid #fa4d56;\n  border-radius: 4px;\n}\n\n.critical-issues-section h5 {\n  color: #fa4d56;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.critical-issues-summary {\n  margin-bottom: 20px;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n/* Critical Issue Item Styles */\n.critical-issue-item {\n  margin-bottom: 10px;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  background-color: #161616;\n  overflow: hidden;\n}\n\n.critical-issue-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex-wrap: wrap;\n  padding: 12px 16px;\n  cursor: pointer;\n  background-color: #161616;\n  transition: background-color 0.2s;\n}\n\n.critical-issue-header:hover {\n  background-color: #262626;\n}\n\n.critical-issue-title {\n  font-weight: 600;\n  min-width: 120px;\n}\n\n.month-tag {\n  margin-right: 10px;\n}\n\n.critical-issue-multiplier {\n  color: #fa4d56;\n  font-weight: 500;\n}\n\n.critical-issue-status {\n  margin-left: auto;\n}\n\n.expand-icon {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #8d8d8d;\n}\n\n.critical-issue-content {\n  padding: 16px;\n  background-color: #262626;\n  border-top: 1px solid #393939;\n}\n\n.critical-issue-ai-description {\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #161616;\n  border-left: 4px solid #0f62fe;\n  border-radius: 0 4px 4px 0;\n}\n\n.critical-issue-ai-description p {\n  line-height: 1.5;\n  font-size: 14px;\n  margin: 0;\n}\n\n/* Update Form Styles */\n.critical-issue-update-form {\n  margin-bottom: 20px;\n}\n\n.critical-issue-update-form h6 {\n  margin-bottom: 10px;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.save-update-button {\n  margin-top: 10px;\n}\n\n/* Previous Updates Styles */\n.previous-updates {\n  margin-top: 20px;\n}\n\n.previous-updates h6 {\n  margin-bottom: 10px;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n/* Carbon Component Overrides */\n\n.cv-tile {\n  background-color: #262626;\n  padding: 16px;\n}\n\n.cv-structured-list {\n  margin-top: 10px;\n  background-color: #262626;\n}\n\n.cv-structured-list-heading {\n  font-size: 12px;\n  color: #8d8d8d;\n}\n\n.cv-structured-list-row {\n  border-bottom: 1px solid #393939;\n}\n\n.cv-structured-list-cell {\n  font-size: 14px;\n  padding: 10px;\n}\n\n/* Custom tooltip styles */\n::v-deep .custom-tooltip {\n  background-color: #262626;\n  border: 1px solid #393939;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 14px;\n  color: #f4f4f4;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n::v-deep .custom-tooltip p {\n  margin: 5px 0;\n  line-height: 1.4;\n}\n\n::v-deep .custom-tooltip p:first-child {\n  margin-top: 0;\n  font-weight: 600;\n}\n\n::v-deep .custom-tooltip p:last-child {\n  margin-bottom: 0;\n}\n\n.tooltip-hint {\n  font-style: italic;\n  color: #8d8d8d;\n  margin-top: 8px;\n}\n\n/* AI Test Tab Styles */\n.ai-test-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.ai-test-header {\n  margin-bottom: 10px;\n}\n\n.ai-test-header h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.ai-test-header p {\n  color: #8d8d8d;\n  margin-top: 0;\n}\n\n.ai-test-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 20px;\n  padding: 15px;\n  background-color: #333333;\n  border-radius: 4px;\n  border: 1px solid #393939;\n}\n\n.model-selection, .temperature-control {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.model-selection {\n  flex: 1;\n  min-width: 250px;\n}\n\n.temperature-control {\n  flex: 1;\n  min-width: 200px;\n}\n\n.temperature-control input[type=\"range\"] {\n  width: 100%;\n  margin-top: 5px;\n}\n\n.prompt-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.prompt-container label {\n  font-weight: bold;\n}\n\n.prompt-container textarea {\n  width: 100%;\n  padding: 10px;\n  border-radius: 4px;\n  border: 1px solid #393939;\n  background-color: #333333;\n  color: #f4f4f4;\n  font-family: 'IBM Plex Mono', monospace;\n  resize: vertical;\n}\n\n.prompt-buttons {\n  display: flex;\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.response-container {\n  margin-top: 20px;\n  min-height: 200px;\n  border-radius: 4px;\n  border: 1px solid #393939;\n  background-color: #333333;\n  padding: 15px;\n}\n\n.ai-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.ai-response {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.ai-response h5 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.response-content {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 15px;\n  overflow: auto;\n  max-height: 400px;\n}\n\n.response-content pre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  margin: 0;\n  font-family: 'IBM Plex Mono', monospace;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.no-response-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #8d8d8d;\n  font-style: italic;\n}\n\n.view-data-cell {\n  text-align: center;\n  white-space: nowrap;\n}\n\n.dashboard-critical-issues-summary {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 10px 15px;\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.dashboard-critical-issues-summary h4 {\n  margin: 0;\n  color: #f4f4f4;\n  font-size: 16px;\n}\n\n.dashboard-critical-issues-summary .critical-count {\n  color: #fa4d56;\n  font-weight: bold;\n  margin-left: 5px;\n}\n\n.critical-issues-tooltip {\n  color: #fa4d56;\n  font-weight: bold;\n  margin-top: 10px;\n}\n\n.click-to-view-tooltip {\n  color: #0f62fe;\n  font-style: italic;\n  margin-top: 5px;\n}\n</style>\n"]}]}