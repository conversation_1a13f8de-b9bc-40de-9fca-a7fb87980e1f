<template>
  <div class="pqe-dashboard-container">
    <div class="content-wrapper">
      <!-- Header Section with Critical Issues Summary -->
      <div class="dashboard-header">
        <h3>PQE Owner Dashboard</h3>
        <div class="last-updated-text">
          Last Updated: {{ new Date().toLocaleString() }}
        </div>
      </div>

      <!-- Key Metrics Section -->
      <div class="key-metrics-section">
        <div class="metric-card">
          <div class="metric-icon new-issues">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">New Critical Issues</div>
            <div class="metric-value">{{ newCriticalIssues.length }}</div>
            <div class="metric-description">This Month</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon critical-issues">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
              <path d="M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Critical Issues</div>
            <div class="metric-value">{{ unresolvedCriticalIssues.length }}</div>
            <div class="metric-description">Need Resolution</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon validated">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Validated Fails</div>
            <div class="metric-value">{{ validatedCount }}</div>
            <div class="metric-description">This Month</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon unvalidated">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
              <path d="M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z"></path>
              <path d="M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z"></path>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-label">Unvalidated Fails</div>
            <div class="metric-value">{{ unvalidatedCount }}</div>
            <div class="metric-description">Need Validation</div>
          </div>
        </div>
      </div>

      <!-- Main Content Layout -->
      <div class="dashboard-main-content">
        <!-- Left Column -->
        <div class="dashboard-column">
          <!-- Critical Issues Section -->
          <div class="section-card">
            <div class="section-header critical-issues-header" @click="toggleCriticalIssuesExpanded">
              <div class="section-title-container">
                <h4 class="section-title">Critical Issues</h4>
                <div class="section-subtitle">Issues requiring immediate attention</div>
              </div>
              <div class="section-controls">
                <div class="status-indicator" :class="{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }">
                  {{ unresolvedCriticalIssues.length }}
                </div>
                <div class="expand-indicator" :class="{ 'expanded': isCriticalIssuesExpanded }">
                  <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                    <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div v-if="isCriticalIssuesExpanded" class="section-content">
              <!-- Filter Controls in a Single Row -->
              <div class="filter-container">
                <div class="filter-header">
                  <h5 class="filter-title">Filter Issues</h5>
                  <cv-button
                    kind="ghost"
                    size="small"
                    class="clear-filters-button"
                    @click="clearFilters"
                    v-if="isFiltersActive"
                  >
                    Clear Filters
                  </cv-button>
                </div>

                <div class="filter-controls">
                  <div class="filter-group">
                    <label for="severity-dropdown" class="filter-label">Severity:</label>
                    <cv-dropdown
                      id="severity-dropdown"
                      v-model="severityFilter"
                      @change="handleSeverityFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Severities</cv-dropdown-item>
                      <cv-dropdown-item value="high">High</cv-dropdown-item>
                      <cv-dropdown-item value="medium">Medium</cv-dropdown-item>
                    </cv-dropdown>
                  </div>

                  <div class="filter-group">
                    <label for="analysis-dropdown" class="filter-label">Analysis Type:</label>
                    <cv-dropdown
                      id="analysis-dropdown"
                      v-model="analysisTypeFilter"
                      @change="handleAnalysisFilterChange"
                      class="filter-dropdown"
                    >
                      <cv-dropdown-item value="all">All Analysis Types</cv-dropdown-item>
                      <cv-dropdown-item value="Root Cause">Root Cause</cv-dropdown-item>
                    </cv-dropdown>
                  </div>
                </div>
              </div>

              <!-- Critical Issues List -->
              <div v-if="filteredCriticalIssues.length > 0" class="issues-list">
                <div
                  v-for="issue in filteredCriticalIssues"
                  :key="issue.id"
                  class="issue-card"
                  @click="toggleIssueExpanded(issue)"
                  :class="{ 'expanded': isIssueExpanded(issue.id) }"
                >
                  <div class="issue-header">
                    <div class="issue-tags">
                      <cv-tag
                        :kind="issue.severity === 'high' ? 'red' : 'magenta'"
                        :label="issue.severity === 'high' ? 'High' : 'Medium'"
                      />
                      <cv-tag
                        kind="purple"
                        class="analysis-tag"
                        :label="issue.analysisType"
                      />
                    </div>
                    <span class="issue-title">{{ issue.category }}</span>
                    <div class="issue-metadata">
                      <cv-tag
                        kind="cool-gray"
                        class="month-tag"
                        :label="issue.month"
                      />
                      <span class="issue-multiplier" :class="issue.severity === 'high' ? 'high-severity' : 'medium-severity'">
                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}
                      </span>
                    </div>
                    <div class="expand-indicator">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z"></path>
                      </svg>
                    </div>
                  </div>

                  <div class="issue-content" v-if="isIssueExpanded(issue.id)">
                    <div class="ai-description">
                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                    </div>
                    <div class="issue-actions">
                      <cv-button
                        kind="tertiary"
                        size="small"
                        @click.stop="viewIssueDetails(issue)"
                      >
                        View Data
                      </cv-button>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="updateIssue(issue)"
                      >
                        Update
                      </cv-button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-data-message">
                No critical issues found matching the selected filters.
              </div>
            </div>
          </div>

          <!-- Breakout Group Alerts Section -->
          <div class="section-card">
            <div class="section-header">
              <div class="section-title-container">
                <h4 class="section-title">Breakout Group Alerts</h4>
                <div class="section-subtitle">
                  Current Month: {{ currentMonth ? new Date(currentMonth + '-01').toLocaleString('en-US', { month: 'long', year: 'numeric' }) : 'Loading...' }}
                </div>
              </div>
              <div class="section-controls">
                <cv-button
                  kind="primary"
                  size="small"
                  @click="showAllAlerts = !showAllAlerts"
                  v-if="sortedCriticalBreakoutGroups.length > 0"
                >
                  {{ showAllAlerts ? 'Show Critical Only' : 'See All Alerts' }}
                </cv-button>
                <cv-button
                  kind="primary"
                  size="small"
                  @click="showAllBreakoutGroups = !showAllBreakoutGroups"
                  style="margin-left: 8px;"
                >
                  {{ showAllBreakoutGroups ? 'Hide Normal Groups' : 'Show All Status' }}
                </cv-button>
              </div>
            </div>

            <!-- Loading State -->
            <div v-if="isLoadingBreakoutGroups" class="loading-container">
              <cv-inline-loading
                status="active"
                loading-text="Loading breakout group alerts..."
              ></cv-inline-loading>
            </div>

            <!-- Critical Alerts (Always Visible) -->
            <div v-else-if="sortedCriticalBreakoutGroups.length > 0" class="breakout-groups-grid">
              <div
                v-for="group in sortedHighPriorityGroups"
                :key="group.name"
                class="breakout-group-card highlighted"
                :class="[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]"
              >
                <div class="breakout-group-header">
                  <h5 class="breakout-group-name">{{ group.name }}</h5>
                </div>

                <div class="breakout-group-metrics">
                  <div class="xfactor-metrics">
                    <div class="xfactor-row">
                      <span class="xfactor-label">Current Month X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.xFactor)">
                        {{ group.xFactor.toFixed(1) }}
                      </span>
                    </div>
                    <div class="xfactor-row">
                      <span class="xfactor-label">Sustained X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.sustainedXFactor)">
                        {{ group.sustainedXFactor.toFixed(1) }}
                      </span>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon" :class="getXFactorSeverityClass(group.xFactor)">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
                        <circle cx="16" cy="16" r="6"></circle>
                      </svg>
                    </div>
                    <div class="status-info">
                      <div class="status-label">Status</div>
                      <div class="status-value">{{ group.status }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Medium Priority Alerts (Shown when "See All Alerts" is clicked) -->
            <div v-if="showAllAlerts && sortedMediumPriorityGroups.length > 0" class="breakout-groups-grid medium-priority-groups">
              <div
                v-for="group in sortedMediumPriorityGroups"
                :key="group.name"
                class="breakout-group-card"
                :class="[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]"
              >
                <div class="breakout-group-header">
                  <h5 class="breakout-group-name">{{ group.name }}</h5>
                </div>

                <div class="breakout-group-metrics">
                  <div class="xfactor-metrics">
                    <div class="xfactor-row">
                      <span class="xfactor-label">Current Month X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.xFactor)">
                        {{ group.xFactor.toFixed(1) }}
                      </span>
                    </div>
                    <div class="xfactor-row">
                      <span class="xfactor-label">Sustained X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.sustainedXFactor)">
                        {{ group.sustainedXFactor.toFixed(1) }}
                      </span>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon" :class="getXFactorSeverityClass(group.xFactor)">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
                        <circle cx="16" cy="16" r="6"></circle>
                      </svg>
                    </div>
                    <div class="status-info">
                      <div class="status-label">Status</div>
                      <div class="status-value">{{ group.status }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- All Normal Groups (Shown when "Show All Status" is clicked) -->
            <div v-if="showAllBreakoutGroups && normalBreakoutGroups.length > 0" class="breakout-groups-grid normal-groups">
              <div
                v-for="group in normalBreakoutGroups"
                :key="group.name"
                class="breakout-group-card"
                :class="[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]"
              >
                <div class="breakout-group-header">
                  <h5 class="breakout-group-name">{{ group.name }}</h5>
                </div>

                <div class="breakout-group-metrics">
                  <div class="xfactor-metrics">
                    <div class="xfactor-row">
                      <span class="xfactor-label">Current Month X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.xFactor)">
                        {{ group.xFactor.toFixed(1) }}
                      </span>
                    </div>
                    <div class="xfactor-row">
                      <span class="xfactor-label">Sustained X-Factor:</span>
                      <span class="xfactor-value" :class="getXFactorSeverityClass(group.sustainedXFactor)">
                        {{ group.sustainedXFactor.toFixed(1) }}
                      </span>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon" :class="getXFactorSeverityClass(group.xFactor)">
                      <svg width="16" height="16" viewBox="0 0 32 32" fill="currentColor">
                        <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"></path>
                        <circle cx="16" cy="16" r="6"></circle>
                      </svg>
                    </div>
                    <div class="status-info">
                      <div class="status-label">Status</div>
                      <div class="status-value">{{ group.status }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="sortedCriticalBreakoutGroups.length === 0" class="no-data-message">
              No alerts found. All breakout groups are operating within normal parameters.
            </div>
          </div>

          <!-- Item Tracking Section -->
          <div class="section-card">
            <div class="section-header">
              <div class="section-title-container">
                <h4 class="section-title">Item Tracking</h4>
                <div class="section-subtitle">
                  Action items related to current month's critical breakout groups
                </div>
              </div>
              <div class="section-controls">
                <cv-button
                  kind="primary"
                  size="small"
                  @click="goToActionTracker"
                  class="action-button"
                >
                  Go to Action Tracker
                </cv-button>
              </div>
            </div>

            <div v-if="isLoadingTrackedItems" class="loading-container">
              <cv-inline-loading
                status="active"
                loading-text="Loading tracked items..."
              ></cv-inline-loading>
            </div>

            <div v-else-if="trackedItems.length > 0" class="tracked-items-list">
              <div
                v-for="item in trackedItems"
                :key="item.id"
                class="tracked-item-card"
                :class="[getItemStatusClass(item.status), {'critical-item': item.isCritical}]"
              >
                <div class="tracked-item-header">
                  <h5 class="tracked-item-name">{{ item.name }}</h5>
                  <div class="tracked-item-status">
                    <span class="status-badge" :class="getItemStatusClass(item.status)">{{ item.status }}</span>
                    <cv-tag v-if="item.isCritical" kind="red" label="Critical" class="critical-tag" />
                  </div>
                </div>

                <div class="tracked-item-details">
                  <div class="detail-row">
                    <span class="detail-label">Owner:</span>
                    <span class="detail-value">{{ item.owner }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Due Date:</span>
                    <span class="detail-value">{{ item.dueDate }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Related Group:</span>
                    <span class="detail-value related-group" :class="{'critical-group': isCriticalGroup(item.relatedIssue)}">
                      {{ item.relatedIssue }}
                      <cv-tag v-if="isCriticalGroup(item.relatedIssue)" kind="blue" label="Current Month" class="month-tag" />
                    </span>
                  </div>
                  <div class="detail-row" v-if="item.progress !== undefined">
                    <span class="detail-label">Progress:</span>
                    <div class="progress-container">
                      <div class="progress-bar" :style="{ width: item.progress + '%' }"></div>
                      <span class="progress-text">{{ item.progress }}%</span>
                    </div>
                  </div>
                </div>

                <div class="tracked-item-footer">
                  <cv-button
                    kind="ghost"
                    size="small"
                    @click.stop="viewItemDetails(item)"
                  >
                    View in Action Tracker
                  </cv-button>
                </div>
              </div>
            </div>

            <div v-else class="no-data-message">
              No tracked items found for current month's critical breakout groups. Items will appear here when they are linked to the action tracker.
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="dashboard-column">
          <!-- Validation Section -->
          <div class="section-card">
            <div class="section-header">
              <h4 class="section-title">Validation Status</h4>
              <div class="last-updated-text">Daily Status</div>
            </div>

            <!-- Validation Chart -->
            <div class="chart-container">
              <div v-if="validationChartData.length > 0">
                <div class="chart-wrapper">
                  <div id="validationChart" class="carbon-chart-container"></div>
                </div>
              </div>
              <div v-else class="no-data-message">
                <cv-inline-loading
                  status="active"
                  loading-text="Loading validation data..."
                ></cv-inline-loading>
              </div>
            </div>

            <div class="section-footer">
              <p class="section-description">
                Go to the validation page to review and validate new fails.
                Currently <strong>{{ unvalidatedCount }}</strong> fails need validation.
              </p>
              <cv-button
                kind="primary"
                @click="goToValidationPage"
                class="action-button"
              >
                Go to Validation Page
              </cv-button>
            </div>
          </div>

          <!-- Root Cause Analysis Section -->
          <div class="section-card">
            <div class="section-header">
              <h4 class="section-title">Root Cause Analysis</h4>
              <div class="last-updated-text">Current Month Analysis</div>
            </div>

            <!-- Root Cause Chart -->
            <div class="chart-container">
              <RootCauseChart
                :data="rootCauseChartData"
                :loading="isRootCauseDataLoading"
                :height="'400px'"
                title="Root Cause Categories by Month"
                @bar-click="handleRootCauseBarClick"
              />
            </div>

            <div class="section-footer">
              <p class="section-description">
                Hover over bars for more info.
              </p>
            </div>
          </div>

          <!-- Advanced Analysis Section -->
          <div class="section-card">
            <div class="section-header">
              <h4 class="section-title">Advanced Analysis</h4>
            </div>
            <div class="analysis-controls">
              <div class="control-row">
                <div class="control-group">
                  <label for="analysis-type-dropdown" class="control-label">Analysis Type:</label>
                  <cv-dropdown
                    id="analysis-type-dropdown"
                    v-model="selectedAnalysisType"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="Root Cause">Root Cause Analysis</cv-dropdown-item>
                  </cv-dropdown>
                </div>
                <cv-button
                  kind="primary"
                  @click="goToGroupAnalysis"
                  class="action-button"
                >
                  Go to Analysis
                </cv-button>
              </div>
            </div>
            <div class="section-footer">
              <p class="section-description">
                Select an analysis type and click "Go to Analysis" to view detailed breakout group analysis in the Group tab.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Critical Issue Details Modal -->
    <cv-modal
      :visible="showIssueModal"
      @modal-hidden="closeIssueModal"
      class="issue-details-modal"
      :size="'lg'"
    >
      <template slot="title">Critical Issue Details</template>
      <template slot="content">
        <div v-if="selectedIssue" class="issue-details-content">
          <h4>{{ selectedIssue.category }} - {{ selectedIssue.month }}</h4>
          <div class="issue-chart-container">
            <ccv-simple-bar-chart
              v-if="issueChartData.length > 0"
              :data="issueChartData"
              :options="issueChartOptions"
            ></ccv-simple-bar-chart>
          </div>
          <div class="issue-ai-description">
            <h5>AI Analysis</h5>
            <p>{{ selectedIssue.aiDescription }}</p>
          </div>
          <div class="issue-comments">
            <cv-text-area
              v-model="selectedIssue.comment"
              label="Comments"
              placeholder="Add your comments here..."
            ></cv-text-area>
          </div>
        </div>
      </template>
      <template slot="footer">
        <cv-button
          kind="secondary"
          @click="closeIssueModal"
        >
          Cancel
        </cv-button>
        <cv-button
          kind="primary"
          @click="updateIssue"
        >
          Update
        </cv-button>
      </template>
    </cv-modal>
  </div>
</template>

<script>
import {
  CvButton,
  CvModal,
  CvTag,
  CvTextArea,
  CvInlineLoading,
  CvDropdown,
  CvDropdownItem
} from '@carbon/vue';

// Import Carbon Charts
import { ComboChart } from "@carbon/charts";
import "@carbon/charts/styles.css";

// Import Root Cause Chart component
import RootCauseChart from '@/components/RootCauseChart/RootCauseChart.vue';

export default {
  name: 'PQEDashboard',
  components: {
    CvButton,
    CvModal,
    CvTag,
    CvTextArea,
    CvInlineLoading,
    CvDropdown,
    CvDropdownItem,
    RootCauseChart
  },
  data() {
    return {
      // Critical Issues Data
      newCriticalIssues: [],
      unresolvedCriticalIssues: [],
      expandedIssueIds: [], // Track which issues are expanded
      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded

      // Filtering
      selectedFilters: {
        severity: [],
        analysisType: []
      },
      severityFilter: 'all',
      analysisTypeFilter: 'all',

      // Advanced Analysis
      selectedAnalysisType: 'Root Cause', // Default analysis type
      selectedBreakoutGroup: null, // Will be set when a breakout group is selected

      // Chart instances
      categorySummaryChart: null,
      validationChart: null,

      // Current Month
      currentMonth: '', // Will be set to current month in YYYY-MM format

      // Loading States
      isLoadingBreakoutGroups: false,
      isLoadingTrackedItems: false,

      // Validation Data
      validatedCount: 0,
      unvalidatedCount: 0,
      validationChartData: [],

      // Root Cause Chart Data
      rootCauseChartData: [],
      isRootCauseDataLoading: false,
      validationChartOptions: {
        title: 'Daily Status',
        axes: {
          left: {
            title: 'Count',
            mapsTo: 'value',
            domain: [0, 150], // Increased range to accommodate volume data
            scaleType: 'linear'
          },
          right: {
            title: 'Yield (%)',
            mapsTo: 'value',
            domain: [0, 100],
            scaleType: 'linear',
            correspondingDatasets: [
              'Daily Yield',
              'Cumulative Yield',
              'Target Yield'
            ]
          },
          bottom: {
            title: 'Day',
            mapsTo: 'date',
            scaleType: 'time'
          }
        },
        height: '350px',
        legend: {
          alignment: 'center',
          enabled: true,
          fontColor: '#f4f4f4' // White text for legend
        },
        color: {
          scale: {
            'Validated': '#0062ff', // Blue
            'Unvalidated': '#ff832b', // Orange
            'Volume': '#24a148', // Green
            'Daily Yield': '#6929c4', // Purple
            'Cumulative Yield': '#1192e8', // Light blue
            'Target Yield': '#fa4d56' // Red
          }
        },
        toolbar: {
          enabled: false
        },
        tooltip: {
          enabled: true
        },
        animations: true,
        data: {
          groupMapsTo: 'group'
        },
        theme: 'g100', // Use darkest theme for better contrast
        grid: {
          x: {
            enabled: false
          },
          y: {
            enabled: true
          }
        },
        comboChartTypes: [
          {
            type: 'stacked-bar',
            correspondingDatasets: ['Validated', 'Unvalidated'],
            options: {
              fillOpacity: 0.8
            }
          },
          {
            type: 'line',
            correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],
            options: {
              points: {
                radius: 3
              },
              strokeWidth: 2
            }
          }
        ],
        thresholds: [
          {
            axis: 'right',
            value: 95, // Target yield threshold
            label: 'Target Yield (95%)',
            fillColor: '#fa4d56',
            opacity: 0.1
          }
        ]
      },

      // Breakout Groups Data
      breakoutGroups: [],
      showAllBreakoutGroups: false, // Toggle for showing all breakout groups
      showAllAlerts: false, // Toggle for showing all alerts (including medium priority)

      // Item Tracking Data
      trackedItems: [],

      // Modal Data
      showIssueModal: false,
      selectedIssue: null,
      issueChartData: [],
      issueChartOptions: {
        title: 'Issue Details',
        axes: {
          left: {
            title: 'X-Factor',
            mapsTo: 'value',
            domain: [0, 4]
          },
          bottom: {
            title: 'Month',
            mapsTo: 'month',
            scaleType: 'labels'
          }
        },
        height: '300px',
        legend: {
          enabled: false
        },
        color: {
          scale: {
            'X-Factor': '#0062ff'
          }
        },
        toolbar: {
          enabled: true
        },
        tooltip: {
          enabled: true,
          customHTML: (data) => {
            return `
              <div style="padding: 10px; background: #f4f4f4; border-radius: 4px;">
                <div style="font-weight: bold; margin-bottom: 5px;">${data[0].data.month}</div>
                <div>X-Factor: ${data[0].value.toFixed(2)}</div>
              </div>
            `;
          }
        },
        animations: true,
        thresholds: [
          {
            value: 1.5,
            label: 'Sustained Problem Threshold',
            fillColor: '#ff9a00',
            opacity: 0.1
          },
          {
            value: 3.0,
            label: 'Critical Spike Threshold',
            fillColor: '#fa4d56',
            opacity: 0.1
          }
        ]
      }
    };
  },
  mounted() {
    this.loadDashboardData();
  },
  computed: {
    // Filtered critical issues based on selected filters
    filteredCriticalIssues() {
      // If no filters are selected, return all issues
      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {
        return this.unresolvedCriticalIssues;
      }

      // Apply filters
      return this.unresolvedCriticalIssues.filter(issue => {
        // Check if issue passes severity filter
        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||
                                    this.selectedFilters.severity.includes(issue.severity);

        // Check if issue passes analysis type filter
        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||
                                    this.selectedFilters.analysisType.includes(issue.analysisType);

        // Issue must pass both filters
        return passesSeverityFilter && passesAnalysisFilter;
      });
    },

    // Check if any filters are active
    isFiltersActive() {
      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;
    },

    // Sort breakout groups by xFactor (highest first) and filter critical ones
    sortedCriticalBreakoutGroups() {
      return [...this.breakoutGroups]
        .filter(group => group.xFactor >= 1.5) // Only groups with xFactor >= 1.5 are considered critical
        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order
    },

    // Get normal breakout groups (non-critical)
    normalBreakoutGroups() {
      return [...this.breakoutGroups]
        .filter(group => group.xFactor < 1.5) // Only groups with xFactor < 1.5
        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order
    },

    // Get high priority groups (xFactor >= 3.0)
    sortedHighPriorityGroups() {
      return [...this.breakoutGroups]
        .filter(group => group.xFactor >= 3.0) // Only groups with xFactor >= 3.0 (critical spike)
        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order
    },

    // Get medium priority groups (1.5 <= xFactor < 3.0)
    sortedMediumPriorityGroups() {
      return [...this.breakoutGroups]
        .filter(group => group.xFactor >= 1.5 && group.xFactor < 3.0) // Groups with 1.5 <= xFactor < 3.0 (sustained problem)
        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order
    }
  },
  methods: {
    loadDashboardData() {
      this.loadCriticalIssues();
      this.loadValidationCounts();
      this.loadBreakoutGroups();
      this.loadTrackedItems();
      this.loadRootCauseData();
    },

    loadCriticalIssues() {
      console.log('Loading critical issues data');

      // Use hardcoded values to match the UI with categories
      const criticalIssues = [
        {
          id: 'ci1',
          category: 'Fan Themis',
          month: '2024-06',
          severity: 'high',
          increaseMultiplier: '3.2',
          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',
          comment: '',
          analysisType: 'Root Cause'
        },
        {
          id: 'ci2',
          category: 'Victoria Crypto',
          month: '2024-06',
          severity: 'medium',
          increaseMultiplier: '1.8',
          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',
          comment: '',
          analysisType: 'Root Cause'
        },

      ];

      // Set new critical issues to 0 to match the UI
      this.newCriticalIssues = [];

      // Set unresolved critical issues
      this.unresolvedCriticalIssues = criticalIssues;

      // Set critical issues section to collapsed by default
      this.isCriticalIssuesExpanded = false;

      console.log('Critical issues set:', {
        new: this.newCriticalIssues,
        unresolved: this.unresolvedCriticalIssues
      });
    },



    loadValidationCounts() {
      console.log('Loading validation counts and chart data');

      // Use hardcoded values to match the UI
      this.validatedCount = 125;
      this.unvalidatedCount = 37;

      // Generate mock data for the validation chart
      this.generateValidationChartData();

      console.log('Validation counts set:', {
        validatedCount: this.validatedCount,
        unvalidatedCount: this.unvalidatedCount
      });

      // Initialize the validation chart after data is loaded
      this.$nextTick(() => {
        this.initValidationChart();
      });
    },

    generateValidationChartData() {
      // Generate mock data for the validation chart
      const data = [];

      // Generate data for the last 14 days
      for (let i = 13; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);

        // Format date for display in band scale
        const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

        // Generate random values for validated and unvalidated defects
        const validated = Math.floor(Math.random() * 12) + 3; // 3-15 validated defects
        const unvalidated = Math.floor(Math.random() * 8); // 0-8 unvalidated defects

        // Calculate daily yield (assuming 100 units processed per day)
        const unitsPerDay = 100;
        const dailyYield = ((unitsPerDay - (validated + unvalidated)) / unitsPerDay) * 100;

        // Generate volume data (units processed per day)
        const volume = unitsPerDay + Math.floor(Math.random() * 50) - 25; // 75-125 units

        // Create data points with all values
        // Defect data for grouped bar chart
        data.push({
          date: dateStr,
          group: 'Validated',
          value: validated
        });

        data.push({
          date: dateStr,
          group: 'Unvalidated',
          value: unvalidated
        });

        // Volume data for grouped bar chart
        data.push({
          date: dateStr,
          group: 'Volume',
          value: volume
        });

        // Yield data for line chart
        data.push({
          date: dateStr,
          group: 'Daily Yield',
          value: dailyYield
        });

        // Calculate cumulative yield (simplified for this example)
        const cumulativeYield = 90 + (Math.random() * 8); // 90-98% range

        data.push({
          date: dateStr,
          group: 'Cumulative Yield',
          value: cumulativeYield
        });

        data.push({
          date: dateStr,
          group: 'Target Yield',
          value: 95 // Target yield is 95%
        });
      }

      // Set the validation chart data
      this.validationChartData = data;
      console.log('Validation chart data generated:', data);
    },

    initValidationChart() {
      console.log('Initializing validation chart...');
      try {
        // Destroy existing chart if it exists
        if (this.validationChart) {
          console.log('Destroying existing chart');
          this.validationChart.destroy();
        }

        // Get the chart container element
        const chartContainer = document.getElementById('validationChart');
        console.log('Chart container:', chartContainer);
        if (!chartContainer) {
          console.error('Validation chart container not found');
          return;
        }

        // Log validation chart data
        console.log('Validation chart data length:', this.validationChartData.length);
        console.log('Sample data:', this.validationChartData.slice(0, 5));

        // Create a simplified chart configuration
        const chartOptions = {
          title: 'Daily Status',
          axes: {
            left: {
              title: 'Count',
              mapsTo: 'value',
              domain: [0, 150],
              scaleType: 'linear'
            },
            right: {
              title: 'Yield (%)',
              mapsTo: 'value',
              domain: [0, 100],
              scaleType: 'linear',
              correspondingDatasets: [
                'Daily Yield',
                'Cumulative Yield',
                'Target Yield'
              ]
            },
            bottom: {
              title: 'Day',
              mapsTo: 'date',
              scaleType: 'band'
            }
          },
          height: '350px',
          width: '100%',
          legend: {
            alignment: 'center',
            enabled: true,
            fontColor: '#f4f4f4'
          },
          color: {
            scale: {
              'Validated': '#0062ff', // Blue
              'Unvalidated': '#ff832b', // Orange
              'Volume': '#24a148', // Green
              'Daily Yield': '#6929c4', // Purple
              'Cumulative Yield': '#1192e8', // Light blue
              'Target Yield': '#fa4d56' // Red
            }
          },
          toolbar: {
            enabled: false
          },
          data: {
            groupMapsTo: 'group'
          },
          theme: 'g100',
          grid: {
            x: {
              enabled: false
            },
            y: {
              enabled: true
            }
          },
          comboChartTypes: [
            {
              type: 'grouped-bar',
              correspondingDatasets: ['Validated', 'Unvalidated', 'Volume']
            },
            {
              type: 'line',
              correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],
              options: {
                points: {
                  radius: 0
                },
                strokeWidth: 2
              }
            }
          ],
          thresholds: [
            {
              axis: 'right',
              value: 95,
              label: 'Target Yield (95%)',
              fillColor: '#fa4d56',
              opacity: 0.1
            }
          ],
          tooltip: {
            enabled: true,
            customHTML: (data) => {
              let html = `<div style="padding: 10px; background: #262626; color: #f4f4f4; border-radius: 4px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);">
                <div style="font-weight: bold; margin-bottom: 5px;">${data[0].date}</div>`;

              data.forEach(item => {
                let displayValue;
                const group = item.group;

                if (group.includes('Yield')) {
                  // Format yield values as percentages
                  displayValue = `${item.value.toFixed(1)}%`;
                } else if (group === 'Volume') {
                  // Format volume as integer with 'units' label
                  displayValue = `${Math.round(item.value)} units`;
                } else {
                  // Format defect counts as integers
                  displayValue = Math.round(item.value);
                }

                html += `<div style="margin: 4px 0;">
                  <span style="display: inline-block; width: 12px; height: 12px; background-color: ${chartOptions.color.scale[group]}; margin-right: 6px; border-radius: 50%;"></span>
                  <span style="font-weight: 500;">${group}:</span> ${displayValue}
                </div>`;
              });

              html += `</div>`;
              return html;
            }
          }
        };

        // Create the combo chart with the simplified configuration
        this.validationChart = new ComboChart(
          chartContainer,
          {
            data: this.validationChartData,
            options: chartOptions
          }
        );

        console.log('Validation chart initialized with ComboChart');
      } catch (error) {
        console.error('Error initializing validation chart:', error);

        // Create a fallback display if the chart fails
        const chartContainer = document.getElementById('validationChart');
        if (chartContainer) {
          console.log('Creating fallback display for validation chart');
          chartContainer.innerHTML = `
            <div style="height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #161616; border-radius: 8px; padding: 16px;">
              <div style="color: #f4f4f4; margin-bottom: 16px; font-weight: 600; font-size: 1rem;">Daily Status</div>
              <div style="color: #c6c6c6; margin-bottom: 16px; font-size: 0.875rem;">Chart could not be displayed. Showing summary data instead.</div>
              <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #0062ff; margin-right: 8px; border-radius: 50%;"></span>
                <span style="color: #f4f4f4;">Validated: <strong>${this.validatedCount}</strong></span>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #ff832b; margin-right: 8px; border-radius: 50%;"></span>
                <span style="color: #f4f4f4;">Unvalidated: <strong>${this.unvalidatedCount}</strong></span>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #24a148; margin-right: 8px; border-radius: 50%;"></span>
                <span style="color: #f4f4f4;">Volume: <strong>100 units</strong></span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #6929c4; margin-right: 8px; border-radius: 50%;"></span>
                <span style="color: #f4f4f4;">Current Yield: <strong>94.2%</strong></span>
              </div>
            </div>
          `;
        }
      }
    },

    async loadBreakoutGroups() {
      console.log('Loading breakout groups from API for current month');
      try {
        this.isLoadingBreakoutGroups = true;

        // Get the current month in YYYY-MM format
        const now = new Date();
        const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
        this.currentMonth = currentMonth;

        console.log(`Current month: ${this.currentMonth}`);

        // Make API call to get breakout groups data
        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            month: currentMonth // Pass the current month to get specific data
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success' && data.breakout_groups) {
          // Add current month flag to each breakout group
          this.breakoutGroups = data.breakout_groups.map(group => ({
            ...group,
            // If sustainedXFactor is not provided, use a default value
            sustainedXFactor: group.sustainedXFactor || (group.status === 'Sustained Problem' ? group.xFactor * 0.9 : group.xFactor * 0.5),
            isCurrentMonth: true, // All groups from the API are for the current month
            inHeatmap: true // Indicate that these groups are also in the heatmap
          }));

          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${currentMonth}`);
        } else {
          // Fallback to sample data if API fails
          console.warn('No breakout groups found in API response, using sample data');
          this.breakoutGroups = [
            {
              name: 'Fan Themis',
              status: 'Short-Term Spike',
              xFactor: 3.2,
              sustainedXFactor: 1.2,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Victoria Crypto',
              status: 'Sustained Problem',
              xFactor: 1.8,
              sustainedXFactor: 1.7,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Quantum Nexus',
              status: 'Sustained Problem',
              xFactor: 1.6,
              sustainedXFactor: 1.6,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Orion Base',
              status: 'Normal',
              xFactor: 0.9,
              sustainedXFactor: 0.8,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Stellar Core',
              status: 'Normal',
              xFactor: 0.8,
              sustainedXFactor: 0.7,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Nebula Drive',
              status: 'Normal',
              xFactor: 0.7,
              sustainedXFactor: 0.7,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Pulsar Matrix',
              status: 'Normal',
              xFactor: 0.6,
              sustainedXFactor: 0.6,
              isCurrentMonth: true,
              inHeatmap: true
            },
            {
              name: 'Quasar Link',
              status: 'Normal',
              xFactor: 0.5,
              sustainedXFactor: 0.5,
              isCurrentMonth: true,
              inHeatmap: true
            }
          ];
        }
      } catch (error) {
        console.error('Error loading breakout groups:', error);
        // Fallback to empty array
        this.breakoutGroups = [];
      } finally {
        this.isLoadingBreakoutGroups = false;
      }

      console.log('Breakout groups set:', this.breakoutGroups);
    },

    getAiDescriptionForIssue(issue) {
      // This would call WatsonX.ai to get an AI description for the issue
      // For now, we'll use the existing description
      console.log(`Getting AI description for issue: ${issue.id}`);
    },

    getStatusClass(status) {
      switch (status) {
        case 'Short-Term Spike':
          return 'status-spike';
        case 'Sustained Problem':
          return 'status-sustained';
        case 'Critical (Both)':
          return 'status-critical';
        default:
          return 'status-normal';
      }
    },

    viewIssueDetails(issue) {
      console.log(`Viewing details for issue: ${issue.id}`);
      this.selectedIssue = { ...issue };
      this.loadIssueChartData(issue);
      this.showIssueModal = true;
    },

    loadIssueChartData(issue) {
      console.log(`Loading chart data for issue: ${issue.id}`);

      // For now, we'll create some sample data
      // In a real implementation, this would come from the API
      const months = [];
      const values = [];

      // Get the issue month
      const issueDate = new Date(issue.month + '-01');

      // Create data for 6 months before and after the issue month
      for (let i = -6; i <= 6; i++) {
        const date = new Date(issueDate);
        date.setMonth(date.getMonth() + i);
        const monthStr = date.toISOString().slice(0, 7); // YYYY-MM format

        // Base value that increases at the issue month
        let value = 0.5;
        if (i < 0) {
          value = 0.5 + (i * 0.05); // Slight variation before issue
        } else if (i === 0) {
          // The spike at the issue month
          value = issue.increaseMultiplier === '(new)' ? 1.0 : parseFloat(issue.increaseMultiplier);
        } else {
          // Gradual decrease after the spike
          value = parseFloat(issue.increaseMultiplier) * Math.pow(0.8, i);
        }

        months.push(monthStr);
        values.push({
          month: monthStr,
          value: value
        });
      }

      // Set the chart data
      this.issueChartData = values;

      // Update chart options
      this.issueChartOptions = {
        title: `${issue.category} - ${issue.month}`,
        axes: {
          left: {
            title: 'X-Factor',
            mapsTo: 'value',
            domain: [0, Math.max(...values.map(v => v.value)) * 1.2]
          },
          bottom: {
            title: 'Month',
            mapsTo: 'month',
            scaleType: 'labels'
          }
        },
        height: '300px',
        color: {
          scale: {
            'X-Factor': '#0062ff'
          }
        }
      };
    },

    closeIssueModal() {
      console.log('Closing issue modal');
      this.showIssueModal = false;
      this.selectedIssue = null;
    },

    updateIssue() {
      console.log(`Updating issue: ${this.selectedIssue.id}`);

      // In a real implementation, this would call an API to update the issue
      // For now, we'll just update the local data
      const index = this.unresolvedCriticalIssues.findIndex(issue => issue.id === this.selectedIssue.id);
      if (index !== -1) {
        this.unresolvedCriticalIssues[index].comment = this.selectedIssue.comment;
        console.log(`Updated issue ${this.selectedIssue.id} with comment: ${this.selectedIssue.comment}`);
      }

      this.closeIssueModal();
    },

    goToValidationPage() {
      console.log('Navigating to validations page');
      this.$router.push('/validations');
    },

    goToGroupAnalysis() {
      console.log(`Navigating to Group tab for ${this.selectedAnalysisType} analysis`);

      // Find the parent MetisXFactors component
      let parent = this.$parent;
      while (parent && parent.$options.name !== 'MetisXFactors') {
        parent = parent.$parent;
      }

      if (parent) {
        console.log('Found MetisXFactors parent component, navigating to Group tab');

        // If a breakout group is selected, use it
        if (this.selectedBreakoutGroup) {
          // Navigate to the Group tab with the selected breakout group
          parent.selectBreakoutFromDashboard(this.selectedBreakoutGroup);

          // After navigation, trigger the appropriate analysis based on the selected type
          this.$nextTick(() => {
            if (this.selectedAnalysisType === 'Root Cause') {
              parent.viewRootCauseAnalysis(3); // Show 3 months of data
            }
          });
        } else {
          // If no breakout group is selected, use the first one from the breakout groups
          if (this.breakoutGroups.length > 0) {
            this.selectedBreakoutGroup = this.breakoutGroups[0].name;
            this.goToGroupAnalysis(); // Call this method again with the selected breakout group
          } else {
            console.error('No breakout groups available');
          }
        }
      } else {
        console.error('Could not find MetisXFactors parent component');
      }
    },

    viewBreakoutDetails(group, targetTab = 'group') {
      console.log(`Viewing details for breakout group: ${group.name} in ${targetTab} tab`);

      // Set the selected breakout group
      this.selectedBreakoutGroup = group.name;

      // Find the parent MetisXFactors component
      let parent = this.$parent;
      while (parent && parent.$options.name !== 'MetisXFactors') {
        parent = parent.$parent;
      }

      if (parent) {
        if (targetTab === 'heatmap') {
          console.log('Found MetisXFactors parent component, navigating to Heatmap tab');
          // Set the active tab to Heatmap (index 0) and then select the breakout group
          parent.activeTab = 0; // Heatmap tab index
          parent.selectBreakoutFromDashboard(group.name);

          // If there's a specific method to highlight this group in the heatmap, call it
          if (parent.highlightGroupInHeatmap) {
            parent.$nextTick(() => {
              parent.highlightGroupInHeatmap(group.name);
            });
          }
        } else {
          console.log('Found MetisXFactors parent component, navigating to Group tab');
          // Default behavior - navigate to Group tab
          parent.selectBreakoutFromDashboard(group.name);
        }
      } else {
        console.error('Could not find MetisXFactors parent component');
      }
    },

    // Navigate to heatmap tab and highlight this group
    viewBreakoutInHeatmap(group) {
      this.viewBreakoutDetails(group, 'heatmap');
    },

    formatMonth(dateStr) {
      // Format YYYY-MM to MMM format (e.g., 2024-06 to Jun)
      const date = new Date(dateStr + '-01'); // Add day to make a valid date
      return date.toLocaleString('en-US', { month: 'short' });
    },

    // Toggle critical issues section expanded/collapsed
    toggleCriticalIssuesExpanded() {
      console.log('Toggling critical issues expanded state');
      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;
    },

    // Handle severity filter dropdown change
    handleSeverityFilterChange() {
      console.log(`Severity filter changed to: ${this.severityFilter}`);

      if (this.severityFilter === 'all') {
        // Clear severity filters
        this.selectedFilters.severity = [];
      } else {
        // Set the selected severity
        this.selectedFilters.severity = [this.severityFilter];
      }
    },

    // Handle analysis type filter dropdown change
    handleAnalysisFilterChange() {
      console.log(`Analysis filter changed to: ${this.analysisTypeFilter}`);

      if (this.analysisTypeFilter === 'all') {
        // Clear analysis type filters
        this.selectedFilters.analysisType = [];
      } else {
        // Set the selected analysis type
        this.selectedFilters.analysisType = [this.analysisTypeFilter];
      }
    },

    // Clear all filters
    clearFilters() {
      console.log('Clearing all filters');
      this.selectedFilters.severity = [];
      this.selectedFilters.analysisType = [];
      this.severityFilter = 'all';
      this.analysisTypeFilter = 'all';
    },

    initCategorySummaryChart() {
      try {
        // Destroy existing chart if it exists
        if (this.categorySummaryChart) {
          this.categorySummaryChart.destroy();
        }

        // Get the chart container element
        const chartContainer = document.getElementById('categorySummaryChart');
        if (!chartContainer) {
          console.error('Category summary chart container not found');
          return;
        }

        // Create mock data for the selected category
        const chartData = this.generateCategorySummaryData();

        // Create chart options
        const options = {
          title: `${this.selectedCategory} Analysis Summary`,
          axes: {
            left: {
              title: 'X-Factor',
              mapsTo: 'value',
              domain: [0, 4]
            },
            bottom: {
              title: 'Month',
              mapsTo: 'date',
              scaleType: 'labels'
            }
          },
          height: '300px',
          width: '100%',
          legend: {
            alignment: 'center',
            enabled: true
          },
          color: {
            scale: {
              'X-Factor': '#0062ff',
              'Threshold': '#ff9a00'
            }
          },
          toolbar: {
            enabled: false
          },
          data: {
            groupMapsTo: 'group'
          },
          theme: 'g90', // Use dark theme to match the UI
          thresholds: [
            {
              value: 1.5,
              label: 'Sustained Problem Threshold',
              fillColor: '#ff9a00',
              opacity: 0.1
            },
            {
              value: 3.0,
              label: 'Critical Spike Threshold',
              fillColor: '#fa4d56',
              opacity: 0.1
            }
          ]
        };

        // Create the chart
        this.categorySummaryChart = new ComboChart(chartContainer, {
          data: chartData,
          options: {
            ...options,
            comboChartTypes: [
              {
                type: 'line',
                correspondingDatasets: ['X-Factor', 'Threshold'],
                options: {
                  points: {
                    radius: 3
                  },
                  strokeWidth: 2
                }
              }
            ]
          }
        });

        console.log('Category summary chart initialized');
      } catch (error) {
        console.error('Error initializing category summary chart:', error);

        // Create a simple fallback display for the data
        const chartContainer = document.getElementById('categorySummaryChart');
        if (chartContainer) {
          chartContainer.innerHTML = `
            <div style="height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
              <div style="color: #f4f4f4; margin-bottom: 16px;">${this.selectedCategory} Analysis Summary</div>
              <div style="color: #0062ff;">No chart data available</div>
            </div>
          `;
        }
      }
    },

    generateCategorySummaryData() {
      // Generate mock data for the category summary chart
      const data = [];
      const currentDate = new Date();

      // Generate data for the last 6 months
      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const monthStr = this.formatMonth(monthDate.toISOString().slice(0, 7));

        // Add X-Factor data point
        const xFactor = i === 0 ?
          (this.selectedCategory === 'Root Cause' ? 3.2 :
           this.selectedCategory === 'Vintage' ? 2.5 :
           this.selectedCategory === 'Supplier' ? 1.6 : 1.0) :
          Math.random() * 1.5 + 0.5; // Random between 0.5-2.0 for previous months

        data.push({
          date: monthStr,
          group: 'X-Factor',
          value: xFactor
        });

        // Add threshold reference line
        data.push({
          date: monthStr,
          group: 'Threshold',
          value: 1.5
        });
      }

      return data;
    },

    // These methods are no longer needed with the new filtering approach
    // but we'll keep them for backward compatibility
    getCriticalCountByCategory(category) {
      return this.unresolvedCriticalIssues.filter(issue => issue.analysisType === category).length;
    },

    toggleIssueExpanded(issue) {
      const index = this.expandedIssueIds.indexOf(issue.id);
      if (index === -1) {
        // Add to expanded list
        this.expandedIssueIds.push(issue.id);
      } else {
        // Remove from expanded list
        this.expandedIssueIds.splice(index, 1);
      }
    },

    isIssueExpanded(issueId) {
      return this.expandedIssueIds.includes(issueId);
    },

    getXFactorSeverityClass(xFactor) {
      if (xFactor >= 3.0) {
        return 'severity-critical';
      } else if (xFactor >= 1.5) {
        return 'severity-warning';
      } else if (xFactor >= 1.0) {
        return 'severity-caution';
      } else {
        return 'severity-normal';
      }
    },

    // Item tracking methods
    async loadTrackedItems() {
      console.log('Loading tracked items from Action Tracker API');
      this.isLoadingTrackedItems = true;

      try {
        // Get the critical breakout groups from the current month
        const criticalBreakoutGroups = this.sortedCriticalBreakoutGroups.map(group => group.name);
        console.log('Critical breakout groups for filtering action items:', criticalBreakoutGroups);

        // Make API call to get action tracker data
        const response = await fetch('/api-statit2/get_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            // Filter by breakout groups with issues in the current month
            breakoutNames: criticalBreakoutGroups
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success' && data.items && data.items.length > 0) {
          // Format the action tracker items for display
          this.trackedItems = data.items.map(item => ({
            id: item.id,
            name: item.action && item.action.length > 50 ? item.action.substring(0, 50) + '...' : item.action || `Action for ${item.group}`,
            status: item.status || 'In Progress',
            owner: item.assignee || 'Unassigned',
            dueDate: item.deadline || 'No deadline',
            relatedIssue: item.group || 'Unknown',
            description: item.notes || 'No description available',
            progress: item.progress || 0,
            // Add a flag to indicate if this item is related to a critical breakout group
            isCritical: criticalBreakoutGroups.includes(item.group)
          }));

          console.log(`Loaded ${this.trackedItems.length} tracked items from API`);
        } else {
          console.warn('No action tracker items found in API response or API call failed, using sample data');

          // Sample data as fallback
          const allActionItems = [
            {
              id: 'item1',
              name: 'Fan Themis Airflow Investigation',
              status: 'In Progress',
              owner: 'John Smith',
              dueDate: '2024-07-15',
              relatedIssue: 'Fan Themis',
              description: 'Investigation into airflow issues in the new Fan Themis design that caused a 3.2x spike in failures.',
              progress: 65,
              isCritical: criticalBreakoutGroups.includes('Fan Themis')
            },
            {
              id: 'item2',
              name: 'Victoria Crypto Power Delivery Fix',
              status: 'Pending Review',
              owner: 'Jane Doe',
              dueDate: '2024-07-10',
              relatedIssue: 'Victoria Crypto',
              description: 'Implementation of power delivery improvements to address the sustained problem in Victoria Crypto units.',
              progress: 85,
              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')
            },
            {
              id: 'item3',
              name: 'Supplier ABC Quality Audit',
              status: 'Completed',
              owner: 'Robert Johnson',
              dueDate: '2024-06-30',
              relatedIssue: 'Victoria Crypto',
              description: 'Quality audit of Supplier ABC to address higher failure rates in Victoria Crypto units.',
              progress: 100,
              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')
            },
            {
              id: 'item4',
              name: 'Quantum Nexus Thermal Analysis',
              status: 'In Progress',
              owner: 'Emily Chen',
              dueDate: '2024-07-20',
              relatedIssue: 'Quantum Nexus',
              description: 'Thermal analysis of Quantum Nexus components to address overheating issues.',
              progress: 40,
              isCritical: criticalBreakoutGroups.includes('Quantum Nexus')
            },
            {
              id: 'item5',
              name: 'Nebula Drive Firmware Update',
              status: 'Blocked',
              owner: 'Michael Brown',
              dueDate: '2024-07-05',
              relatedIssue: 'Nebula Drive',
              description: 'Firmware update to address performance issues in Nebula Drive units.',
              progress: 25,
              isCritical: criticalBreakoutGroups.includes('Nebula Drive')
            }
          ];

          // Filter items to only include those related to breakout groups with issues in the current month
          this.trackedItems = allActionItems.filter(item =>
            criticalBreakoutGroups.some(groupName =>
              item.relatedIssue.includes(groupName)
            )
          );
        }

        // Sort items by status priority and then by whether they're critical
        this.trackedItems.sort((a, b) => {
          const statusPriority = {
            'blocked': 0,
            'in progress': 1,
            'pending review': 2,
            'completed': 3
          };

          // First sort by status priority
          const statusDiff = statusPriority[a.status.toLowerCase()] - statusPriority[b.status.toLowerCase()];

          // If status is the same, sort by critical flag (critical items first)
          if (statusDiff === 0) {
            return b.isCritical - a.isCritical;
          }

          return statusDiff;
        });

        console.log('Tracked items loaded and sorted:', this.trackedItems);
      } catch (error) {
        console.error('Error loading tracked items:', error);
        this.trackedItems = [];
      } finally {
        this.isLoadingTrackedItems = false;
      }
    },

    getItemStatusClass(status) {
      switch (status.toLowerCase()) {
        case 'in progress':
          return 'status-in-progress';
        case 'pending review':
          return 'status-pending';
        case 'completed':
          return 'status-completed';
        case 'blocked':
          return 'status-blocked';
        default:
          return 'status-default';
      }
    },

    // Check if a group is in the critical breakout groups list
    isCriticalGroup(groupName) {
      return this.sortedCriticalBreakoutGroups.some(group => group.name === groupName);
    },

    viewItemDetails(item) {
      console.log('Viewing item details:', item);
      // Navigate to the Action Tracker page with the specific item ID
      this.$router.push(`/action-tracker/items/${item.id}`);
    },

    goToActionTracker() {
      console.log('Navigating to Action Tracker');
      // Navigate to the Action Tracker page using Vue Router
      this.$router.push('/action-tracker');
    },

    // Root Cause Chart methods
    async loadRootCauseData() {
      console.log('Loading root cause chart data for PQE Dashboard');
      this.isRootCauseDataLoading = true;

      try {
        // Generate mock data for the root cause chart
        // In a real implementation, this would call an API
        const mockData = this.generateMockRootCauseData();
        this.rootCauseChartData = mockData;

        console.log('Root cause chart data loaded:', this.rootCauseChartData);
      } catch (error) {
        console.error('Error loading root cause data:', error);
        this.rootCauseChartData = [];
      } finally {
        this.isRootCauseDataLoading = false;
      }
    },

    generateMockRootCauseData() {
      // Generate mock data similar to what MetisXFactors uses
      const data = [];
      const categories = ['Electrical', 'Mechanical', 'Thermal', 'Material', 'Process'];
      const months = ['May 2024', 'Jun 2024', 'Jul 2024'];

      months.forEach(month => {
        categories.forEach(category => {
          // Generate random fail rates between 0.5% and 4%
          const baseRate = Math.random() * 3.5 + 0.5;

          data.push({
            group: category,
            key: month,
            value: parseFloat(baseRate.toFixed(2)),
            data: {
              defects: Math.floor(Math.random() * 20) + 5,
              volume: Math.floor(Math.random() * 1000) + 500,
              month: month,
              category: category,
              isCritical: baseRate > 2.5,
              formattedValue: `${baseRate.toFixed(2)}%`
            }
          });
        });
      });

      return data;
    },

    handleRootCauseBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);

        // You can implement additional functionality here, such as showing more details
        // or navigating to the MetisXFactors Group tab with this specific category
      }
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../styles/carbon-utils";

.pqe-dashboard-container {
  width: 100%;
  color: #f4f4f4; /* Light text for dark mode */
}

.content-wrapper {
  padding: 16px;
  margin-bottom: 16px;
  background-color: #161616; /* Dark background */
  border-radius: 4px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #333333; /* Dark border */
  padding-bottom: 16px;
}

.last-updated-text {
  font-size: 0.875rem;
  color: #c6c6c6; /* Light gray text */
}

/* Key Metrics Section */
.key-metrics-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 32px;
}

.metric-card {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 4px;
  background-color: #262626; /* Dark gray background */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 16px;
  color: #ffffff;

  &.new-issues {
    background-color: #8a3ffc; /* Purple */
  }

  &.critical-issues {
    background-color: #fa4d56; /* Red */
  }

  &.validated {
    background-color: #0f62fe; /* Blue */
  }

  &.unvalidated {
    background-color: #ff832b; /* Orange */
  }
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #c6c6c6; /* Light gray */
  margin-bottom: 4px;
}

.metric-value {
  font-size: 2.25rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */
  line-height: 1.2;
}

.metric-description {
  font-size: 0.75rem;
  color: #c6c6c6; /* Light gray */
  margin-top: 4px;
}

/* Main Content Layout */
.dashboard-main-content {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.dashboard-column {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-card {
  padding: 16px;
  border-radius: 4px;
  background-color: #262626; /* Dark gray background */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border: 1px solid #333333; /* Dark border */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #333333; /* Dark border */
  padding-bottom: 12px;
}

.section-title-container {
  display: flex;
  flex-direction: column;
}

.section-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */
}

.section-subtitle {
  font-size: 0.875rem;
  color: #c6c6c6; /* Light gray */
  margin-top: 4px;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-content {
  animation: fadeIn 0.3s ease-in-out;
}

.section-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #333333; /* Dark border */
}

.section-description {
  color: #c6c6c6; /* Light gray */
  line-height: 1.5;
  margin-bottom: 16px;
}

.action-button {
  align-self: flex-start;
}

/* Critical Issues Section */
.critical-issues-header {
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    background-color: #353535; /* Darker gray */
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 12px;
  border-radius: 16px;
  background-color: #fa4d56; /* Red */
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff; /* White text */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &.flashing {
    animation: flash 1.5s infinite;
  }
}

@keyframes flash {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.expand-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #393939; /* Dark gray */
  color: #c6c6c6; /* Light gray */
  transition: transform 0.3s ease, background-color 0.3s ease;

  &.expanded {
    transform: rotate(180deg);
    background-color: #0f62fe; /* Blue */
    color: #ffffff; /* White */
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Filter Controls */
.filter-container {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #161616; /* Dark background */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #333333; /* Dark border */
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #333333; /* Dark border */
  padding-bottom: 12px;
}

.filter-title {
  font-size: 1rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */
  margin: 0;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #c6c6c6; /* Light gray */
  margin-right: 8px;
}

.filter-dropdown {
  width: 100%;
}

.clear-filters-button {
  margin-left: auto;
  background-color: #393939; /* Dark gray */
  border: none;
  transition: all 0.2s ease;
  color: #f4f4f4; /* White text */

  &:hover {
    background-color: #4d4d4d; /* Darker gray */
    color: #ffffff; /* White */
  }
}

/* Issues List */
.issues-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.no-data-message {
  margin-top: 16px;
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  color: #c6c6c6; /* Light gray */
  background-color: #161616; /* Dark background */
  font-size: 1rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  border: 1px solid #333333; /* Dark border */

  &::before {
    content: '🔍';
    display: block;
    font-size: 2rem;
    margin-bottom: 16px;
  }
}

.issue-card {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #333333; /* Dark border */
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #161616; /* Dark background */

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-color: #4d4d4d; /* Darker border on hover */
  }

  &.expanded {
    .expand-indicator {
      transform: rotate(180deg);
      background-color: #0f62fe; /* Blue */
      color: #ffffff; /* White */
    }

    .issue-header {
      border-bottom: 1px solid #333333; /* Dark border */
    }
  }
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 16px;
  position: relative;
  background-color: #262626; /* Dark gray */
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #353535; /* Darker gray */
  }
}

.issue-tags {
  display: flex;
  gap: 8px;
  align-items: center;
}

.issue-metadata {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
}

.issue-title {
  font-weight: 600;
  margin: 0 16px;
  color: #f4f4f4; /* White text */
  flex: 1;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.issue-multiplier {
  font-weight: 700;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.875rem;

  &.high-severity {
    color: #fa4d56; /* Red */
    background-color: rgba(250, 77, 86, 0.1); /* Transparent red */
  }

  &.medium-severity {
    color: #ff832b; /* Orange */
    background-color: rgba(255, 131, 43, 0.1); /* Transparent orange */
  }
}

.analysis-tag {
  font-size: 0.75rem;
}

.issue-content {
  padding: 16px;
  background-color: #161616; /* Dark background */
  animation: slideDown 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

.ai-description {
  background-color: #262626; /* Dark gray */
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  position: relative;

  &::before {
    content: 'AI Analysis';
    position: absolute;
    top: -10px;
    left: 10px;
    background-color: #6929c4; /* Purple */
    color: #ffffff; /* White text */
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  p {
    margin: 16px 0 0 0;
    line-height: 1.5;
    color: #f4f4f4; /* White text */
  }
}

.issue-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #333333; /* Dark border */
}

/* Breakout Groups Grid */
.breakout-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.breakout-groups-grid.normal-groups {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #333333; /* Dark border */
}

.breakout-group-card {
  padding: 16px;
  border-radius: 4px;
  border-left: 4px solid #393939; /* Dark gray */
  background-color: #262626; /* Dark gray background */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  &.highlighted {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #fa4d56; /* Red */
    }
  }

  &.severity-normal {
    border-left-color: #24a148; /* Green */
  }

  &.severity-caution {
    border-left-color: #f1c21b; /* Yellow */
  }

  &.severity-warning {
    border-left-color: #ff832b; /* Orange */
  }

  &.severity-critical {
    border-left-color: #fa4d56; /* Red */
  }
}

.show-more-footer {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.show-more-button {
  background-color: transparent;
  border: 1px dashed #333333; /* Dark border */
  color: #0f62fe; /* Blue */
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */
    border-color: #0f62fe; /* Blue */
  }
}

.toggle-button {
  background-color: transparent;
  color: #0f62fe; /* Blue */
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */
  }
}

/* Item Tracking Styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  margin: 16px 0;
}

.tracked-items-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.tracked-item-card {
  padding: 16px;
  border-radius: 4px;
  background-color: #262626; /* Dark gray background */
  border-left: 4px solid #393939; /* Default border */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  &.status-in-progress {
    border-left-color: #0f62fe; /* Blue */
  }

  &.status-pending {
    border-left-color: #ff832b; /* Orange */
  }

  &.status-completed {
    border-left-color: #24a148; /* Green */
  }

  &.status-blocked {
    border-left-color: #fa4d56; /* Red */
  }

  &.critical-item {
    border: 1px solid #fa4d56; /* Red border */
    box-shadow: 0 0 0 1px #fa4d56;
  }
}

.tracked-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.tracked-item-name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.tracked-item-status {
  margin-left: 8px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  background-color: #393939; /* Default background */

  &.status-in-progress {
    background-color: rgba(15, 98, 254, 0.2); /* Blue background */
    color: #0f62fe; /* Blue text */
  }

  &.status-pending {
    background-color: rgba(255, 131, 43, 0.2); /* Orange background */
    color: #ff832b; /* Orange text */
  }

  &.status-completed {
    background-color: rgba(36, 161, 72, 0.2); /* Green background */
    color: #24a148; /* Green text */
  }

  &.status-blocked {
    background-color: rgba(250, 77, 86, 0.2); /* Red background */
    color: #fa4d56; /* Red text */
  }
}

.tracked-item-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.detail-label {
  font-weight: 600;
  color: #c6c6c6; /* Light gray */
  width: 100px;
}

.detail-value {
  color: #f4f4f4; /* White */

  &.related-group {
    display: flex;
    align-items: center;
    gap: 8px;

    &.critical-group {
      color: #fa4d56; /* Red */
      font-weight: 600;
    }

    .month-tag {
      margin-left: 4px;
    }
  }
}

.progress-container {
  flex: 1;
  height: 8px;
  background-color: #393939;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-top: 4px;
}

.progress-bar {
  height: 100%;
  background-color: #0f62fe; /* Blue */
  border-radius: 4px;
}

.progress-text {
  position: absolute;
  right: 0;
  top: -16px;
  font-size: 0.75rem;
  color: #c6c6c6;
}

.tracked-item-footer {
  display: flex;
  justify-content: flex-end;
}

.critical-tag {
  margin-left: 8px;
}

.medium-priority-groups {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #333333; /* Dark border */
}

.breakout-group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333333; /* Dark border */
}

.breakout-group-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */
}

.breakout-group-xfactor {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.xfactor-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.xfactor-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.xfactor-label {
  font-size: 0.75rem;
  color: #c6c6c6; /* Light gray */
  margin-right: 8px;
}

.xfactor-value {
  font-size: 1rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */

  &.severity-normal {
    color: #24a148; /* Green */
  }

  &.severity-caution {
    color: #f1c21b; /* Yellow */
  }

  &.severity-warning {
    color: #ff832b; /* Orange */
  }

  &.severity-critical {
    color: #fa4d56; /* Red */
  }
}

.breakout-group-metrics {
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;

  &.severity-normal {
    color: #24a148; /* Green */
  }

  &.severity-caution {
    color: #f1c21b; /* Yellow */
  }

  &.severity-warning {
    color: #ff832b; /* Orange */
  }

  &.severity-critical {
    color: #fa4d56; /* Red */
  }
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-size: 0.75rem;
  color: #c6c6c6; /* Light gray */
}

.status-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f4f4f4; /* White text */
}

.breakout-group-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #333333; /* Dark border */
  justify-content: flex-end;

  .action-button {
    min-width: 120px;
  }
}

.current-month-indicator {
  margin-top: 8px;

  .cv-tag {
    margin-right: 0;
  }
}

.breakout-group-card.current-month {
  border: 1px solid #0f62fe;
  box-shadow: 0 0 0 1px #0f62fe;
}

/* Chart Section */
.chart-container {
  height: 350px;
  width: 100%;
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333; /* Dark border */
  background-color: #161616; /* Dark background */
  position: relative;
}

.chart-wrapper {
  position: relative;
  height: 350px;
  width: 100%;
}

.carbon-chart-container {
  height: 100%;
  width: 100%;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #c6c6c6; /* Light gray */
}

/* Carbon Charts styling */
.carbon-chart-container .bx--chart-holder {
  position: relative;
}

.carbon-chart-container .bx--axis.bx--axis--right {
  display: block !important;
}

.carbon-chart-container .bx--line {
  stroke-width: 2px !important;
}

.carbon-chart-container .bx--axis-title {
  font-weight: 600;
  fill: #f4f4f4; /* White text */
}

.carbon-chart-container .bx--axis-label {
  fill: #f4f4f4; /* White text */
}

/* Ensure the lines are properly displayed */
.carbon-chart-container .bx--cc--line {
  stroke-width: 2px !important;
}

/* Ensure the points are visible */
.carbon-chart-container .bx--cc--line-point {
  r: 3px;
  stroke-width: 1px;
}

/* Ensure the right axis is properly displayed */
.carbon-chart-container .bx--cc--axes g.right-axis {
  display: block !important;
}

/* Ensure the grid lines are properly displayed */
.carbon-chart-container .bx--cc--grid line {
  stroke: #333333; /* Dark border */
  stroke-width: 0.5px;
}

/* Ensure the threshold line is visible */
.carbon-chart-container .bx--cc--threshold line {
  stroke-width: 1.5px !important;
  stroke-dasharray: 5, 5;
}

/* Ensure the legend text is white */
.carbon-chart-container .bx--cc--legend text {
  fill: #f4f4f4 !important; /* White text */
}

/* Analysis Controls */
.analysis-controls {
  margin-bottom: 16px;
}

.control-row {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.control-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #c6c6c6; /* Light gray */
}

.control-dropdown {
  width: 100%;
}

/* Modal Styles */
.issue-details-modal {
  background-color: #262626; /* Dark gray background */
}

.issue-details-content {
  padding: 16px;
  color: #f4f4f4; /* White text */
}

.issue-chart-container {
  height: 300px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333; /* Dark border */
  background-color: #161616; /* Dark background */
}
</style>
