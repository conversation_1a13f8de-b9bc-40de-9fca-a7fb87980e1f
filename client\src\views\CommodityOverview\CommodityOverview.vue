<style scoped lang = "scss">
@import "../../styles/carbon-utils";
/* Modal styling */
.modal-content {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.modal-column {
  flex: 1;
}

.dropdown-column {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.short-dropdown {
  width: 80%;
  margin-bottom: 15px;
}

/* Title and Chevron button styling */
.modal-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chevron-container {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.chevron-button {
  background: none;
  border: none;
  cursor: pointer;
}

.chevron-icon {
  fill: currentColor;
}

/* Label and paragraph styling */
.bx--label {
  margin-bottom: 5px;
  font-weight: bold;
}

.create-checklist__confirm-rows p {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 0.875rem;
}

/* Chart page grid and tile styling */
.chart-page__grid {
  margin-top: $spacing-08;
  padding: 20px;
}

.chart-page__r2 {
  margin-top: 30px;
}

.cv-button {
  background-color: #0f62fe;
  color: #ffffff;
  padding: 8px 12px; /* Adjust the padding for the main button */
  border-radius: 4px;
  font-size: 14px;
}

.view-button {
  margin-top: 5px; /* Adjust as needed */
  margin-bottom: 5px;
  border-radius: 4px;
  font-size: 12px; /* Smaller font size */
  display: inline-flex; /* Use flexbox to center text */
  background-color: red;
}


cv-tile {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Data table styling */
cv-data-table {
  margin-bottom: 20px;
}
</style>


<template>
  <cv-grid class="chart-page__grid">
    <MainHeader :expandedSideNav=false :useFixed="useFixed" />
    <div class="dropdown-column">
      <cv-dropdown
        class="short-dropdown"
        v-model="selectedCommodity"
        label="Select Commodity"
        :items="commodityOptions"
      ></cv-dropdown>
    </div>
    <div class="dropdown-column">
            <cv-dropdown
              class="short-dropdown"
              v-model="selectedTimeFrame"
              label="Select Time Frame"
              :items="timeFrameOptions"
            ></cv-dropdown>
          </div>

          <cv-row class="chart-page__r2">
      <!-- Column with Content Switcher and Chart -->
      <cv-column :sm="4" :md="6" :lg="8">
        <!-- Content Switcher for selecting chart type -->
        <cv-content-switcher
          aria-label="Choose viz type"
          @selected="onSelected"
          class="content-switcher-row"
        >
          <cv-content-switcher-button
            content-selector=".content-1"
            :selected="selectedIndex === 0"
          >Line Chart</cv-content-switcher-button>
          <cv-content-switcher-button
            content-selector=".content-2"
            :selected="selectedIndex === 1"
          >Heatmap</cv-content-switcher-button>
        </cv-content-switcher>
        
        <!-- Chart Tile -->
        <cv-tile :light="lightTile">
          <div v-if="loading" >
          Loading Charts...
          <LineChart :data = [] :loading="loading"/>
        </div>
          <div class="content-1">
          <LineChart
            v-if="line_chart_data.length > 0"
            :data="line_chart_data"
            @point-clicked="handlePointClick"
            :eventType="handleCheckClickInit"
            :loading="loading"
          />
        </div>
        <div class="content-2">
          <div v-if="loading2" >
          Loading Heatmap...
          <LineChart :data = [] :loading="loading2"/>
        </div>
          <HeatMap
            v-if="heatmap_data.length > 0"
            :data="heatmap_data"
          />
        </div>
        </cv-tile>
      </cv-column>
      
      <!-- Data Table Column for Pending Issues -->
      <cv-column>
        <cv-data-table title="Pending Issues" :columns="p_columns">
          <template slot="data">
            <cv-data-table-row v-for="row in p_rows" :key="row.id">
              <cv-data-table-cell>
                <cv-button class="view-button" @click="handleView(row)">View</cv-button>
              </cv-data-table-cell>
              <cv-data-table-cell>{{ row.group }}</cv-data-table-cell>
              <cv-data-table-cell>{{ row.pn }}</cv-data-table-cell>
              <cv-data-table-cell>{{ row.desc }}</cv-data-table-cell>
            </cv-data-table-row>
          </template>
        </cv-data-table>
      </cv-column>
    </cv-row>
    <p>This {{ selectedTimeFrame }}</p>
    <cv-row>
      <cv-column :sm="2" :md="4" :lg="6">
        <cv-tile :light="lightTile">
          <div v-if="loading" >
          Loading Line Chart...
          <HBarChart :data = [] :loading="loading"/>
          </div>
          <!-- Include the BarChart component -->
          <HBarChart v-if="bar_chart_data.length > 0" :data = 'bar_chart_data' :period = "this.selectedTimeFrame" :commodity = "selectedCommodity" :eventType = "handleBarClickInit"/>
        </cv-tile>
      </cv-column>
      </cv-row>
    <template>
      <cv-modal
    :visible="line_modal_visible"
    @modal-hidden="line_modal_visible = false"
  >
  <template slot="title">
      Fail View - {{  this.selectedVar }}
    </template>
  <template slot="content">
  <cv-data-table
    title="Monthly View " 
    :data="filteredLineChartData"
    ref="table"
    action-bar-aria-label
  >
    <template slot="headings">
      <cv-data-table-heading heading="Month" sortable />
      <cv-data-table-heading heading="Code Name" sortable />
      <cv-data-table-heading heading="Fail Count" sortable />
      <cv-data-table-heading heading="Volume" sortable />
      <cv-data-table-heading heading="Yield" sortable />
      <cv-data-table-heading heading="Target" sortable />
      <cv-data-table-heading heading="XFactor" sortable />
    </template>
  </cv-data-table>
</template>

    </cv-modal>

    <cv-modal
    :visible="issue_modal_visible"
    @modal-hidden="issue_modal_visible = false"
  >
    <template slot="title">
      {{ this.desc }}
    </template>
    <template slot="label">
      PN: {{ this.pn }}
    </template>
    <template slot="content">
      <div class="modal-content" tabindex="-1">
        <div class="modal-column">
          <LineChart
            v-if="issue_line_chart_data.length > 0"
            :data="issue_line_chart_data"
            @point-clicked="handlePointClick"
            :eventType="handleCheckClickInit"
            :loading="loading"
            :height = "line_height"
          />
        </div>
        <div class="modal-column dropdown-column">
          <cv-row>
          <cv-column>
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedCompare"
            label="Compare"
            :items="compare_array"
          ></cv-dropdown>
        </cv-column>
          <cv-column>
            <cv-dropdown
            class="short-dropdown"
            v-model="selectedCompareVar"
            label = "Variable"
            :items="selectedCompare === 'Within' ? campare_w_vars : campare_a_vars"
            v-if="selectedCompare"
          ></cv-dropdown>
          
          </cv-column>
          </cv-row>
          <cv-row>
              <cv-column>
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedTime"
            label = "Timeframe"
            :items="timeFrameOptions"
          ></cv-dropdown>
          </cv-column>
        </cv-row>
        </div>
      </div>
    </template>
    <template slot="secondary-button">Return</template>
    <template slot="primary-button">Submit Action</template>
  </cv-modal>



  <cv-modal
    :visible="pn_modal_visible"
    @modal-hidden="pn_modal_visible = false"
    
  >
    <template slot="title">
      Modal View
    </template>
    <template slot="content">
      <div class="modal-content" tabindex="-1">
        <div class="modal-column">
          <BarChart v-if="drill_counts.length > 0" :data="drill_counts" :eventType = "handleBarClick" :loading = "false"/>
        </div>
        <div class="modal-column dropdown-column">
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedDrill"
            label="Drill Mode"
            :items="drillMode"
          ></cv-dropdown>
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedNewCol"
            label="Drill Column"
            :items="drillCol"
          ></cv-dropdown>
          <p> Viewing data in order: {{ this.stepLog }}</p>
        </div>
      </div>
    </template>
  </cv-modal>
  <cv-modal
    :visible="validation_modal_visible"
    @modal-hidden="validation_modal_visible = false"
  >
    <!-- Modal Title with Chevron Navigation -->
    <template slot="title">
      <div class="modal-title-container">
        <span>Validation Modal</span>
        <div class="chevron-container">
          <cv-button class="chevron-button" @click="goToPrevious">
            <ChevronLeft20 class="chevron-icon" />
          </cv-button>
          <cv-button class="chevron-button" @click="goToNext">
            <ChevronRight20 class="chevron-icon" />
          </cv-button>
        </div>
      </div>
    </template>

    <!-- Modal Content -->
    <template slot="content">
      <cv-column>

        <!-- ID Section -->
        <cv-row>
          <cv-column>
            <h1 class="bx--label">ID</h1>
            <p>{{ selectedValidID }}</p>
          </cv-column>
        </cv-row>

        <!-- PN Section -->
        <cv-row>
          <cv-column>
            <h1 class="bx--label">PN</h1>
            <p>{{ selectedValidPN }}</p>
          </cv-column>
        </cv-row>

        <!-- SN Section -->
        <cv-row>
          <cv-column>
            <h1 class="bx--label">SN</h1>
            <p>{{ selectedValidSN }}</p>
          </cv-column>
        </cv-row>
        <!-- Description Section -->
        <cv-row>
          <cv-column>
            <h1 class="bx--label">Description</h1>
            <p>Description of defect here.</p>
          </cv-column>
        </cv-row>

        <!-- Dropdowns for Root Cause 1 and 2 -->
        <cv-row>
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedRC1"
            label="Root Cause 1"
            :items="vRC1"
          ></cv-dropdown>
          <cv-dropdown
            class="short-dropdown"
            v-model="selectedRC2"
            label="Root Cause 2"
            :items="vRC2"
          ></cv-dropdown>
        </cv-row>

      </cv-column>
    </template>

    <!-- Modal Buttons -->
    <template slot="secondary-button">Close</template>
    <template slot="primary-button">Save</template>
  </cv-modal>
</template>
  </cv-grid>
</template>





<script>
import BarChart from '../../components/BarChart';
import LineChart from '../../components/LineChart';
import HeatMap from '../../components/HeatMap';
import HBarChart from '../../components/HBarChart'; //import barchart
import { ChevronLeft20, ChevronRight20 } from '@carbon/icons-vue';
import MainHeader from '../../components/MainHeader';

export default {
  name: 'CommodityOverview',
  components: {
    MainHeader,
    HBarChart,
    LineChart,
    BarChart,
    HeatMap,
    ChevronLeft20,
    ChevronRight20
  },
  data() {
    return {
      lightTile: true,
      bar_chart_data: [], // Initialize bar_chart_data as an empty array
      line_chart_data: [],
      line_modal_visible: false,
      heatmap_data: [],
      pn_modal_visible: false,
      validation_modal_visible: false,
      drillMode: ['Drill', 'Validate',"Display Data"],
      drillCol: ['LVL1', 'LVL2',"ROOT_CAUSE"],
      selectedDrill: "",
      filteredLineChartData:[],
      selectedVar: "",
      selectedNewCol: "",
      timeFrameOptions: ["Week", "Month", "Quarter", "Year"],
      selectedTimeFrame: "per",
      selectedCommodity:"Cable",
      commodityOptions: ["Channel", "Power", "Cable"],
      drill_counts: [],
      vIDs:["ID1","ID2","ID3"],
      vPNs:["PN1","PN2","PN3"],
      vSNs:["SN1","SN2","SN3"],
      vRC1:["Workmanship", "Supplier"],
      vRC2:["Installed", "Uninstalled"],
      selectedRC1:"",
      selectedRC2:"",
      selectedValid:"",
      selectedValidID:"",
      selectedValidPN:"",
      selectedValidSN:"",
      defect_id: 0,
      kind: "primary",
      stepLog: [],
      loading: true,
      loading2: true,
      line_height: "250px",
      selectedIndex: 0,
      p_columns: ['', 'Group', 'PN', 'Description'],
      p_rows: [
        {
          group: 'CDFP',
          pn: 'Multiple',
          desc: "July XFactor above UCL",
          chart: "P_chart"
        },
        {
          group: 'SMP9',
          pn: '02EA657',
          desc: "SMP9 fail rate uptick June - August.",
          chart: "line_chart"
        },
      ],
      useFixed: true,
      compare_array: ["Against", "Within", "Time"],
      campare_w_vars: ["Supplier", "Vintage Date"],
      campare_w_vars_mult: ["PN", "Supplier", "Vintage Date"],
      campare_a_vars: ["PN", "Commodity", "Group"],
      campare_a_vars_mult: ["Commodity", "Group", "Commodity - Other", "Group - Other"],
      issue_line_chart_data: [],
      issue_modal_visible: false,
      desc: "",
      pn: "",
      group:"",
      chart: "",
      selectedCompare: "",
      selectedCompareVar: "",
      selectedTime: "",
      formattedDate: new Date().toISOString().split('T')[0]
    };
  },
  mounted() {
    this.load_pns_group();
    
    //this.load_line_chart_issue();
  },
  watch: {
    selectedCommodity(newCom) {
    if (newCom) {
      this.load_pns_group();
      // this.load_bar_chart()
      // this.load_line_chart();
      // this.load_heatmap();
    }
  },
  },

  methods: {
    handleView(row) {
    this.issue_modal_visible = true;
    this.load_line_chart_issue();
    this.desc = row.desc;
    this.pn = row.pn;
      console.log('See more clicked:', row);
    },
    onSelected() {
      this.heatmap_data = []
      this.load_heatmap();
      // if (this.selectedIndex === 1) {
      //   // Load heatmap data when Heatmap is selected
      //   this.load_heatmap();
      //   console.log("KE")
      // }
    },
    async load_pns_group() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)

        // Fetch data from the API
        fetch(process.env.VUE_APP_API_PATH + "get_pns_from_excel", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify({commodity: this.selectedCommodity}),
      })
        .then((response) => this.handleResponse(response))
        .then((data) => {
          if (data.status_res === "success") {
            this.comPNs = data.pns;
            console.log("JA", this.comPNs)
            this.loading = true;
            this.load_heatmap();
            this.testInitValid();
            this.load_line_chart();
          } 
          console.log(data);
        });
      }catch (error) {
        console.error("Error loading data:", error);
      }
    },
    async load_line_chart() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)

        // Fetch data from the API
        fetch(process.env.VUE_APP_API_PATH + "createPnPercentageArray", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify({PN: this.comPNs, startDate:"2024-06-01", endDate: this.formattedDate}),
      })
        .then((response) => this.handleResponse(response))
        .then((data) => {
          if (data.status_res === "success") {
            this.line_chart_data = data.data;
            console.log("Received line data:", this.line_chart_data);
            this.loading = false;
          } 
          console.log(data);
        });
      }catch (error) {
        console.error("Error loading data:", error);
      }
    },

    async load_line_chart_issue() {
      try {
        
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "createPnPercentageArray", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({PN: ["0000002EA657", "0000002EA660", "0000002EA661"], startDate:"2024-06-01", endDate: "2024-08-30"}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.issue_line_chart_data = data.data;
          console.log("Received data3:", data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }finally {
        this.loading = false;  // Set loading to false once data is loaded
      }
    },

    async load_heatmap() {
      try {
        this.loading2 = true;
        // let user_type = this.$store.getters.getUser_type;
        // let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "createPnPercentageArray", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({PN: this.comPNs, startDate:"2024-06-01", endDate: this.formattedDate}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.heatmap_data = data.data2;
          // this.loading2 = false;
          console.log("Received data22:", data);
          this.loading2 = false;
          
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
  async load_bar_chart() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "populate_chart", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.bar_chart_data = data.data.pn_data;
          this.new_drill_data = data.data.rows_list;
          this.selectedCol = "CODENAME"
          console.log("Received data:", data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    async load_modal_chart() {
      try {
        
        // let token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.HCL8jVfDLmIYqV12hIV8-8Zh2GlmYEAD3Hk35bOkvA4";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_fails_drill", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ column: this.selectedCol, new_column: this.selectedNewCol, var: this.selectedVar, data: this.new_drill_data }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.drill_counts = data.counts_by_name;
          this.new_drill_data = data.filtered_data;
          console.log("Modal - Received data:", data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },
    handlePointClick(data) {
      console.log("Point clicked:", data);
      // Navigate to a new page or display a message
      this.$router.push({ name: 'HelloPage' });
    },
    handleBarClickInit(data) {
      
      this.pn_modal_visible = true
      this.selectedVar = data.detail.datum.codeName
      this.load_modal_chart()
      // Update the chart data to only show the clicked bar
      console.log("Bar data received:", data);
      // this.bar_chart_data = [data];
      
      
    },
    handleCheckClickInit(data) {
      
      this.line_modal_visible = true
      this.selectedVar = data.currentTarget.innerText
      this.filteredLineChartData = this.line_chart_data.filter(item => item['Code Name'] === this.selectedVar);
      console.log("LL",this.filteredLineChartData)
    },

    handleBarClick(data) {
      if(this.selectedDrill === "Validate"){
        this.validation_modal_visible = true
        
      }else{
        console.log("GHA",this.drillMode)
      this.drill_counts = ''
      this.pn_modal_visible = true
      
      this.selectedVar = data.detail.datum.key
      this.load_modal_chart()
      this.stepLog.push(this.selectedVar)
      // Update the chart data to only show the clicked bar
      console.log("Bar data received:", data);
      this.selectedCol = this.selectedNewCol
      // this.bar_chart_data = [data];
      }
    },
    //test
    testInitValid() {
      this.selectedValidID = this.vIDs[this.defect_id];
      this.selectedValidPN = this.vPNs[this.defect_id];
      this.selectedValidSN = this.vSNs[this.defect_id];
    },
    goToPrevious() {
      this.defect_id -= 1
      this.selectedValidID = this.vIDs[this.defect_id];
      this.selectedValidPN = this.vPNs[this.defect_id];
      this.selectedValidSN = this.vSNs[this.defect_id];
      // Logic to go to the previous item
    },
    goToNext() {
      this.defect_id += 1
      this.selectedValidID = this.vIDs[this.defect_id];
      this.selectedValidPN = this.vPNs[this.defect_id];
      this.selectedValidSN = this.vSNs[this.defect_id];
      // Logic to go to the next item
    }

  }
};
</script>
