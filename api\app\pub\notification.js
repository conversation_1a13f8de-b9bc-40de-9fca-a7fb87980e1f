let user = require("./user.js");
const hbs = require('handlebars');
const nodemailer = require('nodemailer');
const logger = require('../utils/logger.js')
const transporter = nodemailer.createTransport({
    host: 'relay.us.ibm.com',
    secure: false,
    ignoreTLS: true,
    port: 25,
});

//function that trigger sending call as public function
/*
    when sending mail:
        to_user could be either uid in Mongo user db or email address.
        cc(optional) need to be email address.
        to_user and cc could be a string contains multi value, seperate with ';', like "aa@ibm;bb@ibm"
    when sending message: to_user and cc_uid(optional,seperate with ';') need to be uid.
    set 'pub/notifications.js -> sendMail -> mailOptions.to ' to yourself when testong on local.
*/
exports.trigger_notification = async (notificationType, key, value) => {

    try {
        console.log('value = ', JSON.stringify(value));
        if (notificationType.toLowerCase() === 'email' || notificationType.toLowerCase() === 'mail') {
            //sendEmail;
            return await email_notification_send(key, value);
        }
        else if (notificationType.toLowerCase() === 'msg' || notificationType.toLowerCase() === 'message') {
            //send message
            return await msg_notification_send(key, value);
        }
        else if (notificationType.toLowerCase() === 'both') {
            // send both
            const mailResult = await msg_notification_send(key, value);
            const msgResult = await email_notification_send(key, value);
            return mailResult && msgResult;
        } else {
            console.log("notificationType error - no match for " + notificationType);
            await logger.logError(`Notification: trigger_notification - no match type error: ${notificationType}`);
            return false;
        }
    }
    catch (error) {
        console.error('Error sending notification:', error);
        await logger.logError(`Notification: trigger_notification - error sending notification: ${error.message}`);
    }
}

// function that would generate email content and call send email - MongoDB removed
async function email_notification_send(key, notificationData) {
    if (!notificationData.to_user || notificationData.to_user === "") return true;

    // MongoDB removed - email templates and configuration disabled
    console.log("MongoDB removed - email notification functionality limited");
    await logger.logInfo(`Notification: email_notification_send - MongoDB removed, email functionality limited for key: ${key}`);

    console.log("start sending email");

    let to_user = notificationData.to_user;
    let to_user_convert = "";
    if (!to_user) return true;
    if (to_user.indexOf("@") < 0) {
        //get email address by uid
        to_user_convert = await user.get_users_mail_by_uidList(to_user);
        console.log('to_user_convert', to_user_convert);
        if (to_user_convert.indexOf("@") < 0) return true;
        to_user = to_user_convert;
    }

    // MongoDB removed - using default template
    const template = {
        subject: "Notification: {{key}}",
        content: "This is a notification regarding {{key}}",
        ReplyTo: "<EMAIL>",
        cc: ""
    };

    // Create simple email content
    const mailReg = {
        subject: `Notification: ${key}`,
        body: `<p>This is a notification regarding ${key}.</p><p>MongoDB has been removed, so template-based emails are disabled.</p>`
    };
    //if (to_user !== '<EMAIL>' && to_user !== '<EMAIL>') return true;
    let cc = notificationData.cc ? notificationData.cc : template.cc ? template.cc : '';
    const result = await sendMail(template.ReplyTo, to_user, mailReg.subject, cc, template.bcc ? template.bcc : '', '', mailReg.body);
    //const result = true;
    if (!result) {
        console.log('Mail sending error - ' + key + ' to: ' + to_user);
        await logger.logError('Mail sending error - ' + key + ' to: ' + to_user + ', subject: ' + mailReg.subject);
    } else {
        console.log("mail log = sent to:" + to_user + " cc to:" + cc + " - " + mailReg.subject);
        await logger.logInfo('Mail sent - ' + key + ', to: ' + to_user + (cc !== '' ? 'cc to:' + cc : '') + ', subject: ' + mailReg.subject);
    }
    return result;
}

//function that would send notification msg - MongoDB removed
async function msg_notification_send(key, notificationData) {
    console.log("start sending msg");
    if (!notificationData.to_user || notificationData.to_user === "") return;

    // MongoDB removed - message notification functionality disabled
    console.log("MongoDB removed - message notification functionality disabled");
    await logger.logInfo(`Notification: msg_notification_send - MongoDB removed, message functionality disabled for key: ${key}`);

    let to_user = notificationData.to_user;
    if (notificationData.cc_uid && notificationData.cc_uid !== '') {
        to_user += ";" + notificationData.cc_uid;
    }

    // Log the message that would have been sent
    if (to_user.indexOf(';') > 0) {
        let toList = to_user.split(';');
        toList = unique(toList);
        for (const user of toList) {
            if (user !== "") {
                console.log(`Message would have been sent to ${user} for key: ${key}`);
                await logger.logInfo(`Notification: msg_notification_send - MongoDB removed, message would have been sent to: ${user} for key: ${key}`);
            }
        }
    } else {
        console.log(`Message would have been sent to ${to_user} for key: ${key}`);
        await logger.logInfo(`Notification: msg_notification_send - MongoDB removed, message would have been sent to: ${to_user} for key: ${key}`);
    }

    return true;
}
function unique(arr) {
    return Array.from(new Set(arr))
}

// MongoDB removed - template retrieval disabled
async function get_template(key, templates) {
    try {
        // MongoDB removed - returning null as template
        await logger.logInfo(`Notification: get_template - MongoDB removed, template retrieval disabled for key: ${key}`);
        return null;
    } catch (err) {
        await handleError(err);
        return null;
    }
}


async function generateMail(mailTemplatePre, data) {

    let template = await hbs.compile(mailTemplatePre.content);

    let html = await template(data);
    template = await hbs.compile(mailTemplatePre.subject);

    let sub = await template(data);
    return { body: html, subject: sub }
}

async function generateNotification(templatePre, data) {
    let template = await hbs.compile(templatePre);

    let html = await template(data);
    return { body: html }
}


//function that send email out with given mail content
exports.sendMail = async (from, to, subject, cc, bcc, text, html) => {
    return new Promise(async (resolve, reject) => {
        // Create our mailing option object
        if (to == '') {
            console.log('mail not sent, send to is empty');
            await logger.logInfo(`Notification: sendMail - mail not sent, send to is empty`);
            return;
        }
        let mailOptions = {};
        mailOptions = {
            from,
            to: to,
            subject,
            text,
            cc,
            bcc,
            text: text,
            html: html
            //,attachments: this.attachments,
        }
        // mailOptions.to = '<EMAIL>';
        // mailOptions.cc = '';
        //mailOptions.to = mailOptions.to.replace("<EMAIL>", "<EMAIL>");
        // mailOptions.cc = mailOptions.cc.replace("<EMAIL>", "<EMAIL>");
        /*if (UAT) {
          if (mailOptions.to != '<EMAIL>') {
            resolve(success);
            return;
          }
        }
        */
        // Send email
        console.log("start call sending " + new Date());
        console.log("mailOptions " + JSON.stringify(mailOptions));
        await logger.logInfo(`Notification: sendMail - mailOptions: ${JSON.stringify(mailOptions)}`);
        transporter.sendMail(mailOptions, async (error, success) => {
            if (error) {
                reject(new Error(error));
                console.log(error);
                await logger.logError(`Notification: sendMail - error: ${error.message}`);
            } else {
                resolve(success);
                console.log('mail sent ' + new Date());
            }
        });
    });
}

async function handleError(err) {
    console.log(err);
    await logger.logError(`Notification: error: ${err.message}`);
}