{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue?vue&type=template&id=5d10017b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\QEDashboard.vue", "mtime": 1748528237445}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}