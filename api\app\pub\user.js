const logger = require('../utils/logger.js')
let cn_names = {};
//function that would get info from user collection - MongoDB removed
exports.get_user_info_by_uid = async (uid) => {
    try {
        // MongoDB removed - returning null as user info
        await logger.logInfo(`Common: get_user_info_by_uid - MongoDB removed, returning null for uid: ${uid}`);
        return null;
    } catch (err) {
        console.log(err);
        await logger.logError(`Common: get_user_info_by_uid - error: ${err.message}`);
        return false;
    }
}
//function that would get info from user collection - MongoDB removed
exports.get_user_info_by_email = async (userMail) => {
    try {
        // MongoDB removed - returning null as user info
        await logger.logInfo(`Common: get_user_info_by_email - MongoDB removed, returning null for email: ${userMail}`);
        return null;
    } catch (err) {
        console.log(err);
        await logger.logError(`Common: get_user_info_by_email - error: ${err.message}`, { mail: userMail });
        return false;
    }
}

//function that would get info from user collection - MongoDB removed
exports.get_user_info_by_cn = async (name) => {
    try {
        // MongoDB removed - returning null as user info
        await logger.logInfo(`Common: get_user_info_by_cn - MongoDB removed, returning null for name: ${name}`);
        return null;
    } catch (err) {
        console.log(err);
        await logger.logError(`Common: get_user_info_by_cn - error: ${err.message}`);
        return false;
    }
}
exports.get_users_mail_by_uidList = async (toString) => {
    const toList = toString.split(';');
    let mailString = "";
    for await (const user of toList) {
        if (user !== "") {
            const result = await this.get_user_info_by_uid(user);
            let targetMail = '';
            if (result) {
                if (typeof (result.mail) === 'string') {
                    targetMail = result.mail;
                } else {
                    targetMail = result.mail[0];
                }
                mailString = mailString === "" ? targetMail : mailString + ";" + targetMail;
            }
        }
    }
    return mailString;
}

//function that would unlock users locked for 5 times failed login - MongoDB removed
exports.unlock_users_locked = async () => {
    let showDate = new Date();
    showDate.setDate(showDate.getDate() - 1);
    console.log('Date lock 24 hours ago:' + showDate.toISOString());

    // MongoDB removed - returning empty array
    await logger.logInfo('MongoDB removed - unlock_users_locked function disabled');
    return [];
}

//function that would get user with expired access request - MongoDB removed
exports.get_users_expired = async () => {
    try {
        console.log("in get_users_expired");
        let showDate = new Date();
        showDate.setDate(showDate.getDate() - 8);
        console.log('Date of 8:' + showDate.toISOString());

        // MongoDB removed - returning empty array
        await logger.logInfo('MongoDB removed - get_users_expired function disabled');
        return [];

    } catch (err) {
        console.log(err);
        await logger.logError(`Common: get_users_expired - error: ${err.message}`);
        return false;
    }
}
//function that would get manager's delegate uid - MongoDB removed
exports.get_manager_delegate_by_uid = async (employee_uid) => {
    try {
        // MongoDB removed - returning empty string
        await logger.logInfo(`Common: get_manager_delegate_by_uid - MongoDB removed, returning empty string for employee_uid: ${employee_uid}`);
        return '';
    } catch (err) {
        console.log(err);
        await logger.logError(`Common: get_manager_delegate_by_uid - error: ${err.message}`);
        return false;
    }
}

exports.cacheMongoNames = async (INPUT) => {
    // MongoDB removed - returning input as name
    await logger.logInfo(`Common: cacheMongoNames - MongoDB removed, returning input as name: ${INPUT}`);
    return INPUT || '';
}

//function that would update user data - MongoDB removed
exports.update_user_data = async (option1, option2) => {
    return new Promise(async (resolve, reject) => {
        try {
            // MongoDB removed - returning mock result
            console.log('MongoDB removed - update_user_data function disabled');
            await logger.logInfo(`Common: update_user_data - MongoDB removed, operation simulated for id: ${option1}`);
            resolve({ acknowledged: true, modifiedCount: 1 });
        } catch (err) {
            console.log(err);
            await logger.logError(`Common: update_user_data - error: ${err.message}`);
            reject(new Error(err));
        }
    })
}

