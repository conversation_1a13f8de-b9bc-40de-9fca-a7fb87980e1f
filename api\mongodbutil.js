const host1 = "da27189a-256f-420b-9361-da334d79c77d-0.blijti4d0v0nkr55oei0.databases.appdomain.cloud";
const host2 = "da27189a-256f-420b-9361-da334d79c77d-1.blijti4d0v0nkr55oei0.databases.appdomain.cloud";
const host3 = "da27189a-256f-420b-9361-da334d79c77d-2.blijti4d0v0nkr55oei0.databases.appdomain.cloud";
const port = ":32040";
const user = "mongodb://admin:4Qualify_4Qualify"
const options = "ibmclouddb?authSource=admin&replicaSet=replset&readPreference=primary&appname=MongoDB%20Compass&ssl=true";
const { MongoClient } = require('mongodb');
const f = require("util").format;
const fs = require("fs");
const url = user + "@" + host1 + port + "," + host2 + port + "," + host3 + port + "/" + options;
const envName = process.env.env_name;

const urlOptions = {
    maxpoolSize: 100, // Number of connections in the pool
    sslValidate: true,
    //sslCA: [fs.readFileSync("cert.pem")],
    sslCA: "cert.pem",
    useNewUrlParser: true,
    useUnifiedTopology: true
};


// let _db;
// let _Notif_db;
//
//
const client = new MongoClient(url, urlOptions);

// Function to connect to the database
async function connectServer() {
    try {
        await client.connect();
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        throw error;
    }
}

// Function to get the MongoDB database instance


function getDB(dbName) {
    if (!envName) {
        return client.db("users");
    } else {
        console.log('envName = '+ envName);
        if(envName === 'Prod'){
            console.log('using Prod_users');
            return client.db("Prod_users");
        }else{
            console.log('using UAT users');
            return client.db("users");
        }
    }
}

function getNotifiDB(dbName) {
    //return client.db("notification");
    if (!envName) {
        return client.db("notification");
    } else {
        if(envName === 'Prod'){
            console.log('using Prod_notification');
            return client.db("Prod_notification");
        }else{
            console.log('using UAT_notification');
            return client.db("notification");
        }
    }
}

function getAdminDB(dbName) {
    
        return client.db("admin");
}


// Export the connection functions
module.exports = {
    connectServer,
    getDB,
    getNotifiDB,
    getAdminDB,
};
