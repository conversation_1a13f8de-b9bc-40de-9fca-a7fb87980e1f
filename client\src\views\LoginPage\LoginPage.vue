<style scoped lang="scss">
@import "../../styles/carbon-utils";

.login-page__r2 {
  margin-top: $spacing-12;
}

.login-page__input {
  margin-top: $spacing-05;
  margin-bottom: $spacing-05;
}

.login-page__button {
  margin-bottom: $spacing-06;
}

.login-page__title {
}

.login-page__remember_me {
}

.login-page__need_help {
  font-size: carbon--type-scale(1);
  margin-top: $spacing-08;
}

.login-page__helper {
  margin-bottom: $spacing-08;
  font-size: small;
  font-style: italic;
}

.policy-style {
  display: flex;
  align-items: center;
  width: fit-content;
}

.modal-title {
  color: #4589ff;
  display: flex;
  align-items: center;
  justify-content: left;
}
</style>
<template>
  <cv-grid class="login-page__grid">
    <login-header />
    <cv-loading :active="isActive" overlay></cv-loading>
    <cv-row class="login-page__r2">
      <cv-column :sm="2" :md="4" :lg="6">
        <cv-tile :light="lightTile">
          <h1 class="login-page__title">Log in</h1>
          <p style="color: #4589ff" class="login-page__helper">
            Requires manger approval
          </p>

          <cv-form @submit.prevent="">
            <cv-text-input
              class="login-page__email login-page__input"
              ref="email"
              :light="light"
              :label="label"
              :placeholder="placeholder"
              :invalid-message="invalidMessageUser"
              :type="inputTypeEmail"
              v-model="user"
              v-on:blur="checkLogin"
              autocomplete="off"
            >
            </cv-text-input>
            <cv-text-input
              id="password"
              class="login-page__pass login-page__input"
              :light="lightPass"
              :label="labelPass"
              :placeholder="placeholderPass"
              :invalid-message="invalidMessagePass"
              :type="inputTypePass"
              v-model="pass"
              autocomplete="off"
            >
            </cv-text-input>

            <div class="policy-style">
              <cv-checkbox
                :checked="true"
                v-model="ibm_policy"
                value="ibm_policy"
              ></cv-checkbox>
              <div>
                Accepted
                <a
                  href="https://w3.ibm.com/w3publisher/w3-privacy-notice"
                  target="_blank"
                  >IBM Privacy Policy</a
                >
              </div>
            </div>

            <div class="policy-style">
              <cv-checkbox
                :checked="true"
                v-model="qualify_policy"
                value="qualify_policy"
              >
              </cv-checkbox>
              <div>
                Accepted
                <a
                  href="javascript:void(0)"
                  @click="ibm_qulify_privacy_visible = true"
                  >IBM Qualify- Privacy Notice</a
                >
              </div>
            </div>

            <cv-button
              style="float: right"
              class="login-page__button"
              kind="primary"
              v-on:click="login"
              :disabled="!ibm_policy || !qualify_policy"
              >Log in
            </cv-button>

            <cv-checkbox
              class="login-page__remember_me"
              :label="rememberText"
              :checked="checked"
              :value="value"
              v-model="rememberMe"
            >
            </cv-checkbox>

            <p class="login-page__need_help">
              Need Help?
              <a href="https://w3.ibm.com/#/support/" target="_blank"
                >IBM help desk</a
              >
            </p>
          </cv-form>
        </cv-tile>
      </cv-column>
      <cv-column :sm="2" :md="4" :lg="6">
        <img
          class="landing-page__illo"
          src="../../assets/tab-illo.png"
          alt="Carbon illustration"
          height="550"
      /></cv-column>
    </cv-row>

    <cv-modal
      @modal-hidden="not_approved_modal_visible = false"
      :visible="not_approved_modal_visible"
    >
      <template slot="title">
        <h2 class="modal-title">Approval request sent to manager</h2>
      </template>
      <template slot="content">
        <p>Manager must approve you to use Qualify</p>
      </template>
      <!--      <template v-if="use_secondaryButton" slot="secondary-button">Add Later</template>-->
      <!--      <template v-if="use_primaryButton" slot="primary-button">Add Checklist Points</template>-->
    </cv-modal>

    <cv-modal
      @modal-hidden="archived_user_modal_visible = false"
      :visible="archived_user_modal_visible"
    >
      <template slot="title">
        <h2 class="modal-title">User Archived</h2>
      </template>
      <template slot="content">
        <p>To reinstate Qualify access, please contact your manager.</p>
      </template>
      <!--      <template v-if="use_secondaryButton" slot="secondary-button">Add Later</template>-->
      <!--      <template v-if="use_primaryButton" slot="primary-button">Add Checklist Points</template>-->
    </cv-modal>

    <cv-modal
      @modal-hidden="ibm_qulify_privacy_visible = false"
      :visible="ibm_qulify_privacy_visible"
    >
      <template slot="title">
        <h2 class="modal-title">
          IBM Qualify’s Privacy Notice - IBM Supply Chain Engineering Tool
        </h2>
      </template>
      <template slot="content">
        <p>
          IBM Qualify provides data services to users to track manufacturing
          certifications.
        </p>
        <p>IBM Qualify is available for use by IBM Employees.</p>
        <p>
          IBM Qualify requires access to your personal information from IBM w3
          such as name, email, and role to help grant proper tool access and
          track certifications.
        </p>
        <p>
          Usage and access to your provided personal information is 1)
          restricted to authorized Users and Administrators in IBM Qualify and
          2) is governed by IBM global privacy rules.
        </p>
        <p>
          Important! In addition to the described data privacy acknowledgements,
          please adhere to the IBM Business Conduct Guidelines and your local
          country regulations.
        </p>
        <p>
          If you have any data privacy concerns or desire more information about
          the function of IBM Qualify, what data is collected and processed by
          the application, as well as to obtain more details about your rights,
          please contact us in the Slack Channel #qualify-application-support.
        </p>
        <p>
          Please acknowledge these terms as well as IBM’s Internal Privacy
          Statement to access IBM Qualify.
        </p>
      </template>
    </cv-modal>

    <cv-modal
      @modal-hidden="warn_modal_visible = false"
      :visible="warn_modal_visible"
    >
      <template slot="title">
        <h2 class="modal-title">Warning</h2>
      </template>
      <template slot="content">
        <p>{{ warning_content }}</p>
      </template>
      <!--      <template v-if="use_secondaryButton" slot="secondary-button">Add Later</template>-->
      <!--      <template v-if="use_primaryButton" slot="primary-button">Add Checklist Points</template>-->
    </cv-modal>
  </cv-grid>
</template>

<script>
import router from "@/router";
import { mapMutations } from "vuex";
import LoginHeader from "../../components/LoginHeader";
// const contentType = "application/json";

let clicked = false;
let typed = false;
export default {
  name: "LoginPage",
  components: {
    LoginHeader,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    checked: {
      type: Boolean,
      default: false,
    },
    rememberText: {
      type: String,
      default: "Stay logged in",
    },
    lightTile: {
      type: Boolean,
      default: true,
    },
    light: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "IBM Email Address",
    },
    placeholder: {
      type: String,
      default: "<EMAIL>",
    },
    lightPass: {
      type: Boolean,
      default: false,
    },
    labelPass: {
      type: String,
      default: "Password",
    },
    placeholderPass: {
      type: String,
      default: "",
    },
    inputTypePass: {
      type: String,
      default: "password",
    },
    inputTypeEmail: {
      type: String,
      default: "email",
    },
  },
  data() {
    return {
      user: "",
      pass: "",
      tables: [
        "admin_name",
        "cert_name_certifier",
        "engineer_name",
        "family_mt",
        "cert_areas",
        "cert_name",
        "oper_cert_training",
        "calc_oper_perf",
        "cert_name_checklist",
      ],
      actions: ["view", "edit"],
      conditionalRendering: {
        seen: false,
      },
      invalidMessageUser: "",
      invalidMessagePass: "",
      rememberMe: false,
      not_approved_modal_visible: false,
      archived_user_modal_visible: false,
      ibm_qulify_privacy_visible: false,
      isActive: false,
      ibm_policy: true,
      qualify_policy: true,
      warn_modal_visible: false,
      warning_content: "",
    };
  },
  mounted() {
    // this.focusEmail();
  },
  methods: {
    ...mapMutations([
      "setUser",
      "setToken",
      "setAuthenticated",
      "setManagerName",
      "setUserType",
      "setUserID",
      "setIsManager",
      "addToMyArray",
      "setEmail",
      "setIsDelegate",
    ]),
    login: function (e) {
      e.preventDefault();
      //router.push('home')
      const { user, pass, conditionalRendering } = this;
      if (user !== "" && pass !== "") {
        // Making data object values accessible here
        //fetch('https://localhost:8080/api-qualify/login', {
        console.log(process.env.VUE_APP_API_PATH);
        this.isActive = true;
        fetch(process.env.VUE_APP_API_PATH + "login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ user, pass }),
        })
          .then((response) => response.json())
          .then((data) => {
            this.isActive = false;
            
            // data stores responseObj from login.js in api

            // invalid cred: 1
            // email not found: 2
            // success: 0
            console.log(data);
            if (data.auth === 0) {
              
              // Store userObject values in local variables
              //const isManager = data.userObject.ismanager
              //const isCertifier = data.userObject.isCertifier
              //const isME = data.userObject.isME
              const manager_uid = data.manager_uid;
              const user_talent_id = data.user_talent_id;
              // Authentication Token and User Basic Info
              //const userBasicInfo = data.userBasicInfo
              const {
                token,
                name,
                user_type,
                username,
                ismanager,
                isDelegate,
              } = data;
              //const username = data.username

              this.setUser(name);
              this.setToken(token);
              this.setUserType(user_type);
              this.setManagerName(manager_uid);
              this.setUserID(user_talent_id);
              this.setAuthenticated(true);
              this.setEmail(username);
              this.setIsManager(ismanager);
              this.setIsDelegate(isDelegate);

              localStorage.setItem("token", token);
              localStorage.setItem("authenticated", "true");
              // localStorage.setItem('user', JSON.stringify(userBasicInfo))
              localStorage.setItem("user", name);
              localStorage.setItem("user_type", user_type);
              // localStorage.setItem('managerName', managerName)
              localStorage.setItem("user_talent_id", user_talent_id);
              localStorage.setItem("email", username);
              localStorage.setItem("ismanager", ismanager);
              localStorage.setItem("isdelegate", isDelegate);
              router.push("home-page")
              //console.log("local storage of name: "+ localStorage.getItem('email'));
              

              // if (data.approved === 1 && (data.archived === 1 || data.archived === null)) {
              //   this.not_approved_modal_visible = true;
              //   fetch(process.env.VUE_APP_API_PATH + "user_request", {
              //     method: "POST",
              //     headers: {
              //       "Content-Type": contentType,
              //       Authorization: "Bearer " + data.token,
              //     },
              //     body: JSON.stringify(data),
              //   }).then((response) => response.json());
              // } else if (data.approved === 0 && data.archived === 0) {
              //   this.archived_user_modal_visible = true;
              // } else if (data.approved === 0 && data.archived === 1) {
                // if (isDelegate) {
                //   router.push("manager");
                // } else {
                //   switch (user_type) {
                //     case "manager":
                //       router.push("manager");
                //       break;
                //     case "certifier":
                //       router.push("certifier");
                //       break;
                //     case "trainer":
                //       router.push("trainer");
                //       break;
                //     case "developer":
                //       router.push("developer");
                //       break;
                //     default:
                //       router.push("home");
                //   }
                // }
              // } else {
              //   console.log("ERROR with approval & archive user status.");
              // }

              // if (isManager === 'Y') {
              //   // this.$router.push({ name: 'mgrHome' });
              // } else if (isME === 'Y') {
              //   // this.$router.push({ name: 'meHome' });
              // } else if (isCertifier === 'Y') {
              //   // this.$router.push({ name: 'certHome' });
              // } else {
              //   // this.$router.push({ name: 'userHome' });
              // }
            } else {
              
              /* conditionalRendering is set to true if authentication  is not successful
               * This values is used in template to display error message "Your w3id or password was entered incorrectly"
               * */
              localStorage.removeItem("token");

              if (pass.length < 15) {
                this.invalidMessagePass =
                  "Incorrect IBMid or password. Passwords must be at least 15 characters.";
              } else {
                this.invalidMessagePass =
                  "Incorrect IBMid or password. Try again.";
              }
              this.invalidMessageUser =
                "Incorrect IBMid or password. Try again.";

              if (data.failed_count === 3 || data.failed_count === 4) {
                this.warning_content = `Fail attempt ${data.failed_count} has been made. User will be locked out after 5 fail attempts.`;
                this.warn_modal_visible = true;
              } else if (data.failed_count === 5) {
                this.warning_content = `Access has been locked for 24 hours, please contact the administrator.`;
                this.warn_modal_visible = true;
              }

              conditionalRendering.seen = true;
            }
          })
          .catch((err) => {
            console.log(err);
            this.isActive = false;
          });
      } else {
        if (user === "") {
          this.invalidMessageUser = "IBMid or email is required";
        }
        if (pass === "") {
          this.invalidMessagePass = "Password is required";
        }
      }
    },


    focusEmail: function () {
      // this.$refs.user.focus();
    },
    checkLogin: function () {
      if (typed) {
        const { user } = this;

        clicked = true;

        const valid = this.validateEmail(user);
        if (valid) {
          this.invalidMessageUser = "";
          this.invalidMessagePass = "";
        } else {
          this.invalidMessageUser = "Enter a valid IBMid or email address";
        }
      }
    },
    validateEmail: function (email) {
      const re = /\S+@\S+\.\S+/;
      return re.test(email);
    },
  },
  created() {
    window.addEventListener("keydown", () => {
      typed = true;
      if (clicked) {
        const { user } = this;

        const valid = this.validateEmail(user);
        if (valid) {
          this.invalidMessageUser = "";
          this.invalidMessagePass = "";
        } else {
          this.invalidMessageUser = "Enter a valid IBMid or email address";
        }
      }
    });
  },
};
</script>

