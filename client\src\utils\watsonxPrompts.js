/**
 * WatsonX.ai Prompt Templates
 *
 * This file contains prompt templates for WatsonX.ai integration.
 * Edit these templates to customize the AI behavior.
 */

// Dashboard AI summary prompt template
export const dashboardSummaryPrompt = `
You are a helpful assistant that analyzes manufacturing quality data.

Task: Summarize the following XFactor data and relate it to the provided action tracker insights.

Instructions:
1. XFactor is the ratio of actual defect rate to target rate. Values above 1.0 indicate problems.
2. High XFactors (above target) are bad. If under target, there are no issues unless trending upwards towards 1.0.
3. Relate any changes in data to action tracker items if possible.
4. If there is a potential issue, use action tracker insights for that item to give suggestions on how to fix.
5. Focus on the current status: whether there are current issues, past issues to look out for, or if the group is performing well.
6. Keep your response to 3 sentences or less.
7. Be factual and avoid speculation.

Data:
{{data}}

Action Tracker Insights:
{{actionTrackerInsights}}

Summary:
`;

// Function to format the dashboard summary prompt with actual data
export function formatDashboardSummaryPrompt(data, actionTrackerInsights) {
  return dashboardSummaryPrompt
    .replace('{{data}}', JSON.stringify(data, null, 2))
    .replace('{{actionTrackerInsights}}', actionTrackerInsights || 'No action tracker insights available.');
}

// Root Cause Analysis prompt template
export const rootCauseAnalysisPrompt = `
You are a manufacturing quality analyst providing a brief performance summary.

Task: Create a simple summary of the breakout group's performance.

Instructions:
1. Start with an overall assessment of the breakout group's performance (good, concerning, critical).
2. IMPORTANT: If critical issues exist, you MUST acknowledge them in your summary.
3. If critical issues exist, state how many there were without listing specific root causes.
4. DO NOT include any numbers about failures or percentages in the summary.
5. Keep your response to 2 short sentences maximum.
6. Be direct and factual - this is just a high-level summary above the chart.
7. Note that critical issues are only flagged when BOTH of these conditions are met:
   - The total fail rate for the month exceeds the target rate
   - EITHER:
     a. A specific root cause shows a 3x increase in fail rate compared to the previous month, OR
     b. A new root cause appears that wasn't present in the previous month (with a significant fail rate)
8. For new root causes, emphasize that these are new failure modes that require immediate investigation.
9. NEVER say there are no critical issues if the data shows critical issues exist.

Breakout Group: {{breakoutGroup}}
Time Period: {{timeRange}}
Critical Issues: {{criticalIssues}}

Summarized Category Data:
{{summaryData}}

Action Tracker Insights:
{{actionTrackerInsights}}

Summary:
`;

// Function to format the root cause analysis prompt with actual data
export function formatRootCauseAnalysisPrompt(data, actionTrackerInsights) {
  // Check if critical issues are directly provided in the data
  let criticalIssueNote;

  if (data.criticalIssues && Array.isArray(data.criticalIssues)) {
    // Use the critical issues directly from the data
    const count = data.criticalIssues.length;
    criticalIssueNote = count > 0 ?
      `${count} critical issue${count > 1 ? 's' : ''} detected` :
      "None detected";

    console.log(`Using ${count} critical issues directly from data`);

    // If we have critical issues but the AI didn't detect them before, force it
    if (count > 0 && data.forceCriticalIssueAcknowledgment) {
      criticalIssueNote = `${count} critical issue${count > 1 ? 's' : ''} detected that require immediate attention`;
      console.log('Forcing critical issue acknowledgment in AI summary');
    }
  } else {
    // Fall back to extracting critical issues from categories
    const criticalIssues = extractCriticalIssues(data.categories);

    // Log the critical issues for debugging
    console.log('Critical issues detected for AI summary:', criticalIssues);

    // Count the number of critical issues
    const criticalIssueCount = criticalIssues === "None detected" ? 0 :
      criticalIssues.split(';').length;

    // Add a note about the number of critical issues for the AI
    criticalIssueNote = criticalIssueCount > 0 ?
      `${criticalIssueCount} critical issue${criticalIssueCount > 1 ? 's' : ''} detected` :
      "None detected";
  }

  // Create a summarized version of the category data
  const summaryData = summarizeCategoryData(data.categories);

  // Create the prompt with the data
  const prompt = rootCauseAnalysisPrompt
    .replace('{{breakoutGroup}}', data.breakoutGroup)
    .replace('{{timeRange}}', data.timeRange)
    .replace('{{criticalIssues}}', criticalIssueNote) // Use the simplified count instead of full details
    .replace('{{summaryData}}', JSON.stringify(summaryData))
    .replace('{{actionTrackerInsights}}', actionTrackerInsights ?
      summarizeActionTrackerData(actionTrackerInsights) :
      'No action tracker insights available.');

  // Log the final prompt for debugging
  console.log('AI prompt generated with critical issues:', criticalIssueNote !== 'None detected');

  return prompt;
}

// Helper function to extract critical issues based on new criteria:
// 1) Full month fail rate must be above target rate
// 2) Root cause for a month is 3x the fail rate from cumulative fails in past months
function extractCriticalIssues(categoryData) {
  if (!categoryData || !Array.isArray(categoryData) || categoryData.length === 0) {
    return "None detected";
  }

  // Group data by category
  const categoriesMap = {};
  categoryData.forEach(item => {
    if (!categoriesMap[item.group]) {
      categoriesMap[item.group] = [];
    }
    categoriesMap[item.group].push({
      month: item.key,
      value: item.value,
      // Include any additional data that might be available
      defects: item.data && item.data.defects,
      volume: item.data && item.data.volume,
      totalFailRate: item.data && item.data.totalFailRate,
      targetRate: item.data && item.data.targetRate,
      isAboveTarget: item.data && item.data.isAboveTarget
    });
  });

  // Calculate total fail rate for each month to compare against target
  const months = [...new Set(categoryData.map(item => item.key))];
  const monthlyTotalFailRates = {};

  months.forEach(month => {
    const monthItems = categoryData.filter(item => item.key === month);
    let totalFailRate = 0;
    let targetRate = 0;

    // Sum up all categories for this month
    monthItems.forEach(item => {
      totalFailRate += item.value;
      // If any item has target rate info, use it
      if (item.data && item.data.targetRate) {
        targetRate = item.data.targetRate;
      }
    });

    // Store the total fail rate for this month
    monthlyTotalFailRates[month] = {
      failRate: totalFailRate,
      targetRate: targetRate,
      isAboveTarget: targetRate > 0 ? totalFailRate > targetRate : false // Only check if we have a target rate
    };
  });

  // Identify critical issues (3x spikes where total fail rate is above target)
  const criticalIssues = [];

  Object.entries(categoriesMap).forEach(([category, data]) => {
    // Sort by date
    data.sort((a, b) => new Date(a.month + '-01') - new Date(b.month + '-01'));

    // Check for 3x spikes
    for (let i = 1; i < data.length; i++) {
      const current = data[i];
      const previous = data[i-1];

      // Check if the month's total fail rate is above target
      const isMonthAboveTarget = monthlyTotalFailRates[current.month] && monthlyTotalFailRates[current.month].isAboveTarget;

      // Only consider as critical if both conditions are met:
      // 1. Month's total fail rate is above target
      // 2. Either:
      //    a. This category has a 3x spike from previous month, OR
      //    b. This is a new root cause that wasn't present before (previous.value is 0)
      if (isMonthAboveTarget &&
          ((current.value >= previous.value * 3 && previous.value > 0) ||
           (previous.value === 0 && current.value > 0.1))) {
        // Format month name
        const [year, monthNum] = current.month.split('-');
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const monthName = monthNames[parseInt(monthNum) - 1];

        // Create a concise version for the AI summary that includes both conditions
        if (previous.value === 0) {
          // New root cause that wasn't present before
          criticalIssues.push(
            `${category} (new) emerged as a new failure mode in ${monthName} ${year} with a rate of ${current.value.toFixed(2)}% when the total fail rate exceeded the target`
          );
        } else {
          // Existing root cause with a spike
          criticalIssues.push(
            `${category} failure rate spiked ${(current.value/previous.value).toFixed(1)}x in ${monthName} ${year} when the total fail rate exceeded the target`
          );
        }
      }
    }
  });

  return criticalIssues.length > 0 ? criticalIssues.join("; ") : "None detected";
}

// Helper function to summarize category data
function summarizeCategoryData(categoryData) {
  if (!categoryData || !Array.isArray(categoryData) || categoryData.length === 0) {
    return [];
  }

  // Group by category and calculate averages
  const categoryAverages = {};
  const categoryTrends = {};

  // Group data by category
  categoryData.forEach(item => {
    if (!categoryAverages[item.group]) {
      categoryAverages[item.group] = {
        sum: 0,
        count: 0,
        values: []
      };
    }

    categoryAverages[item.group].sum += item.value;
    categoryAverages[item.group].count++;
    categoryAverages[item.group].values.push({
      month: item.key,
      value: item.value
    });
  });

  // Calculate averages and trends
  Object.keys(categoryAverages).forEach(category => {
    const data = categoryAverages[category];
    const average = data.sum / data.count;

    // Sort values by date to determine trend
    data.values.sort((a, b) => new Date(a.month + '-01') - new Date(b.month + '-01'));

    // Calculate trend (positive if increasing, negative if decreasing)
    let trend = 0;
    if (data.values.length >= 2) {
      const first = data.values[0].value;
      const last = data.values[data.values.length - 1].value;
      trend = ((last - first) / first) * 100;
    }

    categoryTrends[category] = {
      average: average.toFixed(2),
      trend: trend.toFixed(1),
      direction: trend > 0 ? "increasing" : trend < 0 ? "decreasing" : "stable"
    };
  });

  // Return the top 5 categories by average value
  return Object.entries(categoryTrends)
    .sort((a, b) => parseFloat(b[1].average) - parseFloat(a[1].average))
    .slice(0, 5)
    .map(([category, data]) => ({
      category,
      average: data.average + "%",
      trend: data.direction + " (" + data.trend + "%)"
    }));
}

// Helper function to summarize action tracker data
function summarizeActionTrackerData(actionTrackerData) {
  if (!actionTrackerData || actionTrackerData === 'No action tracker insights available.') {
    return 'No action tracker insights available.';
  }

  // If it's already a string, truncate it to reduce tokens
  if (typeof actionTrackerData === 'string') {
    return actionTrackerData.length > 300 ?
      actionTrackerData.substring(0, 300) + '...' :
      actionTrackerData;
  }

  // If it's an array or object, convert to string and truncate
  const dataString = JSON.stringify(actionTrackerData);
  return dataString.length > 300 ?
    dataString.substring(0, 300) + '...' :
    dataString;
}

// Defect Classification prompt template
export const defectClassificationPrompt = `
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following defect description with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: One of "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - "Scratches": Surface damage, marks, or abrasions
   - "Bent": Components that are deformed, twisted, or not straight
   - "Plugging": Connection issues, improper seating, or insertion problems
   - "Discolor": Discoloration, stains, or color changes
   - "Misalignment": Components not properly aligned or positioned
   - "Need More Info": When the description is too vague to determine a specific subcategory
   - "Other": For issues that don't fit the above subcategories
4. Respond with ONLY the classification in JSON format like this: {"primary": "Mechanical", "subcategory": "Bent"}

Defect Description:
{{description}}

Classification:
`;

// Function to format the defect classification prompt with actual data
export function formatDefectClassificationPrompt(description) {
  return defectClassificationPrompt.replace('{{description}}', description || 'No description available.');
}

// Batch Defect Classification prompt template
export const batchDefectClassificationPrompt = `
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following batch of defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: One of "Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", or "Other"

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - "Scratches": Surface damage, marks, or abrasions
   - "Bent": Components that are deformed, twisted, or not straight
   - "Plugging": Connection issues, improper seating, or insertion problems
   - "Discolor": Discoloration, stains, or color changes
   - "Misalignment": Components not properly aligned or positioned
   - "Need More Info": When the description is too vague to determine a specific subcategory
   - "Other": For issues that don't fit the above subcategories
4. Respond with ONLY a JSON array containing the classifications for each defect in the same order as provided.
   Format: [{"primary": "Mechanical", "subcategory": "Bent"}, {"primary": "Functional", "subcategory": "Other"}, ...]

Defect Descriptions:
{{descriptions}}

Classifications:
`;

// Function to format the batch defect classification prompt with actual data
export function formatBatchDefectClassificationPrompt(descriptions) {
  // Convert array of descriptions to a numbered list
  const formattedDescriptions = descriptions.map((desc, index) =>
    `${index + 1}. ID: ${desc.id} - ${desc.description || 'No description available.'}`
  ).join('\n');

  return batchDefectClassificationPrompt.replace('{{descriptions}}', formattedDescriptions);
}

// Export other prompt templates as needed
