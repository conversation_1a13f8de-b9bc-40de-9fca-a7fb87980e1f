{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748533368649}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAqnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Root Cause Analysis Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Root Cause Analysis</h4>\n            <div class=\"section-subtitle\">Current Month Analysis</div>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart Controls -->\n        <div class=\"chart-controls\">\n          <div class=\"control-group\">\n            <label for=\"root-cause-view-dropdown\" class=\"control-label\">View By:</label>\n            <cv-dropdown\n              id=\"root-cause-view-dropdown\"\n              v-model=\"rootCauseViewBy\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n          <div class=\"control-group\">\n            <label for=\"root-cause-time-dropdown\" class=\"control-label\">Time Range:</label>\n            <cv-dropdown\n              id=\"root-cause-time-dropdown\"\n              v-model=\"rootCauseTimeRange\"\n              @change=\"handleRootCauseTimeRangeChange\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n              <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n          <div class=\"control-group\" v-if=\"breakoutGroups && breakoutGroups.length > 0\">\n            <label for=\"root-cause-group-dropdown\" class=\"control-label\">Group:</label>\n            <cv-dropdown\n              id=\"root-cause-group-dropdown\"\n              v-model=\"rootCauseSelectedGroup\"\n              @change=\"handleRootCauseGroupChange\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n              <cv-dropdown-item\n                v-for=\"group in breakoutGroups\"\n                :key=\"group.name\"\n                :value=\"group.name\"\n              >\n                {{ group.name }}\n              </cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart -->\n        <div class=\"chart-container\">\n          <RootCauseChart\n            ref=\"rootCauseChart\"\n            :data=\"rootCauseChartData\"\n            :loading=\"isRootCauseDataLoading\"\n            :height=\"'400px'\"\n            title=\"Root Cause Categories by Month\"\n            @bar-click=\"handleRootCauseBarClick\"\n          />\n        </div>\n\n        <div class=\"section-footer\">\n          <p class=\"section-description\">\n            Root cause analysis showing defect categories and their fail rates over time.\n            Click on bars to see detailed information.\n          </p>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header outstanding-issues-header\" @click=\"toggleOutstandingIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Outstanding Issues</h4>\n                <div class=\"section-subtitle\">Accepted issues that are being monitored</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator outstanding-indicator\">\n                  {{ outstandingIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isOutstandingIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Outstanding Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearOutstandingFilters\"\n                    v-if=\"isOutstandingFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"outstanding-category-dropdown\"\n                      v-model=\"outstandingCategoryFilter\"\n                      @change=\"handleOutstandingCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in outstandingCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"outstanding-analysis-dropdown\"\n                      v-model=\"outstandingAnalysisTypeFilter\"\n                      @change=\"handleOutstandingAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issues List -->\n              <div v-if=\"filteredOutstandingIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredOutstandingIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card outstanding-issue-card\"\n                  @click=\"toggleOutstandingIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"blue\"\n                        label=\"Outstanding\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-performance' : 'low-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                    <div class=\"acceptance-details\">\n                      <div class=\"acceptance-date\">\n                        <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                      </div>\n                      <div class=\"accepted-by\">\n                        <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Acceptance Comment -->\n                    <div class=\"acceptance-comment\">\n                      <div class=\"comment-label\">Acceptance Reason:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No outstanding issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header resolved-issues-header\" @click=\"toggleResolvedIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Resolved Issues</h4>\n                <div class=\"section-subtitle\">Track performance of resolved issues</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator resolved-indicator\">\n                  {{ resolvedIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isResolvedIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Resolved Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearResolvedFilters\"\n                    v-if=\"isResolvedFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"category-dropdown\"\n                      v-model=\"resolvedCategoryFilter\"\n                      @change=\"handleResolvedCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in resolvedCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"resolved-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"resolved-analysis-dropdown\"\n                      v-model=\"resolvedAnalysisTypeFilter\"\n                      @change=\"handleResolvedAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issues List -->\n              <div v-if=\"filteredResolvedIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredResolvedIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card resolved-issue-card\"\n                  @click=\"toggleResolvedIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"green\"\n                        label=\"Resolved\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                    <div class=\"resolution-details\">\n                      <div class=\"resolution-date\">\n                        <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                      </div>\n                      <div class=\"original-severity\">\n                        <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                        ({{ issue.increaseMultiplier }}x)\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Resolution Comment -->\n                    <div class=\"resolution-comment\">\n                      <div class=\"comment-label\">Resolution Actions:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No resolved issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Resolved Issues Data\n      resolvedIssues: [],\n      expandedResolvedIssueIds: [], // Track which resolved issues are expanded\n      isResolvedIssuesExpanded: false, // Track if resolved issues section is expanded\n\n      // Outstanding Issues Data\n      outstandingIssues: [],\n      expandedOutstandingIssueIds: [], // Track which outstanding issues are expanded\n      isOutstandingIssuesExpanded: false, // Track if outstanding issues section is expanded\n\n      // Performance Tracking\n      performanceData: {}, // Track performance metrics for resolved and outstanding issues\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Resolved Issues Filtering\n      resolvedFilters: {\n        category: [],\n        analysisType: []\n      },\n      resolvedCategoryFilter: 'all',\n      resolvedAnalysisTypeFilter: 'all',\n\n      // Outstanding Issues Filtering\n      outstandingFilters: {\n        category: [],\n        analysisType: []\n      },\n      outstandingCategoryFilter: 'all',\n      outstandingAnalysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Filtered resolved issues based on selected filters\n    filteredResolvedIssues() {\n      // If no filters are selected, return all resolved issues\n      if (this.resolvedFilters.category.length === 0 && this.resolvedFilters.analysisType.length === 0) {\n        return this.resolvedIssues;\n      }\n\n      // Apply filters\n      return this.resolvedIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.resolvedFilters.category.length === 0 ||\n                                  this.resolvedFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.resolvedFilters.analysisType.length === 0 ||\n                                  this.resolvedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any resolved issue filters are active\n    isResolvedFiltersActive() {\n      return this.resolvedFilters.category.length > 0 || this.resolvedFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from resolved issues for filtering\n    resolvedCategories() {\n      const categories = new Set();\n      this.resolvedIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    },\n\n    // Filtered outstanding issues based on selected filters\n    filteredOutstandingIssues() {\n      // If no filters are selected, return all outstanding issues\n      if (this.outstandingFilters.category.length === 0 && this.outstandingFilters.analysisType.length === 0) {\n        return this.outstandingIssues;\n      }\n\n      // Apply filters\n      return this.outstandingIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.outstandingFilters.category.length === 0 ||\n                                  this.outstandingFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.outstandingFilters.analysisType.length === 0 ||\n                                  this.outstandingFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any outstanding issue filters are active\n    isOutstandingFiltersActive() {\n      return this.outstandingFilters.category.length > 0 || this.outstandingFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from outstanding issues for filtering\n    outstandingCategories() {\n      const categories = new Set();\n      this.outstandingIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadResolvedIssues();\n      this.loadOutstandingIssues();\n      this.loadRootCauseData();\n    },\n\n    async loadOutstandingIssues() {\n      console.log(`Loading outstanding issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter outstanding issues to only include those related to this PQE's breakout groups\n          const allOutstandingIssues = data.outstanding_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.outstandingIssues = allOutstandingIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.outstandingIssues = allOutstandingIssues;\n          }\n\n          // Update performance data for outstanding issues\n          if (data.performance_data) {\n            // Merge with existing performance data\n            this.performanceData = {\n              ...this.performanceData,\n              ...data.performance_data\n            };\n          }\n\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues();\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleOutstandingIssues() {\n      // Sample data for development\n      this.outstandingIssues = [\n        {\n          id: 'oi1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n          comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-10',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi2',\n          category: 'Victoria Crypto',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known power delivery issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n          comment: 'Power delivery variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-03-22',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Quality Team'\n        },\n        {\n          id: 'oi3',\n          category: 'Quantum Nexus',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.2',\n          aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate. This is a known process variation that has been accepted. Monitoring for any significant changes.',\n          comment: 'Process variation is within accepted tolerance. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-02-15',\n          currentPerformance: '1.1x',\n          acceptedBy: 'Manufacturing Team'\n        },\n        {\n          id: 'oi4',\n          category: 'Stellar Core',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n          comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-05',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi5',\n          category: 'Nebula Drive',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',\n          comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-05-12',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Product Team'\n        }\n      ];\n\n      // Add performance data for outstanding issues\n      const outstandingPerformanceData = {\n        'Fan Themis': [\n          { month: 'Apr 2024', xFactor: 1.5 },\n          { month: 'May 2024', xFactor: 1.4 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.4 },\n          { month: 'Apr 2024', xFactor: 1.3 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Feb 2024', xFactor: 1.3 },\n          { month: 'Mar 2024', xFactor: 1.2 },\n          { month: 'Apr 2024', xFactor: 1.2 },\n          { month: 'May 2024', xFactor: 1.1 },\n          { month: 'Jun 2024', xFactor: 1.1 }\n        ],\n        'Stellar Core': [\n          { month: 'Apr 2024', xFactor: 1.4 },\n          { month: 'May 2024', xFactor: 1.3 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Nebula Drive': [\n          { month: 'May 2024', xFactor: 1.5 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ]\n      };\n\n      // Merge with existing performance data\n      this.performanceData = {\n        ...this.performanceData,\n        ...outstandingPerformanceData\n      };\n    },\n\n    async loadResolvedIssues() {\n      console.log(`Loading resolved issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter resolved issues to only include those related to this PQE's breakout groups\n          const allResolvedIssues = data.resolved_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.resolvedIssues = allResolvedIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.resolvedIssues = allResolvedIssues;\n          }\n\n          // Load performance data for resolved issues\n          if (data.performance_data) {\n            this.performanceData = data.performance_data;\n          }\n\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues();\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleResolvedIssues() {\n      // Sample data for development\n      this.resolvedIssues = [\n        {\n          id: 'ri1',\n          category: 'Fan Themis',\n          month: '2024-05',\n          severity: 'high',\n          increaseMultiplier: '2.8',\n          aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n          comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-05-15',\n          currentPerformance: '0.9x'\n        },\n        {\n          id: 'ri2',\n          category: 'Victoria Crypto',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.7',\n          aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific power delivery problem. Process was corrected.',\n          comment: 'Improved power delivery design and quality control. Monitoring new units closely.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-04-28',\n          currentPerformance: '0.8x'\n        },\n        {\n          id: 'ri3',\n          category: 'Quantum Nexus',\n          month: '2024-03',\n          severity: 'high',\n          increaseMultiplier: '2.2',\n          aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',\n          comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-03-20',\n          currentPerformance: '0.7x'\n        }\n      ];\n\n      // Sample performance data\n      this.performanceData = {\n        'Fan Themis': [\n          { month: 'May 2024', xFactor: 2.8 },\n          { month: 'Jun 2024', xFactor: 0.9 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.6 },\n          { month: 'Apr 2024', xFactor: 1.7 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 0.8 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Mar 2024', xFactor: 2.2 },\n          { month: 'Apr 2024', xFactor: 1.1 },\n          { month: 'May 2024', xFactor: 0.8 },\n          { month: 'Jun 2024', xFactor: 0.7 }\n        ]\n      };\n    },\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', this.rootCauseChartData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n        this.$nextTick(() => {\n          if (this.$refs.rootCauseChart) {\n            this.$refs.rootCauseChart.forceRefresh();\n          }\n        });\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n</style>\n"]}]}