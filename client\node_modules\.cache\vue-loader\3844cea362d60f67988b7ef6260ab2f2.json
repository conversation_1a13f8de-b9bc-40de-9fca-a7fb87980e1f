{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748532045247}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAmoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Fails Chart Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Fails Analysis</h4>\n            <div class=\"section-subtitle\">Historical data visualization</div>\n          </div>\n        </div>\n\n        <FailsStackedBarChart\n          :pqeOwner=\"pqeOwner\"\n          :breakoutGroups=\"breakoutGroups\"\n        />\n      </div>\n\n      <!-- Root Cause Analysis Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Root Cause Analysis</h4>\n            <div class=\"section-subtitle\">Current Month Analysis</div>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart Controls -->\n        <div class=\"chart-controls\">\n          <div class=\"control-group\">\n            <label for=\"root-cause-view-dropdown\" class=\"control-label\">View By:</label>\n            <cv-dropdown\n              id=\"root-cause-view-dropdown\"\n              v-model=\"rootCauseViewBy\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n          <div class=\"control-group\">\n            <label for=\"root-cause-time-dropdown\" class=\"control-label\">Time Range:</label>\n            <cv-dropdown\n              id=\"root-cause-time-dropdown\"\n              v-model=\"rootCauseTimeRange\"\n              @change=\"handleRootCauseTimeRangeChange\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n              <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n          <div class=\"control-group\" v-if=\"breakoutGroups && breakoutGroups.length > 0\">\n            <label for=\"root-cause-group-dropdown\" class=\"control-label\">Group:</label>\n            <cv-dropdown\n              id=\"root-cause-group-dropdown\"\n              v-model=\"rootCauseSelectedGroup\"\n              @change=\"handleRootCauseGroupChange\"\n              class=\"control-dropdown\"\n            >\n              <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n              <cv-dropdown-item\n                v-for=\"group in breakoutGroups\"\n                :key=\"group.name\"\n                :value=\"group.name\"\n              >\n                {{ group.name }}\n              </cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart -->\n        <div class=\"chart-container\">\n          <RootCauseChart\n            :data=\"rootCauseChartData\"\n            :loading=\"isRootCauseDataLoading\"\n            :height=\"'400px'\"\n            title=\"Root Cause Categories by Month\"\n            @bar-click=\"handleRootCauseBarClick\"\n          />\n        </div>\n\n        <div class=\"section-footer\">\n          <p class=\"section-description\">\n            Root cause analysis showing defect categories and their fail rates over time.\n            Click on bars to see detailed information.\n          </p>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header outstanding-issues-header\" @click=\"toggleOutstandingIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Outstanding Issues</h4>\n                <div class=\"section-subtitle\">Accepted issues that are being monitored</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator outstanding-indicator\">\n                  {{ outstandingIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isOutstandingIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Outstanding Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearOutstandingFilters\"\n                    v-if=\"isOutstandingFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"outstanding-category-dropdown\"\n                      v-model=\"outstandingCategoryFilter\"\n                      @change=\"handleOutstandingCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in outstandingCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"outstanding-analysis-dropdown\"\n                      v-model=\"outstandingAnalysisTypeFilter\"\n                      @change=\"handleOutstandingAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issues List -->\n              <div v-if=\"filteredOutstandingIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredOutstandingIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card outstanding-issue-card\"\n                  @click=\"toggleOutstandingIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"blue\"\n                        label=\"Outstanding\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-performance' : 'low-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                    <div class=\"acceptance-details\">\n                      <div class=\"acceptance-date\">\n                        <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                      </div>\n                      <div class=\"accepted-by\">\n                        <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Acceptance Comment -->\n                    <div class=\"acceptance-comment\">\n                      <div class=\"comment-label\">Acceptance Reason:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No outstanding issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header resolved-issues-header\" @click=\"toggleResolvedIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Resolved Issues</h4>\n                <div class=\"section-subtitle\">Track performance of resolved issues</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator resolved-indicator\">\n                  {{ resolvedIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isResolvedIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Resolved Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearResolvedFilters\"\n                    v-if=\"isResolvedFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"category-dropdown\"\n                      v-model=\"resolvedCategoryFilter\"\n                      @change=\"handleResolvedCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in resolvedCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"resolved-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"resolved-analysis-dropdown\"\n                      v-model=\"resolvedAnalysisTypeFilter\"\n                      @change=\"handleResolvedAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issues List -->\n              <div v-if=\"filteredResolvedIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredResolvedIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card resolved-issue-card\"\n                  @click=\"toggleResolvedIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"green\"\n                        label=\"Resolved\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                    <div class=\"resolution-details\">\n                      <div class=\"resolution-date\">\n                        <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                      </div>\n                      <div class=\"original-severity\">\n                        <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                        ({{ issue.increaseMultiplier }}x)\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Resolution Comment -->\n                    <div class=\"resolution-comment\">\n                      <div class=\"comment-label\">Resolution Actions:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No resolved issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\nimport FailsStackedBarChart from '@/components/Charts/FailsStackedBarChart';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    FailsStackedBarChart,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Resolved Issues Data\n      resolvedIssues: [],\n      expandedResolvedIssueIds: [], // Track which resolved issues are expanded\n      isResolvedIssuesExpanded: false, // Track if resolved issues section is expanded\n\n      // Outstanding Issues Data\n      outstandingIssues: [],\n      expandedOutstandingIssueIds: [], // Track which outstanding issues are expanded\n      isOutstandingIssuesExpanded: false, // Track if outstanding issues section is expanded\n\n      // Performance Tracking\n      performanceData: {}, // Track performance metrics for resolved and outstanding issues\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Resolved Issues Filtering\n      resolvedFilters: {\n        category: [],\n        analysisType: []\n      },\n      resolvedCategoryFilter: 'all',\n      resolvedAnalysisTypeFilter: 'all',\n\n      // Outstanding Issues Filtering\n      outstandingFilters: {\n        category: [],\n        analysisType: []\n      },\n      outstandingCategoryFilter: 'all',\n      outstandingAnalysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Filtered resolved issues based on selected filters\n    filteredResolvedIssues() {\n      // If no filters are selected, return all resolved issues\n      if (this.resolvedFilters.category.length === 0 && this.resolvedFilters.analysisType.length === 0) {\n        return this.resolvedIssues;\n      }\n\n      // Apply filters\n      return this.resolvedIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.resolvedFilters.category.length === 0 ||\n                                  this.resolvedFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.resolvedFilters.analysisType.length === 0 ||\n                                  this.resolvedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any resolved issue filters are active\n    isResolvedFiltersActive() {\n      return this.resolvedFilters.category.length > 0 || this.resolvedFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from resolved issues for filtering\n    resolvedCategories() {\n      const categories = new Set();\n      this.resolvedIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    },\n\n    // Filtered outstanding issues based on selected filters\n    filteredOutstandingIssues() {\n      // If no filters are selected, return all outstanding issues\n      if (this.outstandingFilters.category.length === 0 && this.outstandingFilters.analysisType.length === 0) {\n        return this.outstandingIssues;\n      }\n\n      // Apply filters\n      return this.outstandingIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.outstandingFilters.category.length === 0 ||\n                                  this.outstandingFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.outstandingFilters.analysisType.length === 0 ||\n                                  this.outstandingFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any outstanding issue filters are active\n    isOutstandingFiltersActive() {\n      return this.outstandingFilters.category.length > 0 || this.outstandingFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from outstanding issues for filtering\n    outstandingCategories() {\n      const categories = new Set();\n      this.outstandingIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadResolvedIssues();\n      this.loadOutstandingIssues();\n      this.loadRootCauseData();\n    },\n\n    async loadOutstandingIssues() {\n      console.log(`Loading outstanding issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter outstanding issues to only include those related to this PQE's breakout groups\n          const allOutstandingIssues = data.outstanding_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.outstandingIssues = allOutstandingIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.outstandingIssues = allOutstandingIssues;\n          }\n\n          // Update performance data for outstanding issues\n          if (data.performance_data) {\n            // Merge with existing performance data\n            this.performanceData = {\n              ...this.performanceData,\n              ...data.performance_data\n            };\n          }\n\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues();\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleOutstandingIssues() {\n      // Sample data for development\n      this.outstandingIssues = [\n        {\n          id: 'oi1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n          comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-10',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi2',\n          category: 'Victoria Crypto',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known power delivery issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n          comment: 'Power delivery variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-03-22',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Quality Team'\n        },\n        {\n          id: 'oi3',\n          category: 'Quantum Nexus',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.2',\n          aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate. This is a known process variation that has been accepted. Monitoring for any significant changes.',\n          comment: 'Process variation is within accepted tolerance. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-02-15',\n          currentPerformance: '1.1x',\n          acceptedBy: 'Manufacturing Team'\n        },\n        {\n          id: 'oi4',\n          category: 'Stellar Core',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n          comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-05',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi5',\n          category: 'Nebula Drive',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',\n          comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-05-12',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Product Team'\n        }\n      ];\n\n      // Add performance data for outstanding issues\n      const outstandingPerformanceData = {\n        'Fan Themis': [\n          { month: 'Apr 2024', xFactor: 1.5 },\n          { month: 'May 2024', xFactor: 1.4 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.4 },\n          { month: 'Apr 2024', xFactor: 1.3 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Feb 2024', xFactor: 1.3 },\n          { month: 'Mar 2024', xFactor: 1.2 },\n          { month: 'Apr 2024', xFactor: 1.2 },\n          { month: 'May 2024', xFactor: 1.1 },\n          { month: 'Jun 2024', xFactor: 1.1 }\n        ],\n        'Stellar Core': [\n          { month: 'Apr 2024', xFactor: 1.4 },\n          { month: 'May 2024', xFactor: 1.3 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Nebula Drive': [\n          { month: 'May 2024', xFactor: 1.5 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ]\n      };\n\n      // Merge with existing performance data\n      this.performanceData = {\n        ...this.performanceData,\n        ...outstandingPerformanceData\n      };\n    },\n\n    async loadResolvedIssues() {\n      console.log(`Loading resolved issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter resolved issues to only include those related to this PQE's breakout groups\n          const allResolvedIssues = data.resolved_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.resolvedIssues = allResolvedIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.resolvedIssues = allResolvedIssues;\n          }\n\n          // Load performance data for resolved issues\n          if (data.performance_data) {\n            this.performanceData = data.performance_data;\n          }\n\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues();\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleResolvedIssues() {\n      // Sample data for development\n      this.resolvedIssues = [\n        {\n          id: 'ri1',\n          category: 'Fan Themis',\n          month: '2024-05',\n          severity: 'high',\n          increaseMultiplier: '2.8',\n          aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n          comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-05-15',\n          currentPerformance: '0.9x'\n        },\n        {\n          id: 'ri2',\n          category: 'Victoria Crypto',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.7',\n          aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific power delivery problem. Process was corrected.',\n          comment: 'Improved power delivery design and quality control. Monitoring new units closely.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-04-28',\n          currentPerformance: '0.8x'\n        },\n        {\n          id: 'ri3',\n          category: 'Quantum Nexus',\n          month: '2024-03',\n          severity: 'high',\n          increaseMultiplier: '2.2',\n          aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',\n          comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-03-20',\n          currentPerformance: '0.7x'\n        }\n      ];\n\n      // Sample performance data\n      this.performanceData = {\n        'Fan Themis': [\n          { month: 'May 2024', xFactor: 2.8 },\n          { month: 'Jun 2024', xFactor: 0.9 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.6 },\n          { month: 'Apr 2024', xFactor: 1.7 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 0.8 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Mar 2024', xFactor: 2.2 },\n          { month: 'Apr 2024', xFactor: 1.1 },\n          { month: 'May 2024', xFactor: 0.8 },\n          { month: 'Jun 2024', xFactor: 0.7 }\n        ]\n      };\n    },\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', this.rootCauseChartData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          // Force chart update\n          this.$nextTick(() => {\n            console.log('Chart data after nextTick:', this.rootCauseChartData.length);\n          });\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n</style>\n"]}]}