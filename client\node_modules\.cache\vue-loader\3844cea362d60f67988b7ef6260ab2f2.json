{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748530489935}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAulBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Fails Chart Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Fails Analysis</h4>\n            <div class=\"section-subtitle\">Historical data visualization</div>\n          </div>\n        </div>\n\n        <FailsStackedBarChart\n          :pqeOwner=\"pqeOwner\"\n          :breakoutGroups=\"breakoutGroups\"\n        />\n      </div>\n\n      <!-- Root Cause Analysis Section -->\n      <div class=\"section-card chart-section\">\n        <div class=\"section-header\">\n          <div class=\"section-title-container\">\n            <h4 class=\"section-title\">Root Cause Analysis</h4>\n            <div class=\"section-subtitle\">Current Month Analysis</div>\n          </div>\n        </div>\n\n        <!-- Root Cause Chart -->\n        <div class=\"chart-container\">\n          <RootCauseChart\n            :data=\"rootCauseChartData\"\n            :loading=\"isRootCauseDataLoading\"\n            :height=\"'400px'\"\n            title=\"Root Cause Categories by Month\"\n            @bar-click=\"handleRootCauseBarClick\"\n          />\n        </div>\n\n        <div class=\"section-footer\">\n          <p class=\"section-description\">\n            Root cause analysis showing defect categories and their fail rates over time.\n            Click on bars to see detailed information.\n          </p>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Outstanding Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header outstanding-issues-header\" @click=\"toggleOutstandingIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Outstanding Issues</h4>\n                <div class=\"section-subtitle\">Accepted issues that are being monitored</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator outstanding-indicator\">\n                  {{ outstandingIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isOutstandingIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isOutstandingIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Outstanding Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearOutstandingFilters\"\n                    v-if=\"isOutstandingFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"outstanding-category-dropdown\"\n                      v-model=\"outstandingCategoryFilter\"\n                      @change=\"handleOutstandingCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in outstandingCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"outstanding-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"outstanding-analysis-dropdown\"\n                      v-model=\"outstandingAnalysisTypeFilter\"\n                      @change=\"handleOutstandingAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issues List -->\n              <div v-if=\"filteredOutstandingIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredOutstandingIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card outstanding-issue-card\"\n                  @click=\"toggleOutstandingIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isOutstandingIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"blue\"\n                        label=\"Outstanding\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"parseFloat(issue.currentPerformance) > 1.3 ? 'medium-performance' : 'low-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isOutstandingIssueExpanded(issue.id)\">\n                    <div class=\"acceptance-details\">\n                      <div class=\"acceptance-date\">\n                        <strong>Accepted on:</strong> {{ issue.acceptanceDate }}\n                      </div>\n                      <div class=\"accepted-by\">\n                        <strong>Accepted by:</strong> {{ issue.acceptedBy }}\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Acceptance Comment -->\n                    <div class=\"acceptance-comment\">\n                      <div class=\"comment-label\">Acceptance Reason:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No outstanding issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Resolved Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header resolved-issues-header\" @click=\"toggleResolvedIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Resolved Issues</h4>\n                <div class=\"section-subtitle\">Track performance of resolved issues</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator resolved-indicator\">\n                  {{ resolvedIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isResolvedIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isResolvedIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Resolved Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearResolvedFilters\"\n                    v-if=\"isResolvedFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"category-dropdown\" class=\"filter-label\">Category:</label>\n                    <cv-dropdown\n                      id=\"category-dropdown\"\n                      v-model=\"resolvedCategoryFilter\"\n                      @change=\"handleResolvedCategoryFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Categories</cv-dropdown-item>\n                      <cv-dropdown-item\n                        v-for=\"category in resolvedCategories\"\n                        :key=\"category\"\n                        :value=\"category\"\n                      >\n                        {{ category }}\n                      </cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"resolved-analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"resolved-analysis-dropdown\"\n                      v-model=\"resolvedAnalysisTypeFilter\"\n                      @change=\"handleResolvedAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issues List -->\n              <div v-if=\"filteredResolvedIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredResolvedIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card resolved-issue-card\"\n                  @click=\"toggleResolvedIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isResolvedIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        kind=\"green\"\n                        label=\"Resolved\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.currentPerformance.startsWith('0') ? 'good-performance' : 'medium-performance'\">\n                        Current: {{ issue.currentPerformance }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isResolvedIssueExpanded(issue.id)\">\n                    <div class=\"resolution-details\">\n                      <div class=\"resolution-date\">\n                        <strong>Resolved on:</strong> {{ issue.resolutionDate }}\n                      </div>\n                      <div class=\"original-severity\">\n                        <strong>Original Severity:</strong> {{ issue.severity === 'high' ? 'High' : 'Medium' }}\n                        ({{ issue.increaseMultiplier }}x)\n                      </div>\n                    </div>\n\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Resolution Comment -->\n                    <div class=\"resolution-comment\">\n                      <div class=\"comment-label\">Resolution Actions:</div>\n                      <div class=\"comment-text\">{{ issue.comment }}</div>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewPerformanceData(issue)\"\n                      >\n                        View Performance\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No resolved issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\nimport FailsStackedBarChart from '@/components/Charts/FailsStackedBarChart';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    FailsStackedBarChart,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Resolved Issues Data\n      resolvedIssues: [],\n      expandedResolvedIssueIds: [], // Track which resolved issues are expanded\n      isResolvedIssuesExpanded: false, // Track if resolved issues section is expanded\n\n      // Outstanding Issues Data\n      outstandingIssues: [],\n      expandedOutstandingIssueIds: [], // Track which outstanding issues are expanded\n      isOutstandingIssuesExpanded: false, // Track if outstanding issues section is expanded\n\n      // Performance Tracking\n      performanceData: {}, // Track performance metrics for resolved and outstanding issues\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Resolved Issues Filtering\n      resolvedFilters: {\n        category: [],\n        analysisType: []\n      },\n      resolvedCategoryFilter: 'all',\n      resolvedAnalysisTypeFilter: 'all',\n\n      // Outstanding Issues Filtering\n      outstandingFilters: {\n        category: [],\n        analysisType: []\n      },\n      outstandingCategoryFilter: 'all',\n      outstandingAnalysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n\n      // Loading States\n      isLoading: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Filtered resolved issues based on selected filters\n    filteredResolvedIssues() {\n      // If no filters are selected, return all resolved issues\n      if (this.resolvedFilters.category.length === 0 && this.resolvedFilters.analysisType.length === 0) {\n        return this.resolvedIssues;\n      }\n\n      // Apply filters\n      return this.resolvedIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.resolvedFilters.category.length === 0 ||\n                                  this.resolvedFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.resolvedFilters.analysisType.length === 0 ||\n                                  this.resolvedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any resolved issue filters are active\n    isResolvedFiltersActive() {\n      return this.resolvedFilters.category.length > 0 || this.resolvedFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from resolved issues for filtering\n    resolvedCategories() {\n      const categories = new Set();\n      this.resolvedIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    },\n\n    // Filtered outstanding issues based on selected filters\n    filteredOutstandingIssues() {\n      // If no filters are selected, return all outstanding issues\n      if (this.outstandingFilters.category.length === 0 && this.outstandingFilters.analysisType.length === 0) {\n        return this.outstandingIssues;\n      }\n\n      // Apply filters\n      return this.outstandingIssues.filter(issue => {\n        // Check if issue passes category filter\n        const passesCategoryFilter = this.outstandingFilters.category.length === 0 ||\n                                  this.outstandingFilters.category.includes(issue.category);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.outstandingFilters.analysisType.length === 0 ||\n                                  this.outstandingFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesCategoryFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any outstanding issue filters are active\n    isOutstandingFiltersActive() {\n      return this.outstandingFilters.category.length > 0 || this.outstandingFilters.analysisType.length > 0;\n    },\n\n    // Get unique categories from outstanding issues for filtering\n    outstandingCategories() {\n      const categories = new Set();\n      this.outstandingIssues.forEach(issue => {\n        categories.add(issue.category);\n      });\n      return Array.from(categories);\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue) {\n        if (newValue) {\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadResolvedIssues();\n      this.loadOutstandingIssues();\n      this.loadRootCauseData();\n    },\n\n    async loadOutstandingIssues() {\n      console.log(`Loading outstanding issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch outstanding issues from the API\n        const response = await fetch('/api-statit2/get_pqe_outstanding_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch outstanding issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter outstanding issues to only include those related to this PQE's breakout groups\n          const allOutstandingIssues = data.outstanding_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.outstandingIssues = allOutstandingIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.outstandingIssues = allOutstandingIssues;\n          }\n\n          // Update performance data for outstanding issues\n          if (data.performance_data) {\n            // Merge with existing performance data\n            this.performanceData = {\n              ...this.performanceData,\n              ...data.performance_data\n            };\n          }\n\n          console.log(`Loaded ${this.outstandingIssues.length} outstanding issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load outstanding issues:', data.message);\n          // Use sample data for development\n          this.loadSampleOutstandingIssues();\n        }\n      } catch (error) {\n        console.error('Error loading outstanding issues:', error);\n        // Use sample data for development\n        this.loadSampleOutstandingIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleOutstandingIssues() {\n      // Sample data for development\n      this.outstandingIssues = [\n        {\n          id: 'oi1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Fan Themis showing 1.4x increase over target rate. This is a known issue with the cooling system that has been accepted. Monitoring for any significant changes.',\n          comment: 'Known issue with cooling system. Engineering has accepted this as a limitation of the current design. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-10',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi2',\n          category: 'Victoria Crypto',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Victoria Crypto showing 1.3x increase in failure rate. This is related to a known power delivery issue that has been accepted as within tolerance. Monitoring for any significant changes.',\n          comment: 'Power delivery variation is within accepted tolerance. Cost of fixing exceeds benefit. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-03-22',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Quality Team'\n        },\n        {\n          id: 'oi3',\n          category: 'Quantum Nexus',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.2',\n          aiDescription: 'Quantum Nexus showing 1.2x increase in failure rate. This is a known process variation that has been accepted. Monitoring for any significant changes.',\n          comment: 'Process variation is within accepted tolerance. Monitoring monthly for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-02-15',\n          currentPerformance: '1.1x',\n          acceptedBy: 'Manufacturing Team'\n        },\n        {\n          id: 'oi4',\n          category: 'Stellar Core',\n          month: '2024-05',\n          severity: 'medium',\n          increaseMultiplier: '1.3',\n          aiDescription: 'Stellar Core showing 1.3x increase in failure rate. This issue has been accepted as a known limitation. Monitoring for any significant changes.',\n          comment: 'Units have a known limitation in the power management system. Engineering has accepted this issue. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-04-05',\n          currentPerformance: '1.2x',\n          acceptedBy: 'Engineering Team'\n        },\n        {\n          id: 'oi5',\n          category: 'Nebula Drive',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.4',\n          aiDescription: 'Nebula Drive showing 1.4x increase in failure rate. This is related to a known thermal issue that has been accepted for the current generation. Next generation design will address this issue.',\n          comment: 'Known thermal issue in current generation. Next generation design will address this. Monitoring for any significant changes.',\n          analysisType: 'Root Cause',\n          acceptanceDate: '2024-05-12',\n          currentPerformance: '1.3x',\n          acceptedBy: 'Product Team'\n        }\n      ];\n\n      // Add performance data for outstanding issues\n      const outstandingPerformanceData = {\n        'Fan Themis': [\n          { month: 'Apr 2024', xFactor: 1.5 },\n          { month: 'May 2024', xFactor: 1.4 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.4 },\n          { month: 'Apr 2024', xFactor: 1.3 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Feb 2024', xFactor: 1.3 },\n          { month: 'Mar 2024', xFactor: 1.2 },\n          { month: 'Apr 2024', xFactor: 1.2 },\n          { month: 'May 2024', xFactor: 1.1 },\n          { month: 'Jun 2024', xFactor: 1.1 }\n        ],\n        'Stellar Core': [\n          { month: 'Apr 2024', xFactor: 1.4 },\n          { month: 'May 2024', xFactor: 1.3 },\n          { month: 'Jun 2024', xFactor: 1.2 }\n        ],\n        'Nebula Drive': [\n          { month: 'May 2024', xFactor: 1.5 },\n          { month: 'Jun 2024', xFactor: 1.3 }\n        ]\n      };\n\n      // Merge with existing performance data\n      this.performanceData = {\n        ...this.performanceData,\n        ...outstandingPerformanceData\n      };\n    },\n\n    async loadResolvedIssues() {\n      console.log(`Loading resolved issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch resolved issues from the API\n        const response = await fetch('/api-statit2/get_pqe_resolved_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch resolved issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter resolved issues to only include those related to this PQE's breakout groups\n          const allResolvedIssues = data.resolved_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.resolvedIssues = allResolvedIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.resolvedIssues = allResolvedIssues;\n          }\n\n          // Load performance data for resolved issues\n          if (data.performance_data) {\n            this.performanceData = data.performance_data;\n          }\n\n          console.log(`Loaded ${this.resolvedIssues.length} resolved issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load resolved issues:', data.message);\n          // Use sample data for development\n          this.loadSampleResolvedIssues();\n        }\n      } catch (error) {\n        console.error('Error loading resolved issues:', error);\n        // Use sample data for development\n        this.loadSampleResolvedIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    loadSampleResolvedIssues() {\n      // Sample data for development\n      this.resolvedIssues = [\n        {\n          id: 'ri1',\n          category: 'Fan Themis',\n          month: '2024-05',\n          severity: 'high',\n          increaseMultiplier: '2.8',\n          aiDescription: 'Fan Themis showed 2.8x spike in May 2024. Root cause was identified as a manufacturing defect in the bearing assembly. Issue was resolved by implementing additional quality checks.',\n          comment: 'Updated manufacturing process and added inspection step. Monitoring performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-05-15',\n          currentPerformance: '0.9x'\n        },\n        {\n          id: 'ri2',\n          category: 'Victoria Crypto',\n          month: '2024-04',\n          severity: 'medium',\n          increaseMultiplier: '1.7',\n          aiDescription: 'Victoria Crypto had sustained problem with 1.7x target rate for 2 consecutive months. Issue was traced to a specific power delivery problem. Process was corrected.',\n          comment: 'Improved power delivery design and quality control. Monitoring new units closely.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-04-28',\n          currentPerformance: '0.8x'\n        },\n        {\n          id: 'ri3',\n          category: 'Quantum Nexus',\n          month: '2024-03',\n          severity: 'high',\n          increaseMultiplier: '2.2',\n          aiDescription: 'Quantum Nexus showed 2.2x spike in March 2024. Root cause was identified as a firmware issue affecting power management. Issue was resolved with firmware update.',\n          comment: 'Released firmware update v2.3.1 that addresses the power management issue. Monitoring field performance.',\n          analysisType: 'Root Cause',\n          resolutionDate: '2024-03-20',\n          currentPerformance: '0.7x'\n        }\n      ];\n\n      // Sample performance data\n      this.performanceData = {\n        'Fan Themis': [\n          { month: 'May 2024', xFactor: 2.8 },\n          { month: 'Jun 2024', xFactor: 0.9 }\n        ],\n        'Victoria Crypto': [\n          { month: 'Mar 2024', xFactor: 1.6 },\n          { month: 'Apr 2024', xFactor: 1.7 },\n          { month: 'May 2024', xFactor: 1.2 },\n          { month: 'Jun 2024', xFactor: 0.8 }\n        ],\n        'Quantum Nexus': [\n          { month: 'Mar 2024', xFactor: 2.2 },\n          { month: 'Apr 2024', xFactor: 1.1 },\n          { month: 'May 2024', xFactor: 0.8 },\n          { month: 'Jun 2024', xFactor: 0.7 }\n        ]\n      };\n    },\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range (last 6 months)\n        const endDate = new Date();\n        const startDate = new Date();\n        startDate.setMonth(endDate.getMonth() - 5); // 6 months including current\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', this.rootCauseChartData);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: category,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n      }\n\n      const months = ['2024-03', '2024-04', '2024-05', '2024-06'];\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-container {\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n</style>\n"]}]}