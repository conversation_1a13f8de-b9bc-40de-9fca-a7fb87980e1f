<template>
  <div class="excel-data-viewer">
    <div class="excel-header">
      <h3 class="excel-title">{{ fileName }}</h3>
      <div class="excel-actions">
        <cv-button kind="secondary" size="small" @click="$emit('close')">Close</cv-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">Loading Excel data...</p>
    </div>

    <div v-if="error && excelData.length === 0" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <cv-button kind="secondary" size="small" @click="$emit('close')">Close</cv-button>
    </div>

    <div v-else-if="error && excelData.length > 0" class="error-with-data-container">
      <div class="error-banner">
        <div class="error-icon-small">⚠️</div>
        <p class="error-message-small">{{ error }}</p>
        <p class="error-submessage">Showing sample data instead.</p>
      </div>

      <!-- Continue to show the data table since we have sample data -->
      <div class="excel-content">
        <!-- Search and filter controls here -->
        <div class="search-filter">
          <cv-search
            v-model="searchQuery"
            placeholder="Search in all columns..."
            @input="filterData"
          />

          <div class="filter-controls">
            <cv-dropdown
              v-model="filterColumn"
              placeholder="Filter by column"
              @change="filterData"
            >
              <cv-dropdown-item value="">All Columns</cv-dropdown-item>
              <cv-dropdown-item
                v-for="column in columns"
                :key="column"
                :value="column"
              >
                {{ column }}
              </cv-dropdown-item>
            </cv-dropdown>

            <cv-button-set>
              <cv-button kind="ghost" size="small" @click="resetFilters">Reset Filters</cv-button>
              <cv-button kind="ghost" size="small" @click="exportToCSV">Export to CSV</cv-button>
            </cv-button-set>
          </div>
        </div>

        <!-- Table content here -->
        <div class="table-container">
          <table class="excel-table">
            <thead>
              <tr>
                <th v-for="column in columns" :key="column" @click="sortBy(column)">
                  {{ column }}
                  <span v-if="sortColumn === column" class="sort-indicator">
                    {{ sortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in paginatedData" :key="index">
                <td v-for="column in columns" :key="column">
                  {{ formatCellValue(row[column]) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination controls here -->
        <div class="pagination-controls">
          <div class="pagination-info">
            Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ filteredData.length }} entries
          </div>

          <div class="pagination-buttons">
            <cv-button
              kind="ghost"
              size="small"
              :disabled="currentPage === 1"
              @click="currentPage = 1"
            >
              First
            </cv-button>
            <cv-button
              kind="ghost"
              size="small"
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              Previous
            </cv-button>

            <span class="page-indicator">Page {{ currentPage }} of {{ totalPages }}</span>

            <cv-button
              kind="ghost"
              size="small"
              :disabled="currentPage === totalPages"
              @click="currentPage++"
            >
              Next
            </cv-button>
            <cv-button
              kind="ghost"
              size="small"
              :disabled="currentPage === totalPages"
              @click="currentPage = totalPages"
            >
              Last
            </cv-button>
          </div>

          <div class="page-size-selector">
            <label for="page-size">Rows per page:</label>
            <cv-dropdown
              id="page-size"
              v-model="pageSize"
              @change="currentPage = 1"
            >
              <cv-dropdown-item value="10">10</cv-dropdown-item>
              <cv-dropdown-item value="25">25</cv-dropdown-item>
              <cv-dropdown-item value="50">50</cv-dropdown-item>
              <cv-dropdown-item value="100">100</cv-dropdown-item>
            </cv-dropdown>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="excelData.length === 0" class="empty-container">
      <div class="empty-icon">📊</div>
      <p class="empty-message">No data available in the Excel file.</p>
      <cv-button kind="secondary" size="small" @click="$emit('close')">Close</cv-button>
    </div>

    <div v-else class="excel-content">
      <div class="search-filter">
        <cv-search
          v-model="searchQuery"
          placeholder="Search in all columns..."
          @input="filterData"
        />

        <div class="filter-controls">
          <cv-dropdown
            v-model="filterColumn"
            placeholder="Filter by column"
            @change="filterData"
          >
            <cv-dropdown-item value="">All Columns</cv-dropdown-item>
            <cv-dropdown-item
              v-for="column in columns"
              :key="column"
              :value="column"
            >
              {{ column }}
            </cv-dropdown-item>
          </cv-dropdown>

          <cv-button-set>
            <cv-button kind="ghost" size="small" @click="resetFilters">Reset Filters</cv-button>
            <cv-button kind="ghost" size="small" @click="exportToCSV">Export to CSV</cv-button>
          </cv-button-set>
        </div>
      </div>

      <div class="table-container">
        <table class="excel-table">
          <thead>
            <tr>
              <th v-for="column in columns" :key="column" @click="sortBy(column)">
                {{ column }}
                <span v-if="sortColumn === column" class="sort-indicator">
                  {{ sortDirection === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in paginatedData" :key="index">
              <td v-for="column in columns" :key="column">
                {{ formatCellValue(row[column]) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination-controls">
        <div class="pagination-info">
          Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ filteredData.length }} entries
        </div>

        <div class="pagination-buttons">
          <cv-button
            kind="ghost"
            size="small"
            :disabled="currentPage === 1"
            @click="currentPage = 1"
          >
            First
          </cv-button>
          <cv-button
            kind="ghost"
            size="small"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            Previous
          </cv-button>

          <span class="page-indicator">Page {{ currentPage }} of {{ totalPages }}</span>

          <cv-button
            kind="ghost"
            size="small"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            Next
          </cv-button>
          <cv-button
            kind="ghost"
            size="small"
            :disabled="currentPage === totalPages"
            @click="currentPage = totalPages"
          >
            Last
          </cv-button>
        </div>

        <div class="page-size-selector">
          <label for="page-size">Rows per page:</label>
          <cv-dropdown
            id="page-size"
            v-model="pageSize"
            @change="currentPage = 1"
          >
            <cv-dropdown-item value="10">10</cv-dropdown-item>
            <cv-dropdown-item value="25">25</cv-dropdown-item>
            <cv-dropdown-item value="50">50</cv-dropdown-item>
            <cv-dropdown-item value="100">100</cv-dropdown-item>
          </cv-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExcelDataViewer',
  props: {
    excelData: {
      type: Array,
      required: true
    },
    fileName: {
      type: String,
      default: 'Excel Data'
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchQuery: '',
      filterColumn: '',
      sortColumn: '',
      sortDirection: 'asc',
      currentPage: 1,
      pageSize: 25,
      filteredData: []
    };
  },
  computed: {
    columns() {
      if (this.excelData.length === 0) return [];
      return Object.keys(this.excelData[0]);
    },
    totalPages() {
      return Math.ceil(this.filteredData.length / this.pageSize);
    },
    startIndex() {
      return (this.currentPage - 1) * this.pageSize;
    },
    endIndex() {
      const end = this.startIndex + parseInt(this.pageSize);
      return end > this.filteredData.length ? this.filteredData.length : end;
    },
    paginatedData() {
      return this.filteredData.slice(this.startIndex, this.endIndex);
    }
  },
  watch: {
    excelData: {
      immediate: true,
      handler() {
        this.resetFilters();
      }
    },
    pageSize() {
      // Convert pageSize to number if it's a string
      this.pageSize = parseInt(this.pageSize);
    }
  },
  methods: {
    filterData() {
      if (!this.searchQuery && !this.filterColumn) {
        this.filteredData = [...this.excelData];
        return;
      }

      this.filteredData = this.excelData.filter(row => {
        // If a specific column is selected for filtering
        if (this.filterColumn) {
          const cellValue = row[this.filterColumn];
          return this.cellValueContainsQuery(cellValue, this.searchQuery);
        }

        // Search in all columns
        return Object.values(row).some(value =>
          this.cellValueContainsQuery(value, this.searchQuery)
        );
      });

      // Apply sorting if active
      if (this.sortColumn) {
        this.applySorting();
      }

      // Reset to first page when filtering
      this.currentPage = 1;
    },

    cellValueContainsQuery(value, query) {
      if (value === null || value === undefined) return false;
      return String(value).toLowerCase().includes(query.toLowerCase());
    },

    sortBy(column) {
      // If clicking the same column, toggle direction
      if (this.sortColumn === column) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = column;
        this.sortDirection = 'asc';
      }

      this.applySorting();
    },

    applySorting() {
      const column = this.sortColumn;
      const direction = this.sortDirection;

      this.filteredData.sort((a, b) => {
        const valueA = a[column];
        const valueB = b[column];

        // Handle numeric values
        if (!isNaN(valueA) && !isNaN(valueB)) {
          return direction === 'asc'
            ? Number(valueA) - Number(valueB)
            : Number(valueB) - Number(valueA);
        }

        // Handle string values
        const strA = String(valueA || '').toLowerCase();
        const strB = String(valueB || '').toLowerCase();

        if (direction === 'asc') {
          return strA.localeCompare(strB);
        } else {
          return strB.localeCompare(strA);
        }
      });
    },

    resetFilters() {
      this.searchQuery = '';
      this.filterColumn = '';
      this.sortColumn = '';
      this.sortDirection = 'asc';
      this.currentPage = 1;
      this.filteredData = [...this.excelData];
    },

    formatCellValue(value) {
      if (value === null || value === undefined) return '';

      // Format numbers with commas for thousands
      if (typeof value === 'number') {
        return value.toLocaleString();
      }

      return value;
    },

    exportToCSV() {
      // Get column headers
      const headers = this.columns;

      // Create CSV content
      let csvContent = headers.join(',') + '\n';

      // Add data rows
      this.filteredData.forEach(row => {
        const rowValues = headers.map(header => {
          const value = row[header];
          // Handle values that might contain commas or quotes
          if (value === null || value === undefined) return '';
          const cellValue = String(value);
          if (cellValue.includes(',') || cellValue.includes('"')) {
            return `"${cellValue.replace(/"/g, '""')}"`;
          }
          return cellValue;
        });
        csvContent += rowValues.join(',') + '\n';
      });

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `${this.fileName.replace('.xls', '')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};
</script>

<style scoped>
.excel-data-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #262626;
  color: #f4f4f4;
}

.excel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #393939;
}

.excel-title {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
}

.excel-actions {
  display: flex;
  gap: 0.5rem;
}

.excel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-filter {
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  border-bottom: 1px solid #393939;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  margin-left: auto;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 0 1rem;
}

.excel-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.excel-table th {
  position: sticky;
  top: 0;
  background-color: #393939;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  cursor: pointer;
  user-select: none;
}

.excel-table th:hover {
  background-color: #4c4c4c;
}

.sort-indicator {
  margin-left: 0.5rem;
  font-size: 0.75rem;
}

.excel-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #393939;
}

.excel-table tr:hover {
  background-color: #353535;
}

.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #393939;
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: #c6c6c6;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-indicator {
  font-size: 0.875rem;
  color: #c6c6c6;
  margin: 0 0.5rem;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #c6c6c6;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  flex: 1;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-message,
.empty-message {
  font-size: 1rem;
  color: #c6c6c6;
  margin-bottom: 0.5rem;
}

.error-submessage {
  font-size: 0.875rem;
  color: #8d8d8d;
  font-style: italic;
  margin-bottom: 0;
}

.error-with-data-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.error-banner {
  display: flex;
  align-items: center;
  background-color: rgba(218, 30, 40, 0.1);
  padding: 0.75rem 1rem;
  border-left: 4px solid #da1e28;
  margin-bottom: 1rem;
}

.error-icon-small {
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

.error-message-small {
  font-size: 0.875rem;
  color: #f4f4f4;
  margin: 0 0.75rem 0 0;
  font-weight: 600;
}

.error-icon,
.empty-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .search-filter,
  .pagination-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-controls,
  .pagination-buttons {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}
</style>
