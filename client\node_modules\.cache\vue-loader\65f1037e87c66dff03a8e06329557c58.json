{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue?vue&type=template&id=56d4aac8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\MetisXFactors.vue", "mtime": 1748527827772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}