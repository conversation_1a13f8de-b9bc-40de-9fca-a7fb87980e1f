{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue?vue&type=style&index=0&id=acfb3762&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\PQEDashboard\\PQEDashboard.vue", "mtime": 1748528223529}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEDashboard.vue"], "names": [], "mappings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file": "PQEDashboard.vue", "sourceRoot": "src/components/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>PQE Owner Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon new-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">New Critical Issues</div>\n            <div class=\"metric-value\">{{ newCriticalIssues.length }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon critical-issues\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M16,10a6,6,0,1,0,6,6A6,6,0,0,0,16,10Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Critical Issues</div>\n            <div class=\"metric-value\">{{ unresolvedCriticalIssues.length }}</div>\n            <div class=\"metric-description\">Need Resolution</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon unvalidated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z\"></path>\n              <path d=\"M14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Unvalidated Fails</div>\n            <div class=\"metric-value\">{{ unvalidatedCount }}</div>\n            <div class=\"metric-description\">Need Validation</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">Issues requiring immediate attention</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Breakout Group Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Breakout Group Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Current Month: {{ currentMonth ? new Date(currentMonth + '-01').toLocaleString('en-US', { month: 'long', year: 'numeric' }) : 'Loading...' }}\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"showAllAlerts = !showAllAlerts\"\n                  v-if=\"sortedCriticalBreakoutGroups.length > 0\"\n                >\n                  {{ showAllAlerts ? 'Show Critical Only' : 'See All Alerts' }}\n                </cv-button>\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"showAllBreakoutGroups = !showAllBreakoutGroups\"\n                  style=\"margin-left: 8px;\"\n                >\n                  {{ showAllBreakoutGroups ? 'Hide Normal Groups' : 'Show All Status' }}\n                </cv-button>\n              </div>\n            </div>\n\n            <!-- Loading State -->\n            <div v-if=\"isLoadingBreakoutGroups\" class=\"loading-container\">\n              <cv-inline-loading\n                status=\"active\"\n                loading-text=\"Loading breakout group alerts...\"\n              ></cv-inline-loading>\n            </div>\n\n            <!-- Critical Alerts (Always Visible) -->\n            <div v-else-if=\"sortedCriticalBreakoutGroups.length > 0\" class=\"breakout-groups-grid\">\n              <div\n                v-for=\"group in sortedHighPriorityGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card highlighted\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Medium Priority Alerts (Shown when \"See All Alerts\" is clicked) -->\n            <div v-if=\"showAllAlerts && sortedMediumPriorityGroups.length > 0\" class=\"breakout-groups-grid medium-priority-groups\">\n              <div\n                v-for=\"group in sortedMediumPriorityGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- All Normal Groups (Shown when \"Show All Status\" is clicked) -->\n            <div v-if=\"showAllBreakoutGroups && normalBreakoutGroups.length > 0\" class=\"breakout-groups-grid normal-groups\">\n              <div\n                v-for=\"group in normalBreakoutGroups\"\n                :key=\"group.name\"\n                class=\"breakout-group-card\"\n                :class=\"[getXFactorSeverityClass(group.xFactor), {'current-month': group.isCurrentMonth}]\"\n              >\n                <div class=\"breakout-group-header\">\n                  <h5 class=\"breakout-group-name\">{{ group.name }}</h5>\n                </div>\n\n                <div class=\"breakout-group-metrics\">\n                  <div class=\"xfactor-metrics\">\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Current Month X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                        {{ group.xFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                    <div class=\"xfactor-row\">\n                      <span class=\"xfactor-label\">Sustained X-Factor:</span>\n                      <span class=\"xfactor-value\" :class=\"getXFactorSeverityClass(group.sustainedXFactor)\">\n                        {{ group.sustainedXFactor.toFixed(1) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"status-item\">\n                    <div class=\"status-icon\" :class=\"getXFactorSeverityClass(group.xFactor)\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n                        <circle cx=\"16\" cy=\"16\" r=\"6\"></circle>\n                      </svg>\n                    </div>\n                    <div class=\"status-info\">\n                      <div class=\"status-label\">Status</div>\n                      <div class=\"status-value\">{{ group.status }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"sortedCriticalBreakoutGroups.length === 0\" class=\"no-data-message\">\n              No alerts found. All breakout groups are operating within normal parameters.\n            </div>\n          </div>\n\n          <!-- Item Tracking Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Item Tracking</h4>\n                <div class=\"section-subtitle\">\n                  Action items related to current month's critical breakout groups\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <cv-button\n                  kind=\"primary\"\n                  size=\"small\"\n                  @click=\"goToActionTracker\"\n                  class=\"action-button\"\n                >\n                  Go to Action Tracker\n                </cv-button>\n              </div>\n            </div>\n\n            <div v-if=\"isLoadingTrackedItems\" class=\"loading-container\">\n              <cv-inline-loading\n                status=\"active\"\n                loading-text=\"Loading tracked items...\"\n              ></cv-inline-loading>\n            </div>\n\n            <div v-else-if=\"trackedItems.length > 0\" class=\"tracked-items-list\">\n              <div\n                v-for=\"item in trackedItems\"\n                :key=\"item.id\"\n                class=\"tracked-item-card\"\n                :class=\"[getItemStatusClass(item.status), {'critical-item': item.isCritical}]\"\n              >\n                <div class=\"tracked-item-header\">\n                  <h5 class=\"tracked-item-name\">{{ item.name }}</h5>\n                  <div class=\"tracked-item-status\">\n                    <span class=\"status-badge\" :class=\"getItemStatusClass(item.status)\">{{ item.status }}</span>\n                    <cv-tag v-if=\"item.isCritical\" kind=\"red\" label=\"Critical\" class=\"critical-tag\" />\n                  </div>\n                </div>\n\n                <div class=\"tracked-item-details\">\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Owner:</span>\n                    <span class=\"detail-value\">{{ item.owner }}</span>\n                  </div>\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Due Date:</span>\n                    <span class=\"detail-value\">{{ item.dueDate }}</span>\n                  </div>\n                  <div class=\"detail-row\">\n                    <span class=\"detail-label\">Related Group:</span>\n                    <span class=\"detail-value related-group\" :class=\"{'critical-group': isCriticalGroup(item.relatedIssue)}\">\n                      {{ item.relatedIssue }}\n                      <cv-tag v-if=\"isCriticalGroup(item.relatedIssue)\" kind=\"blue\" label=\"Current Month\" class=\"month-tag\" />\n                    </span>\n                  </div>\n                  <div class=\"detail-row\" v-if=\"item.progress !== undefined\">\n                    <span class=\"detail-label\">Progress:</span>\n                    <div class=\"progress-container\">\n                      <div class=\"progress-bar\" :style=\"{ width: item.progress + '%' }\"></div>\n                      <span class=\"progress-text\">{{ item.progress }}%</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"tracked-item-footer\">\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    @click.stop=\"viewItemDetails(item)\"\n                  >\n                    View in Action Tracker\n                  </cv-button>\n                </div>\n              </div>\n            </div>\n\n            <div v-else class=\"no-data-message\">\n              No tracked items found for current month's critical breakout groups. Items will appear here when they are linked to the action tracker.\n            </div>\n          </div>\n        </div>\n\n        <!-- Right Column -->\n        <div class=\"dashboard-column\">\n          <!-- Validation Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Validation Status</h4>\n              <div class=\"last-updated-text\">Daily Status</div>\n            </div>\n\n            <!-- Validation Chart -->\n            <div class=\"chart-container\">\n              <div v-if=\"validationChartData.length > 0\">\n                <div class=\"chart-wrapper\">\n                  <div id=\"validationChart\" class=\"carbon-chart-container\"></div>\n                </div>\n              </div>\n              <div v-else class=\"no-data-message\">\n                <cv-inline-loading\n                  status=\"active\"\n                  loading-text=\"Loading validation data...\"\n                ></cv-inline-loading>\n              </div>\n            </div>\n\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Go to the validation page to review and validate new fails.\n                Currently <strong>{{ unvalidatedCount }}</strong> fails need validation.\n              </p>\n              <cv-button\n                kind=\"primary\"\n                @click=\"goToValidationPage\"\n                class=\"action-button\"\n              >\n                Go to Validation Page\n              </cv-button>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Root Cause Analysis</h4>\n              <div class=\"last-updated-text\">Current Month Analysis</div>\n            </div>\n\n            <!-- Root Cause Chart -->\n            <div class=\"chart-container\">\n              <RootCauseChart\n                :data=\"rootCauseChartData\"\n                :loading=\"isRootCauseDataLoading\"\n                :height=\"'400px'\"\n                title=\"Root Cause Categories by Month\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n            </div>\n\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div>\n          </div>\n\n          <!-- Advanced Analysis Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\">\n              <h4 class=\"section-title\">Advanced Analysis</h4>\n            </div>\n            <div class=\"analysis-controls\">\n              <div class=\"control-row\">\n                <div class=\"control-group\">\n                  <label for=\"analysis-type-dropdown\" class=\"control-label\">Analysis Type:</label>\n                  <cv-dropdown\n                    id=\"analysis-type-dropdown\"\n                    v-model=\"selectedAnalysisType\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"Root Cause\">Root Cause Analysis</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n                <cv-button\n                  kind=\"primary\"\n                  @click=\"goToGroupAnalysis\"\n                  class=\"action-button\"\n                >\n                  Go to Analysis\n                </cv-button>\n              </div>\n            </div>\n            <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Select an analysis type and click \"Go to Analysis\" to view detailed breakout group analysis in the Group tab.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Critical Issue Details Modal -->\n    <cv-modal\n      :visible=\"showIssueModal\"\n      @modal-hidden=\"closeIssueModal\"\n      class=\"issue-details-modal\"\n      :size=\"'lg'\"\n    >\n      <template slot=\"title\">Critical Issue Details</template>\n      <template slot=\"content\">\n        <div v-if=\"selectedIssue\" class=\"issue-details-content\">\n          <h4>{{ selectedIssue.category }} - {{ selectedIssue.month }}</h4>\n          <div class=\"issue-chart-container\">\n            <ccv-simple-bar-chart\n              v-if=\"issueChartData.length > 0\"\n              :data=\"issueChartData\"\n              :options=\"issueChartOptions\"\n            ></ccv-simple-bar-chart>\n          </div>\n          <div class=\"issue-ai-description\">\n            <h5>AI Analysis</h5>\n            <p>{{ selectedIssue.aiDescription }}</p>\n          </div>\n          <div class=\"issue-comments\">\n            <cv-text-area\n              v-model=\"selectedIssue.comment\"\n              label=\"Comments\"\n              placeholder=\"Add your comments here...\"\n            ></cv-text-area>\n          </div>\n        </div>\n      </template>\n      <template slot=\"footer\">\n        <cv-button\n          kind=\"secondary\"\n          @click=\"closeIssueModal\"\n        >\n          Cancel\n        </cv-button>\n        <cv-button\n          kind=\"primary\"\n          @click=\"updateIssue\"\n        >\n          Update\n        </cv-button>\n      </template>\n    </cv-modal>\n  </div>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvModal,\n  CvTag,\n  CvTextArea,\n  CvInlineLoading,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\n\n// Import Carbon Charts\nimport { ComboChart } from \"@carbon/charts\";\nimport \"@carbon/charts/styles.css\";\n\n// Import Root Cause Chart component\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart.vue';\n\nexport default {\n  name: 'PQEDashboard',\n  components: {\n    CvButton,\n    CvModal,\n    CvTag,\n    CvTextArea,\n    CvInlineLoading,\n    CvDropdown,\n    CvDropdownItem,\n    RootCauseChart\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Filtering\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Advanced Analysis\n      selectedAnalysisType: 'Root Cause', // Default analysis type\n      selectedBreakoutGroup: null, // Will be set when a breakout group is selected\n\n      // Chart instances\n      categorySummaryChart: null,\n      validationChart: null,\n\n      // Current Month\n      currentMonth: '', // Will be set to current month in YYYY-MM format\n\n      // Loading States\n      isLoadingBreakoutGroups: false,\n      isLoadingTrackedItems: false,\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n      validationChartData: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: false,\n      validationChartOptions: {\n        title: 'Daily Status',\n        axes: {\n          left: {\n            title: 'Count',\n            mapsTo: 'value',\n            domain: [0, 150], // Increased range to accommodate volume data\n            scaleType: 'linear'\n          },\n          right: {\n            title: 'Yield (%)',\n            mapsTo: 'value',\n            domain: [0, 100],\n            scaleType: 'linear',\n            correspondingDatasets: [\n              'Daily Yield',\n              'Cumulative Yield',\n              'Target Yield'\n            ]\n          },\n          bottom: {\n            title: 'Day',\n            mapsTo: 'date',\n            scaleType: 'time'\n          }\n        },\n        height: '350px',\n        legend: {\n          alignment: 'center',\n          enabled: true,\n          fontColor: '#f4f4f4' // White text for legend\n        },\n        color: {\n          scale: {\n            'Validated': '#0062ff', // Blue\n            'Unvalidated': '#ff832b', // Orange\n            'Volume': '#24a148', // Green\n            'Daily Yield': '#6929c4', // Purple\n            'Cumulative Yield': '#1192e8', // Light blue\n            'Target Yield': '#fa4d56' // Red\n          }\n        },\n        toolbar: {\n          enabled: false\n        },\n        tooltip: {\n          enabled: true\n        },\n        animations: true,\n        data: {\n          groupMapsTo: 'group'\n        },\n        theme: 'g100', // Use darkest theme for better contrast\n        grid: {\n          x: {\n            enabled: false\n          },\n          y: {\n            enabled: true\n          }\n        },\n        comboChartTypes: [\n          {\n            type: 'stacked-bar',\n            correspondingDatasets: ['Validated', 'Unvalidated'],\n            options: {\n              fillOpacity: 0.8\n            }\n          },\n          {\n            type: 'line',\n            correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],\n            options: {\n              points: {\n                radius: 3\n              },\n              strokeWidth: 2\n            }\n          }\n        ],\n        thresholds: [\n          {\n            axis: 'right',\n            value: 95, // Target yield threshold\n            label: 'Target Yield (95%)',\n            fillColor: '#fa4d56',\n            opacity: 0.1\n          }\n        ]\n      },\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n      showAllBreakoutGroups: false, // Toggle for showing all breakout groups\n      showAllAlerts: false, // Toggle for showing all alerts (including medium priority)\n\n      // Item Tracking Data\n      trackedItems: [],\n\n      // Modal Data\n      showIssueModal: false,\n      selectedIssue: null,\n      issueChartData: [],\n      issueChartOptions: {\n        title: 'Issue Details',\n        axes: {\n          left: {\n            title: 'X-Factor',\n            mapsTo: 'value',\n            domain: [0, 4]\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'month',\n            scaleType: 'labels'\n          }\n        },\n        height: '300px',\n        legend: {\n          enabled: false\n        },\n        color: {\n          scale: {\n            'X-Factor': '#0062ff'\n          }\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true,\n          customHTML: (data) => {\n            return `\n              <div style=\"padding: 10px; background: #f4f4f4; border-radius: 4px;\">\n                <div style=\"font-weight: bold; margin-bottom: 5px;\">${data[0].data.month}</div>\n                <div>X-Factor: ${data[0].value.toFixed(2)}</div>\n              </div>\n            `;\n          }\n        },\n        animations: true,\n        thresholds: [\n          {\n            value: 1.5,\n            label: 'Sustained Problem Threshold',\n            fillColor: '#ff9a00',\n            opacity: 0.1\n          },\n          {\n            value: 3.0,\n            label: 'Critical Spike Threshold',\n            fillColor: '#fa4d56',\n            opacity: 0.1\n          }\n        ]\n      }\n    };\n  },\n  mounted() {\n    this.loadDashboardData();\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n    // Sort breakout groups by xFactor (highest first) and filter critical ones\n    sortedCriticalBreakoutGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 1.5) // Only groups with xFactor >= 1.5 are considered critical\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get normal breakout groups (non-critical)\n    normalBreakoutGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor < 1.5) // Only groups with xFactor < 1.5\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get high priority groups (xFactor >= 3.0)\n    sortedHighPriorityGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 3.0) // Only groups with xFactor >= 3.0 (critical spike)\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    },\n\n    // Get medium priority groups (1.5 <= xFactor < 3.0)\n    sortedMediumPriorityGroups() {\n      return [...this.breakoutGroups]\n        .filter(group => group.xFactor >= 1.5 && group.xFactor < 3.0) // Groups with 1.5 <= xFactor < 3.0 (sustained problem)\n        .sort((a, b) => b.xFactor - a.xFactor); // Sort by xFactor in descending order\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadBreakoutGroups();\n      this.loadTrackedItems();\n      this.loadRootCauseData();\n    },\n\n    loadCriticalIssues() {\n      console.log('Loading critical issues data');\n\n      // Use hardcoded values to match the UI with categories\n      const criticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to 0 to match the UI\n      this.newCriticalIssues = [];\n\n      // Set unresolved critical issues\n      this.unresolvedCriticalIssues = criticalIssues;\n\n      // Set critical issues section to collapsed by default\n      this.isCriticalIssuesExpanded = false;\n\n      console.log('Critical issues set:', {\n        new: this.newCriticalIssues,\n        unresolved: this.unresolvedCriticalIssues\n      });\n    },\n\n\n\n    loadValidationCounts() {\n      console.log('Loading validation counts and chart data');\n\n      // Use hardcoded values to match the UI\n      this.validatedCount = 125;\n      this.unvalidatedCount = 37;\n\n      // Generate mock data for the validation chart\n      this.generateValidationChartData();\n\n      console.log('Validation counts set:', {\n        validatedCount: this.validatedCount,\n        unvalidatedCount: this.unvalidatedCount\n      });\n\n      // Initialize the validation chart after data is loaded\n      this.$nextTick(() => {\n        this.initValidationChart();\n      });\n    },\n\n    generateValidationChartData() {\n      // Generate mock data for the validation chart\n      const data = [];\n\n      // Generate data for the last 14 days\n      for (let i = 13; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n\n        // Format date for display in band scale\n        const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\n\n        // Generate random values for validated and unvalidated defects\n        const validated = Math.floor(Math.random() * 12) + 3; // 3-15 validated defects\n        const unvalidated = Math.floor(Math.random() * 8); // 0-8 unvalidated defects\n\n        // Calculate daily yield (assuming 100 units processed per day)\n        const unitsPerDay = 100;\n        const dailyYield = ((unitsPerDay - (validated + unvalidated)) / unitsPerDay) * 100;\n\n        // Generate volume data (units processed per day)\n        const volume = unitsPerDay + Math.floor(Math.random() * 50) - 25; // 75-125 units\n\n        // Create data points with all values\n        // Defect data for grouped bar chart\n        data.push({\n          date: dateStr,\n          group: 'Validated',\n          value: validated\n        });\n\n        data.push({\n          date: dateStr,\n          group: 'Unvalidated',\n          value: unvalidated\n        });\n\n        // Volume data for grouped bar chart\n        data.push({\n          date: dateStr,\n          group: 'Volume',\n          value: volume\n        });\n\n        // Yield data for line chart\n        data.push({\n          date: dateStr,\n          group: 'Daily Yield',\n          value: dailyYield\n        });\n\n        // Calculate cumulative yield (simplified for this example)\n        const cumulativeYield = 90 + (Math.random() * 8); // 90-98% range\n\n        data.push({\n          date: dateStr,\n          group: 'Cumulative Yield',\n          value: cumulativeYield\n        });\n\n        data.push({\n          date: dateStr,\n          group: 'Target Yield',\n          value: 95 // Target yield is 95%\n        });\n      }\n\n      // Set the validation chart data\n      this.validationChartData = data;\n      console.log('Validation chart data generated:', data);\n    },\n\n    initValidationChart() {\n      console.log('Initializing validation chart...');\n      try {\n        // Destroy existing chart if it exists\n        if (this.validationChart) {\n          console.log('Destroying existing chart');\n          this.validationChart.destroy();\n        }\n\n        // Get the chart container element\n        const chartContainer = document.getElementById('validationChart');\n        console.log('Chart container:', chartContainer);\n        if (!chartContainer) {\n          console.error('Validation chart container not found');\n          return;\n        }\n\n        // Log validation chart data\n        console.log('Validation chart data length:', this.validationChartData.length);\n        console.log('Sample data:', this.validationChartData.slice(0, 5));\n\n        // Create a simplified chart configuration\n        const chartOptions = {\n          title: 'Daily Status',\n          axes: {\n            left: {\n              title: 'Count',\n              mapsTo: 'value',\n              domain: [0, 150],\n              scaleType: 'linear'\n            },\n            right: {\n              title: 'Yield (%)',\n              mapsTo: 'value',\n              domain: [0, 100],\n              scaleType: 'linear',\n              correspondingDatasets: [\n                'Daily Yield',\n                'Cumulative Yield',\n                'Target Yield'\n              ]\n            },\n            bottom: {\n              title: 'Day',\n              mapsTo: 'date',\n              scaleType: 'band'\n            }\n          },\n          height: '350px',\n          width: '100%',\n          legend: {\n            alignment: 'center',\n            enabled: true,\n            fontColor: '#f4f4f4'\n          },\n          color: {\n            scale: {\n              'Validated': '#0062ff', // Blue\n              'Unvalidated': '#ff832b', // Orange\n              'Volume': '#24a148', // Green\n              'Daily Yield': '#6929c4', // Purple\n              'Cumulative Yield': '#1192e8', // Light blue\n              'Target Yield': '#fa4d56' // Red\n            }\n          },\n          toolbar: {\n            enabled: false\n          },\n          data: {\n            groupMapsTo: 'group'\n          },\n          theme: 'g100',\n          grid: {\n            x: {\n              enabled: false\n            },\n            y: {\n              enabled: true\n            }\n          },\n          comboChartTypes: [\n            {\n              type: 'grouped-bar',\n              correspondingDatasets: ['Validated', 'Unvalidated', 'Volume']\n            },\n            {\n              type: 'line',\n              correspondingDatasets: ['Daily Yield', 'Cumulative Yield', 'Target Yield'],\n              options: {\n                points: {\n                  radius: 0\n                },\n                strokeWidth: 2\n              }\n            }\n          ],\n          thresholds: [\n            {\n              axis: 'right',\n              value: 95,\n              label: 'Target Yield (95%)',\n              fillColor: '#fa4d56',\n              opacity: 0.1\n            }\n          ],\n          tooltip: {\n            enabled: true,\n            customHTML: (data) => {\n              let html = `<div style=\"padding: 10px; background: #262626; color: #f4f4f4; border-radius: 4px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\">\n                <div style=\"font-weight: bold; margin-bottom: 5px;\">${data[0].date}</div>`;\n\n              data.forEach(item => {\n                let displayValue;\n                const group = item.group;\n\n                if (group.includes('Yield')) {\n                  // Format yield values as percentages\n                  displayValue = `${item.value.toFixed(1)}%`;\n                } else if (group === 'Volume') {\n                  // Format volume as integer with 'units' label\n                  displayValue = `${Math.round(item.value)} units`;\n                } else {\n                  // Format defect counts as integers\n                  displayValue = Math.round(item.value);\n                }\n\n                html += `<div style=\"margin: 4px 0;\">\n                  <span style=\"display: inline-block; width: 12px; height: 12px; background-color: ${chartOptions.color.scale[group]}; margin-right: 6px; border-radius: 50%;\"></span>\n                  <span style=\"font-weight: 500;\">${group}:</span> ${displayValue}\n                </div>`;\n              });\n\n              html += `</div>`;\n              return html;\n            }\n          }\n        };\n\n        // Create the combo chart with the simplified configuration\n        this.validationChart = new ComboChart(\n          chartContainer,\n          {\n            data: this.validationChartData,\n            options: chartOptions\n          }\n        );\n\n        console.log('Validation chart initialized with ComboChart');\n      } catch (error) {\n        console.error('Error initializing validation chart:', error);\n\n        // Create a fallback display if the chart fails\n        const chartContainer = document.getElementById('validationChart');\n        if (chartContainer) {\n          console.log('Creating fallback display for validation chart');\n          chartContainer.innerHTML = `\n            <div style=\"height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #161616; border-radius: 8px; padding: 16px;\">\n              <div style=\"color: #f4f4f4; margin-bottom: 16px; font-weight: 600; font-size: 1rem;\">Daily Status</div>\n              <div style=\"color: #c6c6c6; margin-bottom: 16px; font-size: 0.875rem;\">Chart could not be displayed. Showing summary data instead.</div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #0062ff; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Validated: <strong>${this.validatedCount}</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #ff832b; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Unvalidated: <strong>${this.unvalidatedCount}</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center; margin-bottom: 12px;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #24a148; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Volume: <strong>100 units</strong></span>\n              </div>\n              <div style=\"display: flex; align-items: center;\">\n                <span style=\"display: inline-block; width: 12px; height: 12px; background-color: #6929c4; margin-right: 8px; border-radius: 50%;\"></span>\n                <span style=\"color: #f4f4f4;\">Current Yield: <strong>94.2%</strong></span>\n              </div>\n            </div>\n          `;\n        }\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log('Loading breakout groups from API for current month');\n      try {\n        this.isLoadingBreakoutGroups = true;\n\n        // Get the current month in YYYY-MM format\n        const now = new Date();\n        const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n        this.currentMonth = currentMonth;\n\n        console.log(`Current month: ${this.currentMonth}`);\n\n        // Make API call to get breakout groups data\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            month: currentMonth // Pass the current month to get specific data\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success' && data.breakout_groups) {\n          // Add current month flag to each breakout group\n          this.breakoutGroups = data.breakout_groups.map(group => ({\n            ...group,\n            // If sustainedXFactor is not provided, use a default value\n            sustainedXFactor: group.sustainedXFactor || (group.status === 'Sustained Problem' ? group.xFactor * 0.9 : group.xFactor * 0.5),\n            isCurrentMonth: true, // All groups from the API are for the current month\n            inHeatmap: true // Indicate that these groups are also in the heatmap\n          }));\n\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${currentMonth}`);\n        } else {\n          // Fallback to sample data if API fails\n          console.warn('No breakout groups found in API response, using sample data');\n          this.breakoutGroups = [\n            {\n              name: 'Fan Themis',\n              status: 'Short-Term Spike',\n              xFactor: 3.2,\n              sustainedXFactor: 1.2,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Victoria Crypto',\n              status: 'Sustained Problem',\n              xFactor: 1.8,\n              sustainedXFactor: 1.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Quantum Nexus',\n              status: 'Sustained Problem',\n              xFactor: 1.6,\n              sustainedXFactor: 1.6,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Orion Base',\n              status: 'Normal',\n              xFactor: 0.9,\n              sustainedXFactor: 0.8,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Stellar Core',\n              status: 'Normal',\n              xFactor: 0.8,\n              sustainedXFactor: 0.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Nebula Drive',\n              status: 'Normal',\n              xFactor: 0.7,\n              sustainedXFactor: 0.7,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Pulsar Matrix',\n              status: 'Normal',\n              xFactor: 0.6,\n              sustainedXFactor: 0.6,\n              isCurrentMonth: true,\n              inHeatmap: true\n            },\n            {\n              name: 'Quasar Link',\n              status: 'Normal',\n              xFactor: 0.5,\n              sustainedXFactor: 0.5,\n              isCurrentMonth: true,\n              inHeatmap: true\n            }\n          ];\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Fallback to empty array\n        this.breakoutGroups = [];\n      } finally {\n        this.isLoadingBreakoutGroups = false;\n      }\n\n      console.log('Breakout groups set:', this.breakoutGroups);\n    },\n\n    getAiDescriptionForIssue(issue) {\n      // This would call WatsonX.ai to get an AI description for the issue\n      // For now, we'll use the existing description\n      console.log(`Getting AI description for issue: ${issue.id}`);\n    },\n\n    getStatusClass(status) {\n      switch (status) {\n        case 'Short-Term Spike':\n          return 'status-spike';\n        case 'Sustained Problem':\n          return 'status-sustained';\n        case 'Critical (Both)':\n          return 'status-critical';\n        default:\n          return 'status-normal';\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log(`Viewing details for issue: ${issue.id}`);\n      this.selectedIssue = { ...issue };\n      this.loadIssueChartData(issue);\n      this.showIssueModal = true;\n    },\n\n    loadIssueChartData(issue) {\n      console.log(`Loading chart data for issue: ${issue.id}`);\n\n      // For now, we'll create some sample data\n      // In a real implementation, this would come from the API\n      const months = [];\n      const values = [];\n\n      // Get the issue month\n      const issueDate = new Date(issue.month + '-01');\n\n      // Create data for 6 months before and after the issue month\n      for (let i = -6; i <= 6; i++) {\n        const date = new Date(issueDate);\n        date.setMonth(date.getMonth() + i);\n        const monthStr = date.toISOString().slice(0, 7); // YYYY-MM format\n\n        // Base value that increases at the issue month\n        let value = 0.5;\n        if (i < 0) {\n          value = 0.5 + (i * 0.05); // Slight variation before issue\n        } else if (i === 0) {\n          // The spike at the issue month\n          value = issue.increaseMultiplier === '(new)' ? 1.0 : parseFloat(issue.increaseMultiplier);\n        } else {\n          // Gradual decrease after the spike\n          value = parseFloat(issue.increaseMultiplier) * Math.pow(0.8, i);\n        }\n\n        months.push(monthStr);\n        values.push({\n          month: monthStr,\n          value: value\n        });\n      }\n\n      // Set the chart data\n      this.issueChartData = values;\n\n      // Update chart options\n      this.issueChartOptions = {\n        title: `${issue.category} - ${issue.month}`,\n        axes: {\n          left: {\n            title: 'X-Factor',\n            mapsTo: 'value',\n            domain: [0, Math.max(...values.map(v => v.value)) * 1.2]\n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'month',\n            scaleType: 'labels'\n          }\n        },\n        height: '300px',\n        color: {\n          scale: {\n            'X-Factor': '#0062ff'\n          }\n        }\n      };\n    },\n\n    closeIssueModal() {\n      console.log('Closing issue modal');\n      this.showIssueModal = false;\n      this.selectedIssue = null;\n    },\n\n    updateIssue() {\n      console.log(`Updating issue: ${this.selectedIssue.id}`);\n\n      // In a real implementation, this would call an API to update the issue\n      // For now, we'll just update the local data\n      const index = this.unresolvedCriticalIssues.findIndex(issue => issue.id === this.selectedIssue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues[index].comment = this.selectedIssue.comment;\n        console.log(`Updated issue ${this.selectedIssue.id} with comment: ${this.selectedIssue.comment}`);\n      }\n\n      this.closeIssueModal();\n    },\n\n    goToValidationPage() {\n      console.log('Navigating to validations page');\n      this.$router.push('/validations');\n    },\n\n    goToGroupAnalysis() {\n      console.log(`Navigating to Group tab for ${this.selectedAnalysisType} analysis`);\n\n      // Find the parent MetisXFactors component\n      let parent = this.$parent;\n      while (parent && parent.$options.name !== 'MetisXFactors') {\n        parent = parent.$parent;\n      }\n\n      if (parent) {\n        console.log('Found MetisXFactors parent component, navigating to Group tab');\n\n        // If a breakout group is selected, use it\n        if (this.selectedBreakoutGroup) {\n          // Navigate to the Group tab with the selected breakout group\n          parent.selectBreakoutFromDashboard(this.selectedBreakoutGroup);\n\n          // After navigation, trigger the appropriate analysis based on the selected type\n          this.$nextTick(() => {\n            if (this.selectedAnalysisType === 'Root Cause') {\n              parent.viewRootCauseAnalysis(3); // Show 3 months of data\n            }\n          });\n        } else {\n          // If no breakout group is selected, use the first one from the breakout groups\n          if (this.breakoutGroups.length > 0) {\n            this.selectedBreakoutGroup = this.breakoutGroups[0].name;\n            this.goToGroupAnalysis(); // Call this method again with the selected breakout group\n          } else {\n            console.error('No breakout groups available');\n          }\n        }\n      } else {\n        console.error('Could not find MetisXFactors parent component');\n      }\n    },\n\n    viewBreakoutDetails(group, targetTab = 'group') {\n      console.log(`Viewing details for breakout group: ${group.name} in ${targetTab} tab`);\n\n      // Set the selected breakout group\n      this.selectedBreakoutGroup = group.name;\n\n      // Find the parent MetisXFactors component\n      let parent = this.$parent;\n      while (parent && parent.$options.name !== 'MetisXFactors') {\n        parent = parent.$parent;\n      }\n\n      if (parent) {\n        if (targetTab === 'heatmap') {\n          console.log('Found MetisXFactors parent component, navigating to Heatmap tab');\n          // Set the active tab to Heatmap (index 0) and then select the breakout group\n          parent.activeTab = 0; // Heatmap tab index\n          parent.selectBreakoutFromDashboard(group.name);\n\n          // If there's a specific method to highlight this group in the heatmap, call it\n          if (parent.highlightGroupInHeatmap) {\n            parent.$nextTick(() => {\n              parent.highlightGroupInHeatmap(group.name);\n            });\n          }\n        } else {\n          console.log('Found MetisXFactors parent component, navigating to Group tab');\n          // Default behavior - navigate to Group tab\n          parent.selectBreakoutFromDashboard(group.name);\n        }\n      } else {\n        console.error('Could not find MetisXFactors parent component');\n      }\n    },\n\n    // Navigate to heatmap tab and highlight this group\n    viewBreakoutInHeatmap(group) {\n      this.viewBreakoutDetails(group, 'heatmap');\n    },\n\n    formatMonth(dateStr) {\n      // Format YYYY-MM to MMM format (e.g., 2024-06 to Jun)\n      const date = new Date(dateStr + '-01'); // Add day to make a valid date\n      return date.toLocaleString('en-US', { month: 'short' });\n    },\n\n    // Toggle critical issues section expanded/collapsed\n    toggleCriticalIssuesExpanded() {\n      console.log('Toggling critical issues expanded state');\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    // Handle severity filter dropdown change\n    handleSeverityFilterChange() {\n      console.log(`Severity filter changed to: ${this.severityFilter}`);\n\n      if (this.severityFilter === 'all') {\n        // Clear severity filters\n        this.selectedFilters.severity = [];\n      } else {\n        // Set the selected severity\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    // Handle analysis type filter dropdown change\n    handleAnalysisFilterChange() {\n      console.log(`Analysis filter changed to: ${this.analysisTypeFilter}`);\n\n      if (this.analysisTypeFilter === 'all') {\n        // Clear analysis type filters\n        this.selectedFilters.analysisType = [];\n      } else {\n        // Set the selected analysis type\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    // Clear all filters\n    clearFilters() {\n      console.log('Clearing all filters');\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    initCategorySummaryChart() {\n      try {\n        // Destroy existing chart if it exists\n        if (this.categorySummaryChart) {\n          this.categorySummaryChart.destroy();\n        }\n\n        // Get the chart container element\n        const chartContainer = document.getElementById('categorySummaryChart');\n        if (!chartContainer) {\n          console.error('Category summary chart container not found');\n          return;\n        }\n\n        // Create mock data for the selected category\n        const chartData = this.generateCategorySummaryData();\n\n        // Create chart options\n        const options = {\n          title: `${this.selectedCategory} Analysis Summary`,\n          axes: {\n            left: {\n              title: 'X-Factor',\n              mapsTo: 'value',\n              domain: [0, 4]\n            },\n            bottom: {\n              title: 'Month',\n              mapsTo: 'date',\n              scaleType: 'labels'\n            }\n          },\n          height: '300px',\n          width: '100%',\n          legend: {\n            alignment: 'center',\n            enabled: true\n          },\n          color: {\n            scale: {\n              'X-Factor': '#0062ff',\n              'Threshold': '#ff9a00'\n            }\n          },\n          toolbar: {\n            enabled: false\n          },\n          data: {\n            groupMapsTo: 'group'\n          },\n          theme: 'g90', // Use dark theme to match the UI\n          thresholds: [\n            {\n              value: 1.5,\n              label: 'Sustained Problem Threshold',\n              fillColor: '#ff9a00',\n              opacity: 0.1\n            },\n            {\n              value: 3.0,\n              label: 'Critical Spike Threshold',\n              fillColor: '#fa4d56',\n              opacity: 0.1\n            }\n          ]\n        };\n\n        // Create the chart\n        this.categorySummaryChart = new ComboChart(chartContainer, {\n          data: chartData,\n          options: {\n            ...options,\n            comboChartTypes: [\n              {\n                type: 'line',\n                correspondingDatasets: ['X-Factor', 'Threshold'],\n                options: {\n                  points: {\n                    radius: 3\n                  },\n                  strokeWidth: 2\n                }\n              }\n            ]\n          }\n        });\n\n        console.log('Category summary chart initialized');\n      } catch (error) {\n        console.error('Error initializing category summary chart:', error);\n\n        // Create a simple fallback display for the data\n        const chartContainer = document.getElementById('categorySummaryChart');\n        if (chartContainer) {\n          chartContainer.innerHTML = `\n            <div style=\"height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;\">\n              <div style=\"color: #f4f4f4; margin-bottom: 16px;\">${this.selectedCategory} Analysis Summary</div>\n              <div style=\"color: #0062ff;\">No chart data available</div>\n            </div>\n          `;\n        }\n      }\n    },\n\n    generateCategorySummaryData() {\n      // Generate mock data for the category summary chart\n      const data = [];\n      const currentDate = new Date();\n\n      // Generate data for the last 6 months\n      for (let i = 5; i >= 0; i--) {\n        const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\n        const monthStr = this.formatMonth(monthDate.toISOString().slice(0, 7));\n\n        // Add X-Factor data point\n        const xFactor = i === 0 ?\n          (this.selectedCategory === 'Root Cause' ? 3.2 :\n           this.selectedCategory === 'Vintage' ? 2.5 :\n           this.selectedCategory === 'Supplier' ? 1.6 : 1.0) :\n          Math.random() * 1.5 + 0.5; // Random between 0.5-2.0 for previous months\n\n        data.push({\n          date: monthStr,\n          group: 'X-Factor',\n          value: xFactor\n        });\n\n        // Add threshold reference line\n        data.push({\n          date: monthStr,\n          group: 'Threshold',\n          value: 1.5\n        });\n      }\n\n      return data;\n    },\n\n    // These methods are no longer needed with the new filtering approach\n    // but we'll keep them for backward compatibility\n    getCriticalCountByCategory(category) {\n      return this.unresolvedCriticalIssues.filter(issue => issue.analysisType === category).length;\n    },\n\n    toggleIssueExpanded(issue) {\n      const index = this.expandedIssueIds.indexOf(issue.id);\n      if (index === -1) {\n        // Add to expanded list\n        this.expandedIssueIds.push(issue.id);\n      } else {\n        // Remove from expanded list\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    getXFactorSeverityClass(xFactor) {\n      if (xFactor >= 3.0) {\n        return 'severity-critical';\n      } else if (xFactor >= 1.5) {\n        return 'severity-warning';\n      } else if (xFactor >= 1.0) {\n        return 'severity-caution';\n      } else {\n        return 'severity-normal';\n      }\n    },\n\n    // Item tracking methods\n    async loadTrackedItems() {\n      console.log('Loading tracked items from Action Tracker API');\n      this.isLoadingTrackedItems = true;\n\n      try {\n        // Get the critical breakout groups from the current month\n        const criticalBreakoutGroups = this.sortedCriticalBreakoutGroups.map(group => group.name);\n        console.log('Critical breakout groups for filtering action items:', criticalBreakoutGroups);\n\n        // Make API call to get action tracker data\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            // Filter by breakout groups with issues in the current month\n            breakoutNames: criticalBreakoutGroups\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success' && data.items && data.items.length > 0) {\n          // Format the action tracker items for display\n          this.trackedItems = data.items.map(item => ({\n            id: item.id,\n            name: item.action && item.action.length > 50 ? item.action.substring(0, 50) + '...' : item.action || `Action for ${item.group}`,\n            status: item.status || 'In Progress',\n            owner: item.assignee || 'Unassigned',\n            dueDate: item.deadline || 'No deadline',\n            relatedIssue: item.group || 'Unknown',\n            description: item.notes || 'No description available',\n            progress: item.progress || 0,\n            // Add a flag to indicate if this item is related to a critical breakout group\n            isCritical: criticalBreakoutGroups.includes(item.group)\n          }));\n\n          console.log(`Loaded ${this.trackedItems.length} tracked items from API`);\n        } else {\n          console.warn('No action tracker items found in API response or API call failed, using sample data');\n\n          // Sample data as fallback\n          const allActionItems = [\n            {\n              id: 'item1',\n              name: 'Fan Themis Airflow Investigation',\n              status: 'In Progress',\n              owner: 'John Smith',\n              dueDate: '2024-07-15',\n              relatedIssue: 'Fan Themis',\n              description: 'Investigation into airflow issues in the new Fan Themis design that caused a 3.2x spike in failures.',\n              progress: 65,\n              isCritical: criticalBreakoutGroups.includes('Fan Themis')\n            },\n            {\n              id: 'item2',\n              name: 'Victoria Crypto Power Delivery Fix',\n              status: 'Pending Review',\n              owner: 'Jane Doe',\n              dueDate: '2024-07-10',\n              relatedIssue: 'Victoria Crypto',\n              description: 'Implementation of power delivery improvements to address the sustained problem in Victoria Crypto units.',\n              progress: 85,\n              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')\n            },\n            {\n              id: 'item3',\n              name: 'Supplier ABC Quality Audit',\n              status: 'Completed',\n              owner: 'Robert Johnson',\n              dueDate: '2024-06-30',\n              relatedIssue: 'Victoria Crypto',\n              description: 'Quality audit of Supplier ABC to address higher failure rates in Victoria Crypto units.',\n              progress: 100,\n              isCritical: criticalBreakoutGroups.includes('Victoria Crypto')\n            },\n            {\n              id: 'item4',\n              name: 'Quantum Nexus Thermal Analysis',\n              status: 'In Progress',\n              owner: 'Emily Chen',\n              dueDate: '2024-07-20',\n              relatedIssue: 'Quantum Nexus',\n              description: 'Thermal analysis of Quantum Nexus components to address overheating issues.',\n              progress: 40,\n              isCritical: criticalBreakoutGroups.includes('Quantum Nexus')\n            },\n            {\n              id: 'item5',\n              name: 'Nebula Drive Firmware Update',\n              status: 'Blocked',\n              owner: 'Michael Brown',\n              dueDate: '2024-07-05',\n              relatedIssue: 'Nebula Drive',\n              description: 'Firmware update to address performance issues in Nebula Drive units.',\n              progress: 25,\n              isCritical: criticalBreakoutGroups.includes('Nebula Drive')\n            }\n          ];\n\n          // Filter items to only include those related to breakout groups with issues in the current month\n          this.trackedItems = allActionItems.filter(item =>\n            criticalBreakoutGroups.some(groupName =>\n              item.relatedIssue.includes(groupName)\n            )\n          );\n        }\n\n        // Sort items by status priority and then by whether they're critical\n        this.trackedItems.sort((a, b) => {\n          const statusPriority = {\n            'blocked': 0,\n            'in progress': 1,\n            'pending review': 2,\n            'completed': 3\n          };\n\n          // First sort by status priority\n          const statusDiff = statusPriority[a.status.toLowerCase()] - statusPriority[b.status.toLowerCase()];\n\n          // If status is the same, sort by critical flag (critical items first)\n          if (statusDiff === 0) {\n            return b.isCritical - a.isCritical;\n          }\n\n          return statusDiff;\n        });\n\n        console.log('Tracked items loaded and sorted:', this.trackedItems);\n      } catch (error) {\n        console.error('Error loading tracked items:', error);\n        this.trackedItems = [];\n      } finally {\n        this.isLoadingTrackedItems = false;\n      }\n    },\n\n    getItemStatusClass(status) {\n      switch (status.toLowerCase()) {\n        case 'in progress':\n          return 'status-in-progress';\n        case 'pending review':\n          return 'status-pending';\n        case 'completed':\n          return 'status-completed';\n        case 'blocked':\n          return 'status-blocked';\n        default:\n          return 'status-default';\n      }\n    },\n\n    // Check if a group is in the critical breakout groups list\n    isCriticalGroup(groupName) {\n      return this.sortedCriticalBreakoutGroups.some(group => group.name === groupName);\n    },\n\n    viewItemDetails(item) {\n      console.log('Viewing item details:', item);\n      // Navigate to the Action Tracker page with the specific item ID\n      this.$router.push(`/action-tracker/items/${item.id}`);\n    },\n\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker');\n      // Navigate to the Action Tracker page using Vue Router\n      this.$router.push('/action-tracker');\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Generate mock data for the root cause chart\n        // In a real implementation, this would call an API\n        const mockData = this.generateMockRootCauseData();\n        this.rootCauseChartData = mockData;\n\n        console.log('Root cause chart data loaded:', this.rootCauseChartData);\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        this.rootCauseChartData = [];\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    generateMockRootCauseData() {\n      // Generate mock data similar to what MetisXFactors uses\n      const data = [];\n      const categories = ['Electrical', 'Mechanical', 'Thermal', 'Material', 'Process'];\n      const months = ['May 2024', 'Jun 2024', 'Jul 2024'];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate random fail rates between 0.5% and 4%\n          const baseRate = Math.random() * 3.5 + 0.5;\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(baseRate.toFixed(2)),\n            data: {\n              defects: Math.floor(Math.random() * 20) + 5,\n              volume: Math.floor(Math.random() * 1000) + 500,\n              month: month,\n              category: category,\n              isCritical: baseRate > 2.5,\n              formattedValue: `${baseRate.toFixed(2)}%`\n            }\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.pqe-dashboard-container {\n  width: 100%;\n  color: #f4f4f4; /* Light text for dark mode */\n}\n\n.content-wrapper {\n  padding: 16px;\n  margin-bottom: 16px;\n  background-color: #161616; /* Dark background */\n  border-radius: 4px;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 16px;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray text */\n}\n\n/* Key Metrics Section */\n.key-metrics-section {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 32px;\n}\n\n.metric-card {\n  flex: 1;\n  min-width: 200px;\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  margin-right: 16px;\n  color: #ffffff;\n\n  &.new-issues {\n    background-color: #8a3ffc; /* Purple */\n  }\n\n  &.critical-issues {\n    background-color: #fa4d56; /* Red */\n  }\n\n  &.validated {\n    background-color: #0f62fe; /* Blue */\n  }\n\n  &.unvalidated {\n    background-color: #ff832b; /* Orange */\n  }\n}\n\n.metric-content {\n  flex: 1;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray */\n  margin-bottom: 4px;\n}\n\n.metric-value {\n  font-size: 2.25rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n  line-height: 1.2;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n  margin-top: 4px;\n}\n\n/* Main Content Layout */\n.dashboard-main-content {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24px;\n}\n\n.dashboard-column {\n  flex: 1;\n  min-width: 300px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.section-card {\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n  border: 1px solid #333333; /* Dark border */\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 12px;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #c6c6c6; /* Light gray */\n  margin-top: 4px;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.section-content {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.section-footer {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.section-description {\n  color: #c6c6c6; /* Light gray */\n  line-height: 1.5;\n  margin-bottom: 16px;\n}\n\n.action-button {\n  align-self: flex-start;\n}\n\n/* Critical Issues Section */\n.critical-issues-header {\n  cursor: pointer;\n  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;\n\n  &:hover {\n    background-color: #353535; /* Darker gray */\n  }\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 32px;\n  height: 32px;\n  padding: 0 12px;\n  border-radius: 16px;\n  background-color: #fa4d56; /* Red */\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #ffffff; /* White text */\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n\n  &.flashing {\n    animation: flash 1.5s infinite;\n  }\n}\n\n@keyframes flash {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.8;\n    transform: scale(1.1);\n  }\n}\n\n.expand-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #393939; /* Dark gray */\n  color: #c6c6c6; /* Light gray */\n  transition: transform 0.3s ease, background-color 0.3s ease;\n\n  &.expanded {\n    transform: rotate(180deg);\n    background-color: #0f62fe; /* Blue */\n    color: #ffffff; /* White */\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Filter Controls */\n.filter-container {\n  margin-bottom: 24px;\n  padding: 16px;\n  background-color: #161616; /* Dark background */\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  border-bottom: 1px solid #333333; /* Dark border */\n  padding-bottom: 12px;\n}\n\n.filter-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n  margin: 0;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.filter-label {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n  margin-right: 8px;\n}\n\n.filter-dropdown {\n  width: 100%;\n}\n\n.clear-filters-button {\n  margin-left: auto;\n  background-color: #393939; /* Dark gray */\n  border: none;\n  transition: all 0.2s ease;\n  color: #f4f4f4; /* White text */\n\n  &:hover {\n    background-color: #4d4d4d; /* Darker gray */\n    color: #ffffff; /* White */\n  }\n}\n\n/* Issues List */\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.no-data-message {\n  margin-top: 16px;\n  padding: 24px;\n  border-radius: 8px;\n  text-align: center;\n  color: #c6c6c6; /* Light gray */\n  background-color: #161616; /* Dark background */\n  font-size: 1rem;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n\n  &::before {\n    content: '🔍';\n    display: block;\n    font-size: 2rem;\n    margin-bottom: 16px;\n  }\n}\n\n.issue-card {\n  padding: 0;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  border: 1px solid #333333; /* Dark border */\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: #161616; /* Dark background */\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);\n    border-color: #4d4d4d; /* Darker border on hover */\n  }\n\n  &.expanded {\n    .expand-indicator {\n      transform: rotate(180deg);\n      background-color: #0f62fe; /* Blue */\n      color: #ffffff; /* White */\n    }\n\n    .issue-header {\n      border-bottom: 1px solid #333333; /* Dark border */\n    }\n  }\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n  padding: 16px;\n  position: relative;\n  background-color: #262626; /* Dark gray */\n  transition: background-color 0.2s ease;\n\n  &:hover {\n    background-color: #353535; /* Darker gray */\n  }\n}\n\n.issue-tags {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.issue-metadata {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  margin-left: auto;\n}\n\n.issue-title {\n  font-weight: 600;\n  margin: 0 16px;\n  color: #f4f4f4; /* White text */\n  flex: 1;\n  font-size: 1rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.issue-multiplier {\n  font-weight: 700;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.875rem;\n\n  &.high-severity {\n    color: #fa4d56; /* Red */\n    background-color: rgba(250, 77, 86, 0.1); /* Transparent red */\n  }\n\n  &.medium-severity {\n    color: #ff832b; /* Orange */\n    background-color: rgba(255, 131, 43, 0.1); /* Transparent orange */\n  }\n}\n\n.analysis-tag {\n  font-size: 0.75rem;\n}\n\n.issue-content {\n  padding: 16px;\n  background-color: #161616; /* Dark background */\n  animation: slideDown 0.3s ease-out;\n  display: flex;\n  flex-direction: column;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    max-height: 500px;\n  }\n}\n\n.ai-description {\n  background-color: #262626; /* Dark gray */\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n  position: relative;\n\n  &::before {\n    content: 'AI Analysis';\n    position: absolute;\n    top: -10px;\n    left: 10px;\n    background-color: #6929c4; /* Purple */\n    color: #ffffff; /* White text */\n    padding: 2px 8px;\n    border-radius: 10px;\n    font-size: 0.75rem;\n    font-weight: 600;\n  }\n\n  p {\n    margin: 16px 0 0 0;\n    line-height: 1.5;\n    color: #f4f4f4; /* White text */\n  }\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n/* Breakout Groups Grid */\n.breakout-groups-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.breakout-groups-grid.normal-groups {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-card {\n  padding: 16px;\n  border-radius: 4px;\n  border-left: 4px solid #393939; /* Dark gray */\n  background-color: #262626; /* Dark gray background */\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n\n  &.highlighted {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);\n    position: relative;\n\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      width: 10px;\n      height: 10px;\n      border-radius: 50%;\n      background-color: #fa4d56; /* Red */\n    }\n  }\n\n  &.severity-normal {\n    border-left-color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    border-left-color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    border-left-color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    border-left-color: #fa4d56; /* Red */\n  }\n}\n\n.show-more-footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 8px;\n}\n\n.show-more-button {\n  background-color: transparent;\n  border: 1px dashed #333333; /* Dark border */\n  color: #0f62fe; /* Blue */\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */\n    border-color: #0f62fe; /* Blue */\n  }\n}\n\n.toggle-button {\n  background-color: transparent;\n  color: #0f62fe; /* Blue */\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: rgba(15, 98, 254, 0.1); /* Transparent blue */\n  }\n}\n\n/* Item Tracking Styles */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100px;\n  margin: 16px 0;\n}\n\n.tracked-items-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 16px;\n  margin-top: 16px;\n}\n\n.tracked-item-card {\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #262626; /* Dark gray background */\n  border-left: 4px solid #393939; /* Default border */\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n\n  &.status-in-progress {\n    border-left-color: #0f62fe; /* Blue */\n  }\n\n  &.status-pending {\n    border-left-color: #ff832b; /* Orange */\n  }\n\n  &.status-completed {\n    border-left-color: #24a148; /* Green */\n  }\n\n  &.status-blocked {\n    border-left-color: #fa4d56; /* Red */\n  }\n\n  &.critical-item {\n    border: 1px solid #fa4d56; /* Red border */\n    box-shadow: 0 0 0 1px #fa4d56;\n  }\n}\n\n.tracked-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.tracked-item-name {\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0;\n  flex: 1;\n}\n\n.tracked-item-status {\n  margin-left: 8px;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 16px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  background-color: #393939; /* Default background */\n\n  &.status-in-progress {\n    background-color: rgba(15, 98, 254, 0.2); /* Blue background */\n    color: #0f62fe; /* Blue text */\n  }\n\n  &.status-pending {\n    background-color: rgba(255, 131, 43, 0.2); /* Orange background */\n    color: #ff832b; /* Orange text */\n  }\n\n  &.status-completed {\n    background-color: rgba(36, 161, 72, 0.2); /* Green background */\n    color: #24a148; /* Green text */\n  }\n\n  &.status-blocked {\n    background-color: rgba(250, 77, 86, 0.2); /* Red background */\n    color: #fa4d56; /* Red text */\n  }\n}\n\n.tracked-item-details {\n  margin-bottom: 16px;\n}\n\n.detail-row {\n  display: flex;\n  margin-bottom: 8px;\n  font-size: 0.875rem;\n}\n\n.detail-label {\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n  width: 100px;\n}\n\n.detail-value {\n  color: #f4f4f4; /* White */\n\n  &.related-group {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n\n    &.critical-group {\n      color: #fa4d56; /* Red */\n      font-weight: 600;\n    }\n\n    .month-tag {\n      margin-left: 4px;\n    }\n  }\n}\n\n.progress-container {\n  flex: 1;\n  height: 8px;\n  background-color: #393939;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-top: 4px;\n}\n\n.progress-bar {\n  height: 100%;\n  background-color: #0f62fe; /* Blue */\n  border-radius: 4px;\n}\n\n.progress-text {\n  position: absolute;\n  right: 0;\n  top: -16px;\n  font-size: 0.75rem;\n  color: #c6c6c6;\n}\n\n.tracked-item-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.critical-tag {\n  margin-left: 8px;\n}\n\n.medium-priority-groups {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #333333; /* Dark border */\n}\n\n.breakout-group-name {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.breakout-group-xfactor {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.xfactor-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.xfactor-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.xfactor-label {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n  margin-right: 8px;\n}\n\n.xfactor-value {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n\n  &.severity-normal {\n    color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    color: #fa4d56; /* Red */\n  }\n}\n\n.breakout-group-metrics {\n  margin-bottom: 16px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n\n  &.severity-normal {\n    color: #24a148; /* Green */\n  }\n\n  &.severity-caution {\n    color: #f1c21b; /* Yellow */\n  }\n\n  &.severity-warning {\n    color: #ff832b; /* Orange */\n  }\n\n  &.severity-critical {\n    color: #fa4d56; /* Red */\n  }\n}\n\n.status-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.status-label {\n  font-size: 0.75rem;\n  color: #c6c6c6; /* Light gray */\n}\n\n.status-value {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #f4f4f4; /* White text */\n}\n\n.breakout-group-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 16px;\n  padding-top: 8px;\n  border-top: 1px solid #333333; /* Dark border */\n  justify-content: flex-end;\n\n  .action-button {\n    min-width: 120px;\n  }\n}\n\n.current-month-indicator {\n  margin-top: 8px;\n\n  .cv-tag {\n    margin-right: 0;\n  }\n}\n\n.breakout-group-card.current-month {\n  border: 1px solid #0f62fe;\n  box-shadow: 0 0 0 1px #0f62fe;\n}\n\n/* Chart Section */\n.chart-container {\n  height: 350px;\n  width: 100%;\n  margin: 16px 0;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333; /* Dark border */\n  background-color: #161616; /* Dark background */\n  position: relative;\n}\n\n.chart-wrapper {\n  position: relative;\n  height: 350px;\n  width: 100%;\n}\n\n.carbon-chart-container {\n  height: 100%;\n  width: 100%;\n}\n\n.no-data-message {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #c6c6c6; /* Light gray */\n}\n\n/* Carbon Charts styling */\n.carbon-chart-container .bx--chart-holder {\n  position: relative;\n}\n\n.carbon-chart-container .bx--axis.bx--axis--right {\n  display: block !important;\n}\n\n.carbon-chart-container .bx--line {\n  stroke-width: 2px !important;\n}\n\n.carbon-chart-container .bx--axis-title {\n  font-weight: 600;\n  fill: #f4f4f4; /* White text */\n}\n\n.carbon-chart-container .bx--axis-label {\n  fill: #f4f4f4; /* White text */\n}\n\n/* Ensure the lines are properly displayed */\n.carbon-chart-container .bx--cc--line {\n  stroke-width: 2px !important;\n}\n\n/* Ensure the points are visible */\n.carbon-chart-container .bx--cc--line-point {\n  r: 3px;\n  stroke-width: 1px;\n}\n\n/* Ensure the right axis is properly displayed */\n.carbon-chart-container .bx--cc--axes g.right-axis {\n  display: block !important;\n}\n\n/* Ensure the grid lines are properly displayed */\n.carbon-chart-container .bx--cc--grid line {\n  stroke: #333333; /* Dark border */\n  stroke-width: 0.5px;\n}\n\n/* Ensure the threshold line is visible */\n.carbon-chart-container .bx--cc--threshold line {\n  stroke-width: 1.5px !important;\n  stroke-dasharray: 5, 5;\n}\n\n/* Ensure the legend text is white */\n.carbon-chart-container .bx--cc--legend text {\n  fill: #f4f4f4 !important; /* White text */\n}\n\n/* Analysis Controls */\n.analysis-controls {\n  margin-bottom: 16px;\n}\n\n.control-row {\n  display: flex;\n  align-items: flex-end;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.control-group {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.control-label {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #c6c6c6; /* Light gray */\n}\n\n.control-dropdown {\n  width: 100%;\n}\n\n/* Modal Styles */\n.issue-details-modal {\n  background-color: #262626; /* Dark gray background */\n}\n\n.issue-details-content {\n  padding: 16px;\n  color: #f4f4f4; /* White text */\n}\n\n.issue-chart-container {\n  height: 300px;\n  margin-bottom: 16px;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333; /* Dark border */\n  background-color: #161616; /* Dark background */\n}\n</style>\n"]}]}