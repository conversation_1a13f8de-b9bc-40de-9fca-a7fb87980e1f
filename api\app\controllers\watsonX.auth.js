const IamTokenManager = require('@ibm-functions/iam-token-manager');
const fs = require('fs');
const path = require('path');

// Load credentials from file
function loadCredentials(fileName) {
    const filePath = path.join(__dirname, `../${fileName}`);
    console.log("Speech to text credentials:", filePath)
    if (fs.existsSync(filePath)) {
        const data = String(fs.readFileSync(filePath));
        const json = JSON.parse(data);
        if (!json.apikey || !json.url) {
            throw new Error(
                `The file ${filePath} does not contain the expected service credentials. Expected "apikey" and "url" properties.`
            );
        }
        return json;
    }
    throw new Error(
        `Missing service credentials for Watson Speech service. Ensure credentials exist in ${filePath}.`
    );
}

const STT_CREDENTIALS = loadCredentials('serviceCredentialsSTT.json');


// Function to get authentication tokens
async function getAuthTokens(values, callback) {
    try {
        const sttManager = new IamTokenManager({ iamApikey: STT_CREDENTIALS.apikey });
        const sttToken = await sttManager.getToken();
        const responseObj = { sttToken, sttURL: STT_CREDENTIALS.url };
        return callback(null, responseObj);
    } catch (error) {
        return callback(error, null);
    }
}

// Each export calls a different function. Pass values from client and the callback
exports.getAuthTokensClient = (req, res) => {
    getAuthTokens(req.body, function (err, data) {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        res.status(201).json(data);
    });
};

