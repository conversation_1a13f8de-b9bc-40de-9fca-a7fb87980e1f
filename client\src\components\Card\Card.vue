<style lang="scss">
@import "../../styles/carbon-utils";

.one-card {
  display: flex;
  background: #393939;
  padding: 10px;
  width: 300px;
  box-shadow: 0 2px 12px 0 #0000006e;
  justify-content: space-between;
  border-radius: 5px;
  .card-content {
    color: #6993dc;
    .name {
      font-size: 20px;
      font-weight: bolder;
    }
    .text {
      margin-top: 10px;
    }
    .date {
      font-size: 20px;
      font-weight: bolder;
      color: #ff0000;
    }
  }

  .card-right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    &.no-comment {
      justify-content: flex-end;
    }
    .comment-style {
      cursor: pointer;
    }
  }
}
</style>
<template>
  <div class="one-card">
    <div class="card-content">
      <div class="name">Name: {{ data.name }}</div>
      <div class="text">Cert: {{ data.cert }}</div>
      <div class="text">Area: {{ data.area }}</div>
      <div class="text">MT: {{ data.mt }}</div>
      <div class="text">Area Owner: {{ data.owner }}</div>
      <div class="text">Date Completed: {{ data.date }}</div>
      <div class="text expiration">{{ data.key }}</div>
      <div class="text date">{{ data.value }}</div>
    </div>
    <div class="card-right" v-if="data.comment">
      <ArrowUpRight20 @click="arrowClick" />
      <cv-overflow-menu v-show="isShowOverFlow">
        <cv-overflow-menu-item @click="approveClick"
          >Approve</cv-overflow-menu-item
        >
        <cv-overflow-menu-item @click="rejectClick"
          >Reject</cv-overflow-menu-item
        >
      </cv-overflow-menu>
    </div>
    <div class="card-right no-comment" v-else>
      <cv-overflow-menu v-show="isShowOverFlow">
        <cv-overflow-menu-item @click="approveClick"
          >Approve</cv-overflow-menu-item
        >
        <cv-overflow-menu-item @click="rejectClick"
          >Reject</cv-overflow-menu-item
        >
      </cv-overflow-menu>
    </div>
    <cv-modal
      @modal-hidden="comment_visible = false"
      :visible="comment_visible"
      class="comment_modal"
    >
      <template slot="title">Comment</template>
      <template slot="content">
        <cv-text-area v-model="comment" disabled> </cv-text-area>
      </template>
    </cv-modal>
  </div>
</template>
    
<script>
import { ArrowUpRight20 } from "@carbon/icons-vue";
export default {
  name: "CardComponents",
  components: {
    ArrowUpRight20,
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          name: "",
          cert: "",
          area: "",
          owner: "",
          date: "",
          key: "",
          value: "",
          comment: "",
        };
      },
    },
    isShowOverFlow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      comment_visible: false,
      comment: "",
    };
  },
  methods: {
    approveClick() {
      this.$emit("approveClick", this.data);
    },
    rejectClick() {
      this.$emit("rejectClick", this.data);
    },
    arrowClick() {
      this.comment = this.data.comment;
      this.comment_visible = true;
    },
  },
};
</script>
    