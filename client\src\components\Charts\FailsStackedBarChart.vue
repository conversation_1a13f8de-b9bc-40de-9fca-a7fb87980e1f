<template>
  <div class="fails-chart-container">
    <div class="chart-controls">
      <div class="control-group">
        <label for="chart-type-dropdown" class="control-label">View By:</label>
        <cv-dropdown
          id="chart-type-dropdown"
          v-model="chartType"
          class="control-dropdown"
        >
          <cv-dropdown-item value="validation">Validated/Unvalidated</cv-dropdown-item>
          <cv-dropdown-item value="rootCause">Root Cause</cv-dropdown-item>
          <cv-dropdown-item value="supplier">Supplier</cv-dropdown-item>
          <cv-dropdown-item value="sector">Sector</cv-dropdown-item>
          <cv-dropdown-item value="vintage">Vintage</cv-dropdown-item>
        </cv-dropdown>
      </div>
      <div class="control-group">
        <label for="time-range-dropdown" class="control-label">Time Range:</label>
        <cv-dropdown
          id="time-range-dropdown"
          v-model="timeRange"
          class="control-dropdown"
        >
          <cv-dropdown-item value="3month">3 Months</cv-dropdown-item>
          <cv-dropdown-item value="6month">6 Months</cv-dropdown-item>
        </cv-dropdown>
      </div>
      <div class="control-group" v-if="breakoutGroups && breakoutGroups.length > 0">
        <label for="group-dropdown" class="control-label">Group:</label>
        <cv-dropdown
          id="group-dropdown"
          v-model="selectedGroup"
          class="control-dropdown"
        >
          <cv-dropdown-item value="all">All Groups</cv-dropdown-item>
          <cv-dropdown-item
            v-for="group in breakoutGroups"
            :key="group.name"
            :value="group.name"
          >
            {{ group.name }}
          </cv-dropdown-item>
        </cv-dropdown>
      </div>
    </div>

    <div class="chart-container">
      <div v-if="isLoading" class="loading-container">
        <cv-inline-loading
          status="active"
          loading-text="Loading chart data..."
        ></cv-inline-loading>
      </div>
      <div v-else-if="chartData.length > 0" class="chart-wrapper">
        <StackedBarChart
          :data="chartData"
          :options="chartOptions"
        ></StackedBarChart>
      </div>
      <div v-else class="no-data-message">
        No data available for the selected criteria.
      </div>
    </div>
  </div>
</template>

<script>
import { CvDropdown, CvDropdownItem, CvInlineLoading } from '@carbon/vue';
import { StackedBarChart } from '@carbon/charts-vue';

export default {
  name: 'FailsStackedBarChart',
  components: {
    CvDropdown,
    CvDropdownItem,
    CvInlineLoading,
    StackedBarChart
  },
  props: {
    pqeOwner: {
      type: String,
      required: true
    },
    breakoutGroups: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isLoading: false,
      chartData: [],
      chartOptions: {},
      chartType: 'rootCause', // 'validation', 'rootCause', 'supplier', 'sector', 'vintage'
      timeRange: '6month', // '3month', '6month'
      selectedGroup: 'all', // 'all' or a specific group name
      months: []
    };
  },
  mounted() {
    console.log('FailsStackedBarChart component mounted');
    if (this.pqeOwner) {
      this.loadChartData();
    }
  },
  watch: {
    pqeOwner: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.loadChartData();
        }
      }
    },
    chartType() {
      this.loadChartData();
    },
    timeRange() {
      this.loadChartData();
    },
    selectedGroup() {
      this.loadChartData();
    }
  },
  methods: {

    async loadChartData() {
      console.log(`Loading chart data for PQE owner: ${this.pqeOwner}, Chart Type: ${this.chartType}, Time Range: ${this.timeRange}, Group: ${this.selectedGroup}`);
      console.log('Breakout groups:', this.breakoutGroups);
      this.isLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Determine the number of months to fetch based on the time range
        const monthsToFetch = this.timeRange === '3month' ? 3 : 6;

        // Fetch chart data from the API
        const response = await fetch('/api-statit2/get_pqe_chart_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner,
            chartType: this.chartType,
            monthsToFetch: monthsToFetch,
            groupName: this.selectedGroup === 'all' ? null : this.selectedGroup
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch chart data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Process the chart data
          this.processChartData(data.chart_data || []);
        } else {
          console.error('Failed to load chart data:', data.message);
          // Use sample data for development
          this.loadSampleChartData();
        }
      } catch (error) {
        console.error('Error loading chart data:', error);
        // Use sample data for development
        this.loadSampleChartData();
      } finally {
        this.isLoading = false;
      }
    },

    loadSampleChartData() {
      console.log('Loading sample chart data');

      // Generate sample months (last 6 or 3 months)
      const now = new Date();
      const months = [];
      const monthsToGenerate = this.timeRange === '3month' ? 3 : 6;

      for (let i = monthsToGenerate - 1; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = date.toLocaleString('default', { month: 'short' });
        const year = date.getFullYear().toString().slice(-2);
        months.push(`${monthName} '${year}`);
      }

      this.months = months;

      // Generate sample data based on chart type
      if (this.chartType === 'validation') {
        this.generateValidationChartData();
      } else if (this.chartType === 'rootCause') {
        this.generateRootCauseChartData();
      } else if (this.chartType === 'supplier') {
        this.generateSupplierChartData();
      } else if (this.chartType === 'sector') {
        this.generateSectorChartData();
      } else if (this.chartType === 'vintage') {
        this.generateVintageChartData();
      }

      // Filter data by selected group if needed
      if (this.selectedGroup !== 'all') {
        this.filterDataByGroup();
      }

      console.log('Generated chart data:', this.chartData);
    },

    filterDataByGroup() {
      // If a specific group is selected, filter the chart data
      console.log(`Filtering chart data by group: ${this.selectedGroup}`);

      if (this.selectedGroup !== 'all') {
        // Create a new filtered dataset that only includes data points for the selected group
        const filteredData = [];

        // For each data point, check if it belongs to the selected group
        this.chartData.forEach(item => {
          // For validation chart type, we need to add the group name to the data
          if (this.chartType === 'validation') {
            // Create a new item with the group name prefixed
            const newItem = { ...item };
            newItem.group = `${this.selectedGroup} - ${item.group}`;
            filteredData.push(newItem);
          }
          // For other chart types, only include items that contain the selected group name
          else if (item.group.includes(this.selectedGroup)) {
            filteredData.push(item);
          }
        });

        console.log(`Filtered from ${this.chartData.length} to ${filteredData.length} data points`);
        this.chartData = filteredData;
      }
    },

    generateValidationChartData() {
      const data = [];

      // For each month, generate validated and unvalidated counts
      this.months.forEach(month => {
        // Generate random values
        const validated = Math.floor(Math.random() * 50) + 20;
        const unvalidated = Math.floor(Math.random() * 30) + 5;

        // Add validated data point
        data.push({
          group: 'Validated',
          date: month,
          value: validated
        });

        // Add unvalidated data point
        data.push({
          group: 'Unvalidated',
          date: month,
          value: unvalidated
        });
      });

      // Sort data by date
      this.sortChartDataByDate(data);

      this.chartData = data;
      this.updateChartOptions();
    },

    sortChartDataByDate(data) {
      // Sort data by date to ensure proper ordering
      data.sort((a, b) => {
        // Extract month and year from date string (e.g., "Jun '24")
        const [aMonth, aYear] = a.date.split(" ");
        const [bMonth, bYear] = b.date.split(" ");

        // Compare years first
        if (aYear !== bYear) {
          return aYear.localeCompare(bYear);
        }

        // If years are the same, compare months
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months.indexOf(aMonth) - months.indexOf(bMonth);
      });
    },

    generateRootCauseChartData() {
      const data = [];
      // Match the root causes from the Metis XFactors format
      const rootCauses = ['KPARS1', 'DIAG', 'OTHER', 'LINK', 'FDB', 'DAP', 'CODE', 'BIOS', 'HLA'];

      // For each month, generate data for each root cause
      this.months.forEach(month => {
        // Generate a total value for the month (this will be used to calculate xFactor)
        const totalValue = Math.floor(Math.random() * 100) + 50;

        // Generate a target value (expected failures)
        const targetValue = Math.floor(totalValue * 0.7);

        // Distribute the total value among the root causes
        let remainingValue = totalValue;

        rootCauses.forEach((cause, index) => {
          // For the last cause, use the remaining value to ensure the sum equals totalValue
          let value;
          if (index === rootCauses.length - 1) {
            value = remainingValue;
          } else {
            // Generate a random portion of the remaining value
            const portion = Math.random() * 0.5; // Up to 50% of remaining
            value = Math.floor(remainingValue * portion);
            remainingValue -= value;
          }

          // Only add data points with non-zero values
          if (value > 0) {
            // Add data point
            data.push({
              group: cause,
              date: month,
              value: value
            });
          }
        });

        // Add a reference line for the target value
        data.push({
          group: 'Target',
          date: month,
          value: targetValue
        });
      });

      // Sort data by date
      this.sortChartDataByDate(data);

      this.chartData = data;
      this.updateChartOptions('rootCause');
    },

    generateSupplierChartData() {
      const data = [];
      const suppliers = ['Supplier A', 'Supplier B', 'Supplier C'];

      // For each month, generate data for each supplier
      this.months.forEach(month => {
        suppliers.forEach(supplier => {
          // Generate random value
          const value = Math.floor(Math.random() * 25) + 5;

          // Add data point
          data.push({
            group: supplier,
            date: month,
            value: value
          });
        });
      });

      // Sort data by date
      this.sortChartDataByDate(data);

      this.chartData = data;
      this.updateChartOptions();
    },

    generateSectorChartData() {
      const data = [];
      const sectors = ['North', 'South', 'East', 'West'];

      // For each month, generate data for each sector
      this.months.forEach(month => {
        sectors.forEach(sector => {
          // Generate random value
          const value = Math.floor(Math.random() * 15) + 5;

          // Add data point
          data.push({
            group: sector,
            date: month,
            value: value
          });
        });
      });

      // Sort data by date
      this.sortChartDataByDate(data);

      this.chartData = data;
      this.updateChartOptions();
    },

    generateVintageChartData() {
      const data = [];
      const vintages = ['Q1 2024', 'Q4 2023', 'Q3 2023', 'Q2 2023'];

      // For each month, generate data for each vintage
      this.months.forEach(month => {
        vintages.forEach(vintage => {
          // Generate random value
          const value = Math.floor(Math.random() * 18) + 3;

          // Add data point
          data.push({
            group: vintage,
            date: month,
            value: value
          });
        });
      });

      // Sort data by date
      this.sortChartDataByDate(data);

      this.chartData = data;
      this.updateChartOptions();
    },

    processChartData(chartData) {
      // Process the chart data from the API
      // Sort data by date
      this.sortChartDataByDate(chartData);
      this.chartData = chartData;
      this.updateChartOptions();
    },

    updateChartOptions(chartType = null) {
      // Set chart options based on the chart type
      const title = this.getChartTitle();
      console.log(`Updating chart options with title: ${title}`);

      // Use the provided chart type or the current chart type
      const type = chartType || this.chartType;

      // Base chart options
      const options = {
        title: title,
        axes: {
          left: {
            mapsTo: 'value',
            title: 'Count',
            scaleType: 'linear',
            includeZero: true
          },
          bottom: {
            mapsTo: 'date',
            title: 'Month',
            scaleType: 'labels'
          }
        },
        height: '400px',
        width: '100%',
        resizable: true,
        theme: 'g90', // Dark theme to match the imported styles
        legend: {
          alignment: 'center',
          position: 'bottom'
        },
        color: {
          scale: this.getColorScale(type)
        },
        toolbar: {
          enabled: true
        },
        data: {
          groupMapsTo: 'group'
        },
        stacked: true
      };

      // For root cause analysis, add special handling for the target line
      if (type === 'rootCause') {
        // Add special handling for the target line
        options.data.groups = [
          {
            name: 'Target',
            type: 'line'
          }
        ];

        // Add corresponding datasets to make the target line work
        options.data.correspondingDatasets = [
          {
            type: 'line',
            select: (data) => data.group === 'Target'
          }
        ];
      }

      this.chartOptions = options;

      console.log('Chart options updated:', this.chartOptions);
    },

    getChartTitle() {
      // Base title based on chart type
      let baseTitle = '';
      switch (this.chartType) {
        case 'validation':
          baseTitle = 'Validated vs Unvalidated Fails';
          break;
        case 'rootCause':
          baseTitle = 'Fails by Root Cause';
          break;
        case 'supplier':
          baseTitle = 'Fails by Supplier';
          break;
        case 'sector':
          baseTitle = 'Fails by Sector';
          break;
        case 'vintage':
          baseTitle = 'Fails by Vintage';
          break;
        default:
          baseTitle = 'Fails Analysis';
      }

      // Add group name if a specific group is selected
      if (this.selectedGroup !== 'all') {
        return `${baseTitle} - ${this.selectedGroup}`;
      }

      return baseTitle;
    },

    getColorScale(chartType = null) {
      // Use the provided chart type or the current chart type
      const type = chartType || this.chartType;

      // Return the appropriate color scale based on the chart type
      switch (type) {
        case 'validation':
          return {
            'Validated': '#24a148',
            'Unvalidated': '#ff832b'
          };
        case 'rootCause':
          return {
            'KPARS1': '#0f62fe',
            'DIAG': '#6929c4',
            'OTHER': '#1192e8',
            'LINK': '#8a3ffc',
            'FDB': '#ee5396',
            'DAP': '#ff7eb6',
            'CODE': '#fa4d56',
            'BIOS': '#d12771',
            'HLA': '#08bdba',
            'Target': '#f1c21b' // Yellow for target line
          };
        case 'supplier':
          return {
            'Supplier A': '#0072c3',
            'Supplier B': '#1192e8',
            'Supplier C': '#33b1ff'
          };
        case 'sector':
          return {
            'North': '#6929c4',
            'South': '#8a3ffc',
            'East': '#a56eff',
            'West': '#d4bbff'
          };
        case 'vintage':
          return {
            'Q1 2024': '#0f62fe',
            'Q4 2023': '#4589ff',
            'Q3 2023': '#78a9ff',
            'Q2 2023': '#a6c8ff'
          };
        default:
          return {};
      }
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    }
  }
};
</script>

<style scoped>
.fails-chart-container {
  width: 100%;
}

.chart-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  white-space: nowrap;
}

.control-dropdown {
  width: 200px;
}

.chart-container {
  padding: 1rem 0;
  min-height: 400px;
}

.chart-wrapper {
  height: 400px;
  width: 100%;
  overflow: hidden;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.no-data-message {
  color: #8d8d8d;
  font-size: 1rem;
  text-align: center;
  padding: 2rem;
}
</style>
