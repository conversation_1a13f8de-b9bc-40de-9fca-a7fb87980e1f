<template>
  <div class="line-bar-combo-chart-container">
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>Loading chart data...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="no-data-message">
      No data available for the chart
    </div>
    <div v-else ref="chartContainer" class="chart-container" :style="{ height }"></div>
  </div>
</template>

<script>
import { ComboChart } from '@carbon/charts';
import '@carbon/charts/styles.css';

export default {
  name: 'LineBarComboChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    options: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    initChart() {
      if (!this.data || this.data.length === 0) return;

      const chartContainer = this.$refs.chartContainer;
      if (!chartContainer) return;

      // Initialize the chart
      this.chart = new ComboChart(chartContainer, {
        data: this.data,
        options: this.options
      });
    },
    updateChart() {
      if (!this.chart) {
        this.initChart();
        return;
      }

      if (!this.data || this.data.length === 0) {
        if (this.chart) {
          this.chart.destroy();
          this.chart = null;
        }
        return;
      }

      // Update the chart data and options
      this.chart.model.setData(this.data);
      this.chart.model.setOptions(this.options);
    }
  }
};
</script>

<style scoped>
.line-bar-combo-chart-container {
  position: relative;
  width: 100%;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0f62fe;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: #666;
  font-style: italic;
}

.chart-container {
  width: 100%;
}
</style>
