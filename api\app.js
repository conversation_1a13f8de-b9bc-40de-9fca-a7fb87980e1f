require("dotenv").config();
var cookieParser = require('cookie-parser');
var csrf = require('csurf')
const express = require("express");
const cors = require("cors");

let mongodbutil = require("./mongodbutil");
const InitManager = require("./app/pub/init");
const helment = require("helmet");
var hpp = require('hpp');
const app = express();
app.use(helment());
app.use(hpp());
InitManager.initCore(mongodbutil);

let corsOptions = {
    origin: true,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization', 'csrf-token', 'Access-Control-Allow-Credentials'],
    //origin: "http://localhost:8081"
};
app.use(cors(corsOptions));

// parse requests of content-type - application/json
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser())
var csrfProtect = csrf({ cookie: true })

// Connect to MongoDB when the application starts
mongodbutil.connectServer()
    .then(() => {
        // simple route
        app.get("/", csrfProtect, (req, res) => {
            res.render({ message: "Welcome to statit api." });
        });
        require("./app/routes/routes")(app, express);
    })
    .catch((error) => {
        console.error('Failed to connect to MongoDB:', error);
        process.exit(1); // Exit the application if the connection fails
    });

// set port, listen for requests
const PORT = parseInt(process.env.PORT, 10) || 8080;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}.`);
});
