const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');

// Export controller functions
exports.get_metis_part_numbers = (req, res) => {
  getMetisPartNumbers(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_part_numbers:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_metis_breakout_names = (req, res) => {
  getMetisBreakoutNames(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_breakout_names:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// New endpoint for category analysis
exports.get_metis_category_analysis = (req, res) => {
  getMetisCategoryAnalysis(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_category_analysis:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// New endpoint for failure mode analysis
exports.get_metis_failure_modes = (req, res) => {
  getMetisFailureModes(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_failure_modes:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// New endpoint for vintage month analysis
exports.get_metis_vintage_analysis = (req, res) => {
  getMetisVintageAnalysis(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_vintage_analysis:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_metis_owning_groups = (req, res) => {
  getMetisOwningGroups(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_owning_groups:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_metis_owners = (req, res) => {
  getMetisOwners(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_owners:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_metis_xfactors = (req, res) => {
  getMetisXFactors(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_xfactors:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// New consolidated endpoint for breakout group analysis
exports.get_metis_breakout_analysis = (req, res) => {
  getMetisBreakoutAnalysis(req.body, function (err, data) {
    if (err) {
      console.error("Error in get_metis_breakout_analysis:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Function to get part numbers from the Metis Excel file
async function getMetisPartNumbers(values, callback) {
  let responseObj = {
    pns: [],
    status_res: null
  };

  try {
    // Load the Excel file
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Check if the 'PN' column exists
    const hasPNColumn = sheet.length > 0 && 'PN' in sheet[0];

    // Check if the 'Full Breakout Name' column exists
    const hasFullBreakoutName = sheet.length > 0 && 'Full Breakout Name' in sheet[0];

    if (!hasPNColumn) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the Excel file:', Object.keys(sheet[0]));
      }
      throw new Error("'PN' column not found in the Excel file");
    }

    let partNumbers = [];

    // If a specific breakout name is provided, filter by that breakout name
    if (values.breakoutName && hasFullBreakoutName) {
      // Filter rows by the specified breakout name
      const filteredRows = sheet.filter(row => row['Full Breakout Name'] === values.breakoutName);

      // Extract part numbers from the filtered rows
      partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    } else {
      // Extract all part numbers
      partNumbers = [...new Set(sheet.map(item => item.PN).filter(pn => pn))];
    }

    responseObj.pns = partNumbers;
    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error reading Excel file:", err);
    responseObj.status_res = "error";
    responseObj.error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get unique breakout names from the Metis Excel file
async function getMetisBreakoutNames(_, callback) { // Using _ to indicate unused parameter
  let responseObj = {
    breakout_names: [],
    status_res: null
  };

  try {
    // Load the Excel file
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      // Try to list all Excel files in the directory to help diagnose the issue
      const excelDir = path.join(__dirname, '../excel');
      if (fs.existsSync(excelDir)) {
        const files = fs.readdirSync(excelDir);
        console.error(`Excel file not found. Files in Excel directory: ${files.join(', ')}`);
      } else {
        console.error(`Excel directory does not exist: ${excelDir}`);
      }

      throw new Error(`Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Check if the 'Full Breakout Name' column exists
    const hasFullBreakoutName = sheet.length > 0 && 'Full Breakout Name' in sheet[0];

    if (!hasFullBreakoutName) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the Excel file:', Object.keys(sheet[0]));

        // Try to find a similar column name
        const columns = Object.keys(sheet[0]);
        const possibleBreakoutColumns = columns.filter(col =>
          col.toLowerCase().includes('breakout') ||
          col.toLowerCase().includes('group') ||
          col.toLowerCase().includes('name')
        );

        if (possibleBreakoutColumns.length > 0) {
          // Use the first possible column as a fallback
          const fallbackColumn = possibleBreakoutColumns[0];
          console.error(`Using '${fallbackColumn}' as fallback for 'Full Breakout Name'`);

          // Extract unique breakout names using the fallback column
          const breakoutNames = [...new Set(sheet.map(item => item[fallbackColumn]))];

          responseObj.breakout_names = breakoutNames.filter(name => name);
          responseObj.status_res = "success";
          return callback(null, responseObj);
        }
      }

      throw new Error("'Full Breakout Name' column not found in the Excel file");
    }

    // Extract unique breakout names
    const breakoutNames = [...new Set(sheet.map(item => item['Full Breakout Name']))];

    responseObj.breakout_names = breakoutNames.filter(name => name); // Filter out null/undefined values
    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error reading Excel file:", err);
    responseObj.status_res = "error";
    responseObj.error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get unique owning groups from the Metis Excel file
async function getMetisOwningGroups(_, callback) { // Using _ to indicate unused parameter
  let responseObj = {
    owning_groups: [],
    status_res: null
  };

  try {
    // Load the Excel file
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Check if the 'Dan OwningGroup' column exists
    const hasOwningGroup = sheet.length > 0 && 'Dan OwningGroup' in sheet[0];

    if (!hasOwningGroup) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the Excel file:', Object.keys(sheet[0]));
      }
      throw new Error("'Dan OwningGroup' column not found in the Excel file");
    }

    // Extract unique owning groups
    const owningGroups = [...new Set(sheet.map(item => item['Dan OwningGroup']))];

    // Filter out null/undefined values and sort alphabetically
    responseObj.owning_groups = owningGroups
      .filter(group => group && group.trim() !== '')
      .sort();

    console.log(`Found ${responseObj.owning_groups.length} unique owning groups`);

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error reading Excel file:", err);
    responseObj.status_res = "error";
    responseObj.error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get owners data from the breakout_targets.xlsx file
async function getMetisOwners(values, callback) {
  let responseObj = {
    owners_data: {
      groups: [],
      dev_owners: [],
      pqe_owners: []
    },
    filter_type: values.filterType || 'group',
    status_res: null
  };

  try {
    // Load the breakout_targets Excel file
    const filePath = path.join(__dirname, '../excel/breakout_targets.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Breakout targets Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);

    // Get the second sheet which contains the owners data
    if (workbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const sheetName = workbook.SheetNames[1]; // Second sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    if (sheet.length === 0) {
      throw new Error(`No data found in the owners sheet of breakout_targets.xlsx`);
    }

    // Check if the required columns exist
    const hasBreakoutGroup = sheet.length > 0 && 'Breakout Group' in sheet[0];
    const hasDevOwner = sheet.length > 0 && 'Dev Owner' in sheet[0];
    const hasPQEOwner = sheet.length > 0 && 'PQE Owner' in sheet[0];

    if (!hasBreakoutGroup || !hasDevOwner || !hasPQEOwner) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the owners sheet:', Object.keys(sheet[0]));
      }
      throw new Error(`Required columns not found in the owners sheet. 'Breakout Group': ${hasBreakoutGroup}, 'Dev Owner': ${hasDevOwner}, 'PQE Owner': ${hasPQEOwner}`);
    }

    // Extract unique groups, dev owners, and PQE owners
    const groups = [...new Set(sheet.map(item => item['Breakout Group']))];
    const devOwners = [...new Set(sheet.map(item => item['Dev Owner']))];
    const pqeOwners = [...new Set(sheet.map(item => item['PQE Owner']))];

    // Filter out null/undefined values and sort alphabetically
    responseObj.owners_data.groups = groups
      .filter(group => group && group.trim() !== '')
      .sort();

    responseObj.owners_data.dev_owners = devOwners
      .filter(owner => owner && owner.trim() !== '')
      .sort();

    responseObj.owners_data.pqe_owners = pqeOwners
      .filter(owner => owner && owner.trim() !== '')
      .sort();

    // If a specific filter type is requested, return the filtered data
    if (values.filterType === 'dev_owner') {
      // Return all breakout groups for the specified dev owner
      if (values.owner) {
        responseObj.filtered_groups = sheet
          .filter(item => item['Dev Owner'] === values.owner)
          .map(item => item['Breakout Group'])
          .filter(group => group && group.trim() !== '');
      }
    } else if (values.filterType === 'pqe_owner') {
      // Return all breakout groups for the specified PQE owner
      if (values.owner) {
        responseObj.filtered_groups = sheet
          .filter(item => item['PQE Owner'] === values.owner)
          .map(item => item['Breakout Group'])
          .filter(group => group && group.trim() !== '');
      }
    }

    console.log(`Found ${responseObj.owners_data.groups.length} groups, ${responseObj.owners_data.dev_owners.length} dev owners, and ${responseObj.owners_data.pqe_owners.length} PQE owners`);

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error reading owners data:", err);
    responseObj.status_res = "error";
    responseObj.error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get category analysis data for a specific breakout group
async function getMetisCategoryAnalysis(values, callback) {
  let responseObj = {
    status_res: null,
    categoryData: {},
    partNumbers: [],
    sql_error_msg: null
  };

  try {
    // Validate required parameters
    if (!values.breakoutName) {
      throw new Error("Breakout name is required");
    }
    if (!values.startDate || !values.endDate) {
      throw new Error("Start date and end date are required");
    }

    const breakoutName = values.breakoutName;
    const startDate = values.startDate;
    const endDate = values.endDate;

    console.log(`Getting category analysis for breakout: ${breakoutName}`);
    console.log(`Date range: ${startDate} to ${endDate}`);

    // Get part numbers for the breakout group
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Filter rows by the specified breakout name
    const filteredRows = sheet.filter(row => row['Full Breakout Name'] === breakoutName);
    console.log(`Found ${filteredRows.length} rows for breakout: ${breakoutName}`);

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for breakout: ${breakoutName}`);

    // Format part numbers for SQL query (ensure they're strings)
    const formattedPNs = partNumbers.map(pn => String(pn));

    // Connect to the database
    const connStr = configSwitch.getConnStr();
    const conn = await ibmdb.open(connStr);
    console.log("Connected to R0ADB2 for category analysis");

    // Get the months between start and end date
    const months = getMonthsBetweenDates(startDate, endDate);
    console.log(`Analyzing ${months.length} months: ${months.join(', ')}`);

    // Query for defect categories by month
    const categoryQuery = `
      SELECT
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
        b.ROOT_CAUSE_1 AS CATEGORY,
        COUNT(*) AS DEFECT_COUNT
      FROM
        QODS.GEM_DEFECT AS a
      JOIN
        QEVAL.GEM_DEFECT_UPDATES AS b
      ON
        a.DEFECT_ID = b.DEFECT_ID
      WHERE
        a.PART_NUM IN (${formattedPNs.map(() => '?').join(',')})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
      GROUP BY
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7), b.ROOT_CAUSE_1
      ORDER BY
        MONTH, DEFECT_COUNT DESC
    `;

    // Prepare query parameters
    const queryParams = [...formattedPNs, startDate, endDate];

    // Execute the query
    const categoryResults = await conn.query(categoryQuery, queryParams);
    console.log(`Retrieved ${categoryResults.length} category records`);

    // Get total defects per month for calculating percentages
    const totalDefectsQuery = `
      SELECT
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
        COUNT(*) AS TOTAL_DEFECTS
      FROM
        QODS.GEM_DEFECT AS a
      WHERE
        a.PART_NUM IN (${formattedPNs.map(() => '?').join(',')})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
      GROUP BY
        SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7)
      ORDER BY
        MONTH
    `;

    // Execute the query
    const totalDefectsResults = await conn.query(totalDefectsQuery, queryParams);
    console.log(`Retrieved ${totalDefectsResults.length} total defect records`);

    // Close the R0ADB2 database connection
    await conn.close();
    console.log("Closed R0ADB2 database connection");

    // Now get volume data from QRYPROD database
    const configVol = configSwitch.config('QRYPROD');
    let connStringVol = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + configVol.database_user + ";"
      + "PWD=" + configVol.database_password + ";"
      + "HOSTNAME=" + configVol.database_host + ";"
      + "PORT=" + configVol.database_port;

    const connVol = await ibmdb.open(connStringVol);
    console.log("Connected to QRYPROD for volume data");

    // Query for volume data
    const volumeQuery = `
      SELECT
        Q2QDPN AS PART_NUM,
        COUNT(*) AS COUNT,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS YEAR_MONTH
      FROM (
        SELECT
          Q2QDPN,
          Q2QDSQ,
          MIN(Q2OUTD) AS Q2OUTD,
          MIN(Q2OUTT) AS Q2OUTT
        FROM
          MFS2PCOMN.FCSPQD20
        WHERE
          Q2QDPN IN (${formattedPNs.map(() => '?').join(',')})
          AND Q2DEAC != 'Y'
          AND Q2FGID = 'PROCFULT'
        GROUP BY
          Q2QDPN, Q2QDSQ
      ) AS Q1
      WHERE
        Q2OUTD >= ?
        AND Q2OUTD <= ?
      GROUP BY
        Q2QDPN,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM')
      ORDER BY
        YEAR_MONTH
    `;

    // Prepare query parameters for volume query
    // Convert YYYY-MM dates to YYYY-MM-DD for the database query
    let queryStartDate = `${startDate}-01`;
    let queryEndDate;

    // For end date, set it to the last day of the month
    const [endYear, endMonth] = endDate.split('-');
    const lastDay = new Date(parseInt(endYear), parseInt(endMonth), 0).getDate();
    queryEndDate = `${endDate}-${lastDay}`;

    const volumeQueryParams = [...formattedPNs, queryStartDate, queryEndDate];

    // Execute the volume query
    const volumeResults = await connVol.query(volumeQuery, volumeQueryParams);
    console.log(`Retrieved ${volumeResults.length} volume records`);

    // Close the QRYPROD database connection
    await connVol.close();
    console.log("Closed QRYPROD database connection");

    // Process the results
    const categoryData = {
      months: months,
      categories: [],
      chartData: [],
      volumeData: {},
      failRates: {},
      defectCounts: {}
    };

    // Get target rate for this breakout group
    const targetRate = getTargetRate(breakoutName);
    categoryData.targetRate = targetRate;

    // Get unique categories
    const uniqueCategories = [...new Set(categoryResults.map(row => row.CATEGORY))];
    categoryData.categories = uniqueCategories;

    // Create a map of total defects by month
    const totalDefectsByMonth = {};
    totalDefectsResults.forEach(row => {
      totalDefectsByMonth[row.MONTH] = row.TOTAL_DEFECTS;
    });

    // Create a map of total volume by month
    const volumeByMonth = {};
    volumeResults.forEach(row => {
      const month = row.YEAR_MONTH;
      if (!volumeByMonth[month]) {
        volumeByMonth[month] = 0;
      }
      volumeByMonth[month] += row.COUNT;
    });

    // Calculate fail rates for each month as percentage
    const failRatesByMonth = {};
    months.forEach(month => {
      const defects = totalDefectsByMonth[month] || 0;
      const volume = volumeByMonth[month] || 0;
      const failRate = volume > 0 ? (defects / volume) * 100 : 0; // Calculate as percentage
      failRatesByMonth[month] = failRate;
    });

    // Create a map of defect counts by category and month
    const defectCountsByCategory = {};
    categoryResults.forEach(row => {
      const month = row.MONTH;
      const category = row.CATEGORY;
      const count = row.DEFECT_COUNT;

      if (!defectCountsByCategory[month]) {
        defectCountsByCategory[month] = {};
      }

      defectCountsByCategory[month][category] = count;
    });

    // Store volume, fail rate, and defect count data
    categoryData.volumeData = volumeByMonth;
    categoryData.failRates = failRatesByMonth;
    categoryData.defectCounts = defectCountsByCategory;

    // Create data structure for the stacked bar chart
    months.forEach(month => {
      const monthCategories = categoryResults.filter(row => row.MONTH === month);
      const totalDefects = totalDefectsByMonth[month] || 0;
      const totalVolume = volumeByMonth[month] || 0;
      const totalFailRate = failRatesByMonth[month] || 0;

      uniqueCategories.forEach(category => {
        const categoryRow = monthCategories.find(row => row.CATEGORY === category);
        const count = categoryRow ? categoryRow.DEFECT_COUNT : 0;

        // Calculate category fail rate (proportional to total fail rate)
        const percentage = totalDefects > 0 ? (count / totalDefects) * 100 : 0;
        const categoryFailRate = totalFailRate * (percentage / 100);

        categoryData.chartData.push({
          group: category,
          key: month,
          value: categoryFailRate, // Use fail rate instead of count
          rawCount: count, // Keep the raw count for reference
          totalCount: totalDefects,
          volume: totalVolume,
          percentage: percentage.toFixed(2),
          totalFailRate: totalFailRate,
          targetRate: targetRate,
          isAboveTarget: totalFailRate > (targetRate * 100) // Convert target to percentage for comparison
        });
      });
    });

    // Store the results in the response
    responseObj.categoryData = categoryData;
    responseObj.partNumbers = partNumbers;
    responseObj.status_res = "success";

    return callback(null, responseObj);
  } catch (err) {
    console.error("Error in getMetisCategoryAnalysis:", err);
    responseObj.status_res = "error";
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get failure mode data for a specific month and breakout group
async function getMetisFailureModes(values, callback) {
  let responseObj = {
    status_res: null,
    failureModeData: {},
    sql_error_msg: null
  };

  try {
    // Validate required parameters
    if (!values.breakoutName) {
      throw new Error("Breakout name is required");
    }
    if (!values.month) {
      throw new Error("Month is required (YYYY-MM format)");
    }

    const breakoutName = values.breakoutName;
    const month = values.month;

    console.log(`Getting failure modes for breakout: ${breakoutName}, month: ${month}`);

    // Get part numbers for the breakout group
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Filter rows by the specified breakout name
    const filteredRows = sheet.filter(row => row['Full Breakout Name'] === breakoutName);
    console.log(`Found ${filteredRows.length} rows for breakout: ${breakoutName}`);

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for breakout: ${breakoutName}`);

    // Format part numbers for SQL query (ensure they're strings)
    const formattedPNs = partNumbers.map(pn => String(pn));

    // Connect to the database
    const connStr = configSwitch.getConnStr();
    const conn = await ibmdb.open(connStr);
    console.log("Connected to R0ADB2 for failure mode analysis");

    // Query for failure modes for the selected month and category
    const failureModeQuery = `
      SELECT
        b.ROOT_CAUSE_1 AS CATEGORY,
        b.ROOT_CAUSE_2 AS FAILURE_MODE,
        COUNT(*) AS DEFECT_COUNT
      FROM
        QODS.GEM_DEFECT AS a
      JOIN
        QEVAL.GEM_DEFECT_UPDATES AS b
      ON
        a.DEFECT_ID = b.DEFECT_ID
      WHERE
        a.PART_NUM IN (${formattedPNs.map(() => '?').join(',')})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) = ?
      GROUP BY
        b.ROOT_CAUSE_1, b.ROOT_CAUSE_2
      ORDER BY
        b.ROOT_CAUSE_1, DEFECT_COUNT DESC
    `;

    // Prepare query parameters
    const queryParams = [...formattedPNs, month];

    // Execute the query
    const failureModeResults = await conn.query(failureModeQuery, queryParams);
    console.log(`Retrieved ${failureModeResults.length} failure mode records`);

    // Get total defects for the month for calculating percentages
    const totalDefectsQuery = `
      SELECT
        COUNT(*) AS TOTAL_DEFECTS
      FROM
        QODS.GEM_DEFECT AS a
      WHERE
        a.PART_NUM IN (${formattedPNs.map(() => '?').join(',')})
        AND a.PROCESS = 'FULL'
        AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) = ?
    `;

    // Execute the query
    const totalDefectsResult = await conn.query(totalDefectsQuery, queryParams);
    const totalDefects = totalDefectsResult[0]?.TOTAL_DEFECTS || 0;
    console.log(`Total defects for ${month}: ${totalDefects}`);

    // Close the database connection
    await conn.close();
    console.log("Closed database connection");

    // Process the results
    const failureModeData = {
      month: month,
      categories: [],
      failureModes: [],
      chartData: []
    };

    // Get unique categories
    const uniqueCategories = [...new Set(failureModeResults.map(row => row.CATEGORY))];
    failureModeData.categories = uniqueCategories;

    // Get unique failure modes
    const uniqueFailureModes = [...new Set(failureModeResults.map(row => row.FAILURE_MODE))];
    failureModeData.failureModes = uniqueFailureModes;

    // Create data structure for the combo chart
    failureModeResults.forEach(row => {
      const percentage = totalDefects > 0 ? (row.DEFECT_COUNT / totalDefects) * 100 : 0;

      failureModeData.chartData.push({
        group: 'Count',
        key: row.FAILURE_MODE,
        value: row.DEFECT_COUNT,
        category: row.CATEGORY
      });

      failureModeData.chartData.push({
        group: 'Percentage',
        key: row.FAILURE_MODE,
        percentage: percentage,
        category: row.CATEGORY
      });
    });

    // Store the results in the response
    responseObj.failureModeData = failureModeData;
    responseObj.status_res = "success";

    return callback(null, responseObj);
  } catch (err) {
    console.error("Error in getMetisFailureModes:", err);
    responseObj.status_res = "error";
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Function to get vintage month analysis data for a specific breakout group
async function getMetisVintageAnalysis(values, callback) {
  let responseObj = {
    status_res: null,
    vintageData: {},
    partNumbers: [],
    sql_error_msg: null
  };

  try {
    // Validate required parameters
    if (!values.breakoutName) {
      throw new Error("Breakout name is required");
    }
    if (!values.startDate || !values.endDate) {
      throw new Error("Start date and end date are required");
    }

    const breakoutName = values.breakoutName;
    const startDate = values.startDate;
    const endDate = values.endDate;

    console.log(`Getting vintage analysis for breakout: ${breakoutName}`);
    console.log(`Date range: ${startDate} to ${endDate}`);

    // Get part numbers for the breakout group
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Filter rows by the specified breakout name
    const filteredRows = sheet.filter(row => row['Full Breakout Name'] === breakoutName);
    console.log(`Found ${filteredRows.length} rows for breakout: ${breakoutName}`);

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for breakout: ${breakoutName}`);

    // Format part numbers for SQL query (ensure they're strings)
    const formattedPNs = partNumbers.map(pn => String(pn));

    // Connect to the database for vintage data (QRYPROD)
    const configVol = configSwitch.config('QRYPROD');
    let connStringVol = "DRIVER={DB2};"
      + "DATABASE=" + 'QRYPROD' + ";"
      + "UID=" + configVol.database_user + ";"
      + "PWD=" + configVol.database_password + ";"
      + "HOSTNAME=" + configVol.database_host + ";"
      + "PORT=" + configVol.database_port;

    const connVol = await ibmdb.open(connStringVol);
    console.log("Connected to QRYPROD for vintage data");

    // Query for vintage month data with serial numbers
    const vintageQuery = `
      SELECT
        Q2QDPN AS PART_NUM,
        Q2QDSQ AS SERIAL_NUM,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS YEAR_MONTH
      FROM MFS2PCOMN.FCSPQD20
      WHERE Q2FGID = 'INIT'
        AND Q2QDPN IN (${formattedPNs.map(() => '?').join(',')})
      ORDER BY
        YEAR_MONTH
    `;

    // Prepare query parameters for vintage query
    const vintageQueryParams = [...formattedPNs];

    // Execute the vintage query
    const vintageResults = await connVol.query(vintageQuery, vintageQueryParams);
    console.log(`Retrieved ${vintageResults.length} vintage records`);

    // Connect to the database for defect data (R0ADB2)
    const connStr = configSwitch.getConnStr();
    const conn = await ibmdb.open(connStr);
    console.log("Connected to R0ADB2 for defect data");

    // Get the months between start and end date
    const months = getMonthsBetweenDates(startDate, endDate);
    console.log(`Analyzing ${months.length} months: ${months.join(', ')}`);

    // Query for defects by month with serial numbers (from R0ADB2)
    const defectsQuery = `
      SELECT
        SUBSTR(CHAR(INCIDENT_DATE, ISO), 1, 7) AS MONTH,
        PART_NUM,
        PART_SER,
        COUNT(*) AS DEFECT_COUNT
      FROM
        QODS.GEM_DEFECT
      WHERE
        PART_NUM IN (${formattedPNs.map(() => '?').join(',')})
        AND PROCESS = 'FULL'
        AND SUBSTR(CHAR(INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
      GROUP BY
        SUBSTR(CHAR(INCIDENT_DATE, ISO), 1, 7),
        PART_NUM,
        PART_SER
      ORDER BY
        MONTH
    `;

    // Prepare query parameters for defects query
    const defectsQueryParams = [...formattedPNs, startDate, endDate];

    // Execute the defects query
    const defectsResults = await conn.query(defectsQuery, defectsQueryParams);
    console.log(`Retrieved ${defectsResults.length} defect records`);

    // Close the R0ADB2 database connection
    await conn.close();
    console.log("Closed R0ADB2 database connection");

    // Query for total volume data
    const volumeQuery = `
      SELECT
        Q2QDPN AS PART_NUM,
        COUNT(*) AS COUNT,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS YEAR_MONTH
      FROM (
        SELECT
          Q2QDPN,
          Q2QDSQ,
          MIN(Q2OUTD) AS Q2OUTD,
          MIN(Q2OUTT) AS Q2OUTT
        FROM
          MFS2PCOMN.FCSPQD20
        WHERE
          Q2QDPN IN (${formattedPNs.map(() => '?').join(',')})
          AND Q2DEAC != 'Y'
          AND Q2FGID = 'PROCFULT'
        GROUP BY
          Q2QDPN, Q2QDSQ
      ) AS Q1
      WHERE
        Q2OUTD >= ?
        AND Q2OUTD <= ?
      GROUP BY
        Q2QDPN,
        VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM')
      ORDER BY
        YEAR_MONTH
    `;

    // Prepare query parameters for volume query
    // Convert YYYY-MM dates to YYYY-MM-DD for the database query
    let queryStartDate = `${startDate}-01`;
    let queryEndDate;

    // For end date, set it to the last day of the month
    const [endYear, endMonth] = endDate.split('-');
    const lastDay = new Date(parseInt(endYear), parseInt(endMonth), 0).getDate();
    queryEndDate = `${endDate}-${lastDay}`;

    const volumeQueryParams = [...formattedPNs, queryStartDate, queryEndDate];

    // Execute the volume query
    const volumeResults = await connVol.query(volumeQuery, volumeQueryParams);
    console.log(`Retrieved ${volumeResults.length} volume records`);

    // Close the QRYPROD database connection
    await connVol.close();
    console.log("Closed QRYPROD database connection");

    // Process the results
    const vintageData = {
      months: months,
      vintageMonths: [],
      chartData: [],
      volumeData: {},
      failRates: {},
      defectCounts: {},
      vintageCounts: {}
    };

    // Get target rate for this breakout group
    const targetRate = getTargetRate(breakoutName);
    vintageData.targetRate = targetRate;

    // Get unique vintage months
    const uniqueVintageMonths = [...new Set(vintageResults.map(row => row.YEAR_MONTH))];
    vintageData.vintageMonths = uniqueVintageMonths.sort();

    // Create maps for defect data
    const totalDefectsByMonth = {};

    // Create a map of serial numbers to their vintage months
    const serialToVintageMonth = {};

    // Process vintage results to map serial numbers to vintage months
    vintageResults.forEach(row => {
      const serialNum = row.SERIAL_NUM;
      const vintageMonth = row.YEAR_MONTH;

      // Store the vintage month for this serial number
      serialToVintageMonth[serialNum] = vintageMonth;
    });

    console.log(`Mapped ${Object.keys(serialToVintageMonth).length} serial numbers to vintage months`);

    // Create a map to store defects by month and vintage month
    const defectsByMonthAndVintage = {};

    // Process defect results by month, part number, and serial number
    defectsResults.forEach(row => {
      const month = row.MONTH;
      const serialNum = row.PART_SER;
      const count = row.DEFECT_COUNT;

      // Initialize month entry if it doesn't exist
      if (!defectsByMonthAndVintage[month]) {
        defectsByMonthAndVintage[month] = {};
        totalDefectsByMonth[month] = 0;
      }

      // Get the vintage month for this serial number
      const vintageMonth = serialToVintageMonth[serialNum];

      if (vintageMonth) {
        // Initialize vintage month entry if it doesn't exist
        if (!defectsByMonthAndVintage[month][vintageMonth]) {
          defectsByMonthAndVintage[month][vintageMonth] = 0;
        }

        // Add defect count to the appropriate vintage month
        defectsByMonthAndVintage[month][vintageMonth] += count;

        // Add to total defects for this month
        totalDefectsByMonth[month] += count;
      } else {
        // If no vintage month found for this serial, use a default category
        const defaultVintage = 'Unknown';

        if (!defectsByMonthAndVintage[month][defaultVintage]) {
          defectsByMonthAndVintage[month][defaultVintage] = 0;
        }

        defectsByMonthAndVintage[month][defaultVintage] += count;
        totalDefectsByMonth[month] += count;
      }
    });

    console.log(`Processed defects by month and vintage month based on serial number joins`);

    // Create a map of total volume by month
    const volumeByMonth = {};
    volumeResults.forEach(row => {
      const month = row.YEAR_MONTH;
      if (!volumeByMonth[month]) {
        volumeByMonth[month] = 0;
      }
      volumeByMonth[month] += row.COUNT;
    });

    // Calculate fail rates for each month as percentage (0-100%)
    const failRatesByMonth = {};
    months.forEach(month => {
      const defects = totalDefectsByMonth[month] || 0;
      const volume = volumeByMonth[month] || 0;
      const failRate = volume > 0 ? (defects / volume) * 100 : 0; // Calculate as percentage
      failRatesByMonth[month] = failRate;
    });

    // Create a map of vintage counts by month
    const vintageCountsByMonth = {};

    // Count unique serial numbers by vintage month
    Object.values(serialToVintageMonth).forEach(vintageMonth => {
      if (!vintageCountsByMonth[vintageMonth]) {
        vintageCountsByMonth[vintageMonth] = 0;
      }

      vintageCountsByMonth[vintageMonth]++;
    });

    // Store volume, fail rate, and defect count data
    vintageData.volumeData = volumeByMonth;
    vintageData.failRates = failRatesByMonth;
    vintageData.defectCounts = totalDefectsByMonth;
    vintageData.vintageCounts = vintageCountsByMonth;
    vintageData.defectsByMonthAndVintage = defectsByMonthAndVintage;

    // Create data structure for the stacked bar chart
    months.forEach(month => {
      const totalDefects = totalDefectsByMonth[month] || 0;
      const totalVolume = volumeByMonth[month] || 0;
      const totalFailRate = failRatesByMonth[month] || 0;

      // Check if this month's total fail rate is above target
      const isMonthAboveTarget = totalFailRate > (targetRate * 100); // Convert target to percentage for comparison

      uniqueVintageMonths.forEach(vintageMonth => {
        // Get count of parts with this vintage month
        const vintagePartsCount = vintageCountsByMonth[vintageMonth] || 0;

        // Get actual defect count for this month and vintage month from our combined data
        const defectCount = defectsByMonthAndVintage[month] &&
                           defectsByMonthAndVintage[month][vintageMonth] ?
                           defectsByMonthAndVintage[month][vintageMonth] : 0;

        // Calculate percentage of total volume for this vintage month
        const percentage = totalVolume > 0 ? (vintagePartsCount / totalVolume) * 100 : 0;

        // Calculate vintage fail rate based on actual defect counts
        let vintageFailRate;
        if (vintagePartsCount > 0) {
          // Calculate actual fail rate if we have both defect count and volume
          vintageFailRate = (defectCount / vintagePartsCount) * 100;
        } else {
          // Fall back to proportional distribution if no volume data
          vintageFailRate = totalFailRate * (percentage / 100);
        }

        vintageData.chartData.push({
          group: vintageMonth, // Vintage month as the group
          key: month, // Current month as the key
          value: vintageFailRate, // Use fail rate instead of count (as percentage 0-100%)
          rawCount: vintagePartsCount, // Keep the raw count for reference
          defectCount: defectCount, // Actual defect count from joined query
          totalCount: totalDefects,
          volume: totalVolume,
          percentage: percentage.toFixed(2),
          totalFailRate: totalFailRate,
          targetRate: targetRate,
          isAboveTarget: isMonthAboveTarget,
          isVintageAboveTarget: vintageFailRate > (targetRate * 100) // Check if this vintage month's contribution is above target
        });
      });
    });

    // Store the results in the response
    responseObj.vintageData = vintageData;
    responseObj.partNumbers = partNumbers;
    responseObj.status_res = "success";

    return callback(null, responseObj);
  } catch (err) {
    console.error("Error in getMetisVintageAnalysis:", err);
    responseObj.status_res = "error";
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Helper function to get months between dates
function getMonthsBetweenDates(startDate, endDate) {
  const result = [];

  // Extract year and month from the input strings
  const [startYear, startMonth] = startDate.split('-').map(num => parseInt(num, 10));
  const [endYear, endMonth] = endDate.split('-').map(num => parseInt(num, 10));

  // Generate all months in the range
  let currentYear = startYear;
  let currentMonth = startMonth;

  while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
    // Format as YYYY-MM
    const yearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;
    result.push(yearMonth);

    // Move to the next month
    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }
  }

  return result;
}

// Function to get XFactors for Metis parts grouped by Full Breakout Name
async function getMetisXFactors(values, callback) {
  let responseObj = {
    status_res: null,
    xfactors: {},
    sql_error_msg: null
  };

  try {
    // Load the Excel file to get part numbers grouped by Full Breakout Name
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Check if the required columns exist
    const hasFullBreakoutName = sheet.length > 0 && 'Full Breakout Name' in sheet[0];
    const hasPNColumn = sheet.length > 0 && 'PN' in sheet[0];

    if (!hasFullBreakoutName || !hasPNColumn) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the Excel file:', Object.keys(sheet[0]));
      }
      throw new Error(`Required columns not found in the Excel file. 'Full Breakout Name': ${hasFullBreakoutName}, 'PN': ${hasPNColumn}`);
    }

    // Group part numbers by Full Breakout Name
    const partsByBreakout = {};

    // Get filter parameters
    const owningGroup = values.owningGroup;
    const filterType = values.filterType;
    const owner = values.owner;

    // If filtering by dev_owner or pqe_owner, get the list of breakout groups for that owner
    let breakoutGroupsForOwner = [];
    if ((filterType === 'dev_owner' || filterType === 'pqe_owner') && owner) {
      try {
        // Load the breakout_targets Excel file to get owner information
        const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');

        if (fs.existsSync(targetsFilePath)) {
          const targetsWorkbook = xlsx.readFile(targetsFilePath);

          if (targetsWorkbook.SheetNames.length >= 2) {
            const ownersSheetName = targetsWorkbook.SheetNames[1]; // Second sheet
            const ownersSheet = xlsx.utils.sheet_to_json(targetsWorkbook.Sheets[ownersSheetName]);

            // Filter by the appropriate owner type
            const ownerColumn = filterType === 'dev_owner' ? 'Dev Owner' : 'PQE Owner';
            breakoutGroupsForOwner = ownersSheet
              .filter(item => item[ownerColumn] === owner)
              .map(item => item['Breakout Group'])
              .filter(group => group && group.trim() !== '');

            console.log(`Found ${breakoutGroupsForOwner.length} breakout groups for ${ownerColumn} "${owner}"`);
          }
        }
      } catch (err) {
        console.error(`Error getting breakout groups for owner:`, err);
      }
    }

    sheet.forEach(row => {
      const breakoutName = row['Full Breakout Name'] || 'Unknown';
      const partNumber = row.PN;
      const rowOwningGroup = row['Dan OwningGroup'];

      // Skip if no part number
      if (!partNumber) {
        return;
      }

      // Apply filters based on filter type
      if (filterType === 'dev_owner' || filterType === 'pqe_owner') {
        // Skip if breakout name is not in the list for this owner
        if (breakoutGroupsForOwner.length > 0 && !breakoutGroupsForOwner.includes(breakoutName)) {
          return;
        }
      } else if (owningGroup) {
        // Default filter by owning group
        if (rowOwningGroup !== owningGroup) {
          return;
        }
      }

      if (!partsByBreakout[breakoutName]) {
        partsByBreakout[breakoutName] = [];
      }
      partsByBreakout[breakoutName].push(partNumber);
    });

    // Get the date range from the request - these are the dates selected by the user
    const startDate = values.startDate;
    const endDate = values.endDate;

    // Check if this is a dashboard request (no specific breakout name)
    const isDashboardRequest = !values.breakoutName;

    // Only log date information for dashboard requests
    if (isDashboardRequest) {
      console.log(`\n=== METIS DASHBOARD API REQUEST ===`);
      console.log(`Start Date: ${startDate}`);
      console.log(`End Date: ${endDate}`);

      // Log additional request parameters if available
      if (values.baseStartDate) {
        console.log(`Base Start Date: ${values.baseStartDate}`);
      }
      if (values.exactDateRange) {
        console.log(`Exact Date Range: ${values.exactDateRange}`);
      }
      console.log(`===================================\n`);
    }

    // Validate that we have both dates
    if (!startDate || !endDate) {
      console.error("Missing required date parameters:", { startDate, endDate });
      throw new Error("Start date and end date are required for analysis");
    }

    // Get the base rate calculation period from the request if provided
    // Otherwise, use a reasonable default (1 year back from end date)
    let baseStartDate;
    if (values.baseStartDate) {
      baseStartDate = values.baseStartDate;
    } else {
      // Calculate a default base period (1 year back from end date)
      const endDateObj = new Date(endDate);
      const baseStartDateObj = new Date(endDateObj);
      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1); // Go back one full year

      // Format as YYYY-MM-DD
      baseStartDate = baseStartDateObj.toISOString().split('T')[0];
    }

    // Process each breakout group
    const breakoutResults = {};

    // If a specific breakout name is provided, only process that one
    const breakoutsToProcess = values.breakoutName
      ? (partsByBreakout[values.breakoutName] ? { [values.breakoutName]: partsByBreakout[values.breakoutName] } : {})
      : partsByBreakout;

    // Collect all part numbers from all breakout groups to process
    const allPartNumbers = [];
    const partNumbersByBreakout = {};

    for (const [breakoutName, partNumbers] of Object.entries(breakoutsToProcess)) {
      if (!breakoutName.trim() || partNumbers.length === 0) {
        continue;
      }

      // Store part numbers for this breakout group
      partNumbersByBreakout[breakoutName] = partNumbers;

      // Add part numbers to the consolidated list
      allPartNumbers.push(...partNumbers);
    }

    // Remove duplicates from the consolidated list
    const uniquePartNumbers = [...new Set(allPartNumbers)];

    // Make a single query for all defect counts
    let allDefectCounts = [];
    let allVolumeCounts = [];

    try {
      // Check if this is a dashboard request (no specific breakout name)
      const isDashboardRequest = !values.breakoutName;

      // Set environment variable for dashboard requests to be used by query functions
      process.env.IS_DASHBOARD_REQUEST = isDashboardRequest ? 'true' : 'false';

      // Only log detailed database query information for dashboard requests
      if (isDashboardRequest) {
        console.log(`\n=== DASHBOARD DATABASE QUERIES ===`);
        console.log(`Querying defect data from R0ADB2 for ${uniquePartNumbers.length} part numbers`);
        console.log(`Date range for queries: ${baseStartDate} to ${endDate}`);
      }

      // Get all defect data with a single query to R0ADB2
      allDefectCounts = await getAllDefectCounts(uniquePartNumbers, baseStartDate, endDate);

      if (isDashboardRequest) {
        console.log(`Retrieved ${allDefectCounts.length} defect records from R0ADB2`);
        console.log(`Querying volume data from QRYPROD for ${uniquePartNumbers.length} part numbers`);
      }

      // Get all volume data with a single query to QRYPROD
      allVolumeCounts = await getAllVolumeCounts(uniquePartNumbers, baseStartDate, endDate);

      if (isDashboardRequest) {
        console.log(`Retrieved ${allVolumeCounts.length} volume records from QRYPROD`);
        console.log(`=======================\n`);
      }

      // Reset environment variable
      process.env.IS_DASHBOARD_REQUEST = 'false';
    } catch (err) {
      console.error(`Error getting consolidated data:`, err);
      console.error(`Query parameters: startDate=${baseStartDate}, endDate=${endDate}, partNumbers=${uniquePartNumbers.length}`);
      responseObj.sql_error_msg = `Error getting consolidated data: ${err.message}`;
      responseObj.status_res = "error";
      return callback(err, responseObj);
    }

    // Process each breakout group with the consolidated data
    for (const [breakoutName, partNumbers] of Object.entries(partNumbersByBreakout)) {
      // Extract start and end month in YYYY-MM format for comparison
      let startYearMonth, endYearMonth;

      if (startDate.length === 7) {
        // Already in YYYY-MM format
        startYearMonth = startDate;
      } else if (startDate.length >= 10) {
        // Format is YYYY-MM-DD or longer, extract YYYY-MM
        startYearMonth = startDate.substring(0, 7);
      } else {
        console.error(`Invalid start date format: ${startDate}`);
        throw new Error(`Invalid start date format: ${startDate}`);
      }

      if (endDate.length === 7) {
        // Already in YYYY-MM format
        endYearMonth = endDate;
      } else if (endDate.length >= 10) {
        // Format is YYYY-MM-DD or longer, extract YYYY-MM
        endYearMonth = endDate.substring(0, 7);
      } else {
        console.error(`Invalid end date format: ${endDate}`);
        throw new Error(`Invalid end date format: ${endDate}`);
      }

      // Filter the consolidated data for this breakout group using string comparison
      const defectCounts = allDefectCounts.filter(row =>
        partNumbers.includes(row.PART_NUM) &&
        row.YEAR_MONTH >= startYearMonth &&
        row.YEAR_MONTH <= endYearMonth
      );

      const volumeCounts = allVolumeCounts.filter(row =>
        partNumbers.includes(row.PART_NUM) &&
        row.YEAR_MONTH >= startYearMonth &&
        row.YEAR_MONTH <= endYearMonth
      );

      const baseDefectCounts = allDefectCounts.filter(row =>
        partNumbers.includes(row.PART_NUM)
      );

      const baseVolumeCounts = allVolumeCounts.filter(row =>
        partNumbers.includes(row.PART_NUM)
      );

      // Calculate monthly percentages and X-factors
      const monthlyData = calculateMonthlyData(defectCounts, volumeCounts, startDate, endDate, values);
      const baseRate = calculateBaseRate(baseDefectCounts, baseVolumeCounts);
      const xFactors = calculateXFactors(monthlyData, baseRate, breakoutName);

      // Get the target rate for this breakout group
      const targetRate = getTargetRate(breakoutName);

      breakoutResults[breakoutName] = {
        monthlyData,
        baseRate,
        targetRate,
        xFactors
      };
    }

    // Only log detailed response summary for dashboard requests
    if (!values.breakoutName) {
      console.log(`\n=== DASHBOARD API RESPONSE SUMMARY ===`);
      console.log(`Total breakout groups processed: ${Object.keys(breakoutResults).length}`);

      // Log ALL breakout groups and their periods for dashboard requests
      console.log(`\n=== DETAILED DASHBOARD DATA FOR ALL BREAKOUT GROUPS ===`);

      // Sort breakout groups alphabetically for consistent output
      const sortedBreakoutNames = Object.keys(breakoutResults).sort();

      sortedBreakoutNames.forEach(breakoutName => {
        const xFactors = breakoutResults[breakoutName].xFactors;
        const periodCount = Object.keys(xFactors).length;
        const targetRate = breakoutResults[breakoutName].targetRate;

        console.log(`\n--- BREAKOUT GROUP: ${breakoutName} ---`);
        console.log(`Target Rate: ${targetRate.toFixed(6)}`);
        console.log(`Total Periods: ${periodCount}`);

        // Get part numbers for this breakout group
        const partNumbers = partNumbersByBreakout[breakoutName] || [];
        console.log(`Part Numbers (${partNumbers.length}): ${partNumbers.slice(0, 5).join(', ')}${partNumbers.length > 5 ? '...' : ''}`);

        // Log ALL periods for this breakout in chronological order
        if (periodCount > 0) {
          console.log(`\nPeriod Data (chronological order):`);
          console.log('Period\t\tXFactor\t\tDefects\tVolume');
          console.log('------------------------------------------------');

          // Sort periods chronologically
          const sortedPeriods = Object.keys(xFactors).sort();
          let totalDefects = 0;
          let totalVolume = 0;

          sortedPeriods.forEach(period => {
            const data = xFactors[period];
            totalDefects += data.defects;
            totalVolume += data.volume;

            // Format the output with proper spacing
            const xFactorStr = data.xFactor.toFixed(2).padStart(6);
            const defectsStr = data.defects.toString().padStart(7);
            const volumeStr = data.volume.toString().padStart(7);

            console.log(`${period}\t${xFactorStr}\t\t${defectsStr}\t${volumeStr}`);
          });

          console.log('------------------------------------------------');
          console.log(`TOTAL\t\t${(totalDefects / (totalVolume || 1)).toFixed(2).padStart(6)}\t\t${totalDefects.toString().padStart(7)}\t${totalVolume.toString().padStart(7)}`);
        } else {
          console.log(`No period data available for this breakout group.`);
        }
      });

      console.log(`\n===========================================\n`);
    }

    // If displayStartDate was provided in the request, include it in the response
    if (values.displayStartDate) {
      responseObj.displayStartDate = values.displayStartDate;
      console.log(`Including display start date in response: ${values.displayStartDate}`);
    }

    responseObj.status_res = "success";
    responseObj.xfactors = breakoutResults;
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error calculating XFactors:", err);
    responseObj.status_res = "error";
    responseObj.sql_error_msg = err.message;
    return callback(err, responseObj);
  }
}

// Helper function to get all defect counts from DB2 with a single query
async function getAllDefectCounts(partNumbers, startDate, endDate) {
  const configFail = configSwitch.config('R0ADB2');
  let connStringFail = "DRIVER={DB2};"
    + "DATABASE=" + 'R0ADB2' + ";"
    + "UID=" + configFail.database_user + ";"
    + "PWD=" + configFail.database_password + ";"
    + "HOSTNAME=" + configFail.database_host + ";"
    + "PORT=" + configFail.database_port;

  let connFail;
  try {
    connFail = await ibmdb.open(connStringFail);

    const pnPlaceholders = partNumbers.map(() => '?').join(', ');
    const failQuery = `SELECT
      PART_NUM,
      VARCHAR_FORMAT(INCIDENT_DATE, 'YYYY-MM') AS YEAR_MONTH,
      COUNT(*) AS COUNT
    FROM
      QODS.GEM_DEFECT
    WHERE
      PART_NUM IN (${pnPlaceholders})
      AND INCIDENT_DATE >= ?
      AND INCIDENT_DATE < ?
      AND PROCESS = 'FULL'
    GROUP BY
      PART_NUM,
      VARCHAR_FORMAT(INCIDENT_DATE, 'YYYY-MM')
    ORDER BY
      YEAR_MONTH DESC`;

    // Check if this is a dashboard request (passed from the calling function)
    const isDashboardRequest = process.env.IS_DASHBOARD_REQUEST === 'true';

    // Standardize date format for queries
    let queryStartDate = startDate;
    let queryEndDate = endDate;

    // If the date is in YYYY-MM format, convert it to YYYY-MM-DD for the database query
    if (startDate.length === 7) {
      queryStartDate = `${startDate}-01`;
    }

    // For end date in YYYY-MM format, set it to the last day of the month
    if (endDate.length === 7) {
      // Get the last day of the month
      const [year, month] = endDate.split('-');
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
      queryEndDate = `${endDate}-${lastDay}`;
    }

    // Only log detailed query information for dashboard requests
    if (isDashboardRequest) {
      console.log(`\n--- R0ADB2 Query Parameters (Dashboard) ---`);
      console.log(`Original Start Date: ${startDate}`);
      console.log(`Original End Date: ${endDate}`);
      console.log(`Query Start Date: ${queryStartDate}`);
      console.log(`Query End Date: ${queryEndDate}`);
      console.log(`Part Numbers Count: ${partNumbers.length}`);

      // For dashboard requests, show ALL part numbers
      if (partNumbers.length > 0) {
        console.log(`\nFull Part Number List for R0ADB2 Query:`);
        // Group part numbers into chunks of 10 for better readability
        for (let i = 0; i < partNumbers.length; i += 10) {
          const chunk = partNumbers.slice(i, i + 10);
          console.log(`  ${chunk.join(', ')}`);
        }

        // Show the actual SQL query with placeholders
        console.log(`\nSQL Query Template:`);
        console.log(failQuery);

        console.log(`\nQuery Parameters:`);
        console.log(`1. Part Numbers: [${partNumbers.length} items]`);
        console.log(`2. Start Date: ${queryStartDate}`);
        console.log(`3. End Date: ${queryEndDate}`);
      }
    }

    const failRows = await new Promise((resolve, reject) => {
      if (isDashboardRequest) {
        console.log(`Executing R0ADB2 query for defect counts...`);
      }

      const startTime = Date.now();

      connFail.query(failQuery, [...partNumbers, queryStartDate, queryEndDate], function (err, rows) {
        const duration = Date.now() - startTime;

        if (err) {
          console.error(`DB2 query error (${duration}ms):`, err);
          return reject(err);
        }

        if (isDashboardRequest) {
          console.log(`R0ADB2 query completed in ${duration}ms, returned ${rows.length} rows`);

          // For dashboard requests, show a summary of the results
          if (rows.length > 0) {
            console.log(`\nR0ADB2 Query Results Summary:`);

            // Group results by YEAR_MONTH
            const resultsByMonth = {};
            rows.forEach(row => {
              const month = row.YEAR_MONTH;
              if (!resultsByMonth[month]) {
                resultsByMonth[month] = { count: 0, partNumbers: new Set() };
              }
              resultsByMonth[month].count += row.COUNT;
              resultsByMonth[month].partNumbers.add(row.PART_NUM);
            });

            // Display summary by month
            console.log(`Month\t\tDefects\tUnique PNs`);
            console.log(`---------------------------------`);
            Object.keys(resultsByMonth).sort().forEach(month => {
              const data = resultsByMonth[month];
              console.log(`${month}\t${data.count.toString().padStart(7)}\t${data.partNumbers.size.toString().padStart(9)}`);
            });

            // Show a sample of the actual rows
            console.log(`\nSample Results (first 5 rows):`);
            rows.slice(0, 5).forEach((row, index) => {
              console.log(`${index + 1}. PART_NUM: ${row.PART_NUM}, YEAR_MONTH: ${row.YEAR_MONTH}, COUNT: ${row.COUNT}`);
            });
            if (rows.length > 5) {
              console.log(`... and ${rows.length - 5} more rows`);
            }
          }
        }

        resolve(rows);
      });
    });

    return failRows;
  } catch (err) {
    console.error("Error in getAllDefectCounts:", err);
    throw err;
  } finally {
    if (connFail) {
      try {
        await connFail.close();
      } catch (closeErr) {
        console.error("Error closing DB2 connection:", closeErr);
      }
    }
  }
}

// Helper function to get all volume counts from DB2 with a single query
async function getAllVolumeCounts(partNumbers, startDate, endDate) {
  const configVol = configSwitch.config('QRYPROD');
  let connStringVol = "DRIVER={DB2};"
    + "DATABASE=" + 'QRYPROD' + ";"
    + "UID=" + configVol.database_user + ";"
    + "PWD=" + configVol.database_password + ";"
    + "HOSTNAME=" + configVol.database_host + ";"
    + "PORT=" + configVol.database_port;

  let connVol;
  try {
    connVol = await ibmdb.open(connStringVol);

    const pnPlaceholders = partNumbers.map(() => '?').join(', ');
    const volQuery = `SELECT
      Q2QDPN AS PART_NUM,
      COUNT(*) AS COUNT,
      VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM') AS YEAR_MONTH
    FROM (
      SELECT
        Q2QDPN,
        Q2QDSQ,
        MIN(Q2OUTD) AS Q2OUTD,
        MIN(Q2OUTT) AS Q2OUTT
      FROM
        MFS2PCOMN.FCSPQD20
      WHERE
        Q2QDPN IN (${pnPlaceholders})
        AND Q2DEAC != 'Y'
        AND Q2FGID = 'PROCFULT'
      GROUP BY
        Q2QDPN, Q2QDSQ
    ) AS Q1
    WHERE
      Q2OUTD > ?
      AND Q2OUTD < ?
    GROUP BY
      Q2QDPN,
      VARCHAR_FORMAT(Q2OUTD, 'YYYY-MM')
    ORDER BY
      YEAR_MONTH`;

    // Check if this is a dashboard request (passed from the calling function)
    const isDashboardRequest = process.env.IS_DASHBOARD_REQUEST === 'true';

    // Standardize date format for queries
    let queryStartDate = startDate;
    let queryEndDate = endDate;

    // If the date is in YYYY-MM format, convert it to YYYY-MM-DD for the database query
    if (startDate.length === 7) {
      queryStartDate = `${startDate}-01`;
    }

    // For end date in YYYY-MM format, set it to the last day of the month
    if (endDate.length === 7) {
      // Get the last day of the month
      const [year, month] = endDate.split('-');
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
      queryEndDate = `${endDate}-${lastDay}`;
    }

    // Only log detailed query information for dashboard requests
    if (isDashboardRequest) {
      console.log(`\n--- QRYPROD Query Parameters (Dashboard) ---`);
      console.log(`Original Start Date: ${startDate}`);
      console.log(`Original End Date: ${endDate}`);
      console.log(`Query Start Date: ${queryStartDate}`);
      console.log(`Query End Date: ${queryEndDate}`);
      console.log(`Part Numbers Count: ${partNumbers.length}`);

      // For dashboard requests, show ALL part numbers
      if (partNumbers.length > 0) {
        console.log(`\nFull Part Number List for QRYPROD Query:`);
        // Group part numbers into chunks of 10 for better readability
        for (let i = 0; i < partNumbers.length; i += 10) {
          const chunk = partNumbers.slice(i, i + 10);
          console.log(`  ${chunk.join(', ')}`);
        }

        // Show the actual SQL query with placeholders
        console.log(`\nSQL Query Template:`);
        console.log(volQuery);

        console.log(`\nQuery Parameters:`);
        console.log(`1. Part Numbers: [${partNumbers.length} items]`);
        console.log(`2. Start Date: ${queryStartDate}`);
        console.log(`3. End Date: ${queryEndDate}`);
      }
    }

    const volRows = await new Promise((resolve, reject) => {
      if (isDashboardRequest) {
        console.log(`Executing QRYPROD query for volume counts...`);
      }

      const startTime = Date.now();

      connVol.query(volQuery, [...partNumbers, queryStartDate, queryEndDate], function (err, rows) {
        const duration = Date.now() - startTime;

        if (err) {
          console.error(`DB2 query error (${duration}ms):`, err);
          return reject(err);
        }

        if (isDashboardRequest) {
          console.log(`QRYPROD query completed in ${duration}ms, returned ${rows.length} rows`);

          // For dashboard requests, show a summary of the results
          if (rows.length > 0) {
            console.log(`\nQRYPROD Query Results Summary:`);

            // Group results by YEAR_MONTH
            const resultsByMonth = {};
            rows.forEach(row => {
              const month = row.YEAR_MONTH;
              if (!resultsByMonth[month]) {
                resultsByMonth[month] = { count: 0, partNumbers: new Set() };
              }
              resultsByMonth[month].count += row.COUNT;
              resultsByMonth[month].partNumbers.add(row.PART_NUM);
            });

            // Display summary by month
            console.log(`Month\t\tVolume\tUnique PNs`);
            console.log(`---------------------------------`);
            Object.keys(resultsByMonth).sort().forEach(month => {
              const data = resultsByMonth[month];
              console.log(`${month}\t${data.count.toString().padStart(7)}\t${data.partNumbers.size.toString().padStart(9)}`);
            });

            // Show a sample of the actual rows
            console.log(`\nSample Results (first 5 rows):`);
            rows.slice(0, 5).forEach((row, index) => {
              console.log(`${index + 1}. PART_NUM: ${row.PART_NUM}, YEAR_MONTH: ${row.YEAR_MONTH}, COUNT: ${row.COUNT}`);
            });
            if (rows.length > 5) {
              console.log(`... and ${rows.length - 5} more rows`);
            }
          }
        }

        resolve(rows);
      });
    });

    return volRows;
  } catch (err) {
    console.error("Error in getAllVolumeCounts:", err);
    throw err;
  } finally {
    if (connVol) {
      try {
        await connVol.close();
      } catch (closeErr) {
        console.error("Error closing DB2 connection:", closeErr);
      }
    }
  }
}

// Helper functions for calculating data

// Helper function to calculate monthly defect rates
function calculateMonthlyData(defectCounts, volumeCounts, startDate, endDate, requestValues) {
  const monthlyData = {};

  // First, generate entries for all months in the date range
  if (startDate && endDate) {
    // Check if this is a dashboard request with useMonthFormat flag
    const isDashboardRequest = process.env.IS_DASHBOARD_REQUEST === 'true';
    const useMonthFormat = requestValues && requestValues.useMonthFormat;

    if (isDashboardRequest) {
      console.log(`\n=== DASHBOARD MONTHLY DATA GENERATION ===`);
      console.log(`Input Start Date: ${startDate}`);
      console.log(`Input End Date: ${endDate}`);
      console.log(`Using Month Format: ${useMonthFormat ? 'Yes' : 'No'}`);
    }

    // Extract the year and month directly from the input strings
    let startYear, startMonth, endYear, endMonth;

    // Parse start date
    if (startDate.length === 7) {
      // Format is YYYY-MM
      [startYear, startMonth] = startDate.split('-');
    } else if (startDate.length >= 10) {
      // Format is YYYY-MM-DD or longer
      [startYear, startMonth] = startDate.substring(0, 7).split('-');
    } else {
      console.error(`Invalid start date format: ${startDate}`);
      return monthlyData;
    }

    // Parse end date
    if (endDate.length === 7) {
      // Format is YYYY-MM
      [endYear, endMonth] = endDate.split('-');
    } else if (endDate.length >= 10) {
      // Format is YYYY-MM-DD or longer
      [endYear, endMonth] = endDate.substring(0, 7).split('-');
    } else {
      console.error(`Invalid end date format: ${endDate}`);
      return monthlyData;
    }

    // Convert to numbers
    startYear = parseInt(startYear, 10);
    startMonth = parseInt(startMonth, 10);
    endYear = parseInt(endYear, 10);
    endMonth = parseInt(endMonth, 10);

    if (isDashboardRequest) {
      console.log(`Extracted start year: ${startYear}, month: ${startMonth}`);
      console.log(`Extracted end year: ${endYear}, month: ${endMonth}`);
    }

    // Validate the date range
    if (startYear > endYear || (startYear === endYear && startMonth > endMonth)) {
      console.error(`Invalid date range: start date is after end date`);
      return monthlyData;
    }

    // Generate all months in the range
    let currentYear = startYear;
    let currentMonth = startMonth;

    if (isDashboardRequest) {
      console.log(`Generating monthly entries from ${startYear}-${startMonth} to ${endYear}-${endMonth}`);
    }

    let monthCount = 0;
    while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
      // Format as YYYY-MM
      const yearMonth = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;
      monthCount++;

      if (!monthlyData[yearMonth]) {
        monthlyData[yearMonth] = { defects: 0, volume: 0 };
        if (isDashboardRequest) {
          console.log(`Added month: ${yearMonth}`);
        }
      }

      // Move to the next month
      currentMonth++;
      if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
      }
    }

    if (isDashboardRequest) {
      console.log(`Generated ${monthCount} months in the date range`);
      console.log(`Months in data: ${Object.keys(monthlyData).join(', ')}`);
      console.log(`===================================\n`);
    }
  }

  // Process defect counts by month
  defectCounts.forEach(defect => {
    // We only need the year-month for grouping, not the part number
    const yearMonth = defect.YEAR_MONTH;

    if (!monthlyData[yearMonth]) {
      monthlyData[yearMonth] = { defects: 0, volume: 0 };
    }

    monthlyData[yearMonth].defects += defect.COUNT;
  });

  // Process volume counts by month
  volumeCounts.forEach(volume => {
    // We only need the year-month for grouping, not the part number
    const yearMonth = volume.YEAR_MONTH;

    if (!monthlyData[yearMonth]) {
      monthlyData[yearMonth] = { defects: 0, volume: 0 };
    }

    monthlyData[yearMonth].volume += volume.COUNT;
  });

  // Calculate defect rates
  Object.keys(monthlyData).forEach(yearMonth => {
    const data = monthlyData[yearMonth];
    data.defectRate = data.volume > 0 ? (data.defects / data.volume) : 0;
  });

  // Return the monthly data without verbose logging
  return monthlyData;
}

// Helper function to calculate base defect rate (YTD)
function calculateBaseRate(baseDefectCounts, baseVolumeCounts) {
  let totalDefects = 0;
  let totalVolume = 0;

  // Sum up all defects
  baseDefectCounts.forEach(defect => {
    totalDefects += defect.COUNT;
  });

  // Sum up all volumes
  baseVolumeCounts.forEach(volume => {
    totalVolume += volume.COUNT;
  });

  // Calculate base defect rate
  return totalVolume > 0 ? (totalDefects / totalVolume) : 0;
}

// Helper function to get target rate for a breakout group
function getTargetRate(breakoutName) {
  try {
    // Load the target rates from the Excel file
    const filePath = path.join(__dirname, '../excel/breakout_targets.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`Target rates file not found at path: ${filePath}`);
      return 0.001; // Default target rate if file not found
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Find the target rate for the specified breakout group
    const targetRow = sheet.find(row => row['Breakout Group'] === breakoutName);

    if (targetRow && targetRow['Target Rate']) {
      console.log(`Found target rate for ${breakoutName}: ${targetRow['Target Rate']}`);
      return targetRow['Target Rate'];
    } else {
      console.warn(`No target rate found for ${breakoutName}, using default`);
      return 0.001; // Default target rate if not found
    }
  } catch (err) {
    console.error(`Error getting target rate for ${breakoutName}:`, err);
    return 0.001; // Default target rate on error
  }
}

// Helper function to calculate X-factors
function calculateXFactors(monthlyData, _, breakoutName) { // Using _ to indicate unused parameter
  const xFactors = {};

  // Get the target rate for this breakout group
  let targetRate = getTargetRate(breakoutName);

  // Avoid division by zero
  if (targetRate === 0) targetRate = 0.0001;

  console.log(`Calculating X-factors for ${breakoutName} using target rate: ${targetRate}`);

  // Calculate X-factor for each month
  Object.keys(monthlyData).forEach(yearMonth => {
    const data = monthlyData[yearMonth];
    xFactors[yearMonth] = {
      defectRate: data.defectRate,
      targetRate: targetRate,
      xFactor: data.defectRate / targetRate, // Use target rate instead of base rate
      defects: data.defects,
      volume: data.volume
    };
  });

  return xFactors;
}

// Consolidated function to get all data for a specific breakout group in a single request
async function getMetisBreakoutAnalysis(values, callback) {
  let responseObj = {
    breakoutName: '',
    partNumbers: [],
    xFactorData: {},
    status_res: null
  };

  try {
    // Validate required parameters
    if (!values.breakoutName) {
      throw new Error("Breakout name is required");
    }
    if (!values.startDate || !values.endDate) {
      throw new Error("Start date and end date are required");
    }

    const breakoutName = values.breakoutName;
    const startDate = values.startDate;
    const endDate = values.endDate;

    console.log(`Consolidated analysis for breakout: ${breakoutName} from ${startDate} to ${endDate}`);
    responseObj.breakoutName = breakoutName;

    // Load the Excel file to get part numbers for this breakout group
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    console.log("Reading Excel file from:", filePath);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found at path: ${filePath}`);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    console.log(`Successfully read Excel file. Found ${sheet.length} rows.`);

    // Check if the required columns exist
    const hasFullBreakoutName = sheet.length > 0 && 'Full Breakout Name' in sheet[0];
    const hasPNColumn = sheet.length > 0 && 'PN' in sheet[0];

    if (!hasFullBreakoutName || !hasPNColumn) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.log('Available columns in the Excel file:', Object.keys(sheet[0]));
      }
      throw new Error("Required columns not found in the Excel file");
    }

    // Filter rows by the specified breakout name
    const filteredRows = sheet.filter(row => row['Full Breakout Name'] === breakoutName);
    console.log(`Found ${filteredRows.length} rows for breakout: ${breakoutName}`);

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for breakout: ${breakoutName}`);

    // Log a sample of the part numbers
    if (partNumbers.length > 0) {
      const samplePNs = partNumbers.slice(0, Math.min(5, partNumbers.length));
      console.log(`Sample PNs for ${breakoutName}: ${samplePNs.join(', ')}${partNumbers.length > 5 ? '...' : ''}`);
    }

    // Store part numbers in the response
    responseObj.partNumbers = partNumbers;

    // If no part numbers found, return early
    if (partNumbers.length === 0) {
      responseObj.status_res = "success";
      responseObj.message = `No part numbers found for breakout: ${breakoutName}`;
      return callback(null, responseObj);
    }

    // Get the base rate calculation period from the request if provided
    // Otherwise, use a reasonable default (1 year back from end date)
    let baseStartDate;
    if (values.baseStartDate) {
      baseStartDate = values.baseStartDate;
      console.log(`Using user-provided base rate start date: ${baseStartDate}`);
    } else {
      // Calculate a default base period (1 year back from end date)
      const endDateObj = new Date(endDate);
      const baseStartDateObj = new Date(endDateObj);
      baseStartDateObj.setFullYear(baseStartDateObj.getFullYear() - 1); // Go back one full year

      // Format as YYYY-MM-DD
      baseStartDate = baseStartDateObj.toISOString().split('T')[0];
      console.log(`Using default base rate start date: ${baseStartDate}`);
    }

    // Make a single consolidated query for all data needed
    let defectCounts = [];
    let volumeCounts = [];
    let baseDefectCounts = [];
    let baseVolumeCounts = [];

    try {
      // Get all data with a single query per database
      console.log(`Getting all data for ${breakoutName} with a single query per database`);

      // Get all defect data with a single query to R0ADB2
      const allDefectCounts = await getAllDefectCounts(partNumbers, baseStartDate, endDate);

      // Using standardized date handling for all API methods

      // Extract start and end month in YYYY-MM format for comparison
      let startYearMonth, endYearMonth;

      if (startDate.length === 7) {
        // Already in YYYY-MM format
        startYearMonth = startDate;
      } else if (startDate.length >= 10) {
        // Format is YYYY-MM-DD or longer, extract YYYY-MM
        startYearMonth = startDate.substring(0, 7);
      } else {
        console.error(`Invalid start date format: ${startDate}`);
        throw new Error(`Invalid start date format: ${startDate}`);
      }

      if (endDate.length === 7) {
        // Already in YYYY-MM format
        endYearMonth = endDate;
      } else if (endDate.length >= 10) {
        // Format is YYYY-MM-DD or longer, extract YYYY-MM
        endYearMonth = endDate.substring(0, 7);
      } else {
        console.error(`Invalid end date format: ${endDate}`);
        throw new Error(`Invalid end date format: ${endDate}`);
      }

      console.log(`Using month range for filtering: ${startYearMonth} to ${endYearMonth}`);

      // Split the results into regular period and base period using string comparison
      defectCounts = allDefectCounts.filter(row => {
        return row.YEAR_MONTH >= startYearMonth && row.YEAR_MONTH <= endYearMonth;
      });

      baseDefectCounts = allDefectCounts;

      // Get all volume data with a single query to QRYPROD
      const allVolumeCounts = await getAllVolumeCounts(partNumbers, baseStartDate, endDate);

      // Split the results into regular period and base period using string comparison
      volumeCounts = allVolumeCounts.filter(row => {
        return row.YEAR_MONTH >= startYearMonth && row.YEAR_MONTH <= endYearMonth;
      });

      baseVolumeCounts = allVolumeCounts;

      console.log(`Successfully retrieved all data for ${breakoutName} with consolidated queries:`);
      console.log(`- Defect counts: ${defectCounts.length} records`);
      console.log(`- Volume counts: ${volumeCounts.length} records`);
      console.log(`- Base defect counts: ${baseDefectCounts.length} records`);
      console.log(`- Base volume counts: ${baseVolumeCounts.length} records`);
    } catch (err) {
      console.error(`Error getting data for breakout ${breakoutName}:`, err);
      responseObj.sql_error_msg = `Error getting data for ${breakoutName}: ${err.message}`;
      responseObj.status_res = "error";
      return callback(err, responseObj);
    }

    // Calculate monthly percentages and X-factors
    const monthlyData = calculateMonthlyData(defectCounts, volumeCounts, startDate, endDate, values);
    const baseRate = calculateBaseRate(baseDefectCounts, baseVolumeCounts);
    const xFactors = calculateXFactors(monthlyData, baseRate, breakoutName);

    console.log(`Calculated XFactors for ${breakoutName}: ${Object.keys(xFactors).length} periods, using target rate from Excel file`);

    // Log detailed information about part numbers, defects, and volumes
    console.log(`\n=== DETAILED DATA FOR ${breakoutName.toUpperCase()} ===`);
    console.log(`Part Numbers (${partNumbers.length} total):`);

    // Group defects and volumes by part number
    const defectsByPN = {};
    const volumesByPN = {};

    // Initialize counters for each part number
    partNumbers.forEach(pn => {
      defectsByPN[pn] = 0;
      volumesByPN[pn] = 0;
    });

    // Count defects by part number
    baseDefectCounts.forEach(defect => {
      const pn = defect.PART_NUM;
      if (defectsByPN[pn] !== undefined) {
        defectsByPN[pn] += defect.COUNT;
      }
    });

    // Count volumes by part number
    baseVolumeCounts.forEach(volume => {
      const pn = volume.PART_NUM;
      if (volumesByPN[pn] !== undefined) {
        volumesByPN[pn] += volume.COUNT;
      }
    });

    // Display part numbers with their defect and volume counts
    partNumbers.forEach(pn => {
      console.log(`  ${pn}: ${defectsByPN[pn]} defects, ${volumesByPN[pn]} volume`);
    });

    // Log monthly summary
    console.log(`\nMonthly Summary:`);
    console.log('Month\t\tDefects\tVolume\tX-Factor');
    console.log('------------------------------------------------');

    // Sort periods chronologically
    const sortedPeriods = Object.keys(xFactors).sort();
    let totalDefects = 0;
    let totalVolume = 0;

    sortedPeriods.forEach(period => {
      const data = xFactors[period];
      totalDefects += data.defects;
      totalVolume += data.volume;
      console.log(`${period}\t${data.defects}\t${data.volume}\t${data.xFactor.toFixed(2)}`);
    });

    console.log('------------------------------------------------');
    console.log(`TOTAL\t\t${totalDefects}\t${totalVolume}\t${(totalDefects / (totalVolume || 1)).toFixed(2)}`);
    console.log('================================================\n');

    // Store the results in the response
    responseObj.xFactorData = {
      monthlyData,
      baseRate,
      xFactors
    };

    responseObj.status_res = "success";
    return callback(null, responseObj);
  } catch (err) {
    console.error("Error in getMetisBreakoutAnalysis:", err);
    responseObj.status_res = "error";
    responseObj.error_msg = err.message;
    return callback(err, responseObj);
  }
}
