# Vue 3 Migration Guide

This document provides guidance on completing the migration from Vue 2 to Vue 3 for the STATIT 2.0 application.

## Changes Already Made

1. Updated package.json with Vue 3 compatible dependencies:
   - Vue 2.7.16 → Vue 3.4.21
   - Vue Router 3.6.5 → Vue Router 4.3.0
   - Vuex 3.6.2 → Vuex 4.1.0
   - @carbon/vue 2.45.1 → @carbon/vue 3.0.25
   - vee-validate 3.4.15 → vee-validate 4.12.6

2. Updated main.js to use Vue 3's createApp pattern instead of the Vue constructor.

3. Updated Vuex store to use createStore from Vuex 4.

4. Updated Vue Router to use createRouter and createWebHistory from Vue Router 4.

5. Updated several components to use Vue 3 compatible syntax:
   - LineChart.vue
   - BarChart.vue
   - HeatMap.vue

## Remaining Changes

### 1. Update Carbon Components Usage

The Carbon Components for Vue have been updated from `@carbon/vue` to `@carbon/vue` v3. This requires updating all component imports and usage:

#### Before:
```javascript
import { CvButton } from '@carbon/vue';
```

#### After:
```javascript
import { CvButton } from '@carbon/vue';
```

Note that the import path is the same, but the internal implementation is different.

### 2. Update Component Registration

In Vue 3, components must be explicitly registered in the components option:

#### Before:
```javascript
import Vue from 'vue';
import { CvButton } from '@carbon/vue';
Vue.component('cv-button', CvButton);
```

#### After:
```javascript
import { CvButton } from '@carbon/vue';

export default {
  components: {
    CvButton
  }
}
```

### 3. Update Lifecycle Hooks

Some lifecycle hooks have been renamed in Vue 3:

- `beforeDestroy` → `beforeUnmount`
- `destroyed` → `unmounted`

### 4. Update Template Syntax

#### Slots

Update slot syntax:

#### Before:
```html
<template slot="title">Title</template>
```

#### After:
```html
<template v-slot:title>Title</template>
<!-- or shorthand -->
<template #title>Title</template>
```

#### v-model

The `.sync` modifier has been replaced with `v-model` with arguments:

#### Before:
```html
<component :value.sync="myValue"></component>
```

#### After:
```html
<component v-model:value="myValue"></component>
```

### 5. Update Carbon Charts Usage

Carbon Charts for Vue 3 has a different usage pattern:

#### Before:
```javascript
import Vue from 'vue';
import chartsVue from '@carbon/charts-vue';
Vue.use(chartsVue);
```

#### After:
```javascript
// Direct import from @carbon/charts
import { LineChart, BarChart } from '@carbon/charts';

// Create chart in mounted/updated lifecycle hooks
mounted() {
  this.chart = new LineChart(this.$refs.chartContainer, {
    data: this.data,
    options: this.options
  });
}
```

### 6. Update vee-validate Usage

vee-validate 4 (for Vue 3) has a different API:

#### Before:
```javascript
import { ValidationProvider, ValidationObserver } from 'vee-validate';

export default {
  components: {
    ValidationProvider,
    ValidationObserver
  }
}
```

#### After:
```javascript
import { Form, Field, ErrorMessage } from 'vee-validate';

export default {
  components: {
    VeeForm: Form,
    VeeField: Field,
    VeeError: ErrorMessage
  }
}
```

### 7. Update Event Handling

In Vue 3, the event system has changed:

#### Before:
```javascript
this.$on('event', handler);
this.$emit('event', payload);
```

#### After:
For global events, use a separate event bus or state management.
Component events still use `this.$emit('event', payload)`.

### 8. Update Refs Usage

In Vue 3, template refs are not automatically created. They need to be accessed after the component is mounted:

```javascript
mounted() {
  // Access this.$refs.myRef here
}
```

## Testing After Migration

After completing the migration, thoroughly test the application:

1. Test all routes and navigation
2. Test all forms and validation
3. Test all charts and data visualization
4. Test all API calls and data fetching
5. Test all user interactions (clicks, form submissions, etc.)

## Resources

- [Vue 3 Migration Guide (Official)](https://v3-migration.vuejs.org/)
- [Carbon Vue Documentation](https://github.com/carbon-design-system/carbon-components-vue)
- [Carbon Charts Documentation](https://github.com/carbon-design-system/carbon-charts/tree/master/packages/core)
- [Vue Router 4 Documentation](https://router.vuejs.org/)
- [Vuex 4 Documentation](https://vuex.vuejs.org/)
- [vee-validate 4 Documentation](https://vee-validate.logaretm.com/v4/)
