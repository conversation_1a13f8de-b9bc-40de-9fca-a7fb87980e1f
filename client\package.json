{"name": "client", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@carbon/charts": "^0.54.1", "@carbon/charts-vue": "^0.54.1", "@carbon/icons-vue": "^10.99.1", "@carbon/styles": "^1.62.0", "@carbon/themes": "^10.55.5", "@carbon/vue": "^2.40.0", "@fontsource/ibm-plex-mono": "^5.0.13", "@fontsource/ibm-plex-sans": "^5.0.20", "@fontsource/ibm-plex-serif": "^5.0.13", "@vue/cli-plugin-eslint": "^4.5.15", "apexcharts": "^3.51.0", "autoprefixer": "^10.4.20", "carbon-components": "^10.48.0", "chart.js": "^2.9.4", "core-js": "^3.38.0", "cors": "^2.8.5", "d3": "^7.9.0", "dotenv": "^16.0.3", "fs": "0.0.1-security", "highcharts": "^11.4.8", "highcharts-vue": "^2.0.1", "http-serve": "^1.0.1", "http-server": "^14.1.1", "ibm-watson": "^10.0.0", "js-cookie": "^3.0.1", "jsonwebtoken": "^8.5.1", "latest": "^0.2.0", "mic": "^2.1.2", "microphone-stream": "^6.0.1", "node-sass": "^6.0.1", "postcss": "^8.4.47", "vee-validate": "^2.2.15", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-chartjs": "^5.3.1", "vue-router": "^3.2.0", "vue3-apexcharts": "^1.5.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "watson-speech": "^0.41.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.25.3", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-loader": "^9.1.3", "cache-loader": "^4.1.0", "css-loader": "^7.1.2", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.1", "eslint-plugin-vue": "^8.5.0", "postcss-loader": "^8.1.1", "sass-loader": "^10.5.2", "style-loader": "^4.0.0", "vue-loader": "^15.11.1", "vue-template-compiler": "^2.7.16", "webpack": "^4.47.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^5.0.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}